const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');


  const WorkHour = sequelize.define('workhour', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    employeeId: {
      type: DataTypes.STRING,
      allowNull: false
    },
    employeeName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    department: {
      type: DataTypes.STRING,
      allowNull: true
    },
    date: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    startTime: {
      type: DataTypes.TIME,
      allowNull: false
    },
    endTime: {
      type: DataTypes.TIME,
      allowNull: false
    },
    workHours: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false
    },
    projectName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    remarks: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    employmentType: {
      type: DataTypes.STRING,
      allowNull: false
    },
    hourlyRate: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    payableAmount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    overtime: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    absence: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    employeeSign: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    supervisorSign: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    performanceLevel: {
      type: DataTypes.STRING,
      allowNull: true
    },
    dailyWage: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    updatedBy: {
      type: DataTypes.INTEGER,
      allowNull: true
    }
  }, {
    timestamps: true,
  });

  WorkHour.associate = (models) => {
    // Define associations here
    if (models.User) {
      WorkHour.belongsTo(models.User, {
        foreignKey: 'createdBy',
        as: 'creator'
      });
    }
  };

  module.exports = { WorkHour };

