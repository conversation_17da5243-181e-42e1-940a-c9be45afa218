// 密码修改问题诊断脚本
async function diagnosePasswordIssue() {
  const API_BASE_URL = process.env.API_URL || 'http://localhost:3009';
  
  console.log('🔍 开始诊断密码修改问题...');
  console.log('📍 API地址:', API_BASE_URL);
  
  try {
    // 1. 检查服务器状态
    console.log('\n1️⃣ 检查服务器状态...');
    try {
      const healthResponse = await fetch(`${API_BASE_URL}/health`);
      if (healthResponse.ok) {
        console.log('✅ 服务器运行正常');
      } else {
        console.log('⚠️ 服务器状态异常:', healthResponse.status);
      }
    } catch (error) {
      console.log('❌ 无法连接到服务器:', error.message);
      return;
    }
    
    // 2. 测试管理员登录
    console.log('\n2️⃣ 测试管理员登录...');
    const adminLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    if (!adminLoginResponse.ok) {
      console.log('❌ 管理员登录失败');
      const errorData = await adminLoginResponse.text();
      console.log('错误详情:', errorData);
      return;
    }
    
    const adminLoginData = await adminLoginResponse.json();
    console.log('✅ 管理员登录成功');
    const adminToken = adminLoginData.tokens?.access?.token;
    
    // 3. 创建测试用户
    console.log('\n3️⃣ 创建测试用户...');
    const testUser = {
      username: 'diagnose' + Date.now(),
      email: 'diagnose' + Date.now() + '@example.com',
      password: 'TestPass123!',
      lastName: '诊断测试用户',
      role: 'user'
    };
    
    const createResponse = await fetch(`${API_BASE_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify(testUser)
    });
    
    if (!createResponse.ok) {
      console.log('❌ 创建用户失败');
      const errorData = await createResponse.text();
      console.log('错误详情:', errorData);
      return;
    }
    
    console.log('✅ 测试用户创建成功:', testUser.username);
    
    // 4. 测试用户登录
    console.log('\n4️⃣ 测试用户登录...');
    const userLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    if (!userLoginResponse.ok) {
      console.log('❌ 用户登录失败');
      const errorData = await userLoginResponse.text();
      console.log('错误详情:', errorData);
      return;
    }
    
    const userLoginData = await userLoginResponse.json();
    console.log('✅ 用户登录成功');
    const userToken = userLoginData.tokens?.access?.token;
    
    // 5. 测试密码修改
    console.log('\n5️⃣ 测试密码修改...');
    const newPassword = 'NewPass456!';
    
    console.log('📝 修改密码请求详情:');
    console.log('  - 当前密码:', testUser.password);
    console.log('  - 新密码:', newPassword);
    console.log('  - 用户Token:', userToken ? userToken.substring(0, 20) + '...' : 'null');
    
    const changePasswordResponse = await fetch(`${API_BASE_URL}/api/auth/change-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken}`
      },
      body: JSON.stringify({
        currentPassword: testUser.password,
        newPassword: newPassword
      })
    });
    
    console.log('📊 密码修改响应状态:', changePasswordResponse.status);
    
    if (!changePasswordResponse.ok) {
      console.log('❌ 密码修改失败');
      const errorData = await changePasswordResponse.text();
      console.log('错误详情:', errorData);
      
      // 尝试解析错误信息
      try {
        const errorJson = JSON.parse(errorData);
        console.log('解析后的错误:', errorJson);
      } catch (e) {
        console.log('无法解析错误JSON');
      }
      return;
    }
    
    console.log('✅ 密码修改成功');
    
    // 6. 测试新密码登录
    console.log('\n6️⃣ 测试新密码登录...');
    
    // 等待一小段时间确保数据库更新完成
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: newPassword
      })
    });
    
    console.log('📊 新密码登录响应状态:', newLoginResponse.status);
    
    if (!newLoginResponse.ok) {
      console.log('❌ 新密码登录失败 - 这是问题所在！');
      const errorData = await newLoginResponse.text();
      console.log('错误详情:', errorData);
      
      // 尝试用原密码登录看看是否还有效
      console.log('\n🔍 检查原密码是否仍然有效...');
      const oldLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: testUser.email,
          password: testUser.password
        })
      });
      
      if (oldLoginResponse.ok) {
        console.log('⚠️ 原密码仍然有效 - 密码可能没有真正更新');
      } else {
        console.log('❌ 原密码也无效 - 可能存在密码哈希问题');
      }
      
      return;
    }
    
    console.log('✅ 新密码登录成功！');
    
    // 7. 验证原密码已失效
    console.log('\n7️⃣ 验证原密码已失效...');
    const oldPasswordResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    if (oldPasswordResponse.ok) {
      console.log('⚠️ 警告：原密码仍然有效！');
    } else {
      console.log('✅ 原密码已正确失效');
    }
    
    console.log('\n🎉 诊断完成：密码修改功能正常工作！');
    
  } catch (error) {
    console.error('\n💥 诊断过程中出现错误:', error.message);
    console.error('详细错误:', error);
  }
}

// 运行诊断
console.log('🚀 启动密码修改问题诊断...');
diagnosePasswordIssue()
  .then(() => {
    console.log('\n✅ 诊断完成');
  })
  .catch(error => {
    console.error('\n💥 诊断失败:', error);
  }); 