const { v4: uuidv4 } = require('uuid');
const { Purchase } = require('../models/purchase.model');
const { User } = require('../models/user.model');
const { Project } = require('../models/project.model');
const { Supplier } = require('../models/supplier.model');
const { faker } = require('@faker-js/faker/locale/zh_CN');

async function seedPurchases() {
  console.log('Starting purchase seeder...');
  try {
    console.log('Checking if purchases already exist...');
    // Check if purchases already exist
    const purchaseCount = await Purchase.count();
    console.log(`Found ${purchaseCount} existing purchases`);
    if (purchaseCount > 0) {
      console.log('Purchases already exist, skipping purchase seeding');
      return;
    }

    // Get or create a default admin user for createdBy field
    console.log('Finding admin user...');
    let adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      console.log('No admin user found for purchase seeding');
      return;
    }
    console.log(`Found admin user: ${adminUser.username}`);


    // Get existing projects
    console.log('Finding projects...');
    const projects = await Project.findAll({ limit: 10 });
    if (projects.length === 0) {
      console.log('No projects found for purchase seeding');
      return;
    }
    console.log(`Found ${projects.length} projects`);


    // Get existing suppliers
    console.log('Finding suppliers...');
    const suppliers = await Supplier.findAll({ limit: 10 });
    if (suppliers.length === 0) {
      console.log('No suppliers found for purchase seeding');
      return;
    }
    console.log(`Found ${suppliers.length} suppliers`);


    const purchases = [];
    const statuses = ['draft', 'pending', 'approved', 'rejected', 'completed'];
    const paymentStatuses = ['unpaid', 'partially_paid', 'paid'];
    const currencies = ['CNY', 'USD', 'EUR'];

    // Create 20 sample purchases
    console.log('Creating 20 sample purchases...');
    for (let i = 0; i < 20; i++) {
      const project = projects[Math.floor(Math.random() * projects.length)];
      const supplier = suppliers[Math.floor(Math.random() * suppliers.length)];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const paymentStatus = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
      const currency = currencies[Math.floor(Math.random() * currencies.length)];

      // Generate purchase date within the last year
      const purchaseDate = faker.date.past({ years: 1 });

      // Generate payment due date (between 7 and 60 days after purchase date)
      const paymentDueDate = new Date(purchaseDate);
      paymentDueDate.setDate(paymentDueDate.getDate() + Math.floor(Math.random() * 53) + 7);

      // Generate total amount between 1000 and 100000
      const totalAmount = faker.number.float({ min: 1000, max: 100000, precision: 2 });

      purchases.push({
        id: uuidv4(),
        projectId: project.id,
        supplierId: supplier.id,
        purchaseNumber: `PO-${String(i + 1).padStart(6, '0')}`,
        purchaseDate: purchaseDate,
        status: status,
        totalAmount: totalAmount,
        currency: currency,
        paymentStatus: paymentStatus,
        paymentDueDate: paymentDueDate,
        description: faker.lorem.paragraph(),
        notes: faker.lorem.sentences(2),
        attachments: [],
        createdBy: adminUser.id,
        createdAt: purchaseDate,
        updatedAt: purchaseDate
      });
    }

    // Bulk create purchases
    console.log(`Bulk creating ${purchases.length} purchases...`);
    await Purchase.bulkCreate(purchases);
    console.log('Successfully seeded purchases');
  } catch (error) {
    console.error('Error seeding purchases:', error);
    throw error;
  }
}

// Run the seeder if this file is executed directly
if (require.main === module) {
  seedPurchases()
    .then(() => {
      console.log('Purchase seeder completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error running purchase seeder:', error);
      process.exit(1);
    });
}

module.exports = seedPurchases;
