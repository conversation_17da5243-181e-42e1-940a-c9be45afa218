<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
      <router-link :to="`/departments/${departmentId}`" class="text-blue-600 hover:text-blue-800 mr-2">
        <i class="fas fa-arrow-left"></i>
      </router-link>
      <h1 class="text-2xl font-bold text-gray-800">编辑部门</h1>
    </div>

    <!-- Loading state -->
    <div v-if="loading && !formLoading" class="flex justify-center items-center py-10">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error message -->
    <div v-else-if="error && !formLoading" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{{ error }}</p>
      <button @click="retryFetch" class="underline mt-2">重试</button>
    </div>

    <!-- Edit form -->
    <div v-else class="bg-white shadow-md rounded-lg overflow-hidden p-6">
      <form @submit.prevent="submitForm">
        <!-- Name field -->
        <div class="mb-4">
          <label for="name" class="form-label">部门名称*</label>
          <input
            id="name"
            v-model="form.name"
            type="text"
            class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            :class="{ 'border-red-500': errors.name }"
            required
          />
          <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
        </div>

        <!-- Code field -->
        <div class="mb-4">
          <label for="code" class="form-label">部门代码*</label>
          <input
            id="code"
            v-model="form.code"
            type="text"
            class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            :class="{ 'border-red-500': errors.code }"
            required
          />
          <p v-if="errors.code" class="mt-1 text-sm text-red-600">{{ errors.code }}</p>
          <p class="mt-1 text-sm text-gray-500">请使用简短的唯一代码标识此部门，如：HR、IT、FIN等</p>
        </div>

        <!-- Description field -->
        <div class="mb-4">
          <label for="description" class="form-label">部门描述</label>
          <textarea
            id="description"
            v-model="form.description"
            rows="3"
            class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          ></textarea>
        </div>

        <!-- Parent department field -->
        <div class="mb-4">
          <label for="parentId" class="form-label">上级部门</label>
          <select
            id="parentId"
            v-model="form.parentId"
            class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">无上级部门</option>
            <option 
              v-for="dept in availableParentDepartments" 
              :key="dept.id" 
              :value="dept.id"
              :disabled="dept.id === departmentId"
            >
              {{ dept.name }}
              <span v-if="dept.id === departmentId"> (当前部门)</span>
            </option>
          </select>
        </div>

        <!-- Status field -->
        <div class="mb-6">
          <label for="status" class="form-label">状态</label>
          <select
            id="status"
            v-model="form.status"
            class="w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="active">活跃</option>
            <option value="inactive">禁用</option>
          </select>
        </div>

        <!-- Error display -->
        <div
          v-if="formError"
          class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
        >
          {{ formError }}
        </div>

        <!-- Form buttons -->
        <div class="flex justify-end">
          <router-link
            :to="`/departments`"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100 mr-2"
          >
            取消
          </router-link>
          <button
            type="submit"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            :disabled="formLoading"
          >
            <span v-if="formLoading">
              <i class="fas fa-spinner fa-spin mr-2"></i>保存中...
            </span>
            <span v-else>保存</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useDepartmentStore } from '@/stores/department';
import axios from 'axios';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'vue-toastification';

const route = useRoute();
const router = useRouter();
const departmentStore = useDepartmentStore();
const authStore = useAuthStore();
const API_URL = import.meta.env.VITE_API_URL || '';
const toast = useToast();

// Route params
const departmentId = computed(() => route.params.id);

// Form state
const form = reactive({
  name: '',
  code: '',
  description: '',
  parentId: '',
  status: 'active',
});

// UI state
const loading = ref(false);
const formLoading = ref(false);
const formError = ref('');
const errors = reactive({
  name: '',
  code: '',
});
const users = ref([]);
const departments = computed(() => departmentStore.getDepartments);
const department = computed(() => departmentStore.getCurrentDepartment);
const error = computed(() => departmentStore.getError);

// Filter out the current department and its children from potential parent departments
const availableParentDepartments = computed(() => {
  if (!departments.value) return [];
  
  // Function to get all descendant IDs recursively
  const getDescendantIds = (departmentId) => {
    const result = [];
    const childDepts = departments.value.filter(d => d.parentId === departmentId);
    
    childDepts.forEach(child => {
      result.push(child.id);
      result.push(...getDescendantIds(child.id));
    });
    
    return result;
  };
  
  // Get all descendant IDs of the current department
  const descendantIds = getDescendantIds(departmentId.value);
  
  // Filter out the current department and all its descendants
  return departments.value.filter(d => 
    d.id !== departmentId.value && !descendantIds.includes(d.id)
  );
});

// Methods
async function fetchUsers() {
  try {
    const response = await axios.get(`${API_URL}/api/users`, {
      headers: {
        Authorization: `Bearer ${authStore.token}`,
      },
    });
    users.value = response.data.data;
  } catch (error) {
    console.error('Error fetching users:', error);
  }
}

async function fetchDepartment() {
  try {
    await departmentStore.fetchDepartment(departmentId.value);
  } catch (error) {
    console.error('Error fetching department:', error);
  }
}

function populateForm() {
  if (department.value) {
    form.name = department.value.name || '';
    form.code = department.value.code || '';
    form.description = department.value.description || '';
    form.parentId = department.value.parentId || '';
    form.status = department.value.status || 'active';
  }
}

function retryFetch() {
  departmentStore.resetError();
  fetchDepartment();
}

function validateForm() {
  let isValid = true;
  errors.name = '';
  errors.code = '';

  if (!form.name.trim()) {
    errors.name = '部门名称不能为空';
    isValid = false;
  }

  if (!form.code.trim()) {
    errors.code = '部门代码不能为空';
    isValid = false;
  } else if (!/^[A-Za-z0-9_-]+$/.test(form.code)) {
    errors.code = '部门代码只能包含字母、数字、下划线和连字符';
    isValid = false;
  }

  return isValid;
}

async function submitForm() {
  if (!validateForm()) return;

  formLoading.value = true;
  formError.value = '';

  try {
    // Prepare data
    const departmentData = {
      ...form,
      parentId: form.parentId || null,
    };

    // Update department
    await departmentStore.updateDepartment(departmentId.value, departmentData);
    
    // 显示保存成功的提示信息
    toast.success(`部门 ${departmentData.name} 保存成功`);
    
    // 保存成功后跳转到部门列表页面
    router.push('/departments?refresh=true');
  } catch (error) {
    console.error('Error updating department:', error);
    formError.value = error.response?.data?.message || '更新部门时发生错误';
    toast.error(formError.value);
  } finally {
    formLoading.value = false;
  }
}

// Lifecycle hooks
onMounted(async () => {
  // Fetch departments, users, and current department
  await Promise.all([
    departmentStore.fetchDepartments(),
    fetchUsers(),
    fetchDepartment()
  ]);
  
  // Populate form with current department data
  populateForm();
});

// Watch for department changes to update form
watch(department, () => {
  populateForm();
});
</script> 