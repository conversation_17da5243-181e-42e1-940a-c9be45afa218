const httpStatus = require('http-status');
const { Employee, ContractWorker, TemporaryWorker, ContactInfo } = require('../models/employee.model');
const { sequelize } = require('../config/database');
const ApiError = require('../utils/ApiError');
const { Op } = require('sequelize');
const userService = require('./user.service');
const { v4: uuidv4 } = require('uuid');

/**
 * Create a contract worker
 * @param {Object} contractWorkerBody
 * @returns {Promise<ContractWorker>}
 */
const createContractWorker = async (contractWorkerBody) => {
  const { contactInfo, ...workerData } = contractWorkerBody;

  if (await Employee.isIdNumberTaken(workerData.idNumber)) {
    throw new ApiError(400, 'ID number already taken');
  }
  if (await ContractWorker.isContractNumberTaken(workerData.contractNumber)) {
    throw new ApiError(400, 'Contract number already taken');
  }

  // Ensure department is provided
  if (!workerData.department) {
    throw new ApiError(400, 'Department is required');
  }

  const result = await sequelize.transaction(async (t) => {
    // Create base employee record first
    const employee = await Employee.create({
      name: workerData.name,
      idNumber: workerData.idNumber,
      position: workerData.position,
      department: workerData.department,
      departmentId: workerData.departmentId,
      status: workerData.status,
      employeeType: 'contract'
    }, { transaction: t });

    // Create contract worker record
    const worker = await ContractWorker.create({
      employeeId: employee.id,
      projectId: workerData.projectId,
      contractNumber: workerData.contractNumber,
      month: workerData.month,
      startDate: workerData.startDate,
      endDate: workerData.endDate,
      salary: workerData.salary
    }, { transaction: t });

    // Create contact info if provided
    if (contactInfo) {
      await ContactInfo.create({
        ...contactInfo,
        employeeId: employee.id
      }, { transaction: t });
    }

    // 创建用户账号，授权登录系统
    try {
      // 生成默认密码（身份证后6位）
      const defaultPassword = workerData.idNumber.slice(-6);

      // 生成用户名（姓名+身份证后4位）
      const username = `${workerData.name}${workerData.idNumber.slice(-4)}`;

      // 生成邮箱（如果没有提供）
      const email = contactInfo?.email || `${username}@example.com`;

      // 创建用户账号
      const userData = {
        id: uuidv4(),
        username: username,
        email: email,
        password: defaultPassword,
        lastName: workerData.name,
        role: 'user', // 默认角色
        gender: 'other', // 默认性别
        departmentId: null, // 部门ID需要根据部门名称查询
        department: workerData.department,
        position: workerData.position,
        idNumber: workerData.idNumber,
        phoneNumber: contactInfo?.phone || '',
        address: contactInfo?.address || '',
        isActive: true,
        isAdmin: false
      };

      await userService.createUser(userData, { transaction: t });
      console.log(`为合同工 ${workerData.name} 创建了用户账号，用户名: ${username}`);
    } catch (error) {
      console.error('创建用户账号失败:', error);
      // 不阻止合同工创建，只记录错误
    }

    return {
      ...employee.toJSON(),
      contractDetails: worker,
      contactInfo
    };
  });

  return result;
};

/**
 * Query for contract workers
 * @param {Object} filter - Filter conditions
 * @param {Object} options - Query options
 * @returns {Promise<{ rows: ContractWorker[], count: number }>}
 */
const queryContractWorkers = async (filter, options) => {
  const { limit = 10, page = 1, sortBy } = options;
  const offset = (page - 1) * limit;
  const { search, month, ...otherFilters } = filter;

  // Build where clause
  const whereClause = { ...otherFilters };

  // Handle search parameter
  if (search) {
    // We'll use the include's where clause for searching in the Employee model
  }

  // Handle month parameter
  if (month) {
    whereClause.month = month;
  }

  const order = sortBy
    ? sortBy.split(',').map((sort) => {
        const [field, direction] = sort.split(':');
        return [field, direction === 'desc' ? 'DESC' : 'ASC'];
      })
    : [['createdAt', 'DESC']];

  // Prepare include for Employee with search condition if needed
  const employeeInclude = {
    model: Employee,
    as: 'employee',
    include: [
      {
        model: ContactInfo,
        as: 'contactInfo'
      }
    ]
  };

  // Add search condition to Employee model if search parameter is provided
  if (search) {
    employeeInclude.where = {
      [Op.or]: [
        { name: { [Op.iLike]: `%${search}%` } },
        { idNumber: { [Op.like]: `%${search}%` } }
      ]
    };
  }

  const { rows, count } = await ContractWorker.findAndCountAll({
    where: whereClause,
    include: [employeeInclude],
    order,
    limit,
    offset,
    distinct: true, // Important when using include with where conditions
  });

  return {
    results: rows,
    page,
    limit,
    totalPages: Math.ceil(count / limit),
    totalResults: count,
  };
};

/**
 * Get contract worker by id
 * @param {number} id
 * @returns {Promise<ContractWorker>}
 */
const getContractWorkerById = async (id) => {
  return ContractWorker.findByPk(id, {
    include: [
      {
        model: Employee,
        as: 'employee',
        include: [
          {
            model: ContactInfo,
            as: 'contactInfo'
          }
        ]
      }
    ],
  });
};

/**
 * Update contract worker by id
 * @param {number} workerId
 * @param {Object} updateBody
 * @returns {Promise<ContractWorker>}
 */
const updateContractWorkerById = async (workerId, updateBody) => {
  const { contactInfo, ...workerData } = updateBody;
  const worker = await getContractWorkerById(workerId);

  if (!worker) {
    throw new ApiError(404, 'Contract worker not found');
  }

  if (workerData.idNumber && workerData.idNumber !== worker.employee?.idNumber &&
      await ContractWorker.isIdNumberTaken(workerData.idNumber, workerId)) {
    throw new ApiError(400, '身份证号码已存在，请检查后重试');
  }
  if (workerData.contractNumber && await ContractWorker.isContractNumberTaken(workerData.contractNumber, workerId)) {
    throw new ApiError(400, 'Contract number already taken');
  }

  // Ensure department is provided if updating employee data
  if (workerData.department === '') {
    throw new ApiError(400, 'Department cannot be empty');
  }

  const result = await sequelize.transaction(async (t) => {
    // Update contract worker record
    await worker.update(workerData, { transaction: t });

    // Update employee record if it exists
    if (worker.employee && worker.employee.id) {
      const employeeUpdateData = {};
      
      // Only include fields that should be updated in the Employee model
      if (workerData.name) employeeUpdateData.name = workerData.name;
      if (workerData.idNumber) employeeUpdateData.idNumber = workerData.idNumber;
      if (workerData.position) employeeUpdateData.position = workerData.position;
      if (workerData.department) employeeUpdateData.department = workerData.department;
      if (workerData.departmentId) employeeUpdateData.departmentId = workerData.departmentId;
      if (workerData.status) employeeUpdateData.status = workerData.status;
      
      // Update the employee record if there are fields to update
      if (Object.keys(employeeUpdateData).length > 0) {
        await Employee.update(employeeUpdateData, { 
          where: { id: worker.employee.id },
          transaction: t 
        });
      }
    }

    // Update or create contact info
    if (contactInfo) {
      await ContactInfo.upsert(
        { ...contactInfo, contractWorkerId: workerId },
        { transaction: t }
      );
    }
    
    return worker;
  });

  return result;
};

/**
 * Delete contract worker by id
 * @param {number} workerId
 * @returns {Promise<ContractWorker>}
 */
const deleteContractWorkerById = async (workerId) => {
  const worker = await getContractWorkerById(workerId);
  if (!worker) {
    throw new ApiError(404, 'Contract worker not found');
  }
  await worker.destroy();
  return worker;
};

/**
 * Create a temporary worker
 * @param {Object} temporaryWorkerBody
 * @returns {Promise<TemporaryWorker>}
 */
const createTemporaryWorker = async (temporaryWorkerBody) => {
  const { contactInfo, ...workerData } = temporaryWorkerBody;

  if (await Employee.isIdNumberTaken(workerData.idNumber)) {
    throw new ApiError(400, 'ID number already taken');
  }

  // Ensure department is provided
  if (!workerData.department) {
    throw new ApiError(400, 'Department is required');
  }

  const result = await sequelize.transaction(async (t) => {
    // Create base employee record first
    const employee = await Employee.create({
      name: workerData.name,
      idNumber: workerData.idNumber,
      position: workerData.position,
      department: workerData.department,
      departmentId: workerData.departmentId,
      status: workerData.status,
      employeeType: 'temporary'
    }, { transaction: t });

    // Create temporary worker record
    const worker = await TemporaryWorker.create({
      employeeId: employee.id,
      projectId: workerData.projectId,
      startDate: workerData.startDate,
      endDate: workerData.endDate,
      dailyRate: workerData.dailyRate
    }, { transaction: t });

    // Create contact info if provided
    if (contactInfo) {
      await ContactInfo.create({
        ...contactInfo,
        employeeId: employee.id
      }, { transaction: t });
    }

    // 创建用户账号，授权登录系统
    try {
      // 生成默认密码（身份证后6位）
      const defaultPassword = workerData.idNumber.slice(-6);

      // 生成用户名（姓名+身份证后4位）
      const username = `${workerData.name}${workerData.idNumber.slice(-4)}`;

      // 生成邮箱（如果没有提供）
      const email = contactInfo?.email || `${username}@example.com`;

      // 创建用户账号
      const userData = {
        id: uuidv4(),
        username: username,
        email: email,
        password: defaultPassword,
        lastName: workerData.name,
        role: 'user', // 默认角色
        gender: 'other', // 默认性别
        departmentId: null, // 部门ID需要根据部门名称查询
        department: workerData.department,
        position: workerData.position,
        idNumber: workerData.idNumber,
        phoneNumber: contactInfo?.phone || '',
        address: contactInfo?.address || '',
        isActive: true,
        isAdmin: false
      };

      await userService.createUser(userData, { transaction: t });
      console.log(`为临时工 ${workerData.name} 创建了用户账号，用户名: ${username}`);
    } catch (error) {
      console.error('创建用户账号失败:', error);
      // 不阻止临时工创建，只记录错误
    }

    return {
      ...employee.toJSON(),
      temporaryDetails: worker,
      contactInfo
    };
  });

  return result;
};

/**
 * Query for temporary workers
 * @param {Object} filter - Filter conditions
 * @param {Object} options - Query options
 * @returns {Promise<{ rows: TemporaryWorker[], count: number }>}
 */
const queryTemporaryWorkers = async (filter, options) => {
  const { limit = 10, page = 1, sortBy } = options;
  const offset = (page - 1) * limit;
  const { search, ...otherFilters } = filter;

  // Build where clause
  const whereClause = { ...otherFilters };

  const order = sortBy
    ? sortBy.split(',').map((sort) => {
        const [field, direction] = sort.split(':');
        return [field, direction === 'desc' ? 'DESC' : 'ASC'];
      })
    : [['createdAt', 'DESC']];

  // Prepare include for Employee with search condition if needed
  const employeeInclude = {
    model: Employee,
    as: 'employee',
    include: [
      {
        model: ContactInfo,
        as: 'contactInfo'
      }
    ]
  };

  // Add search condition to Employee model if search parameter is provided
  if (search) {
    employeeInclude.where = {
      [Op.or]: [
        { name: { [Op.iLike]: `%${search}%` } },
        { idNumber: { [Op.like]: `%${search}%` } }
      ]
    };
  }

  try {
    const { rows, count } = await TemporaryWorker.findAndCountAll({
      where: whereClause,
      include: [employeeInclude],
      order,
      limit,
      offset,
      distinct: true, // Important when using include with where conditions
    });

    return {
      results: rows,
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count,
    };
  } catch (error) {
    console.error('Error querying temporary workers:', error);
    return {
      results: [],
      page,
      limit,
      totalPages: 0,
      totalResults: 0,
    };
  }
};

/**
 * Get temporary worker by id
 * @param {number} id
 * @returns {Promise<TemporaryWorker>}
 */
const getTemporaryWorkerById = async (id) => {
  return TemporaryWorker.findByPk(id, {
    include: [
      {
        model: Employee,
        as: 'employee',
        include: [
          {
            model: ContactInfo,
            as: 'contactInfo'
          }
        ]
      }
    ],
  });
};

/**
 * Update temporary worker by id
 * @param {number} workerId
 * @param {Object} updateBody
 * @returns {Promise<TemporaryWorker>}
 */
const updateTemporaryWorkerById = async (workerId, updateBody) => {
  const { contactInfo, ...workerData } = updateBody;
  const worker = await getTemporaryWorkerById(workerId);

  if (!worker) {
    throw new ApiError(404, 'Temporary worker not found');
  }

  if (workerData.idNumber && workerData.idNumber !== worker.employee?.idNumber &&
      await TemporaryWorker.isIdNumberTaken(workerData.idNumber, workerId)) {
    throw new ApiError(400, '身份证号码已存在，请检查后重试');
  }

  // Ensure department is provided if updating employee data
  if (workerData.department === '') {
    throw new ApiError(400, 'Department cannot be empty');
  }

  console.log('更新临时工数据:', JSON.stringify(workerData, null, 2));
  console.log('原始临时工数据:', JSON.stringify(worker.toJSON(), null, 2));

  const result = await sequelize.transaction(async (t) => {
    // Update employee record if it exists
    if (worker.employee) {
      // 使用明确的赋值而不是使用 || 运算符，以避免 falsy 值被忽略
      const employeeUpdateData = {
        name: workerData.name !== undefined ? workerData.name : worker.employee.name,
        idNumber: workerData.idNumber !== undefined ? workerData.idNumber : worker.employee.idNumber,
        position: workerData.position !== undefined ? workerData.position : worker.employee.position,
        department: workerData.department !== undefined ? workerData.department : worker.employee.department,
        departmentId: workerData.departmentId !== undefined ? workerData.departmentId : worker.employee.departmentId,
        status: workerData.status !== undefined ? workerData.status : worker.employee.status
      };

      console.log('更新员工基本信息:', JSON.stringify(employeeUpdateData, null, 2));
      await worker.employee.update(employeeUpdateData, { transaction: t });
    }

    // Update temporary worker record - 使用明确的赋值
    const temporaryWorkerUpdateData = {
      projectId: workerData.projectId !== undefined ? workerData.projectId : worker.projectId,
      startDate: workerData.startDate !== undefined ? workerData.startDate : worker.startDate,
      endDate: workerData.endDate !== undefined ? workerData.endDate : worker.endDate,
      dailyRate: workerData.dailyRate !== undefined ? workerData.dailyRate : worker.dailyRate
    };

    console.log('更新临时工特定信息:', JSON.stringify(temporaryWorkerUpdateData, null, 2));
    await worker.update(temporaryWorkerUpdateData, { transaction: t });

    // Update contact info if provided
    if (contactInfo && worker.employee) {
      const existingContactInfo = await ContactInfo.findOne({
        where: { employeeId: worker.employeeId }
      });

      if (existingContactInfo) {
        // 确保不会丢失现有的联系信息
        const contactUpdateData = {
          phone: contactInfo.phone !== undefined ? contactInfo.phone : existingContactInfo.phone,
          email: contactInfo.email !== undefined ? contactInfo.email : existingContactInfo.email,
          address: contactInfo.address !== undefined ? contactInfo.address : existingContactInfo.address
        };

        console.log('更新联系信息:', JSON.stringify(contactUpdateData, null, 2));
        await existingContactInfo.update(contactUpdateData, { transaction: t });
      } else {
        console.log('创建新的联系信息:', JSON.stringify(contactInfo, null, 2));
        await ContactInfo.create({
          ...contactInfo,
          employeeId: worker.employeeId
        }, { transaction: t });
      }
    }

    // 重新获取更新后的完整数据
    const updatedWorker = await TemporaryWorker.findByPk(workerId, {
      include: [
        {
          model: Employee,
          as: 'employee',
          include: [
            {
              model: ContactInfo,
              as: 'contactInfo'
            }
          ]
        }
      ],
      transaction: t
    });

    return updatedWorker;
  });

  return result;
};

/**
 * Delete temporary worker by id
 * @param {number} workerId
 * @returns {Promise<TemporaryWorker>}
 */
const deleteTemporaryWorkerById = async (workerId) => {
  const worker = await getTemporaryWorkerById(workerId);
  if (!worker) {
    throw new ApiError(404, 'Temporary worker not found');
  }
  await worker.destroy();
  return worker;
};

/**
 * Query for all employees (both contract and temporary workers)
 * @param {Object} filter - Filter conditions
 * @param {Object} options - Query options
 * @returns {Promise<{ results: Employee[], page: number, limit: number, totalPages: number, totalResults: number }>}
 */
const queryAllEmployees = async (filter, options) => {
  const { limit = 10, page = 1, sortBy } = options;
  const offset = (page - 1) * limit;
  const { search, employeeType, department, status, ...otherFilters } = filter;

  // Build where clause
  const whereClause = { ...otherFilters };

  // Add employeeType filter if provided
  if (employeeType) {
    whereClause.employeeType = employeeType;
  }

  // Add department filter if provided
  if (department) {
    whereClause.department = department;
  }

  // Add status filter if provided
  if (status) {
    whereClause.status = status;
  }

  // Add search condition if provided
  if (search) {
    whereClause[Op.or] = [
      { name: { [Op.iLike]: `%${search}%` } },
      { idNumber: { [Op.like]: `%${search}%` } }
    ];
  }

  const order = sortBy
    ? sortBy.split(',').map((sort) => {
        const [field, direction] = sort.split(':');
        return [field, direction === 'desc' ? 'DESC' : 'ASC'];
      })
    : [['createdAt', 'DESC']];

  try {
    // Query the base Employee model with includes for both contract and temporary details
    const { rows, count } = await Employee.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: ContactInfo,
          as: 'contactInfo'
        },
        {
          model: ContractWorker,
          as: 'contractDetails',
          required: false
        },
        {
          model: TemporaryWorker,
          as: 'temporaryDetails',
          required: false
        }
      ],
      order,
      limit,
      offset,
      distinct: true
    });

    return {
      results: rows,
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count,
    };
  } catch (error) {
    console.error('Error querying employees:', error);
    return {
      results: [],
      page,
      limit,
      totalPages: 0,
      totalResults: 0,
    };
  }
};

module.exports = {
  createContractWorker,
  queryContractWorkers,
  getContractWorkerById,
  updateContractWorkerById,
  deleteContractWorkerById,
  createTemporaryWorker,
  queryTemporaryWorkers,
  getTemporaryWorkerById,
  updateTemporaryWorkerById,
  deleteTemporaryWorkerById,
  queryAllEmployees,
};