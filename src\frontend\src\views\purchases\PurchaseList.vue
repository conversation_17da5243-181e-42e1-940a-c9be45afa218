<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">采购管理</h1>
        <p class="page-subtitle">管理和跟踪采购订单信息</p>
      </div>
      <router-link to="/purchases/create" class="btn btn-primary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        新建采购单
      </router-link>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-card">
      <div class="flex justify-between gap-4">
        <div class="flex-1">
          <label for="supplier-filter" class="form-label font-medium">供应商</label>
          <select
            id="supplier-filter"
            v-model="filters.supplier"
            class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
          >
            <option value="">全部供应商</option>
            <option v-for="supplier in suppliers" :key="supplier.id" :value="supplier.id">
              {{ supplier.name }}
            </option>
          </select>
        </div>
        <div class="flex-1">
          <label for="status-filter" class="form-label font-medium">状态</label>
          <select
            id="status-filter"
            v-model="filters.status"
            class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
          >
            <option value="">全部状态</option>
            <option value="pending">待审核</option>
            <option value="approved">已审核</option>
            <option value="completed">已完成</option>
            <option value="cancelled">已取消</option>
          </select>
        </div>
        <div class="flex-1">
          <label for="date-from" class="form-label font-medium">开始日期</label>
          <input
            type="date"
            id="date-from"
            v-model="filters.dateFrom"
            class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
            max="3000-12-31"
          />
        </div>
        <div class="flex-1">
          <label for="date-to" class="form-label font-medium">结束日期</label>
          <input
            type="date"
            id="date-to"
            v-model="filters.dateTo"
            class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
            max="3000-12-31"
          />
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button
          @click="resetFilters"
          class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200"
          :disabled="loading"
        >
          重置
        </button>
        <button
          @click="applyFilters"
          class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200"
          :disabled="loading"
        >
          <span v-if="loading" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            查询中...
          </span>
          <span v-else>查询</span>
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="card overflow-hidden shadow-md p-8 text-center">
      <div class="flex justify-center">
        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
      <p class="mt-4 text-gray-600">正在加载采购单数据...</p>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="card overflow-hidden shadow-md bg-red-50 border-l-4 border-red-500 p-4">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-700">{{ error }}</p>
        </div>
      </div>
      <div class="mt-4 flex justify-center">
        <button @click="fetchPurchases" class="btn btn-primary">
          <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          重新加载
        </button>
      </div>
    </div>

    <!-- 空数据提示 -->
    <div v-else-if="!loading && purchases.length === 0" class="card overflow-hidden shadow-md p-8 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">暂无采购单数据</h3>
      <p class="mt-1 text-sm text-gray-500">您还没有任何采购单记录。点击下方按钮开始创建您的第一个采购单吧！</p>
      <div class="mt-6">
        <router-link to="/purchases/create" class="btn btn-primary">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          新建采购单
        </router-link>
      </div>
    </div>

    <!-- 表格区域 -->
    <div v-else class="card overflow-hidden shadow-md">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">采购单号</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">采购日期</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总金额</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="purchase in purchases" :key="purchase.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{{ purchase.purchaseNumber || purchase.id }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 relative group">
                <div class="cursor-pointer hover:text-blue-600 transition-colors duration-200">
                  {{ purchase.supplier?.name || 'N/A' }}
                  <span class="ml-1 text-gray-400">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </span>
                </div>

                <!-- 供应商信息悬浮卡片 -->
                <div v-if="purchase.supplierInfo" class="absolute z-10 left-0 mt-2 w-72 bg-white rounded-lg shadow-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform group-hover:translate-y-0 translate-y-1 border border-gray-200">
                  <div class="p-4">
                    <div class="flex items-center justify-between mb-3">
                      <h3 class="text-sm font-bold text-gray-800">{{ purchase.supplierName }}</h3>
                      <span v-if="purchase.supplierInfo.preferred" class="badge badge-blue">首选供应商</span>
                    </div>

                    <div class="space-y-2 text-xs">
                      <div class="flex justify-between">
                        <span class="text-gray-500">联系人：</span>
                        <span class="text-gray-800 font-medium">{{ purchase.supplierInfo.contactName || '无' }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-gray-500">联系电话：</span>
                        <span class="text-gray-800 font-medium">{{ purchase.supplierInfo.phone || '无' }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-gray-500">电子邮箱：</span>
                        <span class="text-gray-800 font-medium">{{ purchase.supplierInfo.email || '无' }}</span>
                      </div>
                      <div v-if="purchase.supplierInfo.cooperationYears" class="flex justify-between">
                        <span class="text-gray-500">合作时长：</span>
                        <span class="text-gray-800 font-medium">{{ purchase.supplierInfo.cooperationYears }}年</span>
                      </div>
                      <div v-if="purchase.supplierInfo.rating" class="flex justify-between">
                        <span class="text-gray-500">信用评级：</span>
                        <div class="flex text-yellow-400">
                          <svg v-for="star in 5" :key="star" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4"
                               :class="{'text-yellow-400': star <= purchase.supplierInfo.rating, 'text-gray-300': star > purchase.supplierInfo.rating}"
                               viewBox="0 0 20 20" fill="currentColor">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        </div>
                      </div>
                    </div>

                    <div class="mt-3 pt-3 border-t border-gray-100 flex justify-between">
                      <span class="text-xs text-gray-500">历史订单：{{ purchase.supplierInfo.orderCount || 0 }}笔</span>
                      <router-link :to="`/suppliers/${purchase.supplierId}`" class="text-xs text-blue-600 hover:text-blue-800 font-medium">查看详情</router-link>
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ new Date(purchase.purchaseDate).toLocaleDateString() }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ (purchase.totalAmount || 0).toLocaleString('zh-CN', { style: 'currency', currency: purchase.currency || 'CNY' }) }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="badge" :class="{
                  'badge-success': purchase.status === 'completed',
                  'badge-warning': purchase.status === 'pending',
                  'badge-blue': purchase.status === 'approved',
                  'badge-red': purchase.status === 'cancelled'
                }">
                  {{ getStatusText(purchase.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                <router-link
                  :to="`/purchases/edit/${purchase.id}`"
                  class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                >
                  <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                  </svg>
                  <span class="font-medium">编辑</span>
                </router-link>
                <a
                  @click.prevent="deletePurchase(purchase.id)"
                  class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group cursor-pointer"
                >
                  <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                  </svg>
                  <span class="font-medium">删除</span>
                </a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="mt-4 flex justify-between items-center px-6 py-4 border-t border-gray-200">
        <div class="text-sm text-gray-500">
          显示第 <span class="font-medium">{{ (pagination.currentPage - 1) * pagination.itemsPerPage + 1 }}</span>
          至 <span class="font-medium">{{ Math.min(pagination.currentPage * pagination.itemsPerPage, pagination.totalItems) }}</span> 条，
          共 <span class="font-medium">{{ pagination.totalItems }}</span> 条记录
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="changePage(pagination.currentPage - 1)"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="pagination.currentPage <= 1 || loading"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <span class="text-sm font-medium text-gray-700">第 {{ pagination.currentPage }} / {{ pagination.totalPages }} 页</span>
          <button
            @click="changePage(pagination.currentPage + 1)"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm"
            :disabled="pagination.currentPage >= pagination.totalPages || loading"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 animate-fadeIn">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all duration-300 ease-in-out" :class="{'scale-100 opacity-100': showDeleteModal, 'scale-95 opacity-0': !showDeleteModal}">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-1">确认删除</h3>
          <p class="text-gray-500">
            您确定要删除采购单号为 "{{ purchaseToDelete?.purchaseOrderNo || '' }}" 的记录吗？请注意，此操作执行后将无法恢复，相关的采购数据将被永久删除。删除采购记录可能会影响财务报表和库存数据，请确认此记录确实不再需要保留。
          </p>

          <div class="mt-6 flex justify-center space-x-3">
            <button
              type="button"
              @click="closeDeleteModal"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="button"
              @click="performDelete"
              class="btn btn-danger"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                删除中...
              </span>
              <span v-else>确认删除</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作结果通知 -->
    <div v-if="notification.show"
      class="fixed bottom-4 right-4 p-4 rounded-lg shadow-lg transform transition-all duration-500 max-w-md animate-slideInUp"
      :class="{
        'bg-green-50 text-green-800 border border-green-200': notification.type === 'success',
        'bg-red-50 text-red-800 border border-red-200': notification.type === 'error'
      }">
      <div class="flex">
        <div v-if="notification.type === 'success'" class="flex-shrink-0">
          <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div v-else class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium">{{ notification.message }}</p>
        </div>
        <div class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button
              @click="notification.show = false"
              class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2"
              :class="{
                'text-green-500 hover:bg-green-100 focus:ring-green-600 focus:ring-offset-green-50': notification.type === 'success',
                'text-red-500 hover:bg-red-100 focus:ring-red-600 focus:ring-offset-red-50': notification.type === 'error'
              }">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';

const router = useRouter();
const route = useRoute();

// 状态变量
const loading = ref(false);
const error = ref(null);
const purchases = ref([]);
const suppliers = ref([]);

// 分页参数
const pagination = reactive({
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: 10
});

// 过滤条件
const filters = reactive({
  supplier: '',
  status: '',
  dateFrom: '',
  dateTo: ''
});


// 删除相关状态
const showDeleteModal = ref(false);
const purchaseToDelete = ref(null);
const isDeleting = ref(false);

// 通知状态
const notification = ref({
  show: false,
  message: '',
  type: 'success',
  timeout: null
});

// 显示通知
const showNotification = (message, type = 'success') => {
  // 清除之前的超时计时器
  if (notification.value.timeout) {
    clearTimeout(notification.value.timeout);
  }

  // 设置新的通知
  notification.value = {
    show: true,
    message,
    type,
    timeout: setTimeout(() => {
      notification.value.show = false;
    }, 5000)  // 5秒后自动关闭
  };
};

// 打开删除确认模态框
const confirmDelete = (purchase) => {
  purchaseToDelete.value = purchase;
  showDeleteModal.value = true;
};

// 关闭删除确认模态框
const closeDeleteModal = () => {
  showDeleteModal.value = false;
  setTimeout(() => {
    purchaseToDelete.value = null;
  }, 300); // 等待动画完成后再重置
};

// 执行删除操作
const performDelete = async () => {
  if (!purchaseToDelete.value) return;

  isDeleting.value = true;
  const id = purchaseToDelete.value.id;

  try {
    // 调用API删除采购记录
    console.log(`删除采购记录: ${id}`);

    // 实际删除操作
    await axios.delete(`/api/purchases/${id}`);

    // 从列表中移除被删除的采购记录
    purchases.value = purchases.value.filter(p => p.id !== id);

    // 更新分页信息
    pagination.totalItems--;
    pagination.totalPages = Math.ceil(pagination.totalItems / pagination.itemsPerPage) || 1;

    // 如果当前页没有数据了，则返回上一页
    if (purchases.value.length === 0 && pagination.currentPage > 1) {
      pagination.currentPage--;
      await fetchPurchases();
    }

    // 关闭模态框
    closeDeleteModal();

    // 显示成功通知
    showNotification('恭喜！您的采购记录已成功删除。系统数据已更新，相关的财务和库存信息也已同步调整。');
  } catch (err) {
    console.error('删除失败:', err);
    // 显示错误通知
    showNotification(err.response?.data?.message || '很抱歉，删除采购记录时遇到了问题。这可能是由于网络连接问题或系统临时故障导致的。请稍后再次尝试，如果问题持续存在，请联系系统管理员获取帮助。', 'error');
  } finally {
    isDeleting.value = false;
  }
};

// 删除采购记录
const deletePurchase = (id) => {
  const purchase = purchases.value.find(p => p.id === id);
  if (purchase) {
    confirmDelete(purchase);
  }
};

// 格式化金额
function formatCurrency(value) {
  if (!value && value !== 0) return '0.00';
  return parseFloat(value).toFixed(2);
}

// 获取状态文本
function getStatusText(status) {
  switch (status) {
    case 'pending': return '待审核';
    case 'approved': return '已审核';
    case 'completed': return '已完成';
    case 'cancelled': return '已取消';
    default: return '未知状态';
  }
}

// 获取采购单列表
async function fetchPurchases() {
  loading.value = true;
  error.value = null;

  try {
    // 构建查询参数
    const params = {
      page: pagination.currentPage,
      limit: pagination.itemsPerPage,
      ...filters
    };

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (!params[key]) delete params[key];
    });

    const response = await axios.get('/api/purchases', { params });
    console.log('API response:', response.data);
    purchases.value = response.data.items || [];

    // 更新分页信息
    pagination.totalItems = response.data.meta?.totalItems || 0;
    pagination.totalPages = response.data.meta?.totalPages || Math.ceil(pagination.totalItems / pagination.itemsPerPage) || 1;

  } catch (err) {
    console.error('获取采购单列表失败:', err);
    error.value = err.response?.data?.message || '很抱歉，无法获取采购单列表。这可能是由于网络连接问题或系统临时故障导致的。请检查您的网络连接，然后点击下方的"重新加载"按钮再次尝试，如果问题持续存在，请联系系统管理员获取帮助。';
  } finally {
    loading.value = false;
  }
}

// 获取供应商列表
async function fetchSuppliers() {
  try {
    const response = await axios.get('/api/suppliers');
    suppliers.value = response.data.results || [];
  } catch (err) {
    console.error('获取供应商列表失败:', err);
  }
}

// 切换页面
function changePage(page) {
  pagination.currentPage = page;
  fetchPurchases();
}

// 重置过滤条件
function resetFilters() {
  Object.keys(filters).forEach(key => {
    filters[key] = '';
  });
}

// 应用过滤条件
function applyFilters() {
  pagination.currentPage = 1; // 重置到第一页
  fetchPurchases();
}

onMounted(async () => {
  console.log('采购管理页面已加载');

  // 检查URL中是否有refresh参数，用于创建/编辑后的自动刷新
  const shouldRefresh = route.query.refresh === 'true';
  if (shouldRefresh) {
    // 移除refresh参数，避免刷新页面时重复触发
    router.replace({ path: route.path });
  }

  // 并行加载数据
  await Promise.all([
    fetchPurchases(),
    fetchSuppliers()
  ]);

  // 如果是从创建/编辑页面返回，显示成功通知
  if (shouldRefresh) {
    showNotification('恭喜！您的采购数据已成功更新。系统已保存所有修改内容，现在显示的是最新信息。您可以继续管理采购记录或进行其他操作。');
  }
});
</script>

<style scoped>
/* Page transitions */
.page-header, .filter-card, .card {
  animation: fadeSlideIn 0.6s ease-out;
}

.page-header {
  animation-delay: 0s;
}

.filter-card {
  animation-delay: 0.1s;
}

.card {
  animation-delay: 0.2s;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

/* Badge animations */
.badge-success {
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(74, 222, 128, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0);
  }
}
</style>