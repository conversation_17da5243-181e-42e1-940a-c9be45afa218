const { User } = require('../models');
const { Project } = require('../models/project.model');

async function generateRandomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

async function generateRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

async function seedProjects() {
  try {
    // Get or create a default admin user for createdBy field
    let adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      adminUser = await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin'
      });
    }

    // Check if projects already exist
    const projectCount = await Project.count();
    if (projectCount > 0) {
      console.log('Projects already exist, skipping project seeding');
      return;
    }

    // Since we're only seeding if the table is empty, we don't need to check for duplicates
    // We'll log that we're creating new projects

    console.log('Creating new projects');

    // Generate 100 projects
    const projects = [];
    let nextIndex = 1;

    // Since existingCodes is now always empty, we can start with index 1
    // This code is kept for reference if we need to check for duplicates in the future
    // while (existingCodes.has(`P${String(nextIndex).padStart(6, '0')}`)) {
    //   nextIndex++;
    // }

    for (let i = 0; i < 1; i++) {
      const status = ['planning', 'in_progress', 'completed', 'on_hold', 'cancelled'][Math.floor(Math.random() * 5)];
      const progress = Math.floor(Math.random() * 100);
      const startDate = await generateRandomDate(new Date(2023, 0, 1), new Date(2024, 11, 31));
      const endDate = await generateRandomDate(new Date(startDate), new Date(2025, 11, 31));

      // Generate unique code
      let code = `P${String(nextIndex).padStart(6, '0')}`;
      let projectNumber = `PN${String(nextIndex).padStart(6, '0')}`;
      nextIndex++;

      projects.push({
        code: code,
        name: `项目${nextIndex-1}`,
        projectNumber: projectNumber,
        members: [],
        manager: `负责人${await generateRandomNumber(1, 10)}`,
        constructionUnit: `建设单位${await generateRandomNumber(1, 20)}`,
        designUnit: `设计单位${await generateRandomNumber(1, 20)}`,
        contractorUnit: `施工单位${await generateRandomNumber(1, 20)}`,
        supervisorUnit: `监理单位${await generateRandomNumber(1, 20)}`,
        review_unit: `审图单位${await generateRandomNumber(1, 20)}`,
        address: `地址${await generateRandomNumber(1, 100)}`,
        startDate: startDate,
        endDate: endDate,
        archiveNumber: `A${String(i).padStart(6, '0')}`,
        engineeringNumber: `E${String(i).padStart(6, '0')}`,
        usageType: ['商业', '住宅', '工业', '公共建筑'][Math.floor(Math.random() * 4)],
        engineeringType: '建筑工程', // 使用固定值，确保不会为空
        constructionPermitNumber: `C${String(i).padStart(6, '0')}`,
        area: await generateRandomNumber(100, 10000),
        specialSystem: `特殊系统${await generateRandomNumber(1, 5)}`,
        companyManager: `公司负责人${await generateRandomNumber(1, 10)}`,
        status,
        progress,
        clientContact: `对接人${await generateRandomNumber(1, 10)}`,
        tags: ['重要', '紧急', '常规'].slice(0, Math.floor(Math.random() * 3) + 1),
        createdBy: adminUser.id
      });
    }

    // Bulk create projects
    if (projects.length > 0) {
      await Project.bulkCreate(projects);
      console.log(`Successfully seeded ${projects.length} projects`);
    } else {
      console.log('No new projects to seed');
    }
  } catch (error) {
    console.error('Error seeding projects:', error);
    throw error;
  }
}

module.exports = seedProjects;
