const { expect } = require('chai');
const Joi = require('joi');
const supplierValidation = require('../../../src/validations/supplier.validation');

// Add validate function if it doesn't exist
if (!supplierValidation.validate) {
  supplierValidation.validate = (data, schemaName) => {
    const schema = supplierValidation[schemaName];
    if (!schema) {
      throw new Error(`Schema ${schemaName} not found`);
    }

    // For API validation, we use body schema
    const validationSchema = schema.body || schema;
    return validationSchema.validate(data, { abortEarly: false });
  };
}

describe('Supplier Validation', () => {
  describe('createSupplier', () => {
    it('should validate a valid supplier', () => {
      const validSupplier = {
        name: 'Test Supplier',
        category: 'material',
        address: 'Test Address',
        phone: '13800138000',
        contactName: 'Test Contact',
        contactPhone: '13900139000',
        contactEmail: '<EMAIL>',
        rating: 4,
        products: [],
        notes: 'Test notes',
        tags: ['tag1', 'tag2'],
        createdBy: '00000000-0000-0000-0000-000000000000' // 添加必需的 createdBy 字段
      };

      const { error } = supplierValidation.validate(validSupplier, 'createSupplier');
      expect(error).to.be.undefined;
    });

    it('should require name field', () => {
      const invalidSupplier = {
        category: 'material',
        address: 'Test Address',
        phone: '13800138000'
      };

      const { error } = supplierValidation.validate(invalidSupplier, 'createSupplier');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('name');
    });

    it('should require category field', () => {
      const invalidSupplier = {
        name: 'Test Supplier',
        address: 'Test Address',
        phone: '13800138000'
      };

      const { error } = supplierValidation.validate(invalidSupplier, 'createSupplier');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('category');
    });

    it('should validate email format', () => {
      const invalidSupplier = {
        name: 'Test Supplier',
        category: 'material',
        address: 'Test Address',
        phone: '13800138000',
        contactEmail: 'invalid-email'
      };

      const { error } = supplierValidation.validate(invalidSupplier, 'createSupplier');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('contactEmail');
    });

    it('should validate rating is a number', () => {
      const invalidSupplier = {
        name: 'Test Supplier',
        category: 'material',
        address: 'Test Address',
        phone: '13800138000',
        rating: 'high'
      };

      const { error } = supplierValidation.validate(invalidSupplier, 'createSupplier');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('rating');
    });

    it('should allow optional fields to be missing', () => {
      const minimalSupplier = {
        name: 'Test Supplier',
        category: 'material',
        createdBy: '00000000-0000-0000-0000-000000000000' // 添加必需的 createdBy 字段
      };

      const { error } = supplierValidation.validate(minimalSupplier, 'createSupplier');
      expect(error).to.be.undefined;
    });
  });

  describe('updateSupplier', () => {
    it('should validate a valid supplier update', () => {
      const validUpdate = {
        name: 'Updated Supplier',
        address: 'Updated Address',
        notes: 'Updated notes'
      };

      const { error } = supplierValidation.validate(validUpdate, 'updateSupplier');
      expect(error).to.be.undefined;
    });

    it('should require at least one field for update', () => {
      const emptyUpdate = {};

      const { error } = supplierValidation.validate(emptyUpdate, 'updateSupplier');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('at least');
    });

    it('should validate email when provided', () => {
      const invalidUpdate = {
        contactEmail: 'invalid-email'
      };

      const { error } = supplierValidation.validate(invalidUpdate, 'updateSupplier');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('contactEmail');
    });

    it('should validate rating when provided', () => {
      const invalidUpdate = {
        rating: 'high'
      };

      const { error } = supplierValidation.validate(invalidUpdate, 'updateSupplier');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('rating');
    });
  });
});
