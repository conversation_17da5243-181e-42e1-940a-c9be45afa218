const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { contractService } = require('../services');

/**
 * Create a new contract
 * @route POST /api/contracts
 */
const createContract = catchAsync(async (req, res) => {
  const contract = await contractService.createContract({
    ...req.body,
    createdBy: req.user.id
  });
  res.status(201).send(contract);
});

/**
 * Get a contract by ID
 * @route GET /api/contracts/:contractId
 */
const getContract = catchAsync(async (req, res) => {
  const contract = await contractService.getContractById(req.params.contractId);
  res.send(contract);
});

/**
 * Update a contract
 * @route PATCH /api/contracts/:contractId
 */
const updateContract = catchAsync(async (req, res) => {
  const contract = await contractService.updateContractById(req.params.contractId, req.body);
  res.send(contract);
});

/**
 * Delete a contract
 * @route DELETE /api/contracts/:contractId
 */
const deleteContract = catchAsync(async (req, res) => {
  await contractService.deleteContractById(req.params.contractId);
  res.status(204).send();
});

/**
 * Get all contracts with pagination
 * @route GET /api/contracts
 */
const getContracts = catchAsync(async (req, res) => {
  const filter = {
    projectId: req.query.projectId,
    status: req.query.status,
    contractType: req.query.contractType,
    search: req.query.search,
    startDateFrom: req.query.startDateFrom,
    startDateTo: req.query.startDateTo,
    endDateFrom: req.query.endDateFrom,
    endDateTo: req.query.endDateTo
  };
  const options = {
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10,
    sortBy: req.query.sortBy || 'createdAt',
    sortOrder: req.query.sortOrder || 'desc'
  };
  const result = await contractService.queryContracts(filter, options);
  res.send(result);
});

/**
 * Get all contracts for a project
 * @route GET /api/projects/:projectId/contracts
 */
const getProjectContracts = catchAsync(async (req, res) => {
  const filter = {
    projectId: req.params.projectId,
    status: req.query.status,
    contractType: req.query.contractType,
    search: req.query.search
  };
  const options = {
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10,
    sortBy: req.query.sortBy || 'createdAt',
    sortOrder: req.query.sortOrder || 'desc'
  };
  const result = await contractService.queryContracts(filter, options);
  res.send(result);
});

module.exports = {
  createContract,
  getContract,
  updateContract,
  deleteContract,
  getContracts,
  getProjectContracts
}; 