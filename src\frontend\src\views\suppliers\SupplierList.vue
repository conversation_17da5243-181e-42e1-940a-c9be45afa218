<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">{{ isPublicMode ? '供应商展示' : '供应商管理' }}</h1>
        <p class="page-subtitle">{{ isPublicMode ? '查看公开的供应商信息' : '管理和维护供应商关系信息' }}</p>
      </div>
      <div class="flex space-x-3">
        <router-link v-if="isPublicMode" to="/login" class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
          </svg>
          登录系统
        </router-link>
        <router-link v-if="!isPublicMode" to="/suppliers/create" class="btn btn-primary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          新建供应商
        </router-link>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-card">
      <div class="flex justify-between gap-4">
        <div class="flex-1">
          <label for="search" class="form-label font-medium">搜索供应商</label>
          <div class="relative">
            <input
              id="search"
              v-model="searchQuery"
              type="text"
              placeholder="搜索供应商名称、联系人或电话..."
              class="form-input pl-10 py-2 mt-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
              @input="debouncedFetchSuppliers"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <label for="type-filter" class="form-label font-medium">供应商类型</label>
          <select id="type-filter" v-model="typeFilter" class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200" @change="fetchSuppliers">
            <option value="">全部类型</option>
            <option value="manufacturer">制造商</option>
            <option value="distributor">经销商</option>
            <option value="wholesaler">批发商</option>
            <option value="retailer">零售商</option>
          </select>
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button @click="resetFilters" class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200">重置</button>
        <button @click="fetchSuppliers" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200">查询</button>
      </div>
    </div>

    <!-- 供应商列表 -->
    <div v-if="loading" class="text-center py-12">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-4 text-gray-600 font-medium">加载供应商中...</p>
    </div>

    <div v-else-if="suppliers.length === 0" class="card text-center py-16 shadow-md">
      <svg class="w-20 h-20 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
      </svg>
      <h3 class="mt-6 text-xl font-medium text-gray-900">暂无供应商</h3>
      <p class="mt-3 text-gray-600 max-w-md mx-auto">
        {{ searchQuery || typeFilter ? '尝试调整搜索条件或筛选条件' : '点击新建供应商按钮创建您的第一个供应商' }}
      </p>
      <div class="mt-8" v-if="!searchQuery && !typeFilter">
        <router-link to="/suppliers/create" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200">
          创建第一个供应商
        </router-link>
      </div>
    </div>

    <div v-else>
      <!-- 供应商表格 -->
      <div class="card overflow-hidden shadow-md">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商名称</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系人</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类别</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="supplier in suppliers" :key="supplier.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center">
                      {{ getSupplierInitials(supplier.name) }}
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 hover:text-blue-600">
                        <router-link :to="`/suppliers/${supplier.id}`">{{ supplier.name }}</router-link>
                      </div>
                      <div class="text-xs text-gray-500">{{ supplier.code }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ supplier.contactName }}</div>
                  <div class="text-xs text-gray-500">{{ supplier.contactTitle }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ supplier.phone }}</div>
                  <div class="text-xs text-gray-500">{{ supplier.email }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="['badge', getCategoryClass(supplier.category)]">
                    {{ getCategoryText(supplier.category) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="['badge', getStatusClass(supplier.status)]">
                    {{ getStatusText(supplier.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">                  <router-link
                    :to="`/suppliers/${supplier.id}`"
                    class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                    </svg>
                    <span class="font-medium">编辑</span>
                  </router-link>
                  <a
                    v-if="!isPublicMode"
                    @click.prevent="deleteSupplier(supplier.id)"
                    class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group cursor-pointer"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                    <span class="font-medium">删除</span>
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 分页 -->
      <div class="mt-8 flex justify-between items-center px-2">
        <div class="text-sm text-gray-500">
          显示 {{ suppliers.length }} 个供应商中的 {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, totalSuppliers) }} 个
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="prevPage"
            :disabled="currentPage === 1"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm"
            :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <span class="text-sm font-medium text-gray-700">第 {{ currentPage }} 页</span>
          <button
            @click="nextPage"
            :disabled="currentPage * pageSize >= totalSuppliers"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm"
            :class="{'opacity-50 cursor-not-allowed': currentPage * pageSize >= totalSuppliers}"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 animate-fadeIn">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all duration-300 ease-in-out" :class="{'scale-100 opacity-100': showDeleteModal, 'scale-95 opacity-0': !showDeleteModal}">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-1">确认删除</h3>
          <p class="text-gray-500">
            您确定要删除供应商 "{{ supplierToDelete?.name || '' }}" 吗？此操作无法撤销。
          </p>

          <div class="mt-6 flex justify-center space-x-3">
            <button
              type="button"
              @click="closeDeleteModal"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="button"
              @click="performDelete"
              class="btn btn-danger"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                删除中...
              </span>
              <span v-else>确认删除</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作结果通知 -->
    <div v-if="notification.show"
      class="fixed bottom-4 right-4 p-4 rounded-lg shadow-lg transform transition-all duration-500 max-w-md animate-slideInUp"
      :class="{
        'bg-green-50 text-green-800 border border-green-200': notification.type === 'success',
        'bg-red-50 text-red-800 border border-red-200': notification.type === 'error'
      }">
      <div class="flex">
        <div v-if="notification.type === 'success'" class="flex-shrink-0">
          <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div v-else class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium">{{ notification.message }}</p>
        </div>
        <div class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button
              @click="notification.show = false"
              class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2"
              :class="{
                'text-green-500 hover:bg-green-100 focus:ring-green-600 focus:ring-offset-green-50': notification.type === 'success',
                'text-red-500 hover:bg-red-100 focus:ring-red-600 focus:ring-offset-red-50': notification.type === 'error'
              }">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import apiService from '@/services/apiService';

// 路由
const router = useRouter();
const route = useRoute();
const isPublicMode = computed(() => route.path.includes('/public'));

// 状态变量
const loading = ref(false);
const error = ref(null);
const suppliers = ref([]);
const totalSuppliers = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchQuery = ref('');
const typeFilter = ref('');

// 删除相关状态
const showDeleteModal = ref(false);
const supplierToDelete = ref(null);
const isDeleting = ref(false);

// 通知状态
const notification = ref({
  show: false,
  message: '',
  type: 'success',
  timeout: null
});

// 获取供应商列表
const fetchSuppliers = async () => {
  loading.value = true;
  error.value = null;

  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      category: typeFilter.value,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    };

    // 根据模式选择不同的API端点
    const response = isPublicMode.value
      ? await apiService.getPublicSuppliers(params)
      : await apiService.getSuppliers(params);

    suppliers.value = response.results.map(supplier => ({
      ...supplier,
      id: supplier._id || supplier.id // 确保始终有 id 字段
    }));
    totalSuppliers.value = response.totalResults;
  } catch (err) {
    console.error('获取供应商列表失败:', err);
    error.value = err.response?.data?.message || '获取供应商列表失败';
    showNotification(error.value, 'error');
  } finally {
    loading.value = false;
  }
};

// 防抖处理搜索查询
const debouncedFetchSuppliers = (() => {
  let timeout;
  return () => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      fetchSuppliers();
    }, 300);
  };
})();

// 分页处理
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchSuppliers();
  }
};

const nextPage = () => {
  if (currentPage.value * pageSize.value < totalSuppliers.value) {
    currentPage.value++;
    fetchSuppliers();
  }
};

// 获取供应商类别样式和文本
const getCategoryClass = (category) => {
  switch (category) {
    case 'manufacturer': return 'badge-blue';
    case 'distributor': return 'badge-purple';
    case 'wholesaler': return 'badge-green';
    case 'retailer': return 'badge-yellow';
    default: return 'badge-gray';
  }
};

const getCategoryText = (category) => {
  switch (category) {
    case 'manufacturer': return '制造商';
    case 'distributor': return '经销商';
    case 'wholesaler': return '批发商';
    case 'retailer': return '零售商';
    case 'other': return '其他';
    default: return '未知';
  }
};

// 获取供应商状态样式和文本
const getStatusClass = (status) => {
  switch (status) {
    case 'active': return 'badge-green';
    case 'inactive': return 'badge-gray';
    case 'potential': return 'badge-yellow';
    default: return 'badge-gray';
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 'active': return '合作中';
    case 'inactive': return '已暂停';
    case 'potential': return '潜在';
    default: return '未知';
  }
};

// 获取供应商名称首字母
const getSupplierInitials = (name) => {
  if (!name) return '?';

  const names = name.split(' ');
  if (names.length === 1) {
    return name.charAt(0).toUpperCase();
  }
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
};

// 显示通知
const showNotification = (message, type = 'success') => {
  // 清除之前的超时计时器
  if (notification.value.timeout) {
    clearTimeout(notification.value.timeout);
  }

  // 设置新的通知
  notification.value = {
    show: true,
    message,
    type,
    timeout: setTimeout(() => {
      notification.value.show = false;
    }, 5000)  // 5秒后自动关闭
  };
};

// 打开删除确认模态框
const confirmDelete = (supplier) => {
  supplierToDelete.value = supplier;
  showDeleteModal.value = true;
};

// 关闭删除确认模态框
const closeDeleteModal = () => {
  showDeleteModal.value = false;
  setTimeout(() => {
    supplierToDelete.value = null;
  }, 300); // 等待动画完成后再重置
};

// 执行删除操作
const performDelete = async () => {
  if (!supplierToDelete.value) return;

  isDeleting.value = true;
  const id = supplierToDelete.value._id || supplierToDelete.value.id;

  try {
    await apiService.deleteSupplier(id);

    // 从列表中移除被删除的供应商
    suppliers.value = suppliers.value.filter(s => (s._id || s.id) !== id);
    totalSuppliers.value--;

    // 如果当前页没有数据了，且不是第一页，则回到上一页
    if (suppliers.value.length === 0 && currentPage.value > 1) {
      currentPage.value--;
      await fetchSuppliers();
    }

    // 关闭模态框
    closeDeleteModal();

    // 显示成功通知
    showNotification('供应商删除成功！');
  } catch (error) {
    console.error('删除失败:', error);
    // 显示错误通知
    showNotification(error.response?.data?.message || '删除失败，请稍后重试。', 'error');
  } finally {
    isDeleting.value = false;
  }
};

// 删除供应商
const deleteSupplier = (id) => {
  const supplier = suppliers.value.find(s => s.id === id);
  if (supplier) {
    confirmDelete(supplier);
  }
};

// 重置过滤器
const resetFilters = () => {
  searchQuery.value = '';
  typeFilter.value = '';
  fetchSuppliers();
};

// 初始加载
onMounted(() => {
  fetchSuppliers();
});
</script>

<style scoped>
/* Page transitions */
.page-header, .filter-card, .card {
  animation: fadeSlideIn 0.6s ease-out;
}

.page-header {
  animation-delay: 0s;
}

.filter-card {
  animation-delay: 0.1s;
}

.card {
  animation-delay: 0.2s;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}
</style>