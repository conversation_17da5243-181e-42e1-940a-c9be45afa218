/* Dark Mode Styles */

/* Base styles for dark mode */
.dark {
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --bg-tertiary: #2d2d2d;
  --text-primary: #e0e0e0;
  --text-secondary: #a0a0a0;
  --text-tertiary: #707070;
  --border-color: #3a3a3a;
  --primary-color: #6366f1;
  --primary-focus: #4f46e5;
  --primary-content: #ffffff;
  --secondary-color: #4b5563;
  --secondary-focus: #374151;
  --secondary-content: #ffffff;
  --accent-color: #0ea5e9;
  --accent-focus: #0284c7;
  --accent-content: #ffffff;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;
  --secondary-border: #3a3a3a;
}

/* Dark mode overrides for specific components */
.dark {
  color-scheme: dark;
  
  /* Base elements */
  body {
    @apply bg-gray-900 text-gray-100;
  }
  
  /* Form elements */
  input, select, textarea {
    @apply bg-gray-800 border-gray-700 text-gray-100;
  }
  
  input::placeholder, textarea::placeholder {
    @apply text-gray-500;
  }
  
  /* Buttons */
  .btn-primary {
    @apply bg-indigo-600 hover:bg-indigo-700 text-white;
  }
  
  .btn-secondary {
    @apply bg-gray-700 hover:bg-gray-600 text-white;
  }
  
  /* Cards */
  .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  /* Tables */
  table {
    @apply bg-gray-800 border-gray-700;
  }
  
  th {
    @apply bg-gray-900 text-gray-300;
  }
  
  tr {
    @apply border-gray-700;
  }
  
  tr:hover {
    @apply bg-gray-700;
  }
  
  /* Sidebar */
  .sidebar {
    @apply bg-gray-900 border-gray-700;
  }
  
  /* Header */
  header {
    @apply bg-gray-900 border-gray-700;
  }
  
  /* Dropdown menus */
  .dropdown-menu {
    @apply bg-gray-800 border-gray-700;
  }
  
  /* Modals */
  .modal {
    @apply bg-gray-800 border-gray-700;
  }
  
  /* Alerts */
  .alert-success {
    @apply bg-green-900 text-green-100 border-green-800;
  }
  
  .alert-warning {
    @apply bg-yellow-900 text-yellow-100 border-yellow-800;
  }
  
  .alert-error {
    @apply bg-red-900 text-red-100 border-red-800;
  }
  
  .alert-info {
    @apply bg-blue-900 text-blue-100 border-blue-800;
  }
}

/* Dark mode transitions */
.dark *, .dark *::before, .dark *::after {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
