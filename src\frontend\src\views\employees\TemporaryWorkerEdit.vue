<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">{{ isEdit ? '编辑临时工' : '新增临时工' }}</h1>
        <p class="page-subtitle">{{ isEdit ? '修改现有临时工信息' : '添加新的临时工信息' }}</p>
      </div>
      <button @click="goBack" class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        返回列表
      </button>
    </div>

    <!-- 信息提示 -->
    <div v-if="isCreate" class="bg-blue-50 border-l-4 border-blue-400 text-blue-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">填写说明：</span>
      </div>
      <p class="mt-2">请填写临时工的基本信息和工作信息。带<span class="form-required">*</span>的字段为必填项。</p>
      <p class="mt-1">临时工创建后，系统将自动生成登录账号，默认用户名为"姓名+身份证后4位"，默认密码为"身份证后6位"。</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
      <div v-if="error.includes('身份证号')" class="mt-2 text-sm">
        <p>身份证号码问题可能的原因：</p>
        <ul class="list-disc ml-5 mt-1">
          <li>身份证号码格式不正确，请确保输入18位有效身份证号</li>
          <li>此身份证号码已被其他员工使用，请核实后重新输入</li>
        </ul>
      </div>
      <div v-else-if="error.includes('保存失败')" class="mt-2 text-sm">
        <p>可能的原因：</p>
        <ul class="list-disc ml-5 mt-1">
          <li>网络连接问题，请检查网络连接</li>
          <li>服务器暂时不可用，请稍后再试</li>
          <li>填写的信息不符合系统要求，请检查必填字段</li>
          <li>身份证号码或合同编号可能已存在</li>
        </ul>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
      <p class="mt-2 text-sm text-green-600" v-if="isCreate">
        系统已成功创建临时工记录，相关信息已保存到数据库。临时工可以使用系统分配的默认账号登录系统。
      </p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="card shadow-md p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">加载中...</p>
    </div>

    <form v-if="!loading" @submit.prevent="validateForm" class="space-y-8 bg-white p-8 rounded-lg shadow">
      <!-- 基本信息 -->
      <div class="form-section">
        <h2 class="form-section-title">基本信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="name" class="form-label">姓名 <span class="form-required">*</span></label>
            <input id="name" type="text" v-model="form.name" class="form-input" placeholder="请输入临时工姓名" />
            <p v-if="fieldErrors.name" class="text-sm text-red-500 mt-1">{{ fieldErrors.name }}</p>
          </div>
          <div>
            <label for="idNumber" class="form-label">身份证号 <span class="form-required">*</span></label>
            <input id="idNumber" type="text" v-model="form.idNumber" class="form-input" placeholder="请输入18位身份证号码" />
            <p v-if="fieldErrors.idNumber" class="text-sm text-red-500 mt-1">{{ fieldErrors.idNumber }}</p>
            <p v-else class="text-sm text-gray-500 mt-1">请输入18位有效身份证号码，将用于系统登录</p>
          </div>
        </div>
      </div>

      <!-- 联系方式 -->
      <div class="form-section">
        <h2 class="form-section-title">联系方式</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="phone" class="form-label">联系电话 <span class="form-required">*</span></label>
            <input id="phone" type="tel" v-model="form.contactInfo.phone" class="form-input" placeholder="请输入11位手机号码" />
            <p v-if="fieldErrors.phone" class="text-sm text-red-500 mt-1">{{ fieldErrors.phone }}</p>
            <p v-else class="text-sm text-gray-500 mt-1">请输入11位手机号码</p>
          </div>
          <div>
            <label for="email" class="form-label">邮箱</label>
            <input id="email" type="email" v-model="form.contactInfo.email" class="form-input" placeholder="选填，用于接收系统通知" />
          </div>
          <div class="md:col-span-2">
            <label for="address" class="form-label">地址</label>
            <input id="address" type="text" v-model="form.contactInfo.address" class="form-input" placeholder="请填写临时工现居住地址（选填）" />
          </div>
        </div>
      </div>

      <!-- 工作信息 -->
      <div class="form-section">
        <h2 class="form-section-title">工作信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="startDate" class="form-label">入职时间 <span class="form-required">*</span></label>
            <input id="startDate" type="date" v-model="form.startDate" class="form-input" max="3000-12-31" />
            <p v-if="fieldErrors.startDate" class="text-sm text-red-500 mt-1">{{ fieldErrors.startDate }}</p>
            <p v-else class="text-sm text-gray-500 mt-1">临时工入职日期</p>
          </div>
          <div>
            <label for="endDate" class="form-label">结束时间 <span class="form-required">*</span></label>
            <input id="endDate" type="date" v-model="form.endDate" class="form-input" max="3000-12-31" />
            <p v-if="fieldErrors.endDate" class="text-sm text-red-500 mt-1">{{ fieldErrors.endDate }}</p>
            <p v-else class="text-sm text-gray-500 mt-1">临时工合同结束日期（必须晚于入职日期）</p>
          </div>
          <div>
            <label for="department" class="form-label">部门 <span class="form-required">*</span></label>
            <select id="department" v-model="form.departmentId" class="form-input">
              <option value="">请选择临时工所在部门</option>
              <option v-for="dept in departmentOptions" :key="dept.value" :value="dept.value">
                {{ dept.label }}
              </option>
            </select>
            <p v-if="fieldErrors.departmentId" class="text-sm text-red-500 mt-1">{{ fieldErrors.departmentId }}</p>
          </div>
          <div>
            <label for="position" class="form-label">职位 <span class="form-required">*</span></label>
            <select id="position" v-model="form.position" class="form-input">
              <option value="">请选择临时工职位</option>
              <option value="木工">木工</option>
              <option value="瓦工">瓦工</option>
              <option value="钢筋工">钢筋工</option>
              <option value="电工">电工</option>
              <option value="焊工">焊工</option>
              <option value="油漆工">油漆工</option>
              <option value="普工">普工</option>
            </select>
            <p v-if="fieldErrors.position" class="text-sm text-red-500 mt-1">{{ fieldErrors.position }}</p>
          </div>
          <div>
            <label for="salary" class="form-label">日薪（元/天）</label>
            <input id="salary" type="number" v-model="form.salary" min="0" step="0.01" class="form-input" placeholder="请输入临时工日薪金额" />
            <p v-if="fieldErrors.salary" class="text-sm text-red-500 mt-1">{{ fieldErrors.salary }}</p>
            <p v-else class="text-sm text-gray-500 mt-1">临时工的日薪标准，将用于工时计算</p>
          </div>
          <div>
            <label for="contractNumber" class="form-label">合同编号 <span class="form-required">*</span></label>
            <input id="contractNumber" type="text" v-model="form.contractNumber" class="form-input" placeholder="请输入合同编号，格式为TW-开头" />
            <p v-if="fieldErrors.contractNumber" class="text-sm text-red-500 mt-1">{{ fieldErrors.contractNumber }}</p>
            <p v-else class="text-sm text-gray-500 mt-1">示例: TW-12345678（TW-前缀+8位数字）</p>
          </div>
        </div>
      </div>

      <!-- 表单操作按钮 -->
      <div class="flex justify-end space-x-4 border-t pt-6">
        <button type="button" @click="resetForm" class="btn btn-secondary">清空表单</button>
        <button type="submit" class="btn btn-primary">{{ isEdit ? '保存修改' : '创建临时工' }}</button>
      </div>
      <div class="text-center text-sm text-gray-500 mt-4" v-if="!isEdit">
        注意：创建临时工后，系统将自动生成登录账号，用户名为"姓名+身份证后4位"，密码为"身份证后6位"
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from '@/plugins/axios'
import { useDepartmentStore } from '@/stores/department'

const route = useRoute()
const router = useRouter()
const isEdit = computed(() => !!route.params.id)
const isCreate = computed(() => !isEdit.value)

// 部门存储
const departmentStore = useDepartmentStore()
const departmentOptions = computed(() => departmentStore.getDepartmentOptions)

const loading = ref(false)
const error = ref(null)
const successMessage = ref('')
const fieldErrors = ref({})

// 表单验证函数
const validateIdNumber = (idNumber) => {
  // 简单的身份证号码验证（18位）
  const idPattern = /(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return idPattern.test(idNumber);
};

const validatePhone = (phone) => {
  // 简单的手机号验证（11位数字）
  const phonePattern = /^1[3-9]\d{9}$/;
  return phonePattern.test(phone);
};

// 统一的日期格式化函数，将任何日期格式转换为YYYY-MM-DD
const formatStandardDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    console.warn('无效的日期格式:', dateString);
    return '';
  }
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const form = reactive({
  name: '',
  idNumber: '',
  projectId: 'acc9388e-e929-4b77-8d1b-a4e12745b95d',
  startDate: '',
  endDate: '',
  position: '',
  departmentId: '',
  salary: 0,
  status: 'active',
  contractNumber: '',
  contactInfo: {
    phone: '',
    email: '',
    address: ''
  }
})

onMounted(async () => {
  loading.value = true;
  error.value = null;

  try {
    // 获取部门数据
    await departmentStore.fetchDepartments();

    if (isEdit.value) {
      try {
        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();
        const response = await axios.get(`/api/employees/temporary-workers/${route.params.id}?_t=${timestamp}`);
        const data = response;

        // 打印原始数据以便调试
        console.log('获取到的原始数据:', JSON.stringify(data, null, 2));

        // 重置表单，确保没有残留的旧数据
        resetForm();
        // 检查API响应
        if (!data) {
          console.error('API返回的数据为空');
          error.value = '获取临时工数据失败，返回的数据为空';
          loading.value = false;
          return;
        }

        // **特殊处理** - 针对确切的API响应格式映射数据
        try {
          // 1. 基础信息 - 从employee对象获取
          if (data.employee) {
            // 基本信息
            form.name = data.employee.name || '';
            form.idNumber = data.employee.idNumber || '';
            form.position = data.employee.position || '';
            form.status = data.employee.status || 'active';

            // 部门处理 - 先检查后端响应中的department字段
            if (data.employee.department) {
              // 通过部门名称查找部门ID
              console.log('查找部门:', data.employee.department);
              console.log('可用部门列表:', departmentOptions.value);

              const foundDept = departmentOptions.value.find(
                dept => dept.label === data.employee.department
              );

              if (foundDept) {
                form.departmentId = foundDept.value;
                console.log('根据名称找到部门ID:', foundDept.value);
              } else {
                console.warn(`未找到名为"${data.employee.department}"的部门`);
                form.departmentId = ''; // 设置为空，让用户重新选择
              }
            } else if (data.employee.departmentId) {
              form.departmentId = data.employee.departmentId;
              console.log('使用员工记录中的部门ID:', data.employee.departmentId);
            }

            // 联系信息
            if (data.employee.contactInfo) {
              form.contactInfo.phone = data.employee.contactInfo.phone || '';
              form.contactInfo.email = data.employee.contactInfo.email || '';
              form.contactInfo.address = data.employee.contactInfo.address || '';
              console.log('设置联系信息:', JSON.stringify(form.contactInfo, null, 2));
            } else {
              console.warn('员工记录中没有联系信息');
            }
          } else {
            console.warn('临时工数据中没有员工基本信息');
          }

          // 2. 日期处理 - 使用统一的日期格式化函数
          if (data.startDate) {
            form.startDate = formatStandardDate(data.startDate);
            console.log('设置开始日期:', form.startDate);
          } else {
            console.warn('临时工数据中没有开始日期');
          }

          if (data.endDate) {
            form.endDate = formatStandardDate(data.endDate);
            console.log('设置结束日期:', form.endDate);
          } else {
            console.warn('临时工数据中没有结束日期');
          }

          // 3. 项目ID
          form.projectId = data.projectId || 'acc9388e-e929-4b77-8d1b-a4e12745b95d';

          // 4. 处理合同编号 - 优先使用API返回的，否则从临时工ID生成
          if (data.contractNumber) {
            form.contractNumber = data.contractNumber;
            console.log('使用API返回的合同编号:', data.contractNumber);
          } else if (data.id) {
            form.contractNumber = `TW-${data.id.substring(0, 8)}`;
            console.log('生成的合同编号:', form.contractNumber);
          }

          // 5. 处理薪资 - dailyRate可能为null
          if (data.dailyRate !== null && data.dailyRate !== undefined) {
            form.salary = parseFloat(data.dailyRate);
            console.log('设置薪资(从dailyRate):', form.salary);
          } else {
            form.salary = 0;
            console.warn('临时工数据中没有薪资信息，设置为默认值0');
          }

          // 数据处理完成，清除错误信息
          error.value = null;

        } catch (dataError) {
          // 如果在数据处理过程中出错，记录详细错误，但不阻止页面加载
          console.error('处理临时工数据时出错:', dataError);
          error.value = '处理临时工数据时出错: ' + dataError.message;
        }

        // 最后再次检查并打印表单数据
        console.log('表单数据设置完成:', {
          name: form.name,
          idNumber: form.idNumber,
          position: form.position,
          departmentId: form.departmentId,
          startDate: form.startDate,
          endDate: form.endDate,
          contractNumber: form.contractNumber,
          salary: form.salary,
          contactInfo: form.contactInfo
        });

      } catch (err) {
        console.error('加载临时工数据失败:', err);
        error.value = err.response?.data?.message || '获取临时工信息失败';
      }
    }
  } catch (err) {
    console.error('初始化页面失败:', err);
    error.value = '初始化页面失败，请刷新页面重试';
  } finally {
    loading.value = false;
  }
});

const validateForm = () => {
  // 重置错误信息
  fieldErrors.value = {};
  error.value = null;
  let hasErrors = false;

  // 验证必填字段
  if (!form.name) {
    fieldErrors.value.name = '请填写姓名';
    hasErrors = true;
  }

  if (!form.idNumber) {
    fieldErrors.value.idNumber = '请填写身份证号';
    hasErrors = true;
  } else if (!validateIdNumber(form.idNumber)) {
    fieldErrors.value.idNumber = '身份证号格式不正确，请输入18位有效身份证号';
    hasErrors = true;
  }

  if (!form.contactInfo.phone) {
    fieldErrors.value.phone = '请填写联系电话';
    hasErrors = true;
  } else if (!validatePhone(form.contactInfo.phone)) {
    fieldErrors.value.phone = '手机号格式不正确，请输入11位有效手机号';
    hasErrors = true;
  }

  if (!form.position) {
    fieldErrors.value.position = '请选择职位';
    hasErrors = true;
  }

  if (!form.departmentId) {
    fieldErrors.value.departmentId = '请选择部门';
    hasErrors = true;
  }

  if (!form.startDate) {
    fieldErrors.value.startDate = '请选择入职日期';
    hasErrors = true;
  }

  if (!form.endDate) {
    fieldErrors.value.endDate = '请选择结束日期';
    hasErrors = true;
  } else if (form.startDate && new Date(form.endDate) <= new Date(form.startDate)) {
    fieldErrors.value.endDate = '结束日期必须晚于入职日期';
    hasErrors = true;
  }

  if (form.salary && form.salary <= 0) {
    fieldErrors.value.salary = '日薪必须大于0元';
    hasErrors = true;
  }

  if (!form.contractNumber) {
    fieldErrors.value.contractNumber = '请填写合同编号';
    hasErrors = true;
  }

  if (hasErrors) {
    // 显示第一个错误信息
    const firstError = Object.values(fieldErrors.value)[0];
    error.value = firstError;
    return;
  }

  // 没有错误，提交表单
  handleSubmit();
};

const handleSubmit = async () => {
  loading.value = true;
  error.value = null;
  successMessage.value = '';

  try {
    // 查找部门名称
    const departmentName = departmentOptions.value.find(dept => dept.value === form.departmentId)?.label || '未知部门';
    console.log('部门ID:', form.departmentId, '部门名称:', departmentName);

    // 构造与API期望格式完全匹配的payload
    const payload = {
      // 基本信息 - employee相关
      name: form.name,
      idNumber: form.idNumber,
      position: form.position,
      department: departmentName,  // 使用部门名称，这是必需的
      status: form.status || 'active',

      // 项目信息
      projectId: form.projectId,
      contractNumber: form.contractNumber,

      // 时间信息
      startDate: formatStandardDate(form.startDate),
      endDate: formatStandardDate(form.endDate),

      // 薪资 - 使用dailyRate字段
      dailyRate: parseFloat(form.salary) || 0,

      // 联系信息
      contactInfo: {
        phone: form.contactInfo.phone,
        email: form.contactInfo.email || '',
        address: form.contactInfo.address || ''
      }
    };

    // 打印用于调试的payload
    console.log('提交临时工数据:', JSON.stringify(payload, null, 2));
    
    // 专门记录日期格式，确保格式正确
    console.log('日期格式检查:', {
      原始入职日期: form.startDate,
      处理后入职日期: payload.startDate,
      原始结束日期: form.endDate,
      处理后结束日期: payload.endDate
    });

    let response;
    if (isEdit.value) {
      // 编辑现有临时工 - 添加时间戳防止缓存
      const timestamp = new Date().getTime();
      response = await axios.put(`/api/employees/temporary-workers/${route.params.id}?_t=${timestamp}`, payload);
      console.log('更新临时工响应:', response.data);
    } else {
      // 创建新临时工
      response = await axios.post('/api/employees/temporary-workers', payload);
      console.log('创建临时工响应:', response.data);
    }

    // 成功处理
    successMessage.value = isEdit.value 
      ? `临时工 ${form.name} 的信息已成功更新` 
      : `临时工 ${form.name} 已成功添加到系统，默认密码为身份证后6位`;
    
    // 延迟跳转，让用户看到成功信息
    setTimeout(() => {
      router.push('/employees/temporary');
    }, 2000);
  } catch (err) {
    console.error('保存错误:', err);

    // 详细错误信息处理
    if (err.response && err.response.data) {
      if (err.response.data.message) {
        error.value = err.response.data.message;
      } else if (err.response.data.error) {
        error.value = err.response.data.error;
      } else {
        error.value = JSON.stringify(err.response.data);
      }
    } else {
      error.value = err.message || '保存失败，请重试';
    }
  } finally {
    loading.value = false;
  }
}

function resetForm() {
  // 清空所有表单字段
  form.name = ''
  form.idNumber = ''
  form.startDate = ''
  form.endDate = ''
  form.position = ''
  form.departmentId = ''
  form.salary = 0
  form.contractNumber = ''
  form.contactInfo.phone = ''
  form.contactInfo.email = ''
  form.contactInfo.address = ''
  
  // 清空错误和成功提示
  error.value = null
  successMessage.value = ''
  fieldErrors.value = {}
}

const goBack = () => {
  router.push('/employees/temporary')
}
</script>

<style scoped>
.page-title {
  @apply text-2xl font-bold text-gray-800;
}
.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}
.page-header {
  @apply flex justify-between items-center mb-6;
  animation: fadeSlideIn 0.5s ease-out;
}
.form-section-title {
  @apply text-lg font-semibold text-gray-700 mb-4;
}
.form-section {
  @apply mb-8;
}
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}
.form-input {
  @apply mt-1 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm border-gray-300 rounded-md;
}
.btn {
  @apply inline-flex justify-center py-2 px-4 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2;
}
.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 border-transparent focus:ring-blue-500;
}
.btn-secondary {
  @apply text-gray-700 bg-white hover:bg-gray-50 border-gray-300 focus:ring-blue-500;
}
</style>