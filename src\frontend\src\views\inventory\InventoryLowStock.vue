<template>
  <div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
    <div class="py-8">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
          <div class="flex items-center justify-between">
            <div>
              <div class="flex items-center space-x-3">
                <div class="p-2 bg-red-100 rounded-lg">
                  <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                </div>
                <div>
                  <h1 class="text-3xl font-bold text-gray-900">库存不足物品</h1>
                  <p class="text-gray-600 mt-1">需要及时补充的库存物品</p>
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-3">
              <router-link 
                to="/inventory" 
                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                返回库存列表
              </router-link>
              <router-link 
                to="/purchases/create" 
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-all duration-200"
              >
                <svg class="-ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                创建采购单
              </router-link>
            </div>
          </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-3 mb-8">
          <div class="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-200 hover:shadow-xl transition-shadow duration-300">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="p-3 bg-red-100 rounded-full">
                    <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">库存不足物品</dt>
                    <dd class="text-2xl font-bold text-gray-900">{{ lowStockItems.length }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-200 hover:shadow-xl transition-shadow duration-300">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="p-3 bg-yellow-100 rounded-full">
                    <svg class="h-6 w-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">预估采购金额</dt>
                    <dd class="text-2xl font-bold text-gray-900">¥{{ formatCurrency(estimatedCost) }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow-lg rounded-xl border border-gray-200 hover:shadow-xl transition-shadow duration-300">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="p-3 bg-blue-100 rounded-full">
                    <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">涉及供应商</dt>
                    <dd class="text-2xl font-bold text-gray-900">{{ uniqueSuppliers.length }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Filters -->
        <div class="bg-white shadow-lg rounded-xl mb-6 border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50 rounded-t-xl">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="h-5 w-5 text-gray-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z" />
                </svg>
                筛选条件
              </h3>
              <button 
                @click="resetFilters"
                class="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200"
              >
                重置筛选
              </button>
            </div>
          </div>
          <div class="px-6 py-6">
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">物品名称</label>
                <div class="relative">
                  <input 
                    v-model="filters.name"
                    type="text" 
                    placeholder="搜索物品名称..."
                    class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200"
                  >
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">分类</label>
                <select 
                  v-model="filters.category"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200"
                >
                  <option value="">全部分类</option>
                  <option v-for="category in categories" :key="category" :value="category">{{ category }}</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">供应商</label>
                <select 
                  v-model="filters.supplier"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200"
                >
                  <option value="">全部供应商</option>
                  <option v-for="supplier in suppliers" :key="supplier" :value="supplier">{{ supplier }}</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">紧急程度</label>
                <select 
                  v-model="filters.urgency"
                  class="block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-colors duration-200"
                >
                  <option value="">全部</option>
                  <option value="critical">紧急</option>
                  <option value="warning">警告</option>
                  <option value="low">一般</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- Low Stock Items Table -->
        <div class="bg-white shadow-lg rounded-xl overflow-hidden border border-gray-200">
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <svg class="h-5 w-5 text-gray-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                库存不足物品列表
              </h3>
              <div class="flex items-center space-x-3">
                <span class="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">共 {{ filteredItems.length }} 项</span>
                <button 
                  @click="exportToExcel"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  <svg class="-ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  导出
                </button>
                <button 
                  @click="fetchLowStockItems"
                  :disabled="loading"
                  class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
                >
                  <svg class="-ml-0.5 mr-2 h-4 w-4" :class="{ 'animate-spin': loading }" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  {{ loading ? '刷新中...' : '刷新' }}
                </button>
              </div>
            </div>
          </div>

          <!-- Error state -->
          <div v-if="error" class="px-6 py-4 bg-red-50 border-l-4 border-red-400 m-4 rounded-lg">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-red-700 font-medium">{{ error }}</p>
                <p class="text-xs text-red-600 mt-1">已加载模拟数据用于演示</p>
                <button 
                  @click="fetchLowStockItems"
                  class="mt-2 text-sm text-red-600 hover:text-red-500 underline font-medium"
                >
                  重试
                </button>
              </div>
            </div>
          </div>

          <!-- Loading state -->
          <div v-if="loading && !error" class="px-6 py-12 text-center">
            <div class="flex flex-col items-center">
              <svg class="mx-auto h-12 w-12 text-gray-400 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p class="mt-4 text-sm text-gray-500">正在加载库存不足物品...</p>
            </div>
          </div>

          <div v-if="!loading && !error" class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    物品信息
                  </th>
                  <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    当前库存
                  </th>
                  <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    最低库存
                  </th>
                  <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    建议采购
                  </th>
                  <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    供应商
                  </th>
                  <th scope="col" class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    紧急程度
                  </th>
                  <th scope="col" class="px-6 py-4 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="item in filteredItems" :key="item.id" class="hover:bg-gray-50 transition-colors duration-200">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-12 w-12">
                        <div class="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center border border-blue-200">
                          <svg class="h-6 w-6 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                          </svg>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-semibold text-gray-900">{{ item.name }}</div>
                        <div class="text-sm text-gray-500">{{ item.code }}</div>
                        <div class="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full inline-block mt-1">{{ item.category }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ item.currentStock }}</div>
                    <div class="text-xs text-gray-500">{{ item.unit }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ item.minStock }}</div>
                    <div class="text-xs text-gray-500">{{ item.unit }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-semibold text-gray-900">{{ item.suggestedOrder }} {{ item.unit }}</div>
                    <div class="text-xs text-green-600 font-medium">¥{{ formatCurrency(item.estimatedCost) }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">{{ item.supplier }}</div>
                    <div class="text-xs text-gray-500">{{ item.supplierContact }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span 
                      class="inline-flex px-3 py-1 text-xs font-semibold rounded-full border"
                      :class="getUrgencyClass(item.urgency)"
                    >
                      {{ getUrgencyText(item.urgency) }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div class="flex items-center justify-end space-x-3">
                      <router-link 
                        :to="`/inventory/${item.id}`"
                        class="text-blue-600 hover:text-blue-900 font-medium transition-colors duration-200"
                      >
                        查看
                      </router-link>
                      <button 
                        @click="createPurchaseOrder(item)"
                        class="bg-green-100 text-green-700 hover:bg-green-200 px-3 py-1 rounded-lg text-xs font-medium transition-colors duration-200"
                      >
                        采购
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Empty state -->
          <div v-if="!loading && !error && filteredItems.length === 0" class="text-center py-16">
            <div class="flex flex-col items-center">
              <div class="p-4 bg-green-100 rounded-full mb-4">
                <svg class="mx-auto h-12 w-12 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 class="mt-2 text-lg font-semibold text-gray-900">暂无库存不足物品</h3>
              <p class="mt-1 text-sm text-gray-500">所有物品库存充足，无需补充。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { inventoryService } from '@/services/apiService'

export default {
  name: 'InventoryLowStock',
  setup() {
    const router = useRouter()
    
    // Reactive data
    const lowStockItems = ref([])
    const loading = ref(false)
    const error = ref(null)
    const filters = ref({
      name: '',
      category: '',
      supplier: '',
      urgency: ''
    })

    // Helper function to calculate urgency based on stock levels
    const calculateUrgency = (currentStock, minStock) => {
      const ratio = currentStock / minStock
      if (ratio <= 0.2) return 'critical'  // 20% or less of minimum stock
      if (ratio <= 0.5) return 'warning'   // 50% or less of minimum stock
      return 'low'                         // Above 50% but still below minimum
    }

    // Helper function to calculate suggested order quantity
    const calculateSuggestedOrder = (currentStock, minStock) => {
      const deficit = minStock - currentStock
      // Suggest ordering 2x the deficit to have buffer stock
      return Math.max(deficit * 2, minStock)
    }

    // Helper function to transform API data to component format
    const transformApiData = (apiItems) => {
      return apiItems.map(item => ({
        id: item.id,
        name: item.name,
        code: item.code || `PRD${item.id.toString().padStart(3, '0')}`,
        category: item.category || '未分类',
        currentStock: item.stock || 0,
        minStock: item.minStock || 0,
        unit: item.unit || '个',
        suggestedOrder: calculateSuggestedOrder(item.stock || 0, item.minStock || 0),
        estimatedCost: (calculateSuggestedOrder(item.stock || 0, item.minStock || 0) * (item.price || 0)),
        supplier: item.supplier || '待确定供应商',
        supplierContact: item.supplierContact || '联系方式待更新',
        urgency: calculateUrgency(item.stock || 0, item.minStock || 0),
        lastUpdated: new Date(item.updatedAt || item.createdAt || new Date()),
        price: item.price || 0
      }))
    }

    // Computed properties
    const filteredItems = computed(() => {
      let items = lowStockItems.value
      
      if (filters.value.name) {
        items = items.filter(item => 
          item.name.toLowerCase().includes(filters.value.name.toLowerCase()) ||
          item.code.toLowerCase().includes(filters.value.name.toLowerCase())
        )
      }
      
      if (filters.value.category) {
        items = items.filter(item => item.category === filters.value.category)
      }
      
      if (filters.value.supplier) {
        items = items.filter(item => item.supplier === filters.value.supplier)
      }
      
      if (filters.value.urgency) {
        items = items.filter(item => item.urgency === filters.value.urgency)
      }
      
      return items
    })

    const categories = computed(() => {
      return [...new Set(lowStockItems.value.map(item => item.category))]
    })

    const suppliers = computed(() => {
      return [...new Set(lowStockItems.value.map(item => item.supplier))]
    })

    const uniqueSuppliers = computed(() => {
      return [...new Set(filteredItems.value.map(item => item.supplier))]
    })

    const estimatedCost = computed(() => {
      return filteredItems.value.reduce((total, item) => total + item.estimatedCost, 0)
    })

    // Methods
    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('zh-CN').format(amount)
    }

    const getUrgencyClass = (urgency) => {
      switch (urgency) {
        case 'critical':
          return 'bg-red-100 text-red-800 border-red-200'
        case 'warning':
          return 'bg-yellow-100 text-yellow-800 border-yellow-200'
        case 'low':
          return 'bg-blue-100 text-blue-800 border-blue-200'
        default:
          return 'bg-gray-100 text-gray-800 border-gray-200'
      }
    }

    const getUrgencyText = (urgency) => {
      switch (urgency) {
        case 'critical':
          return '紧急'
        case 'warning':
          return '警告'
        case 'low':
          return '一般'
        default:
          return '未知'
      }
    }

    const resetFilters = () => {
      filters.value = {
        name: '',
        category: '',
        supplier: '',
        urgency: ''
      }
    }

    const createPurchaseOrder = (item) => {
      // 跳转到采购单创建页面，并预填充物品信息
      router.push({
        path: '/purchases/create',
        query: {
          itemId: item.id,
          itemName: item.name,
          quantity: item.suggestedOrder,
          supplier: item.supplier
        }
      })
    }

    const exportToExcel = () => {
      // 准备导出数据
      const exportData = filteredItems.value.map(item => ({
        '物品名称': item.name,
        '物品编码': item.code,
        '分类': item.category,
        '当前库存': item.currentStock,
        '最低库存': item.minStock,
        '单位': item.unit,
        '建议采购数量': item.suggestedOrder,
        '预估采购金额': item.estimatedCost,
        '供应商': item.supplier,
        '供应商联系方式': item.supplierContact,
        '紧急程度': getUrgencyText(item.urgency)
      }))

      // 创建CSV内容
      const headers = Object.keys(exportData[0] || {})
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => 
          headers.map(header => `"${row[header] || ''}"`).join(',')
        )
      ].join('\n')

      // 创建并下载文件
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      const link = document.createElement('a')
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)
      link.setAttribute('download', `库存不足物品_${new Date().toISOString().split('T')[0]}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      console.log('导出库存不足物品列表完成')
    }

    const fetchLowStockItems = async () => {
      loading.value = true
      error.value = null
      try {
        console.log('正在获取库存不足物品...')
        const response = await inventoryService.getStockAlerts()
        console.log('API响应:', response)
        
        // Transform the API data to match component expectations
        const transformedData = transformApiData(response.data || response)
        lowStockItems.value = transformedData
        
        console.log('转换后的数据:', transformedData)
      } catch (err) {
        console.error('获取库存不足物品失败:', err)
        error.value = '获取库存不足物品失败，请稍后重试'
        
        // 添加模拟数据作为fallback，以便演示页面功能
        const mockData = [
          {
            id: 1,
            name: '钢筋',
            code: 'STL001',
            category: '建筑材料',
            currentStock: 5,
            minStock: 20,
            unit: '吨',
            suggestedOrder: 30,
            estimatedCost: 150000,
            supplier: '华东钢铁有限公司',
            supplierContact: '021-12345678',
            urgency: 'critical',
            price: 5000
          },
          {
            id: 2,
            name: '水泥',
            code: 'CMT001',
            category: '建筑材料',
            currentStock: 8,
            minStock: 15,
            unit: '吨',
            suggestedOrder: 20,
            estimatedCost: 8000,
            supplier: '中建材水泥',
            supplierContact: '010-87654321',
            urgency: 'warning',
            price: 400
          },
          {
            id: 3,
            name: '电线',
            code: 'ELC001',
            category: '电气设备',
            currentStock: 12,
            minStock: 20,
            unit: '米',
            suggestedOrder: 25,
            estimatedCost: 2500,
            supplier: '远东电缆',
            supplierContact: '0512-98765432',
            urgency: 'warning',
            price: 100
          },
          {
            id: 4,
            name: '安全帽',
            code: 'SAF001',
            category: '安全防护',
            currentStock: 3,
            minStock: 50,
            unit: '个',
            suggestedOrder: 100,
            estimatedCost: 3000,
            supplier: '安全防护用品公司',
            supplierContact: '0571-11223344',
            urgency: 'critical',
            price: 30
          },
          {
            id: 5,
            name: '螺栓',
            code: 'BOL001',
            category: '五金配件',
            currentStock: 45,
            minStock: 100,
            unit: '个',
            suggestedOrder: 150,
            estimatedCost: 750,
            supplier: '五金批发市场',
            supplierContact: '0755-55667788',
            urgency: 'low',
            price: 5
          }
        ]
        
        lowStockItems.value = mockData
        console.log('使用模拟数据:', mockData)
      } finally {
        loading.value = false
      }
    }

    // Lifecycle
    onMounted(() => {
      fetchLowStockItems()
    })

    return {
      lowStockItems,
      loading,
      error,
      filters,
      filteredItems,
      categories,
      suppliers,
      uniqueSuppliers,
      estimatedCost,
      formatCurrency,
      getUrgencyClass,
      getUrgencyText,
      resetFilters,
      createPurchaseOrder,
      exportToExcel,
      fetchLowStockItems
    }
  }
}
</script> 