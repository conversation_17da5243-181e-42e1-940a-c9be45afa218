const express = require('express');
const app = express();
const PORT = process.env.PORT || 80;

// Middleware for parsing JSON bodies
app.use(express.json());

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to the Node.js service!' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK' });
});

// Example API endpoint
app.get('/api/data', (req, res) => {
  res.json({
    data: [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' },
      { id: 3, name: 'Item 3' }
    ]
  });
});

// Example POST endpoint
app.post('/api/data', (req, res) => {
  const { body } = req;
  console.log('Received data:', body);
  res.status(201).json({ message: 'Data received successfully', data: body });
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});