<template>
  <div class="flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
    <div class="card w-full max-w-md">
      <div class="text-center">
        <h2 class="text-3xl font-bold text-gray-900">重置密码</h2>
      </div>

      <div v-if="status" :class="['mt-4 p-3 rounded-md', 
        status.type === 'error' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700']">
        {{ status.message }}
      </div>

      <div v-if="tokenError" class="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
        <p>{{ tokenError }}</p>
        <p class="mt-2">
          请重新申请密码重置链接
          <router-link to="/forgot-password" class="text-indigo-600 hover:text-indigo-500 font-medium">
            点击这里
          </router-link>
        </p>
      </div>

      <form v-if="!resetComplete && !tokenError" @submit.prevent="handleSubmit" class="mt-6 space-y-6">
        <div>
          <label for="password" class="form-label">新密码 <span class="text-red-500">*</span></label>
          <input
            id="password"
            v-model="password"
            name="password"
            type="password"
            autocomplete="new-password"
            required
            class="form-input"
            :disabled="isLoading"
          />
          <div v-if="errors.password" class="text-red-500 text-sm mt-1">{{ errors.password }}</div>
          <p class="text-sm text-gray-500 mt-1">
            密码至少需要8个字符，并包含大写字母、小写字母和数字。
          </p>
        </div>

        <div>
          <label for="confirm-password" class="form-label">确认密码 <span class="text-red-500">*</span></label>
          <input
            id="confirm-password"
            v-model="confirmPassword"
            name="confirm-password"
            type="password"
            autocomplete="new-password"
            required
            class="form-input"
            :disabled="isLoading"
          />
          <div v-if="errors.confirmPassword" class="text-red-500 text-sm mt-1">{{ errors.confirmPassword }}</div>
        </div>

        <div>
          <button
            type="submit"
            class="w-full btn btn-primary flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="isLoading"
          >
            <span v-if="isLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              重置密码中...
            </span>
            <span v-else>重置密码</span>
          </button>
        </div>
        
        <div class="text-center">
          <router-link to="/login" class="text-sm text-indigo-600 hover:text-indigo-500 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            返回登录
          </router-link>
        </div>
      </form>

      <div v-else-if="resetComplete" class="mt-6 space-y-6">
        <div class="text-center">
          <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h3 class="text-xl font-medium text-gray-900">密码重置成功</h3>
          <p class="mt-2 text-gray-600">
            您的密码已成功重置。现在可以使用新密码登录系统。
          </p>
        </div>
        
        <div class="text-center">
          <router-link to="/login" class="block w-full py-2 px-4 border border-indigo-300 text-sm font-medium rounded-md text-indigo-600 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
            <div class="flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              前往登录页面
            </div>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useAuthStore } from '../stores/auth';

// Component state
const password = ref('');
const confirmPassword = ref('');
const errors = reactive({});
const status = ref(null);
const isLoading = ref(false);
const resetComplete = ref(false);
const tokenError = ref(null);

// Router and store
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

// Get the token from query parameters
const token = ref('');

onMounted(() => {
  token.value = route.query.token;
  
  // Validate token existence
  if (!token.value) {
    tokenError.value = '无效或缺失的密码重置令牌。';
  }
});

// Form validation
const validateForm = () => {
  errors.password = '';
  errors.confirmPassword = '';
  
  let isValid = true;
  
  if (!password.value || password.value.length < 8 || 
      !/[A-Z]/.test(password.value) || 
      !/[a-z]/.test(password.value) || 
      !/[0-9]/.test(password.value)) {
    errors.password = '密码至少需要8个字符，并包含大写字母、小写字母和数字';
    isValid = false;
  }
  
  if (password.value !== confirmPassword.value) {
    errors.confirmPassword = '两次输入的密码不一致';
    isValid = false;
  }
  
  return isValid;
};

// Submit handler
const handleSubmit = async () => {
  if (!validateForm()) {
    return;
  }
  
  isLoading.value = true;
  status.value = null;
  
  try {
    await authStore.resetPassword(token.value, password.value);
    resetComplete.value = true;
  } catch (error) {
    if (error.response?.status === 401 || error.response?.status === 400) {
      tokenError.value = '此密码重置链接无效或已过期。';
    } else {
      status.value = {
        type: 'error',
        message: error.response?.data?.message || '重置密码失败，请稍后再试。'
      };
    }
  } finally {
    isLoading.value = false;
  }
};
</script>