const { <PERSON><PERSON> } = require('../config/joi');

/**
 * Validation middleware
 * @param {Object} schema - Joi validation schema
 * @returns {Function} Express middleware function
 */
const validate = (schema) => (req, res, next) => {
  const validSchema = pick(schema, ['params', 'query', 'body']);
  const object = pick(req, Object.keys(validSchema));
  const { error } = Joi.compile(validSchema)
    .prefs({ 
      errors: { label: 'key' }, 
      abortEarly: false,
      // 确保使用详细的中文错误消息
      messages: {
        'zh-cn': {
          'any.required': '{#label}是必填项',
          'string.empty': '{#label}不能为空',
          'number.base': '{#label}必须是数字'
          // 更多中文错误消息会从config/joi.js中继承
        }
      } 
    })
    .validate(object);

  if (error) {
    const errorDetails = error.details.map((detail) => ({
      message: detail.message,
      path: detail.path,
      type: detail.type
    }));
    
    // 组织错误消息，将所有字段错误合并成一个字符串
    const errorMessage = errorDetails.map(detail => detail.message).join('，');
    
    return res.status(400).json({
      success: false,
      message: errorMessage,
      errors: errorDetails,
      code: 400
    });
  }
  next();
};

/**
 * Create an object composed of the picked object properties
 * @param {Object} object
 * @param {string[]} keys
 * @returns {Object}
 */
const pick = (object, keys) => {
  return keys.reduce((obj, key) => {
    if (object && Object.prototype.hasOwnProperty.call(object, key)) {
      obj[key] = object[key];
    }
    return obj;
  }, {});
};

module.exports = validate;
