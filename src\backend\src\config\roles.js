const allRoles = {
  user: ['getProjects', 'getCustomers', 'getSuppliers', 'getProducts', 'getEmployees', 'getContracts', 'getSubcontracts'],
  admin: [
    'getUsers', 'manageUsers',
    'getProjects', 'manageProjects',
    'getCustomers', 'manageCustomers',
    'getSuppliers', 'manageSuppliers',
    'getProducts', 'manageProducts',
    'getEmployees', 'manageEmployees',
    'getContracts', 'manageContracts',
    'getSubcontracts', 'manageSubcontracts',
    'getDocuments', 'manageDocuments',
    'getFinances', 'manageFinances'
  ],
  projectManager: [
    'getProjects', 'manageProjects',
    'getCustomers', 'getSuppliers',
    'getProducts', 'getEmployees',
    'getContracts', 'getSubcontracts'
  ],
};

const roles = Object.keys(allRoles);
const roleRights = new Map(Object.entries(allRoles));

module.exports = {
  roles,
  roleRights,
};