server {
    listen 80;

    root /usr/share/nginx/html;
    index index.html;

    # Handle all routes for Single Page Application
    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api/ {
        pass_proxy http://host.docker.internal:5180/api/;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires max;
        add_header Cache-Control "public, no-transform";
    }
}
