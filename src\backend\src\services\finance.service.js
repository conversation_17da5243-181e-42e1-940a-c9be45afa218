const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { Finance, Project, User, Supplier } = require('../models');

/**
 * Create a finance record
 * @param {Object} financeBody
 * @returns {Promise<Finance>}
 */
const createFinance = async (financeBody) => {
  // Check if project exists if projectId is provided
  if (financeBody.projectId) {
    const project = await Project.findByPk(financeBody.projectId);
    if (!project) {
      throw new ApiError(400, 'Project not found');
    }
  }

  // Check if supplier exists if supplierId is provided
  if (financeBody.supplierId) {
    const supplier = await Supplier.findByPk(financeBody.supplierId);
    if (!supplier) {
      throw new ApiError(400, 'Supplier not found');
    }
  }

  return Finance.create(financeBody);
};

/**
 * Get finance record by id
 * @param {string} id
 * @returns {Promise<Finance>}
 */
const getFinanceById = async (id) => {
  const finance = await Finance.findByPk(id, {
    include: [
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: Supplier,
        attributes: ['id', 'name', 'category', 'contactName']
      },
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!finance) {
    throw new ApiError(404, 'Finance record not found');
  }
  return finance;
};

/**
 * Update finance record by id
 * @param {string} financeId
 * @param {Object} updateBody
 * @returns {Promise<Finance>}
 */
const updateFinanceById = async (financeId, updateBody) => {
  const finance = await getFinanceById(financeId);

  // Check if project exists if projectId is being updated
  if (updateBody.projectId) {
    const project = await Project.findByPk(updateBody.projectId);
    if (!project) {
      throw new ApiError(400, 'Project not found');
    }
  }

  // Check if supplier exists if supplierId is being updated
  if (updateBody.supplierId) {
    const supplier = await Supplier.findByPk(updateBody.supplierId);
    if (!supplier) {
      throw new ApiError(400, 'Supplier not found');
    }
  }

  Object.assign(finance, updateBody);
  await finance.save();
  return finance;
};

/**
 * Delete finance record by id
 * @param {string} financeId
 * @returns {Promise<void>}
 */
const deleteFinanceById = async (financeId) => {
  const finance = await getFinanceById(financeId);
  await finance.destroy();
};

/**
 * Query for finance records
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Finance records and pagination info
 */
const queryFinances = async (filter, options) => {
  const { page, limit, sortBy, sortOrder } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};

  if (filter.projectId) {
    whereClause.projectId = filter.projectId;
  }

  if (filter.supplierId) {
    whereClause.supplierId = filter.supplierId;
  }

  if (filter.type) {
    whereClause.type = filter.type;
  }

  if (filter.category) {
    whereClause.category = filter.category;
  }

  if (filter.status) {
    whereClause.status = filter.status;
  }

  if (filter.search) {
    whereClause[Op.or] = [
      { description: { [Op.like]: `%${filter.search}%` } },
      { reference: { [Op.like]: `%${filter.search}%` } }
    ];
  }

  if (filter.dateFrom) {
    whereClause.date = {
      ...whereClause.date,
      [Op.gte]: new Date(filter.dateFrom)
    };
  }

  if (filter.dateTo) {
    whereClause.date = {
      ...whereClause.date,
      [Op.lte]: new Date(filter.dateTo)
    };
  }

  if (filter.minAmount) {
    whereClause.amount = {
      ...whereClause.amount,
      [Op.gte]: filter.minAmount
    };
  }

  if (filter.maxAmount) {
    whereClause.amount = {
      ...whereClause.amount,
      [Op.lte]: filter.maxAmount
    };
  }

  // Query with pagination
  const { count, rows } = await Finance.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: Supplier,
        attributes: ['id', 'name', 'category', 'contactName']
      },
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ],
    order: [[sortBy, sortOrder]],
    limit,
    offset,
    distinct: true
  });

  return {
    results: rows,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

/**
 * Get finance statistics
 * @param {Object} filter - Filter options for date range
 * @returns {Promise<Object>} - Finance statistics
 */
const getFinanceStats = async (filter) => {
  // Build the where clause based on filters
  const whereClause = {};

  if (filter.dateFrom) {
    whereClause.date = {
      ...whereClause.date,
      [Op.gte]: new Date(filter.dateFrom)
    };
  }

  if (filter.dateTo) {
    whereClause.date = {
      ...whereClause.date,
      [Op.lte]: new Date(filter.dateTo)
    };
  }

  // Query all finances within the date range
  const finances = await Finance.findAll({
    where: whereClause,
    order: [['date', 'ASC']],
    include: [
      {
        model: Project,
        attributes: ['id', 'name']
      },
      {
        model: Supplier,
        attributes: ['id', 'name']
      }
    ]
  });

  // Calculate statistics
  let totalIncome = 0;
  let totalExpense = 0;
  const incomeTypes = {};
  const expenseTypes = {};
  const monthlyData = {};

  finances.forEach(finance => {
    const amount = parseFloat(finance.amount);
    const date = new Date(finance.date);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    // Initialize monthly data if not exists
    if (!monthlyData[monthKey]) {
      monthlyData[monthKey] = { income: 0, expense: 0, netProfit: 0 };
    }

    if (finance.type === 'income') {
      totalIncome += amount;
      
      // 更新收入类型统计
      if (!incomeTypes[finance.category]) {
        incomeTypes[finance.category] = { amount: 0, count: 0 };
      }
      incomeTypes[finance.category].amount += amount;
      incomeTypes[finance.category].count += 1;
      
      monthlyData[monthKey].income += amount;
    } else if (finance.type === 'expense') {
      totalExpense += amount;
      
      // 更新支出类型统计
      if (!expenseTypes[finance.category]) {
        expenseTypes[finance.category] = { amount: 0, count: 0 };
      }
      expenseTypes[finance.category].amount += amount;
      expenseTypes[finance.category].count += 1;
      
      monthlyData[monthKey].expense += amount;
    }

    // Calculate profit
    monthlyData[monthKey].netProfit = monthlyData[monthKey].income - monthlyData[monthKey].expense;
  });

  // Convert monthly data to array and sort by date
  const monthlyTrend = Object.entries(monthlyData).map(([month, data]) => ({
    month,
    income: data.income,
    expense: data.expense,
    netProfit: data.netProfit
  })).sort((a, b) => a.month.localeCompare(b.month));

  return {
    totalIncome,
    totalExpense,
    netProfit: totalIncome - totalExpense,
    incomeTypes,
    expenseTypes,
    monthlyTrend
  };
};

module.exports = {
  createFinance,
  getFinanceById,
  updateFinanceById,
  deleteFinanceById,
  queryFinances,
  getFinanceStats
};