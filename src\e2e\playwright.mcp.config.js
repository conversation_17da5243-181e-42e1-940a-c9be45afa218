import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright MCP (Multi-Channel Protocol) Configuration
 * This configuration enables advanced testing capabilities with automatic error detection and fixing
 */
export default defineConfig({
  testDir: './tests',
  testIgnore: ['**/src/backend/tests/**'],
  timeout: 30000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1, // Allow one retry in local development
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html', { open: 'never' }],
    ['list'],
    ['json', { outputFile: 'test-results/test-results.json' }]
  ],
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
    video: 'on-first-retry',
    screenshot: 'only-on-failure',
    viewport: { width: 1920, height: 900 },
    actionTimeout: 10000,
    navigationTimeout: 15000,
    // Enable MCP features
    launchOptions: {
      args: [
        '--enable-features=NetworkServiceInProcess',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    }
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: [
    {
      command: 'cd ../frontend && npm run dev',
      port: 5173,
      reuseExistingServer: !process.env.CI,
    },
    {
      command: 'cd ../backend && npm run dev',
      port: 3008,
      reuseExistingServer: !process.env.CI,
    },
  ],
  // Output directory for test artifacts
  outputDir: 'test-results',
});
