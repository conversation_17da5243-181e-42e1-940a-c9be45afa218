<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">临时工管理</h1>
        <p class="page-subtitle">管理和跟踪临时工信息</p>
      </div>
      <div class="flex space-x-2">
        <button @click="goToAdd" class="btn btn-primary flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          添加临时工
        </button>
      </div>
    </div>
    <!-- 查询区域 -->
    <div class="filter-card mb-6">
      <div class="flex justify-between gap-4">
        <div class="flex-1">
          <label for="search" class="form-label font-medium">姓名/身份证号/手机号</label>
          <div class="relative">
            <input
              type="text"
              id="search"
              v-model="filters.search"
              class="form-input pl-10 py-2 mt-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
              placeholder="请输入姓名、身份证号或手机号进行搜索"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button @click="resetFilters" class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200">重置</button>
        <button @click="handleFilterChange" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200">查询</button>
      </div>
    </div>
    <!-- 错误提示 -->
    <div v-if="error" class="mb-6">
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="font-medium">错误：</span>
            <span class="ml-2">获取临时工数据失败</span>
          </div>
          <button @click="fetchTemporaryWorkers()" class="text-red-700 hover:text-red-900 font-medium">
            重试
          </button>
        </div>
        <p class="mt-2 whitespace-pre-line">{{ error }}</p>
      </div>
    </div>

    <!-- 删除错误提示 -->
    <div v-if="deleteError" class="mb-6">
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="font-medium">错误：</span>
            <span class="ml-2">删除临时工记录失败</span>
          </div>
        </div>
        <p class="mt-2 whitespace-pre-line">{{ deleteError }}</p>
      </div>
    </div>
    <!-- 数据表格 -->
    <div class="card overflow-hidden shadow-md">
      <div v-if="loading" class="p-10 text-center">
        <div class="flex flex-col items-center justify-center">
          <svg class="animate-spin h-8 w-8 text-blue-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p class="text-gray-600">正在加载数据，请稍候...</p>
        </div>
      </div>

      <div v-else-if="filteredRecords.length === 0" class="p-10 text-center">
        <div class="empty-state">
          <svg class="empty-state-icon mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          <h3 class="empty-state-title">暂无临时工数据</h3>
          <p class="empty-state-description">
            {{ filters.search ? '没有找到符合条件的记录，请尝试调整筛选条件' : '点击添加临时工按钮创建新记录' }}
          </p>
        </div>
      </div>

      <div v-else>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">身份证号</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">入职时间</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">班组</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工种</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="record in filteredRecords" :key="record.id" class="hover:bg-gray-50 transition-colors duration-150">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ record.name || (record.employee && record.employee.name) || '' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ record.idNumber || (record.employee && record.employee.idNumber) || '' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ record.contactInfo?.phone || (record.employee && record.employee.contactInfo && record.employee.contactInfo.phone) || '' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ record.startDate ? new Date(record.startDate).toLocaleDateString() : '' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ record.department || (record.employee && record.employee.department) || '' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ record.position || (record.employee && record.employee.position) || '' }}
              </td>
     
   
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button
                  @click="goToEdit(record)"
                  class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                >
                  <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                  </svg>
                  <span class="font-medium">编辑</span>
                </button>
                <button
                  @click="deleteRecord(record)"
                  class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                >
                  <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                  </svg>
                  <span class="font-medium">删除</span>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

const router = useRouter()
const filters = ref({
  search: ''
})

const records = ref([])
const loading = ref(false)
const error = ref(null)
const deleteError = ref(null)

// 获取临时工数据
const fetchTemporaryWorkers = async () => {
  loading.value = true;
  error.value = null;

  try {
    const params = {};

    if (filters.value.search) {
      params.search = filters.value.search;
    }

    const response = await axios.get('/api/employees/temporary-workers', { params });
    records.value = response.data.results || [];
  } catch (err) {
    console.error('获取临时工数据失败:', err);
    error.value = err.response?.data?.message || '很抱歉，系统暂时无法获取临时工数据。\n\n可能的原因：\n· 您的网络连接可能不稳定\n· 服务器可能正在维护中\n· 您的登录会话可能已过期\n\n建议操作：\n1. 检查网络连接\n2. 刷新页面后重试\n3. 如果您长时间未操作，可能需要重新登录\n\n如果问题持续存在，请联系系统管理员获取帮助。我们将尽快解决此问题，感谢您的理解与耐心。';
    records.value = [];
  } finally {
    loading.value = false;
  }
};

// 监听筛选条件变化
const handleFilterChange = () => {
  fetchTemporaryWorkers();
};

// 在组件挂载时获取数据
onMounted(() => {
  fetchTemporaryWorkers();
});

// 过滤记录
const filteredRecords = computed(() => {
  return records.value.filter(record => {
    // 如果没有搜索条件，返回所有记录
    if (!filters.value.search) {
      return true;
    }

    // 获取员工信息，可能在record或record.employee中
    const employeeName = record.employeeName || record.name || (record.employee && record.employee.name) || '';
    const idNumber = record.idNumber || (record.employee && record.employee.idNumber) || '';
    const phone = record.phone ||
                 (record.contactInfo && record.contactInfo.phone) ||
                 (record.employee && record.employee.contactInfo && record.employee.contactInfo.phone) || '';
    const team = record.team || '';

    // 转换搜索条件为小写
    const searchLower = filters.value.search.toLowerCase();

    // 检查是否匹配
    return employeeName.toLowerCase().includes(searchLower) ||
           idNumber.includes(filters.value.search) ||
           phone.includes(filters.value.search) ||
           (team && team.toLowerCase().includes(searchLower));
  })
})

// 删除记录
async function deleteRecord(record) {
  const employeeName = record.name || (record.employee && record.employee.name) || '此临时工';
  if (confirm(`尊敬的用户，您确定要删除"${employeeName}"的临时工记录吗？\n\n请您注意以下重要影响：\n· 删除后将无法恢复此记录\n· 相关的工资计算数据将被删除\n· 考勤记录可能会受到影响\n· 历史工时统计将不再包含此员工数据\n\n如果您确定要删除，请点击"确定"按钮继续操作。`)) {
    try {
      await axios.delete(`/api/employees/temporary-workers/${record.id}`);
      // 刷新数据
      fetchTemporaryWorkers();
    } catch (err) {
      console.error('删除记录失败:', err);
      deleteError.value = err.response?.data?.message || '很抱歉，删除临时工记录时遇到了问题。\n\n可能的原因：\n· 网络连接暂时中断\n· 系统临时故障\n· 该记录可能已被关联到其他重要数据\n· 您可能没有足够的权限执行此操作\n\n建议您：\n1. 检查网络连接\n2. 稍后再次尝试\n3. 确认您有删除权限\n\n如果问题持续存在，请联系系统管理员获取帮助。';

      // 5秒后自动清除错误信息
      setTimeout(() => {
        deleteError.value = null;
      }, 5000);
    }
  }
}

// 重置过滤条件
const resetFilters = () => {
  filters.value.search = '';
  fetchTemporaryWorkers();
};

function goToAdd() {
  router.push('/employees/temporary/add')
}
function goToEdit(record) {
  router.push(`/employees/temporary/edit/${record.id}`)
}
</script>