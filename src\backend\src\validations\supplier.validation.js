const Joi = require('joi');

const createSupplier = {
  body: Joi.object().keys({
    name: Joi.string().required().messages({
      'string.empty': '供应商名称不能为空',
      'any.required': '供应商名称是必填项'
    }),
    code: Joi.string().required().messages({
      'string.empty': '供应商编号不能为空',
      'any.required': '供应商编号是必填项'
    }),
    category: Joi.string().required().messages({
      'string.empty': '类别不能为空',
      'any.required': '类别是必填项'
    }),
    address: Joi.string().allow('', null),
    phone: Joi.string().allow('', null),
    contactName: Joi.string().allow('', null),
    contactPhone: Joi.string().allow('', null),
    contactEmail: Joi.string().email().allow('', null).messages({
      'string.email': '联系人邮箱格式不正确'
    }),
    rating: Joi.number().min(1).max(5).messages({
      'number.base': '评分必须是数字',
      'number.min': '评分不能小于{#limit}',
      'number.max': '评分不能大于{#limit}'
    }),
    products: Joi.array().items(Joi.string()).default([]),
    notes: Joi.string().allow('', null),
    tags: Joi.array().items(Joi.string()).default([]),
    createdBy: Joi.string().uuid().required().messages({
      'string.empty': '创建者ID不能为空',
      'string.guid': '创建者ID必须是有效的UUID格式',
      'any.required': '创建者ID是必填项'
    })
  })
};

const getSupplier = {
  params: Joi.object().keys({
    supplierId: Joi.string().uuid().required().messages({
      'string.empty': '供应商ID不能为空',
      'string.guid': '供应商ID必须是有效的UUID格式',
      'any.required': '供应商ID是必填项'
    })
  })
};

const updateSupplier = {
  params: Joi.object().keys({
    supplierId: Joi.string().uuid().required().messages({
      'string.empty': '供应商ID不能为空',
      'string.guid': '供应商ID必须是有效的UUID格式',
      'any.required': '供应商ID是必填项'
    })
  }),
  body: Joi.object().keys({
    name: Joi.string().messages({
      'string.empty': '供应商名称不能为空'
    }),
    code: Joi.string().required().messages({
      'string.empty': '供应商编号不能为空',
      'any.required': '供应商编号是必填项'
    }),
    category: Joi.string().messages({
      'string.empty': '类别不能为空'
    }),
    address: Joi.string().messages({
      'string.empty': '地址不能为空'
    }),
    phone: Joi.string().messages({
      'string.empty': '电话不能为空'
    }),
    contactName: Joi.string().messages({
      'string.empty': '联系人名称不能为空'
    }),
    contactPhone: Joi.string().messages({
      'string.empty': '联系人电话不能为空'
    }),
    contactEmail: Joi.string().email().messages({
      'string.empty': '联系人邮箱不能为空',
      'string.email': '联系人邮箱格式不正确'
    }),
    rating: Joi.number().min(1).max(5).messages({
      'number.base': '评分必须是数字',
      'number.min': '评分不能小于{#limit}',
      'number.max': '评分不能大于{#limit}'
    }),
    products: Joi.array().items(Joi.string()),
    notes: Joi.string().allow('', null),
    tags: Joi.array().items(Joi.string())
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deleteSupplier = {
  params: Joi.object().keys({
    supplierId: Joi.string().uuid().required().messages({
      'string.empty': '供应商ID不能为空',
      'string.guid': '供应商ID必须是有效的UUID格式',
      'any.required': '供应商ID是必填项'
    })
  })
};

const getSuppliers = {
  query: Joi.object().keys({
    category: Joi.string().allow('', null),
    rating: Joi.number().min(1).max(5).messages({
      'number.base': '评分必须是数字',
      'number.min': '评分不能小于{#limit}',
      'number.max': '评分不能大于{#limit}'
    }),
    search: Joi.string().allow('', null),
    tags: Joi.array().items(Joi.string()),
    sortBy: Joi.string().valid('name', 'createdAt', 'rating', 'category').default('createdAt').messages({
      'any.only': '排序字段必须是名称(name)、创建时间(createdAt)、评分(rating)或类别(category)'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    }),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于{#limit}'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    })
  })
};

module.exports = {
  createSupplier,
  getSupplier,
  updateSupplier,
  deleteSupplier,
  getSuppliers
};