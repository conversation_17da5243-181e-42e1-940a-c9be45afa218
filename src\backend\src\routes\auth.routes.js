const express = require('express');
const validate = require('../middlewares/validate');
const authValidation = require('../validations/auth.validation');
const authController = require('../controllers/auth.controller');
const { authenticate } = require('../middlewares/auth.middleware');

const router = express.Router();

/**
 * @route GET /api/auth/me
 * @desc Get current user profile
 * @access Private
 */
router.get('/me', authenticate, authController.getProfile);

/**
 * @route POST /api/auth/register
 * @desc Register a new user
 * @access Public
 */
router.post('/register', validate(authValidation.register), authController.register);

/**
 * @route POST /api/auth/login
 * @desc Login with email and password
 * @access Public
 */
router.post('/login', validate(authValidation.login), authController.login);

/**
 * @route POST /api/auth/logout
 * @desc Logout (invalidate refresh token)
 * @access Public
 */
router.post('/logout', validate(authValidation.logout), authController.logout);

/**
 * @route POST /api/auth/refresh-tokens
 * @desc Refresh auth tokens
 * @access Public
 */
router.post('/refresh-tokens', validate(authValidation.refreshTokens), authController.refreshTokens);

/**
 * @route POST /api/auth/forgot-password
 * @desc Send password reset email
 * @access Public
 */
router.post('/forgot-password', validate(authValidation.forgotPassword), authController.forgotPassword);

/**
 * @route POST /api/auth/reset-password
 * @desc Reset password
 * @access Public
 */
router.post('/reset-password', validate(authValidation.resetPassword), authController.resetPassword);

/**
 * @route POST /api/auth/verify-email
 * @desc Verify email
 * @access Public
 */
router.post('/verify-email', validate(authValidation.verifyEmail), authController.verifyEmail);

/**
 * @route POST /api/auth/change-password
 * @desc Change password (logged in user)
 * @access Private
 */
router.post(
  '/change-password',
  authenticate,
  validate(authValidation.changePasswordSchema),
  authController.changePassword
);

module.exports = router;