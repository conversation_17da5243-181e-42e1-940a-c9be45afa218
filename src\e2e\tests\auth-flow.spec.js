// @ts-check
import { test, expect } from '@playwright/test';

test.describe('认证流程测试', () => {
  test('登录成功流程', async ({ page }) => {
    // 访问登录页面
    await page.goto('/login');

    // 验证页面标题和元素
    expect(await page.textContent('h2')).toContain('登录系统');
    expect(await page.isVisible('form')).toBeTruthy();
    expect(await page.isVisible('input[type="email"]')).toBeTruthy();
    expect(await page.isVisible('input[type="password"]')).toBeTruthy();
    expect(await page.isVisible('button[type="submit"]')).toBeTruthy();

    // 输入登录信息
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');

    // 点击登录按钮
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证登录成功，跳转到仪表盘
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/dashboard');
    
    // 验证仪表盘页面元素
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('仪表盘');
    expect(await page.isVisible('nav')).toBeTruthy();
  });

  test('登录失败场景', async ({ page }) => {
    // 访问登录页面
    await page.goto('/login');

    // 输入错误的登录信息
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'WrongPassword');

    // 点击登录按钮
    await page.click('button[type="submit"]');

    // 验证错误消息显示
    await page.waitForSelector('.bg-red-50');
    expect(await page.isVisible('.bg-red-50')).toBeTruthy();
    
    // 验证页面没有跳转
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/login');
  });

  test('忘记密码流程', async ({ page }) => {
    // 访问登录页面
    await page.goto('/login');
    
    // 点击忘记密码链接
    await page.click('text=忘记密码？');
    
    // 等待页面跳转
    await page.waitForNavigation();
    
    // 验证跳转到忘记密码页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/forgot-password');
    
    // 验证忘记密码页面元素
    expect(await page.isVisible('input[type="email"]')).toBeTruthy();
    expect(await page.isVisible('button[type="submit"]')).toBeTruthy();
    
    // 输入邮箱
    await page.fill('input[type="email"]', '<EMAIL>');
    
    // 点击提交按钮
    await page.click('button[type="submit"]');
    
  });


});
