<template>
  <div class="container mx-auto px-4 py-8">
    <div class="mb-6">
      <router-link to="/attendance" class="text-blue-600 hover:text-blue-800 flex items-center">
        <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        返回考勤列表
      </router-link>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden">
      <!-- 考勤记录头部信息 -->
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h1 class="page-title">考勤记录详情</h1>
          <div class="flex space-x-2">
            <button @click="printAttendance" class="btn btn-secondary">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
              </svg>
              打印
            </button>
            <router-link :to="`/attendance/edit/${attendanceId}`" class="btn btn-primary">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              编辑
            </router-link>
          </div>
        </div>
      </div>

      <!-- 加载中状态 -->
      <div v-if="loading" class="px-6 py-12 text-center">
        <svg class="animate-spin h-10 w-10 text-blue-600 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="text-gray-600">正在加载考勤详情...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="px-6 py-8 text-center">
        <div class="text-red-600 mb-4">
          <svg class="h-12 w-12 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        </div>
        <p class="text-red-600 font-medium">{{ error }}</p>
        <button @click="fetchAttendanceDetail" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          重试
        </button>
      </div>

      <!-- 考勤详细信息 -->
      <div v-else-if="attendance" class="px-6 py-5">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 基本信息 -->
          <div class="space-y-4">
            <div>
              <h3 class="text-sm font-medium text-gray-500">员工信息</h3>
              <div class="mt-1 flex items-center">
                <div class="h-10 w-10 rounded-full bg-blue-600 flex items-center justify-center text-white font-medium text-lg">
                  {{ attendance.employee?.name?.charAt(0) || '?' }}
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900">{{ attendance.employee?.name || '未知员工' }}</p>
                  <p class="text-sm text-gray-500">{{ attendance.employee?.department || '未知部门' }}</p>
                </div>
              </div>
            </div>

            <div>
              <h3 class="text-sm font-medium text-gray-500">日期</h3>
              <p class="mt-1 text-sm text-gray-900">{{ formattedDate }}</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-gray-500">考勤类型</h3>
              <p class="mt-1 text-sm text-gray-900">{{ attendance.type || '常规考勤' }}</p>
            </div>
          </div>

          <!-- 时间记录 -->
          <div class="space-y-4">
            <div>
              <h3 class="text-sm font-medium text-gray-500">上班时间</h3>
              <p class="mt-1 text-sm text-gray-900">{{ attendance.checkIn ? formattedTime(attendance.checkIn) : '未记录' }}</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-gray-500">下班时间</h3>
              <p class="mt-1 text-sm text-gray-900">{{ attendance.checkOut ? formattedTime(attendance.checkOut) : '未记录' }}</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-gray-500">工作时长</h3>
              <p class="mt-1 text-sm text-gray-900">{{ attendance.workHours || '0' }} 小时</p>
            </div>

            <div>
              <h3 class="text-sm font-medium text-gray-500">状态</h3>
              <p class="mt-1">
                <span :class="{
                  'px-2 py-1 text-xs font-medium rounded-full': true,
                  'bg-green-100 text-green-800': attendance.status === 'normal',
                  'bg-yellow-100 text-yellow-800': attendance.status === 'late',
                  'bg-red-100 text-red-800': attendance.status === 'absent',
                  'bg-blue-100 text-blue-800': attendance.status === 'leave'
                }">
                  {{ {
                    'normal': '正常',
                    'late': '迟到',
                    'absent': '缺勤',
                    'leave': '请假'
                  }[attendance.status] || attendance.status }}
                </span>
              </p>
            </div>
          </div>
        </div>

        <!-- 备注 -->
        <div class="mt-6">
          <h3 class="text-sm font-medium text-gray-500">备注</h3>
          <div class="mt-2 p-4 bg-gray-50 rounded-md">
            <p class="text-sm text-gray-700">{{ attendance.notes || '无备注信息' }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'AttendanceDetail',
  data() {
    return {
      attendanceId: this.$route.params.id,
      attendance: null,
      loading: true,
      error: null
    }
  },
  computed: {
    // 格式化日期
    formattedDate() {
      if (!this.attendance || !this.attendance.date) return '';
      const date = new Date(this.attendance.date);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      });
    },
    // 格式化时间
    formattedTime() {
      return (time) => {
        if (!time) return '';
        return time.substring(0, 5); // 只显示小时和分钟 (HH:MM)
      };
    }
  },
  methods: {
    // 获取考勤详情
    async fetchAttendanceDetail() {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/attendances/${this.attendanceId}`);
        this.attendance = response.data;
      } catch (err) {
        console.error('获取考勤详情失败:', err);
        this.error = err.response?.data?.message || '获取考勤详情失败，请重试';
      } finally {
        this.loading = false;
      }
    },

    // 打印考勤记录
    printAttendance() {
      window.print();
    }
  },
  mounted() {
    this.fetchAttendanceDetail();
  }
}
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md text-sm font-medium flex items-center;
}
.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}
.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}
</style>