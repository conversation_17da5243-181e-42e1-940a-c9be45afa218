<template>
  <div class="max-w-6xl mx-auto">
    <!-- Error messages -->
    <div v-if="errorMessage" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ errorMessage }}</p>
    </div>

    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Documents</h1>
      <router-link to="/documents/create" class="btn btn-primary">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Create Document
      </router-link>
    </div>

    <!-- Search and Filters -->
    <div class="card mb-6">
      <div class="flex justify-between gap-4">
        <div class="flex-1">
          <label for="search" class="form-label">Search Documents</label>
          <div class="relative">
            <input
              id="search"
              v-model="searchQuery"
              type="text"
              placeholder="Search by title, description or content..."
              class="form-input pl-10 mt-2 w-full"
              @input="debouncedFetchDocuments"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <label for="category-filter" class="form-label">Filter by Category</label>
          <select id="category-filter" v-model="categoryFilter" class="form-input mt-2 w-full" @change="fetchDocuments">
            <option value="">All Categories</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>
        </div>
        <div class="flex-1">
          <label for="sort-by" class="form-label">Sort By</label>
          <select id="sort-by" v-model="sortBy" class="form-input mt-2 w-full" @change="fetchDocuments">
            <option value="-createdAt">Date Created (Newest)</option>
            <option value="createdAt">Date Created (Oldest)</option>
            <option value="-updatedAt">Last Updated (Newest)</option>
            <option value="updatedAt">Last Updated (Oldest)</option>
            <option value="title">Title (A-Z)</option>
            <option value="-title">Title (Z-A)</option>
          </select>
        </div>
      </div>
    </div>

    <!-- View Toggle - Moved to top right -->
    <div class="flex justify-end mb-4">
      <div class="bg-white border rounded-md p-1 inline-flex">
        <button
          @click="viewMode = 'grid'"
          :class="['p-1.5 rounded', viewMode === 'grid' ? 'bg-gray-100 text-gray-900' : 'text-gray-500']"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
          </svg>
        </button>
        <button
          @click="viewMode = 'list'"
          :class="['p-1.5 rounded', viewMode === 'list' ? 'bg-gray-100 text-gray-900' : 'text-gray-500']"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Documents Grid/List -->
    <div v-if="loading" class="text-center py-8">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-2 text-gray-600">Loading documents...</p>
    </div>

    <div v-else-if="documents.length === 0" class="card text-center py-12">
      <svg class="w-16 h-16 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="mt-4 text-xl font-medium text-gray-900">No documents found</h3>
      <p class="mt-2 text-gray-600">
        {{ searchQuery || categoryFilter ? 'Try adjusting your search or filters' : 'Get started by creating your first document' }}
      </p>
      <div class="mt-6" v-if="!searchQuery && !categoryFilter">
        <router-link to="/documents/create" class="btn btn-primary">
          Create Your First Document
        </router-link>
      </div>
    </div>

    <div v-else>
      <!-- Grid View -->
      <div v-if="viewMode === 'grid'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-for="doc in documents" :key="doc.id" class="card hover:shadow-md transition-shadow">
          <div class="flex justify-between items-start">
            <div class="flex space-x-2">
              <div class="bg-blue-100 p-2 rounded-md">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <span v-if="doc.category" class="badge badge-blue">{{ doc.category.name }}</span>
            </div>
            <div class="relative">
              <button @click="toggleDropdown(doc.id)" class="text-gray-500 hover:text-gray-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                </svg>
              </button>
              <div
                v-if="activeDropdown === doc.id"
                class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border"
              >
                <router-link :to="`/documents/${doc.id}`" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50">
                  View
                </router-link>
                <router-link :to="`/documents/${doc.id}`" class="block px-4 py-2 text-sm text-gray-700 hover:bg-blue-50">
                  Edit
                </router-link>
                <button @click="confirmDelete(doc)" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                  Delete
                </button>
              </div>
            </div>
          </div>

          <router-link :to="`/documents/${doc.id}`" class="block mt-4">
            <h3 class="text-lg font-medium text-gray-900 hover:text-blue-600">{{ doc.title }}</h3>
            <p class="mt-2 text-sm text-gray-600 line-clamp-2">{{ doc.description || 'No description' }}</p>
          </router-link>

          <div class="mt-4 pt-4 border-t border-gray-100">
            <div class="flex justify-between text-xs text-gray-500">
              <span>Created {{ formatDate(doc.createdAt) }}</span>
              <span>{{ doc.comments?.length || 0 }} comments</span>
            </div>
          </div>
        </div>
      </div>

      <!-- List View -->
      <div v-else class="card overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Updated</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="doc in documents" :key="doc.id" class="hover:bg-gray-50">
                <td class="px-6 py-4">
                  <router-link :to="`/documents/${doc.id}`" class="text-blue-600 hover:text-blue-900">
                    {{ doc.title }}
                  </router-link>
                </td>
                <td class="px-6 py-4">
                  <span v-if="doc.category" class="badge badge-blue">{{ doc.category.name }}</span>
                  <span v-else class="text-gray-400">—</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(doc.createdAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(doc.updatedAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">                  <router-link
                    :to="`/documents/${doc.id}`"
                    class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                    </svg>
                    <span class="font-medium">Edit</span>
                  </router-link>
                  <button
                    @click="confirmDelete(doc)"
                    class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                    <span class="font-medium">Delete</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Pagination -->
      <div class="mt-6 flex items-center justify-between">
        <div class="text-sm text-gray-500">
          Showing {{ startIndex + 1 }} to {{ Math.min(endIndex, totalDocuments) }} of {{ totalDocuments }} documents
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="changePage(currentPage - 1)"
            class="btn btn-secondary py-1 px-2"
            :disabled="currentPage === 1"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <span class="text-sm text-gray-700">Page {{ currentPage }} of {{ totalPages }}</span>
          <button
            @click="changePage(currentPage + 1)"
            class="btn btn-secondary py-1 px-2"
            :disabled="currentPage === totalPages"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-1">Confirm Deletion</h3>
          <p class="text-gray-500">
            Are you sure you want to delete "{{ documentToDelete?.title }}"? This action cannot be undone.
          </p>

          <div class="mt-6 flex justify-center space-x-3">
            <button
              type="button"
              @click="closeDeleteModal"
              class="btn btn-secondary"
            >
              Cancel
            </button>
            <button
              type="button"
              @click="deleteDocument"
              class="btn btn-danger"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Deleting...
              </span>
              <span v-else>Delete Document</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';

const router = useRouter();

// Document listing state
const documents = ref([]);
const categories = ref([]);
const loading = ref(true);
const totalDocuments = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchQuery = ref('');
const categoryFilter = ref('');
const sortBy = ref('-updatedAt');
const viewMode = ref(localStorage.getItem('documentViewMode') || 'grid');

// Dropdown state
const activeDropdown = ref(null);

// Delete modal state
const showDeleteModal = ref(false);
const documentToDelete = ref(null);
const isDeleting = ref(false);
const errorMessage = ref('');

// Calculate pagination values
const startIndex = computed(() => (currentPage.value - 1) * pageSize.value);
const endIndex = computed(() => startIndex.value + pageSize.value);
const totalPages = computed(() => Math.ceil(totalDocuments.value / pageSize.value) || 1);

// Fetch documents and categories on component mount
onMounted(() => {
  fetchDocuments();
  fetchCategories();

  // Add event listener to close dropdown when clicking outside
  document.addEventListener('click', handleOutsideClick);
});

// Remove event listeners on component unmount
onBeforeUnmount(() => {
  document.removeEventListener('click', handleOutsideClick);
});

// Save view mode preference
const setViewMode = (mode) => {
  viewMode.value = mode;
  localStorage.setItem('documentViewMode', mode);
};

// Close dropdown when clicking outside
const handleOutsideClick = (event) => {
  if (activeDropdown.value !== null && !event.target.closest('.dropdown-container')) {
    activeDropdown.value = null;
  }
};

// Toggle document action dropdown
const toggleDropdown = (documentId) => {
  activeDropdown.value = activeDropdown.value === documentId ? null : documentId;
};

// Fetch all categories for filter dropdown
const fetchCategories = async () => {
  try {
    const response = await axios.get('/api/categories', { params: { limit: 100 } });
    categories.value = response.data.results;
  } catch (error) {
    console.error('Error fetching categories:', error);
  }
};

// Fetch documents with pagination, search, filters, and sorting
const fetchDocuments = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value,
      categoryId: categoryFilter.value,
      sort: sortBy.value
    };

    const response = await axios.get('/api/documents', { params });
    documents.value = response.data.results;
    totalDocuments.value = response.data.totalResults;

    // Adjust current page if it's out of bounds
    if (currentPage.value > totalPages.value && totalPages.value > 0) {
      currentPage.value = totalPages.value;
      fetchDocuments();
    }
  } catch (error) {
    console.error('Error fetching documents:', error);
    // Handle error displaying
  } finally {
    loading.value = false;
  }
};

// Debounced search implementation
let searchTimeout;
const debouncedFetchDocuments = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    currentPage.value = 1; // Reset to first page on new search
    fetchDocuments();
  }, 300);
};

// Pagination handling
const changePage = (page) => {
  if (page < 1 || page > totalPages.value) return;
  currentPage.value = page;
  fetchDocuments();
};

// Format date for display
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Delete document handling
const confirmDelete = (document) => {
  documentToDelete.value = document;
  showDeleteModal.value = true;
};

const closeDeleteModal = () => {
  showDeleteModal.value = false;
  documentToDelete.value = null;
};

const deleteDocument = async () => {
  if (!documentToDelete.value) return;

  isDeleting.value = true;

  try {
    await axios.delete(`/api/documents/${documentToDelete.value.id}`);
    closeDeleteModal();
    fetchDocuments();
  } catch (error) {
    console.error('Error deleting document:', error);
    errorMessage.value = '删除文档失败，请稍后重试。';
    setTimeout(() => {
      errorMessage.value = '';
    }, 5000);
    closeDeleteModal();
  } finally {
    isDeleting.value = false;
  }
};
</script>