const jwt = require('jsonwebtoken');
const config = require('../config/config');
const logger = require('../config/logger');
const { User } = require('../models');

/**
 * Simple authentication middleware
 * Validates JWT token and attaches user to request
 * Does not check token in database
 */
const simpleAuthenticate = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: {
          message: 'Authorization denied. No token provided.',
          code: 'NO_TOKEN'
        }
      });
    }

    const token = authHeader.split(' ')[1];

    if (!token || token.trim() === '') {
      return res.status(401).json({
        error: {
          message: 'Authorization denied. Empty token provided.',
          code: 'EMPTY_TOKEN'
        }
      });
    }

    // Ensure JWT_SECRET is properly configured
    if (!config.jwt || !config.jwt.secret) {
      logger.error('JWT_SECRET not configured properly');
      return res.status(500).json({
        error: {
          message: 'Server authentication configuration error',
          code: 'CONFIG_ERROR'
        }
      });
    }

    try {
      // Verify the token signature and expiration
      const decoded = jwt.verify(token, config.jwt.secret);
      logger.info(`Token verified successfully for user: ${decoded.sub}`);

      // Get user from database (excluding password)
      const user = await User.findByPk(decoded.sub, {
        attributes: { exclude: ['password'] }
      });

      if (!user) {
        return res.status(401).json({
          error: {
            message: 'User not found',
            code: 'USER_NOT_FOUND'
          }
        });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(403).json({
          error: {
            message: 'Account is disabled. Please contact an administrator.',
            code: 'ACCOUNT_DISABLED'
          }
        });
      }

      // Attach user to request
      req.user = user;
      next();
    } catch (error) {
      logger.error(`JWT verification error: ${error.message}`, {
        stack: error.stack,
        name: error.name
      });

      if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          error: {
            message: 'Invalid token',
            details: error.message,
            code: 'INVALID_TOKEN'
          }
        });
      } else if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          error: {
            message: 'Token expired',
            code: 'TOKEN_EXPIRED',
            expiredAt: error.expiredAt
          }
        });
      }

      return res.status(401).json({
        error: {
          message: 'Authentication failed',
          details: error.message,
          code: 'AUTH_FAILED'
        }
      });
    }
  } catch (error) {
    logger.error(`Authentication error: ${error.message}`, {
      stack: error.stack
    });

    // Return a generic error message to avoid leaking sensitive information
    return res.status(500).json({
      error: {
        message: 'Server error during authentication',
        code: 'AUTH_SERVER_ERROR'
      }
    });
  }
};

module.exports = {
  simpleAuthenticate
};
