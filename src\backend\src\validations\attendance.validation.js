const Joi = require('joi');
const { objectId } = require('./custom.validation');

const createAttendance = {
  body: Joi.object().keys({
    projectId: Joi.string().custom(objectId).required().messages({
      'string.empty': '项目编号不能为空',
      'any.required': '项目编号是必填项',
      'any.custom': '项目编号必须是有效的ObjectID格式'
    }),
    employeeName: Joi.string().required().messages({
      'string.empty': '员工姓名不能为空',
      'any.required': '员工姓名是必填项'
    }),
    employeeId: Joi.string().messages({
      'string.empty': '员工编号不能为空'
    }),
    date: Joi.date().required().messages({
      'date.base': '日期格式不正确',
      'any.required': '日期是必填项'
    }),
    timeIn: Joi.string().required().messages({
      'string.empty': '签到时间不能为空',
      'any.required': '签到时间是必填项'
    }),
    timeOut: Joi.string().messages({
      'string.empty': '签退时间不能为空'
    }),
    hoursWorked: Joi.number().min(0).messages({
      'number.base': '工作时长必须是数字',
      'number.min': '工作时长不能小于{#limit}'
    }),
    status: Joi.string().valid('present', 'absent', 'late', 'half-day').default('present').messages({
      'any.only': '状态必须是：出勤、缺勤、迟到或半天'
    }),
    location: Joi.string().messages({
      'string.empty': '签到位置不能为空'
    }),
    latitude: Joi.number().allow(null).messages({
      'number.base': '签到纬度必须是数字'
    }),
    longitude: Joi.number().allow(null).messages({
      'number.base': '签到经度必须是数字'
    }),
    checkInMethod: Joi.string().valid('manual', 'gps', 'wifi', 'beacon').default('manual').messages({
      'any.only': '签到方式必须是：手动、GPS、WIFI或信标'
    }),
    notes: Joi.string().allow('', null)
  })
};

const getAttendance = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      'string.empty': '考勤记录ID不能为空',
      'any.required': '考勤记录ID是必填项',
      'any.custom': '考勤记录ID必须是有效的ObjectID格式'
    })
  })
};

const updateAttendance = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      'string.empty': '考勤记录ID不能为空',
      'any.required': '考勤记录ID是必填项',
      'any.custom': '考勤记录ID必须是有效的ObjectID格式'
    })
  }),
  body: Joi.object().keys({
    projectId: Joi.string().custom(objectId).messages({
      'string.empty': '项目编号不能为空',
      'any.custom': '项目编号必须是有效的ObjectID格式'
    }),
    employeeName: Joi.string().messages({
      'string.empty': '员工姓名不能为空'
    }),
    employeeId: Joi.string().messages({
      'string.empty': '员工编号不能为空'
    }),
    date: Joi.date().messages({
      'date.base': '日期格式不正确'
    }),
    timeIn: Joi.string().messages({
      'string.empty': '签到时间不能为空'
    }),
    timeOut: Joi.string().messages({
      'string.empty': '签退时间不能为空'
    }),
    hoursWorked: Joi.number().min(0).messages({
      'number.base': '工作时长必须是数字',
      'number.min': '工作时长不能小于{#limit}'
    }),
    status: Joi.string().valid('present', 'absent', 'late', 'half-day').messages({
      'any.only': '状态必须是：出勤、缺勤、迟到或半天'
    }),
    location: Joi.string().messages({
      'string.empty': '签到位置不能为空'
    }),
    latitude: Joi.number().allow(null).messages({
      'number.base': '签到纬度必须是数字'
    }),
    longitude: Joi.number().allow(null).messages({
      'number.base': '签到经度必须是数字'
    }),
    checkInMethod: Joi.string().valid('manual', 'gps', 'wifi', 'beacon').messages({
      'any.only': '签到方式必须是：手动、GPS、WIFI或信标'
    }),
    notes: Joi.string().allow('', null)
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deleteAttendance = {
  params: Joi.object().keys({
    id: Joi.string().custom(objectId).required().messages({
      'string.empty': '考勤记录ID不能为空',
      'any.required': '考勤记录ID是必填项',
      'any.custom': '考勤记录ID必须是有效的ObjectID格式'
    })
  })
};

const getAttendances = {
  query: Joi.object().keys({
    projectId: Joi.string().custom(objectId).messages({
      'string.empty': '项目编号不能为空',
      'any.custom': '项目编号必须是有效的ObjectID格式'
    }),
    department: Joi.number().integer().messages({
      'number.base': '部门ID必须是数字',
      'number.integer': '部门ID必须是整数'
    }),
    employeeName: Joi.string().allow(null).messages({
      'string.empty': '员工姓名不能为空'
    }),
    status: Joi.string().valid('present', 'absent', 'late', 'half-day').messages({
      'any.only': '状态必须是：出勤、缺勤、迟到或半天'
    }),
    dateFrom: Joi.date().messages({
      'date.base': '开始日期格式不正确'
    }),
    dateTo: Joi.date().messages({
      'date.base': '结束日期格式不正确'
    }),
    sortBy: Joi.string().default('date'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
      'any.only': '排序方式必须是：升序或降序'
    }),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于{#limit}'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    })
  })
};

module.exports = {
  createAttendance,
  getAttendance,
  updateAttendance,
  deleteAttendance,
  getAttendances
};