<template>
  <footer class="bg-white border-t mt-auto py-6">
    <div class="container mx-auto px-4">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="mb-4 md:mb-0">
          <div class="text-sm text-gray-600">
            &copy; {{ currentYear }} Information Management System. All rights reserved.
          </div>
        </div>
        
        <div class="flex space-x-6">
          <a href="#" class="text-gray-500 hover:text-blue-600 text-sm">Privacy Policy</a>
          <a href="#" class="text-gray-500 hover:text-blue-600 text-sm">Terms of Service</a>
          <a href="#" class="text-gray-500 hover:text-blue-600 text-sm">Help Center</a>
        </div>
      </div>
      
      <div class="mt-4 text-xs text-center text-gray-500">
        Version 1.0.0 | Last updated: {{ lastUpdatedDate }}
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue';

// Current year for copyright
const currentYear = computed(() => new Date().getFullYear());

// Last updated date (for demonstration purposes)
const lastUpdatedDate = computed(() => {
  const date = new Date();
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
});
</script>