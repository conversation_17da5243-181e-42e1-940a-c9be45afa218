# MySQL Support Documentation

This document provides information on how to set up and use MySQL with the application.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Configuration](#configuration)
3. [Setup](#setup)
4. [Migrations](#migrations)
5. [Backup and Restore](#backup-and-restore)
6. [Advanced Configuration](#advanced-configuration)
7. [NPM Scripts](#npm-scripts)
8. [Common Database Operations](#common-database-operations)
9. [Troubleshooting](#troubleshooting)

## Prerequisites

Before setting up MySQL support, ensure you have:

- MySQL server installed and running (version 5.7 or later recommended)
- Node.js and npm installed
- Access to a MySQL user with permissions to create databases and tables

## Configuration

### Environment Variables

The application uses the following environment variables for MySQL configuration:

```
# Database Configuration
DB_DIALECT=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=infoManager

# Database Pool Configuration
DB_POOL_MAX=5
DB_POOL_MIN=0
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000
DB_POOL_EVICT=1000
```

You can set these variables in the `.env` file in the root of the backend directory.

### Variable Descriptions

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_DIALECT` | Database dialect (set to `mysql`) | `sqlite` |
| `DB_HOST` | MySQL server hostname | `localhost` |
| `DB_PORT` | MySQL server port | `3306` |
| `DB_USER` | MySQL username | - |
| `DB_PASSWORD` | MySQL password | - |
| `DB_NAME` | Database name | `infoManager` |
| `DB_POOL_MAX` | Maximum number of connections in the pool | `5` |
| `DB_POOL_MIN` | Minimum number of connections in the pool | `0` |
| `DB_POOL_ACQUIRE` | Maximum time in milliseconds to acquire a connection | `30000` |
| `DB_POOL_IDLE` | Maximum idle time in milliseconds for a connection | `10000` |
| `DB_POOL_EVICT` | Time in milliseconds after which an idle connection is evicted | `1000` |

## Setup

### Initial Setup

After configuring the environment variables, run the MySQL setup script to create the database and initialize tables:

```bash
cd src/backend
node src/scripts/setup-mysql.js
```

This script will:
1. Check the connection to the MySQL server
2. Create the database if it doesn't exist
3. Create all necessary tables based on the application models

### Starting the Application with MySQL

Once the database is set up, you can start the application as usual:

```bash
npm run dev
```

The application will detect the MySQL configuration and connect to the MySQL database instead of SQLite.

## Migrations

The application includes a migration system to manage database schema changes. Migrations help when you need to modify the database structure while preserving data.

### Creating a Migration

To create a new migration file:

```bash
node src/scripts/mysql-migration.js create add-new-field
```

This will create a timestamped migration file in the `src/migrations` directory with `up` and `down` functions.

### Running Migrations

To apply pending migrations:

```bash
node src/scripts/mysql-migration.js run
```

### Rolling Back Migrations

To rollback the most recent migration:

```bash
node src/scripts/mysql-migration.js rollback
```

### Listing Migrations

To view the status of all migrations:

```bash
node src/scripts/mysql-migration.js list
```

## Backup and Restore

The application includes a comprehensive backup and restore system for MySQL databases.

### Using the Backup and Restore Script

The MySQL backup and restore script provides a simple way to create backups, list existing backups, restore from backups, and delete unneeded backups.

#### Creating a Backup

To create a backup of your database:

```bash
node src/scripts/mysql-backup.js backup
```

You can also add a name to your backup:

```bash
node src/scripts/mysql-backup.js backup "Pre-feature-launch"
```

Backups are stored in the `src/backend/backups` directory with timestamped filenames.

#### Listing Available Backups

To see all available backups:

```bash
node src/scripts/mysql-backup.js list
```

This will display a numbered list of backups with details such as file size and creation date.

#### Restoring from a Backup

To restore from a backup:

```bash
node src/scripts/mysql-backup.js restore
```

You can also specify a backup number directly:

```bash
node src/scripts/mysql-backup.js restore 1  # Restores the most recent backup
```

The script will create a safety backup before performing the restore operation.

#### Deleting Backups

To delete a backup:

```bash
node src/scripts/mysql-backup.js delete
```

Or specify a backup number directly:

```bash
node src/scripts/mysql-backup.js delete 3  # Deletes the third backup in the list
```

### Automated Backups

For production environments, it's recommended to set up automated backups using cron or a similar scheduler.

Example cron job to create daily backups:

```
0 2 * * * cd /path/to/application/src/backend && node src/scripts/mysql-backup.js backup "daily" > /dev/null 2>&1
```

### Backup Retention Policy

It's important to implement a backup retention policy to manage disk space. You might want to:

- Keep daily backups for the last week
- Keep weekly backups for the last month
- Keep monthly backups for the last year

You can create a script to automate this cleanup process based on your specific needs.

## Advanced Configuration

### Additional MySQL Configuration Options

The application supports several advanced MySQL configuration options through environment variables:

#### Connection Settings

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_LOGGING` | Enable SQL query logging (set to `true` to enable) | `false` |
| `DB_CONNECT_TIMEOUT` | MySQL connection timeout in milliseconds | `10000` |
| `DB_CHARSET` | Character set for MySQL connections | `utf8mb4` |
| `DB_COLLATE` | Collation for MySQL connections | `utf8mb4_unicode_ci` |
| `DB_TIMEZONE` | Timezone for MySQL connections | Server default |
| `DB_MARIADB` | Set to `true` if using MariaDB instead of MySQL | `false` |

#### SSL Configuration

For secure connections to MySQL:

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_SSL` | Enable SSL/TLS for MySQL connection | `false` |
| `DB_SSL_REJECT_UNAUTHORIZED` | Reject unauthorized SSL certificates | `true` |
| `DB_SSL_CA` | Path to CA certificate for SSL verification | - |

#### Connection Pool Settings

The connection pool can be fine-tuned with these variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_POOL_MAX` | Maximum number of connections in the pool | `5` |
| `DB_POOL_MIN` | Minimum number of connections in the pool | `0` |
| `DB_POOL_ACQUIRE` | Maximum time in milliseconds to acquire a connection | `30000` |
| `DB_POOL_IDLE` | Maximum idle time in milliseconds for a connection | `10000` |
| `DB_POOL_EVICT` | Time in milliseconds after which an idle connection is evicted | `1000` |

### Example Advanced Configuration

Here's an example of a `.env` file with advanced MySQL configuration:

```
# Basic MySQL Configuration
DB_DIALECT=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=password
DB_NAME=infoManager

# Advanced MySQL Configuration
DB_LOGGING=true
DB_CONNECT_TIMEOUT=15000
DB_CHARSET=utf8mb4
DB_COLLATE=utf8mb4_unicode_ci
DB_TIMEZONE=+00:00

# Connection Pool Configuration
DB_POOL_MAX=10
DB_POOL_MIN=2
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000
DB_POOL_EVICT=1000

# SSL Configuration (if needed)
# DB_SSL=true
# DB_SSL_REJECT_UNAUTHORIZED=true
# DB_SSL_CA=/path/to/ca-cert.pem
```

### Performance Considerations

For optimal performance with MySQL:

1. **Indexes**: Ensure proper indexing for columns used in WHERE, JOIN, and ORDER BY clauses.
2. **Connection Pool**: Adjust pool settings based on your application load.
3. **Query Optimization**: Use Sequelize's logging feature to identify slow queries.
4. **Server Configuration**: Adjust MySQL server settings (buffer sizes, cache sizes) based on server resources.

## NPM Scripts

For convenience, the application includes several npm scripts to make working with MySQL easier.

### Setup and Migrations

| Script | Description |
|--------|-------------|
| `npm run mysql:setup` | Initialize the MySQL database and tables |
| `npm run mysql:migration:create <name>` | Create a new migration file |
| `npm run mysql:migration:run` | Run all pending migrations |
| `npm run mysql:migration:rollback` | Rollback the last migration |
| `npm run mysql:migration:list` | List all migrations and their status |

### Backup and Restore

| Script | Description |
|--------|-------------|
| `npm run mysql:backup [name]` | Create a backup, optionally with a name |
| `npm run mysql:backup:list` | List all available backups |
| `npm run mysql:backup:restore [number]` | Restore from a backup by number |
| `npm run mysql:backup:delete [number]` | Delete a backup by number |

### Usage Examples

Initialize MySQL database:
```bash
npm run mysql:setup
```

Create a migration:
```bash
npm run mysql:migration:create add-user-profile
```

Create a backup before a major update:
```bash
npm run mysql:backup pre-update-v2
```

## Common Database Operations

For detailed examples of common database operations (create, read, update, delete) using Sequelize with MySQL, see the [Database Operations Guide](./database-operations.md).

This guide covers:
- Basic CRUD operations
- Working with transactions
- Advanced querying techniques
- Managing associations between models

## Troubleshooting

### Common Issues

#### Connection Refused

If you see an error like "Connection refused":

- Make sure MySQL is running
- Check that the hostname and port are correct
- Check your firewall settings

#### Access Denied

If you see an error like "Access denied for user":

- Verify your username and password
- Make sure the user has appropriate permissions

#### Unknown Database

If you see an error about an unknown database:

- Run the setup script to create the database
- Check that the database name in your `.env` file is correct

### Switching Back to SQLite

If you need to switch back to SQLite:

1. Update the `.env` file:
   ```
   DB_DIALECT=sqlite
   DB_STORAGE=./database.sqlite
   ```

2. Restart the application

### Debugging

For more detailed logging of database queries, you can modify the database.js file to enable query logging:

```javascript
// In src/config/database.js
sequelizeConfig.logging = console.log; // or logger.debug
```

## Additional Resources

- [Sequelize Documentation](https://sequelize.org/docs/v6/)
- [MySQL Documentation](https://dev.mysql.com/doc/) 