const httpStatus = require('http-status');
const { Op } = require('sequelize');
const { Project, User, Client, ProjectClientAssociation } = require('../models');
const ApiError = require('../utils/ApiError');
const sequelize = require('sequelize');

/**
 * Create a project
 * @param {Object} projectBody
 * @returns {Promise<Project>}
 */
const createProject = async (projectBody) => {
  // 如果未提供项目编号，自动生成一个
  if (!projectBody.code) {
    // 生成格式为 YH-年份-4位随机数字 的项目编号
    const year = new Date().getFullYear();
    const randomNum = Math.floor(1000 + Math.random() * 9000); // 生成1000-9999之间的随机数
    projectBody.code = `YH-${year}-${randomNum}`;
  }
  return Project.create(projectBody);
};

/**
 * Query for projects
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Projects and pagination info
 */
const queryProjects = async (filter, options) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;
  const offset = (page - 1) * limit;

  // Build where clause
  const whereClause = {};

  try {
    // First, describe the table to get the actual columns
    const projectAttributes = await Project.describe();
    console.log('Project table attributes:', Object.keys(projectAttributes));

    // Only add filters for columns that actually exist in the database
    if (filter.name && projectAttributes.name) {
      whereClause.name = { [Op.iLike]: `%${filter.name}%` };
    }

    if (filter.search && projectAttributes.name) {
      whereClause.name = { [Op.iLike]: `%${filter.search}%` };
    }

    if (filter.status && projectAttributes.status) {
      whereClause.status = filter.status;
    }

    if (filter.manager) {
      // Check if manager column exists directly or as a differently named column
      const managerColumn = projectAttributes.manager || projectAttributes.managerId;
      if (managerColumn) {
        const columnName = projectAttributes.manager ? 'manager' : 'managerId';
        whereClause[columnName] = { [Op.iLike]: `%${filter.manager}%` };
      }
    }

    if (filter.usageType && projectAttributes.usageType) {
      whereClause.usageType = filter.usageType;
    }

    if (filter.engineeringType && projectAttributes.engineeringType) {
      whereClause.engineeringType = filter.engineeringType;
    }

    if (filter.startDate && projectAttributes.startDate) {
      whereClause.startDate = { [Op.gte]: filter.startDate };
    }

    if (filter.endDate && projectAttributes.endDate) {
      whereClause.endDate = { [Op.lte]: filter.endDate };
    }

    if (filter.tags && filter.tags.length > 0 && projectAttributes.tags) {
      whereClause.tags = { [Op.overlap]: filter.tags };
    }

    // Verify the sortBy column exists and use a safe default if not
    let actualSortBy = 'id';  // Fallback to id which should always exist

    if (projectAttributes[sortBy]) {
      actualSortBy = sortBy;
    } else if (projectAttributes[sortBy.toLowerCase()]) {
      actualSortBy = sortBy.toLowerCase();
    } else if (projectAttributes[sortBy + '_at']) {
      // Handle cases like createdAt -> createdAt
      actualSortBy = sortBy + '_at';
    } else {
      console.log(`Warning: Sort column '${sortBy}' does not exist, defaulting to 'id'`);
    }

    // Create includes array for relations, only including them if they exist
    const includes = [];

    console.log('Final where clause:', whereClause);
    console.log('Using sort column:', actualSortBy);

    const { count, rows } = await Project.findAndCountAll({
      where: whereClause,
      include: includes,
      order: [[actualSortBy, sortOrder.toUpperCase()]],
      limit,
      offset,
      distinct: true
    });

    return {
      results: rows,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(count / limit),
        totalResults: count
      }
    };
  } catch (error) {
    console.error('Error in queryProjects:', error);
    // Return empty results instead of throwing an error for public endpoints
    return {
      results: [],
      pagination: {
        page,
        limit,
        totalPages: 0,
        totalResults: 0
      }
    };
  }
};

/**
 * Get project by id
 * @param {string} id
 * @returns {Promise<Project>}
 */
const getProjectById = async (id) => {
  const project = await Project.findByPk(id, {
    include: [
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!project) {
    throw new ApiError(404, 'Project not found');
  }
  return project;
};

/**
 * Update project by id
 * @param {string} projectId
 * @param {Object} updateBody
 * @returns {Promise<Project>}
 */
const updateProjectById = async (projectId, updateBody) => {
  const project = await getProjectById(projectId);
  Object.assign(project, updateBody);
  await project.save();
  return project;
};

/**
 * Delete project by id
 * @param {string} projectId
 * @returns {Promise<void>}
 */
const deleteProjectById = async (projectId) => {
  const project = await getProjectById(projectId);
  await project.destroy();
};

/**
 * 获取所有去重的建设单位，可选模糊搜索
 * @param {string} search
 * @returns {Promise<string[]>}
 */
const getConstructionUnits = async (search) => {
  const where = {};
  if (search) {
    where.constructionUnit = { [Op.iLike]: `%${search}%` };
  }
  const units = await Project.findAll({
    attributes: [[sequelize.fn('DISTINCT', sequelize.col('constructionUnit')), 'constructionUnit']],
    where,
    raw: true,
  });
  return units.map(u => u.constructionUnit).filter(Boolean);
};

/**
 * 获取项目列表用于下拉选择
 * @param {string} search - 可选的搜索关键词
 * @returns {Promise<Array>} - 包含id和name的项目列表
 */
const getProjectsForDropdown = async (search) => {
  const whereClause = {};
  if (search) {
    whereClause.name = { [Op.iLike]: `%${search}%` };
  }

  const projects = await Project.findAll({
    attributes: ['id', 'name', 'code'],
    where: whereClause,
    order: [['name', 'ASC']],
    limit: 100, // 限制返回数量
    raw: true
  });
  return projects;
};

/**
 * 获取项目关联的客户
 * @param {string} projectId
 * @returns {Promise<Client[]>}
 */
const getProjectClients = async (projectId) => {
  // 验证项目存在
  try {
    await getProjectById(projectId);
  } catch (error) {
    // 如果项目不存在，抛出更具体的错误信息
    if (error.statusCode === 404) {
      throw new ApiError(404, `项目不存在: ID为 ${projectId} 的项目未找到，可能已被删除或从未创建`);
    }
    throw error;
  }
  
  // 使用关联查询获取项目关联的所有客户
  const project = await Project.findByPk(projectId, {
    include: [
      {
        model: Client,
        as: 'clients',
        through: {
          model: ProjectClientAssociation,
          as: 'association',
          attributes: ['id', 'role', 'notes', 'createdAt']
        },
        attributes: ['id', 'name', 'code', 'type', 'status', 'contactName', 'phone', 'email']
      }
    ]
  });

  return project.clients || [];
};

/**
 * 为项目添加客户关联
 * @param {string} projectId
 * @param {string} clientId
 * @param {Object} associationData - 关联额外数据 (role, notes)
 * @returns {Promise<Object>}
 */
const addProjectClient = async (projectId, clientId, associationData = {}) => {
  // 验证项目和客户是否存在
  const project = await getProjectById(projectId);
  const client = await Client.findByPk(clientId);
  
  if (!client) {
    throw new ApiError(404, 'Client not found');
  }

  // 检查是否已经存在关联
  const existingAssociation = await ProjectClientAssociation.findOne({
    where: {
      projectId,
      clientId
    }
  });

  if (existingAssociation) {
    throw new ApiError(409, 'Client is already associated with this project');
  }

  // 创建新的关联
  const association = await ProjectClientAssociation.create({
    projectId,
    clientId,
    role: associationData.role || null,
    notes: associationData.notes || null
  });

  return {
    ...association.toJSON(),
    client: {
      id: client.id,
      name: client.name,
      code: client.code,
      status: client.status
    }
  };
};

/**
 * 移除项目客户关联
 * @param {string} projectId
 * @param {string} clientId
 * @returns {Promise<void>}
 */
const removeProjectClient = async (projectId, clientId) => {
  // 验证项目存在
  await getProjectById(projectId);
  
  // 查找关联记录
  const association = await ProjectClientAssociation.findOne({
    where: {
      projectId,
      clientId
    }
  });

  if (!association) {
    throw new ApiError(404, 'Client association not found');
  }

  // 删除关联
  await association.destroy();
};

module.exports = {
  createProject,
  queryProjects,
  getProjectById,
  updateProjectById,
  deleteProjectById,
  getConstructionUnits,
  getProjectsForDropdown,
  getProjectClients,
  addProjectClient,
  removeProjectClient
};