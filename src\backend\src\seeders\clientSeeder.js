const { v4: uuidv4 } = require('uuid');
const { User } = require('../models');
const { Client } = require('../models/client.model');

async function seedClients() {
  try {
    // Check if clients already exist
    const clientCount = await Client.count();
    if (clientCount > 0) {
      console.log('Clients already exist, skipping client seeding');
      return;
    }

    // Get or create a default admin user for createdBy field
    let adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      adminUser = await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin'
      });
    }

    // Mock client data
    const clients = [
      {
        id: uuidv4(),
        name: '北京科技有限公司',
        code: 'CL-2023-001',
        type: 'company',
        status: 'active',
        description: '专注于人工智能和大数据分析的科技公司',
        contactName: '张经理',
        contactTitle: '采购总监',
        phone: '***********',
        email: '<EMAIL>',
        address: '北京市海淀区中关村南大街5号',
        taxId: '91110108MA01C2XR9B',
        bankAccount: '6222021001234567890',
        bankName: '中国工商银行北京分行',
        bankCode: '************',
        source: '推荐',
        industry: '科技',
        notes: '重要客户，需要定期跟进',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '上海贸易有限公司',
        code: 'CL-2023-002',
        type: 'company',
        status: 'active',
        description: '专业从事国际贸易的公司',
        contactName: '李总',
        contactTitle: '总经理',
        phone: '***********',
        email: '<EMAIL>',
        address: '上海市浦东新区陆家嘴金融贸易区',
        taxId: '91310115MA1K3DXU9R',
        bankAccount: '6222021001234567891',
        bankName: '中国建设银行上海分行',
        bankCode: '************',
        source: '展会',
        industry: '贸易',
        notes: '合作关系稳定',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '广州市政府',
        code: 'CL-2023-003',
        type: 'government',
        status: 'active',
        description: '广州市政府机构',
        contactName: '王科长',
        contactTitle: '采购科长',
        phone: '***********',
        email: '<EMAIL>',
        address: '广州市越秀区府前路1号',
        taxId: '91440101MA59UQXT2A',
        bankAccount: '6222021001234567892',
        bankName: '中国银行广州分行',
        bankCode: '************',
        source: '招标',
        industry: '政府',
        notes: '政府项目，流程严格',
        createdBy: adminUser.id
      }
    ];

    // Bulk create clients
    await Client.bulkCreate(clients);
    console.log('Successfully seeded clients');
  } catch (error) {
    console.error('Error seeding clients:', error);
    throw error;
  }
}

module.exports = seedClients;
