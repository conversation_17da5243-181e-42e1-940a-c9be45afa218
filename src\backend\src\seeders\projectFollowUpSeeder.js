const { ProjectFollowUp } = require('../models/projectFollowUp.model');
const { Project } = require('../models/project.model');
const { Employee } = require('../models/employee.model');
const { faker } = require('@faker-js/faker/locale/zh_CN');

async function seedProjectFollowUps() {
  try {
    console.log('Starting to seed project follow-ups...');

    // Clear existing data
    await ProjectFollowUp.destroy({ where: {} });
    console.log('Cleared existing project follow-ups');

    // Get all projects and employees for reference
    const projects = await Project.findAll();
    const employees = await Employee.findAll();

    if (!projects.length) {
      console.log('No projects found. Please seed projects first');
      return;
    }

    if (!employees.length) {
      console.log('No employees found. Please seed employees first');
      return;
    }

    console.log(`Found ${projects.length} projects and ${employees.length} employees`);

    const followUps = [];

    // Create follow-ups for each project
    for (const project of projects) {
      // Create 2-4 follow-ups per project
      const followUpCount = faker.number.int({ min: 2, max: 4 });
      
      for (let i = 0; i < followUpCount; i++) {
        const followUpDate = faker.date.between({
          from: project.startDate || '2023-01-01',
          to: project.endDate || '2024-12-31'
        });

        const followUp = {
          projectId: project.id,
          followUpDate: followUpDate,
          content: faker.lorem.paragraph(),
          createdBy: faker.helpers.arrayElement(employees).id,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        followUps.push(followUp);
      }
    }

    if (followUps.length > 0) {
      await ProjectFollowUp.bulkCreate(followUps);
      console.log(`Successfully created ${followUps.length} project follow-ups`);
    }

    console.log('Project follow-ups seeder executed successfully');
  } catch (error) {
    console.error('Error seeding project follow-ups:', error);
    throw error; // 重新抛出错误以便上层捕获
  }
}

module.exports = seedProjectFollowUps; 