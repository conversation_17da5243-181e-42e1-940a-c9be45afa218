// 加载测试环境配置
require('dotenv').config({ path: '.env.test' });

const { sequelize } = require('../src/models');
const config = require('../src/config/config');

// 确保测试环境
process.env.NODE_ENV = 'test';

// 在所有测试开始前运行
exports.mochaHooks = {
  beforeAll: async function() {
    this.timeout(10000); // 增加超时时间
    console.log('Setting up test environment...');

    try {
      // 连接数据库
      await sequelize.authenticate();
      console.log('Database connection established successfully.');
    } catch (error) {
      console.error('Unable to connect to the database:', error);
      throw error;
    }
  },

  // 在所有测试结束后运行
  afterAll: async function() {
    console.log('Cleaning up test environment...');

    // 关闭数据库连接
    await sequelize.close();
    console.log('Database connection closed.');
  },

  beforeEach: async function() {
    // 可以在这里添加每个测试前的设置
    this.currentTest.transaction = await sequelize.transaction();
  },

  afterEach: async function() {
    // 回滚事务，确保测试数据不会影响其他测试
    if (this.currentTest.transaction) {
      await this.currentTest.transaction.rollback();
    }
  }
};
