<template>
  <Transition
    name="message"
    enter-active-class="transform transition ease-out duration-300"
    enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
    enter-to-class="translate-y-0 opacity-100 sm:translate-x-0"
    leave-active-class="transition ease-in duration-200"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
    @after-leave="$emit('closed')"
  >
    <div
      v-if="visible"
      :class="[
        'fixed flex items-center gap-3 p-4 rounded-lg shadow-md z-50',
        typeClasses,
        positionClasses
      ]"
    >
      <!-- Icon based on message type -->
      <div class="flex-shrink-0">
        <!-- Success Icon -->
        <svg v-if="type === 'success'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        
        <!-- Error Icon -->
        <svg v-else-if="type === 'error'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
        
        <!-- Warning Icon -->
        <svg v-else-if="type === 'warning'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
        </svg>
        
        <!-- Info Icon -->
        <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      
      <!-- Message content -->
      <div>
        <p class="font-medium">{{ title }}</p>
        <p class="text-sm" v-if="message">{{ message }}</p>
      </div>
      
      <!-- Close button -->
      <button
        v-if="closable"
        @click="close"
        class="ml-auto flex-shrink-0 inline-flex text-current hover:opacity-75 focus:outline-none"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  </Transition>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  type: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
  },
  title: {
    type: String,
    required: true
  },
  message: {
    type: String,
    default: ''
  },
  duration: {
    type: Number,
    default: 3005
  },
  position: {
    type: String,
    default: 'top-right',
    validator: (value) => [
      'top-right', 'top-left', 'top-center',
      'bottom-right', 'bottom-left', 'bottom-center'
    ].includes(value)
  },
  closable: {
    type: Boolean,
    default: true
  },
  autodismiss: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['closed']);
const visible = ref(true);
let timer = null;

const typeClasses = computed(() => {
  switch (props.type) {
    case 'success':
      return 'bg-green-50 text-green-800 dark:bg-green-900/50 dark:text-green-300';
    case 'error':
      return 'bg-red-50 text-red-800 dark:bg-red-900/50 dark:text-red-300';
    case 'warning':
      return 'bg-yellow-50 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300';
    case 'info':
    default:
      return 'bg-blue-50 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300';
  }
});

const positionClasses = computed(() => {
  switch (props.position) {
    case 'top-left':
      return 'top-4 left-4';
    case 'top-center':
      return 'top-4 left-1/2 transform -translate-x-1/2';
    case 'bottom-right':
      return 'bottom-4 right-4';
    case 'bottom-left':
      return 'bottom-4 left-4';
    case 'bottom-center':
      return 'bottom-4 left-1/2 transform -translate-x-1/2';
    case 'top-right':
    default:
      return 'top-4 right-4';
  }
});

const close = () => {
  visible.value = false;
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }
};

onMounted(() => {
  if (props.autodismiss && props.duration > 0) {
    timer = setTimeout(() => {
      close();
    }, props.duration);
  }
});

onUnmounted(() => {
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }
});

defineExpose({ close });
</script>

<style scoped>
/* Add any additional styling here if needed */
</style> 