const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

// First, let's force a sync to see what's in the database

const Token = sequelize.define('token', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  token: {
    type: DataTypes.STRING,
    allowNull: false
  },
  type: {
    type: DataTypes.STRING,
    allowNull: false
  },
  expires: {
    type: DataTypes.DATE,
    allowNull: false
  },
  blacklisted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  // Match the exact column name that <PERSON><PERSON><PERSON> is expecting
  "userId": {
    type: DataTypes.UUID,
    allowNull: false,
    field: 'userId',
    references: {
      model: 'user',
      key: 'id'
    }
  }
}, {
  timestamps: true,
});

module.exports = { Token };