const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { categoryService } = require('../services');
const pick = require('../utils/pick');

/**
 * Create a category
 * @route POST /api/categories
 */
const createCategory = catchAsync(async (req, res) => {
  const category = await categoryService.createCategory(req.body);
  res.status(201).send(category);
});

/**
 * Get all categories
 * @route GET /api/categories
 */
const getCategories = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['name', 'isActive', 'parentId']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  
  // Set default values for pagination
  options.page = parseInt(options.page, 10) || 1;
  options.limit = parseInt(options.limit, 10) || 10;
  options.offset = (options.page - 1) * options.limit;

  const result = await categoryService.queryCategories(filter, options);
  res.send(result);
});

/**
 * Get category by id
 * @route GET /api/categories/:id
 */
const getCategory = catchAsync(async (req, res) => {
  const category = await categoryService.getCategoryById(req.params.categoryId);
  if (!category) {
    return res.status(404).send({ message: 'Category not found' });
  }
  res.send(category);
});

/**
 * Update category
 * @route PATCH /api/categories/:id
 */
const updateCategory = catchAsync(async (req, res) => {
  const category = await categoryService.updateCategoryById(req.params.categoryId, req.body);
  res.send(category);
});

/**
 * Delete category
 * @route DELETE /api/categories/:id
 */
const deleteCategory = catchAsync(async (req, res) => {
  await categoryService.deleteCategoryById(req.params.categoryId);
  res.status(204).send();
});

module.exports = {
  createCategory,
  getCategories,
  getCategory,
  updateCategory,
  deleteCategory,
};