const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { customerService } = require('../services');

/**
 * Create a new customer
 * @route POST /api/customers
 */
const createCustomer = catchAsync(async (req, res) => {
  const customer = await customerService.createCustomer({
    ...req.body,
    createdBy: req.user.id
  });
  res.status(201).send(customer);
});

/**
 * Get a customer by ID
 * @route GET /api/customers/:id
 */
const getCustomer = catchAsync(async (req, res) => {
  const customer = await customerService.getCustomerById(req.params.id);
  res.send(customer);
});

/**
 * Update a customer
 * @route PATCH /api/customers/:id
 */
const updateCustomer = catchAsync(async (req, res) => {
  const customer = await customerService.updateCustomerById(req.params.id, req.body);
  res.send(customer);
});

/**
 * Delete a customer
 * @route DELETE /api/customers/:id
 */
const deleteCustomer = catchAsync(async (req, res) => {
  await customerService.deleteCustomerById(req.params.id);
  res.status(204).send();
});

/**
 * Get all customers with pagination
 * @route GET /api/customers
 */
const getCustomers = catchAsync(async (req, res) => {
  const filter = {
    category: req.query.category,
    level: req.query.level,
    followUpStage: req.query.followUpStage,
    search: req.query.search,
    tags: req.query.tags
  };
  const options = {
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10,
    sortBy: req.query.sortBy || 'createdAt',
    sortOrder: req.query.sortOrder || 'desc'
  };
  const result = await customerService.queryCustomers(filter, options);
  res.send(result);
});

module.exports = {
  createCustomer,
  getCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomers
}; 