const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { ProjectTask, Project, User } = require('../models');

/**
 * Create a project task
 * @param {Object} taskBody
 * @returns {Promise<ProjectTask>}
 */
const createProjectTask = async (taskBody) => {
  // Check if project exists
  const project = await Project.findByPk(taskBody.projectId);
  if (!project) {
    throw new ApiError(400, 'Project not found');
  }

  // Check if assignee exists
  const assignee = await User.findByPk(taskBody.assigneeId);
  if (!assignee) {
    throw new ApiError(400, 'Assignee not found');
  }

  return ProjectTask.create(taskBody);
};

/**
 * Get project task by id
 * @param {string} id
 * @returns {Promise<ProjectTask>}
 */
const getProjectTaskById = async (id) => {
  const task = await ProjectTask.findByPk(id, {
    include: [
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: User,
        as: 'Assignee',
        attributes: ['id', 'firstName', 'lastName', 'email', 'department', 'position']
      },
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!task) {
    throw new ApiError(404, 'Project task not found');
  }
  return task;
};

/**
 * Update project task by id
 * @param {string} taskId
 * @param {Object} updateBody
 * @returns {Promise<ProjectTask>}
 */
const updateProjectTaskById = async (taskId, updateBody) => {
  const task = await getProjectTaskById(taskId);
  
  // Check if assignee exists if being updated
  if (updateBody.assigneeId) {
    const assignee = await User.findByPk(updateBody.assigneeId);
    if (!assignee) {
      throw new ApiError(400, 'Assignee not found');
    }
  }

  Object.assign(task, updateBody);
  await task.save();
  return task;
};

/**
 * Delete project task by id
 * @param {string} taskId
 * @returns {Promise<void>}
 */
const deleteProjectTaskById = async (taskId) => {
  const task = await getProjectTaskById(taskId);
  await task.destroy();
};

/**
 * Query for project tasks
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Tasks and pagination info
 */
const queryProjectTasks = async (filter, options) => {
  const { page, limit, sortBy, sortOrder } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};
  
  if (filter.projectId) {
    whereClause.projectId = filter.projectId;
  }
  
  if (filter.status) {
    whereClause.status = filter.status;
  }
  
  if (filter.priority) {
    whereClause.priority = filter.priority;
  }
  
  if (filter.assigneeId) {
    whereClause.assigneeId = filter.assigneeId;
  }
  
  if (filter.taskType) {
    whereClause.taskType = filter.taskType;
  }
  
  if (filter.search) {
    whereClause[Op.or] = [
      { name: { [Op.like]: `%${filter.search}%` } },
      { description: { [Op.like]: `%${filter.search}%` } }
    ];
  }

  // Query with pagination
  const { count, rows } = await ProjectTask.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'Assignee',
        attributes: ['id', 'firstName', 'lastName', 'email', 'department', 'position']
      }
    ],
    order: [[sortBy, sortOrder]],
    limit,
    offset
  });

  return {
    results: rows,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

module.exports = {
  createProjectTask,
  getProjectTaskById,
  updateProjectTaskById,
  deleteProjectTaskById,
  queryProjectTasks
}; 