<template>
  <div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <!-- 页面头部信息 -->
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <div>
            <div class="flex items-center">
              <router-link v-if="inventoryId" :to="`/inventory/${inventoryId}`" class="text-blue-600 hover:text-blue-800 mr-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
              </router-link>
          
              <h1 class="text-2xl font-bold text-gray-900">库存记录</h1>
            </div>
            <p v-if="inventoryId" class="text-gray-500 mt-1">{{ inventoryInfo.name }} ({{ inventoryInfo.code }})</p>
            <p v-else class="text-gray-500 mt-1">所有产品的出入库记录</p>
          </div>
          <div class="flex space-x-2">
           
           
        

                <router-link :to="inventoryId ? `/inventory/${inventoryId}` : '/inventory'" class="btn  flex items-center shadow-md hover:shadow-lg transition-all duration-200">
               <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          返回列表
        </router-link>
          </div>
        </div>
      </div>

      <!-- 筛选条件 -->
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex flex-wrap gap-4">
          <div v-if="!inventoryId" class="w-full md:w-auto flex-1 min-w-[200px]">
            <label for="material-name" class="block text-sm font-medium text-gray-700 mb-1">产品名称</label>
            <input
              type="text"
              id="material-name"
              v-model="filters.materialName"
              class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              placeholder="输入产品名称"
            >
          </div>
          <div v-if="!inventoryId" class="w-full md:w-auto flex-1 min-w-[200px]">
            <label for="material-code" class="block text-sm font-medium text-gray-700 mb-1">产品编码</label>
            <div class="flex">
              <input
                type="text"
                id="material-code"
                v-model="filters.materialCode"
                c class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                placeholder="输入产品编码"
              >
          
            </div>
          </div>
          <div class="w-full md:w-auto flex-1 min-w-[200px]">
            <label for="transaction-type" class="block text-sm font-medium text-gray-700 mb-1">类型</label>
            <select
              id="transaction-type"
              v-model="filters.type"
              class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            >
              <option value="">全部类型</option>
              <option value="入库">入库</option>
              <option value="出库">出库</option>
              <option value="盘点">盘点</option>
            </select>
          </div>
          <div class="w-full md:w-auto flex-1 min-w-[200px]">
            <label for="date-start" class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
            <input
              type="date"
              id="date-start"
              v-model="filters.dateStart"
              class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              max="3000-12-31"
            >
          </div>
          <div class="w-full md:w-auto flex-1 min-w-[200px]">
            <label for="date-end" class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
            <input
              type="date"
              id="date-end"
              v-model="filters.dateEnd"
              class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              max="3000-12-31"
            >
          </div>
          <div class="flex justify-end mt-6">
            <button 
              @click="resetFilters" 
              class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200"
              :disabled="loading"
            >
              重置
            </button>
            <button 
              @click="applyFilters" 
              class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200"
              :disabled="loading"
            >
              <span v-if="loading" class="inline-block animate-spin mr-1">↻</span>
              {{ loading ? '加载中...' : '查询' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 错误提示 -->
      <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="font-medium">错误：</span>
        </div>
        <p class="mt-2">{{ error }}</p>
        <div class="mt-3 flex space-x-3">
          <button @click="fetchTransactions()" class="text-red-700 hover:text-red-900 font-medium underline">
            重试
          </button>
          <button @click="backToDetail()" class="text-blue-700 hover:text-blue-900 font-medium underline">
            返回产品详情
          </button>
        </div>
      </div>

      <!-- 库存记录表格 -->
      <div v-if="!error" class="overflow-x-auto">
        <div v-if="loading" class="py-12 flex justify-center items-center">
          <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <table v-else class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单号</th>
              <th v-if="!inventoryId" scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品名称</th>
              <th v-if="!inventoryId" scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品编码</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作人</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联信息</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(record, index) in transactions" :key="index" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-800">
                <a @click="showTransactionDetail(record)" class="cursor-pointer">{{ record.code }}</a>
              </td>
              <td v-if="!inventoryId" class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <router-link :to="`/inventory/${record.productId}`" class="text-blue-600 hover:text-blue-900">
                  {{ record.productName || '未知产品' }}
                </router-link>
              </td>
              <td v-if="!inventoryId" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ record.productCode || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm">
                <span
                  :class="{
                    'bg-green-100 text-green-800': record.type === '入库',
                    'bg-red-100 text-red-800': record.type === '出库',
                    'bg-gray-100 text-gray-800': record.type === '盘点'
                  }"
                  class="px-2 py-1 rounded-full text-xs font-medium">
                  {{ record.type }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <span :class="{'text-green-600': record.type === '入库', 'text-red-600': record.type === '出库'}">
                  {{ record.type === '入库' ? '+' : record.type === '出库' ? '-' : '' }}{{ record.quantity }} {{ record.unit || inventoryInfo.unit }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ record.date }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ record.operator }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div v-if="record.type === '入库'">
                  <span>供应商: {{ record.supplier || '无' }}</span>
                </div>
                <div v-else-if="record.type === '出库'">
                  <span v-if="record.projectName">项目: {{ record.projectName }}</span>
                  <span v-if="record.recipient">领用人: {{ record.recipient }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ record.remarks || '-' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <a @click="showTransactionDetail(record)" class="text-blue-600 hover:text-blue-900 cursor-pointer">
                  详情
                </a>
              </td>
            </tr>
            <tr v-if="transactions.length === 0">
              <td colspan="8" class="px-6 py-12 text-center text-gray-500">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">暂无库存记录</h3>
                <p class="mt-1 text-sm text-gray-500">该产品还没有任何入库或出库操作记录</p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页控件 -->
      <div class="px-6 py-4 bg-white border-t border-gray-200 flex items-center justify-between">
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示
              <span class="font-medium">{{ startIndex + 1 }}</span>
              至
              <span class="font-medium">{{ Math.min(endIndex, totalCount) }}</span>
              条，共
              <span class="font-medium">{{ totalCount }}</span>
              条记录
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                @click="changePage(currentPage - 1)"
                :disabled="currentPage === 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="sr-only">上一页</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </button>
              <div v-for="page in displayedPages" :key="page">
                <button
                  v-if="page !== '...'"
                  @click="changePage(page)"
                  :class="[
                    'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                    page === currentPage
                      ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                  ]"
                >
                  {{ page }}
                </button>
                <span
                  v-else
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"
                >
                  {{ page }}
                </span>
              </div>
              <button
                @click="changePage(currentPage + 1)"
                :disabled="currentPage >= totalPages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span class="sr-only">下一页</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
        <div class="flex sm:hidden">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          <span class="mx-4 flex items-center text-sm text-gray-500">
            第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
          </span>
          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage >= totalPages"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
      </div>
    </div>

    <!-- 打印预览模态框 -->
    <div v-if="showPrintPreview" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="p-5 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">打印预览</h3>
          <button @click="showPrintPreview = false" class="text-gray-400 hover:text-gray-500">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="p-5" id="print-content">
          <div class="mb-6 text-center">
            <h2 class="text-xl font-bold">库存记录</h2>
            <p class="text-gray-600">{{ inventoryInfo.name }} ({{ inventoryInfo.code }})</p>
            <p class="text-gray-500 text-sm mt-1">打印时间: {{ new Date().toLocaleString() }}</p>
          </div>

          <table class="min-w-full border border-gray-200">
            <thead>
              <tr>
                <th class="px-4 py-2 border text-left text-xs font-medium text-gray-500 uppercase">单号</th>
                <th class="px-4 py-2 border text-left text-xs font-medium text-gray-500 uppercase">类型</th>
                <th class="px-4 py-2 border text-left text-xs font-medium text-gray-500 uppercase">数量</th>
                <th class="px-4 py-2 border text-left text-xs font-medium text-gray-500 uppercase">日期</th>
                <th class="px-4 py-2 border text-left text-xs font-medium text-gray-500 uppercase">操作人</th>
                <th class="px-4 py-2 border text-left text-xs font-medium text-gray-500 uppercase">关联信息</th>
                <th class="px-4 py-2 border text-left text-xs font-medium text-gray-500 uppercase">备注</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(record, index) in transactions" :key="index">
                <td class="px-4 py-2 border text-sm">{{ record.code }}</td>
                <td class="px-4 py-2 border text-sm">{{ record.type }}</td>
                <td class="px-4 py-2 border text-sm">
                  {{ record.type === '入库' ? '+' : record.type === '出库' ? '-' : '' }}{{ record.quantity }} {{ inventoryInfo.unit }}
                </td>
                <td class="px-4 py-2 border text-sm">{{ record.date }}</td>
                <td class="px-4 py-2 border text-sm">{{ record.operator }}</td>
                <td class="px-4 py-2 border text-sm">
                  <div v-if="record.type === '入库'">
                    <span>供应商: {{ record.supplier || '无' }}</span>
                  </div>
                  <div v-else-if="record.type === '出库'">
                    <span v-if="record.projectName">项目: {{ record.projectName }}</span>
                    <span v-if="record.recipient">领用人: {{ record.recipient }}</span>
                  </div>
                </td>
                <td class="px-4 py-2 border text-sm">{{ record.remarks || '-' }}</td>
              </tr>
            </tbody>
          </table>

          <div class="mt-6 flex justify-between text-sm text-gray-500">
            <div>总计: {{ transactions.length }} 条记录</div>
            <div>入库总量: {{ inboundTotal }} {{ inventoryInfo.unit }}</div>
            <div>出库总量: {{ outboundTotal }} {{ inventoryInfo.unit }}</div>
          </div>
        </div>
        <div class="p-5 border-t border-gray-200 flex justify-end">
          <button
            @click="doPrint"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            打印文档
          </button>
        </div>
      </div>
    </div>

    <!-- 交易详情模态框 -->
    <div v-if="showTransactionDetailModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-4">
        <div class="p-5 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">库存操作详情</h3>
          <button @click="showTransactionDetailModal = false" class="text-gray-400 hover:text-gray-500">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="p-5" v-if="selectedTransaction">
          <div class="space-y-4">
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <p class="text-sm font-medium text-gray-500">交易单号</p>
                  <p class="text-lg font-bold">{{ selectedTransaction.code }}</p>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-500">交易类型</p>
                  <p>
                    <span
                      :class="{
                        'bg-green-100 text-green-800': selectedTransaction.type === '入库',
                        'bg-red-100 text-red-800': selectedTransaction.type === '出库',
                        'bg-gray-100 text-gray-800': selectedTransaction.type === '盘点'
                      }"
                      class="px-2 py-1 rounded-full text-xs font-medium">
                      {{ selectedTransaction.type }}
                    </span>
                  </p>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm font-medium text-gray-500">交易数量</p>
                <p class="text-base" :class="{'text-green-600': selectedTransaction.type === '入库', 'text-red-600': selectedTransaction.type === '出库'}">
                  {{ selectedTransaction.type === '入库' ? '+' : selectedTransaction.type === '出库' ? '-' : '' }}
                  {{ selectedTransaction.quantity }} {{ inventoryInfo.unit }}
                </p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500">交易日期</p>
                <p class="text-base">{{ selectedTransaction.date }}</p>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-500">操作人</p>
                <p class="text-base">{{ selectedTransaction.operator }}</p>
              </div>
              <div v-if="selectedTransaction.type === '入库' && selectedTransaction.supplier">
                <p class="text-sm font-medium text-gray-500">供应商</p>
                <p class="text-base">{{ selectedTransaction.supplier }}</p>
              </div>
              <div v-if="selectedTransaction.type === '出库' && selectedTransaction.projectName">
                <p class="text-sm font-medium text-gray-500">关联项目</p>
                <p class="text-base">{{ selectedTransaction.projectName }}</p>
              </div>
              <div v-if="selectedTransaction.type === '出库' && selectedTransaction.recipient">
                <p class="text-sm font-medium text-gray-500">领用人</p>
                <p class="text-base">{{ selectedTransaction.recipient }}</p>
              </div>
            </div>

            <div v-if="selectedTransaction.remarks">
              <p class="text-sm font-medium text-gray-500">备注</p>
              <p class="text-base p-3 bg-gray-50 rounded-md">{{ selectedTransaction.remarks }}</p>
            </div>
          </div>
        </div>
        <div class="p-5 border-t border-gray-200 flex justify-end">
          <button
            @click="showTransactionDetailModal = false"
            class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { inventoryService } from '@/services/apiService';

export default {
  name: 'InventoryTransactions',
  data() {
    return {
      loading: true,
      error: null,
      inventoryId: '',
      inventoryInfo: {
        name: '',
        code: '',
        unit: ''
      },
      transactions: [],
      filters: {
        type: '',
        dateStart: '',
        dateEnd: '',
        materialName: '',
        materialCode: ''
      },
      pagination: {
        totalCount: 0,
        pageSize: 10,
        currentPage: 1
      },
      showPrintPreview: false,
      showTransactionDetailModal: false,
      selectedTransaction: null,
      pendingCodeFilter: null,
      exactMatchOnly: false
    }
  },
  computed: {
    currentPage: {
      get() {
        return this.pagination.currentPage;
      },
      set(value) {
        this.pagination.currentPage = value;
      }
    },
    totalCount() {
      return this.pagination.totalCount;
    },
    pageSize() {
      return this.pagination.pageSize;
    },
    totalPages() {
      return Math.ceil(this.totalCount / this.pageSize);
    },
    startIndex() {
      return (this.currentPage - 1) * this.pageSize;
    },
    endIndex() {
      return this.startIndex + this.pageSize;
    },
    displayedPages() {
      const pages = [];
      const totalPages = this.totalPages;
      const currentPage = this.currentPage;

      if (totalPages <= 7) {
        // 显示所有页码
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // 总是显示第一页
        pages.push(1);

        // 当前页靠近开始
        if (currentPage <= 3) {
          pages.push(2, 3, 4, '...', totalPages - 1, totalPages);
        }
        // 当前页靠近结束
        else if (currentPage >= totalPages - 2) {
          pages.push('...', totalPages - 3, totalPages - 2, totalPages - 1, totalPages);
        }
        // 当前页在中间
        else {
          pages.push('...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages);
        }
      }

      return pages;
    },
    inboundTotal() {
      return this.transactions
        .filter(t => t.type === '入库')
        .reduce((sum, t) => sum + t.quantity, 0);
    },
    outboundTotal() {
      return this.transactions
        .filter(t => t.type === '出库')
        .reduce((sum, t) => sum + t.quantity, 0);
    }
  },
  created() {
    // 根据路由判断是查看特定产品的库存记录还是全局库存记录
    this.inventoryId = this.$route.params.id;
    if (this.inventoryId) {
      this.fetchInventoryInfo();
    }
    this.fetchTransactions();
  },
  methods: {
    // 返回产品详情页面
    backToDetail() {
      if (this.inventoryId) {
        this.$router.push(`/inventory/${this.inventoryId}`);
      } else {
        this.$router.push('/inventory');
      }
    },
    
    // 显示交易详情模态框
    showTransactionDetail(transaction) {
      this.selectedTransaction = transaction;
      this.showTransactionDetailModal = true;
    },
    
    async fetchInventoryInfo() {
      if (!this.inventoryId) return;
      
      try {
        this.error = null;
        // 直接使用axios调用产品API
        const response = await axios.get(`/api/products/${this.inventoryId}`);
        this.inventoryInfo = response.data;
      } catch (err) {
        console.error('获取产品信息失败', err);
        this.error = err.response?.data?.message || '获取产品信息失败，请重试';
        // 如果API调用失败，使用基本信息
        this.inventoryInfo = {
          name: '产品' + this.inventoryId,
          code: 'M' + this.inventoryId,
          unit: '个'
        };
      }
    },

    async fetchTransactions() {
      try {
        this.loading = true;
        this.error = null;

        // 如果是特定产品的库存记录，验证inventoryId格式
        if (this.inventoryId) {
          // 验证inventoryId是否符合MongoDB ObjectId或UUID格式
          const objectIdPattern = /^[0-9a-fA-F]{24}$/;
          const uuidPattern = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
          
          if (!objectIdPattern.test(this.inventoryId) && !uuidPattern.test(this.inventoryId)) {
            this.error = '产品ID格式无效，必须是有效的MongoDB ObjectId或UUID格式';
            this.loading = false;
            return;
          }
        }

        // 构建查询参数
        const params = {
          page: this.currentPage,
          limit: this.pageSize,
        };

        // 处理筛选条件
        if (this.filters.type) {
          // 将中文类型转换为英文API类型
          const typeMap = {
            '入库': 'in',
            '出库': 'out',
            '盘点': 'adjustment'
          };
          params.type = typeMap[this.filters.type];
        }

        if (this.filters.dateStart) {
          params.transactionDateFrom = this.filters.dateStart;
        }

        if (this.filters.dateEnd) {
          params.transactionDateTo = this.filters.dateEnd;
        }

        // 仅全局视图时添加产品名称和编码筛选
        if (!this.inventoryId) {
          if (this.filters.materialName) {
            params.productName = this.filters.materialName;
          }
          if (this.filters.materialCode) {
            // 由于API不允许模糊搜索参数，我们将使用客户端过滤
            this.pendingCodeFilter = this.filters.materialCode;
            
            // 除非需要精确匹配，否则我们不发送此参数到API
            if (this.exactMatchOnly) {
              params.productCode = this.filters.materialCode;
            }
          }
        }

        // 根据是否有inventoryId决定API路径
        const apiUrl = this.inventoryId 
          ? `/api/products/${this.inventoryId}/transactions` 
          : '/api/inventory/transactions';
        
        // 调用API获取库存记录
        const response = await axios.get(apiUrl, { params });
        
        // 调试信息 - 查看API返回结构
        console.log('API Response:', JSON.stringify(response.data));
        console.log('Sample item:', response.data.results && response.data.results.length > 0 ? 
          JSON.stringify(response.data.results[0]) : 'No results');
          
        if (response.data && Array.isArray(response.data.results)) {
          // 数据转换 - 将API返回的数据映射到组件需要的格式
          this.transactions = response.data.results.map(item => ({
            id: item.id,
            code: item.reference || `TX-${item.id.slice(0, 8)}`,
            type: item.type === 'in' ? '入库' : item.type === 'out' ? '出库' : '盘点',
            quantity: item.quantity,
            date: new Date(item.transactionDate).toLocaleDateString(),
            operator: item.Creator?.username || '系统',
            supplier: item.supplier,
            projectName: item.Project?.name,
            recipient: item.recipient,
            remarks: item.notes,
            // 全局视图需要的额外字段
            productId: item.productId || item.product_id || item.productID,
            productName: item.Product?.name || item.product?.name || item.productName || item.product_name || '未知产品',
            productCode: item.Product?.code || item.product?.code || item.productCode || item.product_code || '-',
            unit: item.Product?.unit || item.product?.unit || item.unit || this.inventoryInfo?.unit || '个'
          }));
          
          // 设置分页信息
          this.pagination.totalCount = response.data.totalResults || response.data.total || 0;
          this.pagination.currentPage = response.data.page || 1;
          
          // 应用客户端产品编码模糊搜索
          if (this.pendingCodeFilter && !this.exactMatchOnly) {
            const filteredResults = this.transactions.filter(item => 
              item.productCode && 
              item.productCode.toLowerCase().includes(this.pendingCodeFilter.toLowerCase())
            );
            
            this.transactions = filteredResults;
            this.pagination.totalCount = filteredResults.length;
          }
        } else if (response.data && Array.isArray(response.data.data)) {
          // 可能的替代API响应格式
          console.log('使用替代API响应格式 (data 字段)');
          this.transactions = response.data.data.map(item => ({
            id: item.id,
            code: item.reference || `TX-${item.id.slice(0, 8)}`,
            type: item.type === 'in' ? '入库' : item.type === 'out' ? '出库' : '盘点',
            quantity: item.quantity,
            date: new Date(item.transactionDate || item.transaction_date || item.date).toLocaleDateString(),
            operator: item.Creator?.username || item.creator?.username || item.operator || '系统',
            supplier: item.supplier || '',
            projectName: item.Project?.name || item.project?.name || '',
            recipient: item.recipient || '',
            remarks: item.notes || item.remarks || '',
            // 全局视图需要的额外字段
            productId: item.productId || item.product_id || item.productID,
            productName: item.Product?.name || item.product?.name || item.productName || item.product_name || '未知产品',
            productCode: item.Product?.code || item.product?.code || item.productCode || item.product_code || '-',
            unit: item.Product?.unit || item.product?.unit || item.unit || this.inventoryInfo?.unit || '个'
          }));
          
          // 设置分页信息
          this.pagination.totalCount = response.data.totalCount || response.data.total || response.data.count || 0;
          this.pagination.currentPage = response.data.page || response.data.current_page || 1;
          
          // 应用客户端产品编码模糊搜索
          if (this.pendingCodeFilter && !this.exactMatchOnly) {
            const filteredResults = this.transactions.filter(item => 
              item.productCode && 
              item.productCode.toLowerCase().includes(this.pendingCodeFilter.toLowerCase())
            );
            
            this.transactions = filteredResults;
            this.pagination.totalCount = filteredResults.length;
          }
        } else if (response.data && Array.isArray(response.data)) {
          // 数组形式的响应格式
          console.log('使用数组格式的API响应');
          this.transactions = response.data.map(item => ({
            id: item.id,
            code: item.reference || `TX-${item.id.slice(0, 8)}`,
            type: item.type === 'in' ? '入库' : item.type === 'out' ? '出库' : '盘点',
            quantity: item.quantity,
            date: new Date(item.transactionDate || item.transaction_date || item.date).toLocaleDateString(),
            operator: item.Creator?.username || item.creator?.username || item.operator || '系统',
            supplier: item.supplier || '',
            projectName: item.Project?.name || item.project?.name || '',
            recipient: item.recipient || '',
            remarks: item.notes || item.remarks || '',
            // 全局视图需要的额外字段
            productId: item.productId || item.product_id || item.productID,
            productName: item.Product?.name || item.product?.name || item.productName || item.product_name || '未知产品',
            productCode: item.Product?.code || item.product?.code || item.productCode || item.product_code || '-',
            unit: item.Product?.unit || item.product?.unit || item.unit || this.inventoryInfo?.unit || '个'
          }));
          
          this.pagination.totalCount = this.transactions.length;
          this.pagination.currentPage = 1;
          
          // 应用客户端产品编码模糊搜索
          if (this.pendingCodeFilter && !this.exactMatchOnly) {
            const filteredResults = this.transactions.filter(item => 
              item.productCode && 
              item.productCode.toLowerCase().includes(this.pendingCodeFilter.toLowerCase())
            );
            
            this.transactions = filteredResults;
            this.pagination.totalCount = filteredResults.length;
          }
        } else {
          this.transactions = [];
          this.pagination.totalCount = 0;
          console.error('返回数据格式不正确:', response.data);
          this.error = '返回数据格式不正确，请重试';
        }
      } catch (err) {
        console.error('获取库存记录失败', err);
        this.transactions = [];
        this.pagination.totalCount = 0;
        this.error = err.response?.data?.message || '获取库存记录失败，请重试';
      } finally {
        this.loading = false;
      }
    },

    changePage(page) {
      if (page < 1 || page > this.totalPages) return;
      this.currentPage = page;
      this.fetchTransactions();
    },

    applyFilters() {
      this.currentPage = 1; // 重置到第一页
      this.fetchTransactions();
    },

    // 客户端模糊搜索过滤方法（当API不支持模糊搜索时的备选方案）
    clientSideFilter(transactions) {
      // 如果没有过滤条件，直接返回原始数据
      if (!this.filters.materialCode && !this.filters.materialName && !this.filters.type && 
          !this.filters.dateStart && !this.filters.dateEnd) {
        return transactions;
      }
      
      return transactions.filter(record => {
        // 产品编码模糊搜索
        if (this.filters.materialCode && 
            (!record.productCode || !record.productCode.toLowerCase().includes(this.filters.materialCode.toLowerCase()))) {
          return false;
        }
        
        // 产品名称过滤
        if (this.filters.materialName && 
            (!record.productName || !record.productName.toLowerCase().includes(this.filters.materialName.toLowerCase()))) {
          return false;
        }
        
        // 交易类型过滤
        if (this.filters.type && record.type !== this.filters.type) {
          return false;
        }
        
        // 日期过滤
        if (this.filters.dateStart) {
          const startDate = new Date(this.filters.dateStart);
          const recordDate = new Date(record.date);
          if (recordDate < startDate) {
            return false;
          }
        }
        
        if (this.filters.dateEnd) {
          const endDate = new Date(this.filters.dateEnd);
          endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间
          const recordDate = new Date(record.date);
          if (recordDate > endDate) {
            return false;
          }
        }
        
        return true;
      });
    },

    resetFilters() {
      this.filters = {
        type: '',
        dateStart: '',
        dateEnd: '',
        materialName: '',
        materialCode: ''
      };
      this.pendingCodeFilter = null;
      this.currentPage = 1;
      this.fetchTransactions();
    },

    printTransactions() {
      this.showPrintPreview = true;
    },

    doPrint() {
      const printContents = document.getElementById('print-content').innerHTML;

      // 创建一个带有基本样式的打印页面
      const printPage = `
        <html>
          <head>
            <title>库存记录打印</title>
            <style>
              body { font-family: Arial, sans-serif; }
              table { width: 100%; border-collapse: collapse; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              th { background-color: #f2f2f2; }
              h2 { text-align: center; margin-bottom: 5px; }
              p { text-align: center; margin-top: 0; color: #666; }
              .footer { display: flex; justify-content: space-between; margin-top: 20px; font-size: 12px; color: #666; }
            </style>
          </head>
          <body>
            ${printContents}
          </body>
        </html>
      `;

      // 创建一个新窗口用于打印
      const printWindow = window.open('', '_blank');
      printWindow.document.open();
      printWindow.document.write(printPage);
      printWindow.document.close();

      // 打印后关闭窗口
      printWindow.onload = function() {
        printWindow.print();
        printWindow.onafterprint = function() {
          printWindow.close();
        };
      };
    },

    exportToExcel() {
      try {
        // 后端API限制每页最多100条数据，所以我们需要分页获取
        this.loading = true;
        const allTransactions = [];
        const params = {
          limit: 100, // 每页最大限制
          page: 1
        };

        // 处理筛选条件
        if (this.filters.type) {
          const typeMap = {
            '入库': 'in',
            '出库': 'out',
            '盘点': 'adjustment'
          };
          params.type = typeMap[this.filters.type];
        }

        if (this.filters.dateStart) {
          params.transactionDateFrom = this.filters.dateStart;
        }

        if (this.filters.dateEnd) {
          params.transactionDateTo = this.filters.dateEnd;
        }

        // 仅全局视图时添加产品名称和编码筛选
        if (!this.inventoryId) {
          if (this.filters.materialName) {
            params.productName = this.filters.materialName;
          }
          if (this.filters.materialCode) {
            // 由于API不允许模糊搜索参数，我们将使用客户端过滤
            this.pendingCodeFilter = this.filters.materialCode;
            
            // 除非需要精确匹配，否则我们不发送此参数到API
            if (this.exactMatchOnly) {
              params.productCode = this.filters.materialCode;
            }
          }
        }

        // 递归函数用于获取所有页面数据
        const fetchAllPages = (currentPage) => {
          params.page = currentPage;
          
          // 根据是否有inventoryId决定API路径
          const apiUrl = this.inventoryId 
            ? `/api/products/${this.inventoryId}/transactions` 
            : '/api/inventory/transactions';

          return axios.get(apiUrl, { params })
            .then(response => {
              // 调试信息 - 查看API返回结构
              console.log('Export - API Response format:', Object.keys(response.data));

              let items = [];
              if (response.data && Array.isArray(response.data.results)) {
                items = response.data.results;
              } else if (response.data && Array.isArray(response.data.data)) {
                items = response.data.data;
              } else if (Array.isArray(response.data)) {
                items = response.data;
              } else {
                throw new Error('返回数据格式不正确');
              }

              if (items.length > 0) {
                // 将当前页数据添加到收集数组
                const pageData = items.map(item => {
                  const data = {
                    code: item.reference || `TX-${item.id.slice(0, 8)}`,
                    type: item.type === 'in' ? '入库' : item.type === 'out' ? '出库' : '盘点',
                    quantity: item.quantity,
                    date: new Date(item.transactionDate || item.transaction_date || item.date).toLocaleDateString(),
                    operator: item.Creator?.username || item.creator?.username || item.operator || '系统',
                    supplier: item.supplier || '',
                    projectName: item.Project?.name || item.project?.name || '',
                    recipient: item.recipient || '',
                    remarks: item.notes || item.remarks || '',
                    productCode: item.Product?.code || item.product?.code || item.productCode || item.product_code || ''
                  };
                  
                  // 全局视图添加产品信息
                  if (!this.inventoryId) {
                    data.productName = item.Product?.name || item.product?.name || item.productName || item.product_name || '';
                    data.unit = item.Product?.unit || item.product?.unit || item.unit || '';
                  } else {
                    data.unit = this.inventoryInfo.unit;
                  }
                  
                  return data;
                });
                
                // 应用客户端产品编码模糊搜索
                let processedData = pageData;
                if (this.filters.materialCode && !this.exactMatchOnly) {
                  processedData = pageData.filter(item => 
                    item.productCode && 
                    item.productCode.toLowerCase().includes(this.filters.materialCode.toLowerCase())
                  );
                }
                
                allTransactions.push(...processedData);
                
                // 检查是否有更多页
                let totalResults = 0;
                if (response.data.totalResults) {
                  totalResults = response.data.totalResults;
                } else if (response.data.total) {
                  totalResults = response.data.total;
                } else if (response.data.count) {
                  totalResults = response.data.count;
                } else if (response.data.totalCount) {
                  totalResults = response.data.totalCount;
                } else {
                  totalResults = items.length;
                }
                
                const totalPages = Math.ceil(totalResults / params.limit);
                if (currentPage < totalPages) {
                  // 还有更多页，继续获取
                  return fetchAllPages(currentPage + 1);
                } else {
                  // 没有更多页了，生成Excel文件
                  this.generateExcelFile(allTransactions);
                  this.loading = false;
                  return Promise.resolve();
                }
              } else {
                // 数据格式不正确
                throw new Error('返回数据格式不正确');
              }
            });
        };

        // 开始获取第一页
        fetchAllPages(1)
          .catch(error => {
            console.error('获取库存记录失败', error);
            this.error = error.response?.data?.message || '获取库存记录失败，请重试';
            alert('导出失败，无法获取数据：' + (error.message || '未知错误'));
            this.loading = false;
          });
      } catch (err) {
        console.error('导出Excel失败', err);
        this.error = err.message || '导出失败，请重试';
        alert('导出失败，请重试：' + (err.message || '未知错误'));
        this.loading = false;
      }
    },

    // 生成Excel文件并下载
    generateExcelFile(data) {
      // 如果没有数据
      if (!data || data.length === 0) {
        alert('没有库存记录可供导出');
        return;
      }

      try {
        // 生成CSV内容
        let csvHeader = '';
        if (!this.inventoryId) {
          csvHeader = '单号,产品名称,产品编码,操作类型,数量,单位,日期,操作人,供应商,关联项目,领用人,备注\n';
        } else {
          csvHeader = '单号,操作类型,数量,日期,操作人,供应商,关联项目,领用人,备注\n';
        }
        
        let csvContent = csvHeader;
        
        data.forEach(item => {
          // 转义字段中的逗号，使用双引号包围
          const escapeCSV = (field) => {
            if (field === null || field === undefined) return '';
            const str = String(field);
            return str.includes(',') ? `"${str}"` : str;
          };
          
          let rowData = [];
          
          rowData.push(escapeCSV(item.code));
          
          // 全局视图添加产品信息
          if (!this.inventoryId) {
            rowData.push(escapeCSV(item.productName));
            rowData.push(escapeCSV(item.productCode));
          }
          
          rowData.push(escapeCSV(item.type));
          
          // 处理数量和单位
          if (!this.inventoryId) {
            rowData.push(escapeCSV(item.type === '入库' ? '+' + item.quantity : item.type === '出库' ? '-' + item.quantity : item.quantity));
            rowData.push(escapeCSV(item.unit || ''));
          } else {
            rowData.push(escapeCSV(item.type === '入库' ? '+' + item.quantity : item.type === '出库' ? '-' + item.quantity : item.quantity) + ' ' + this.inventoryInfo.unit);
          }
          
          rowData.push(escapeCSV(item.date));
          rowData.push(escapeCSV(item.operator));
          rowData.push(escapeCSV(item.supplier));
          rowData.push(escapeCSV(item.projectName));
          rowData.push(escapeCSV(item.recipient));
          rowData.push(escapeCSV(item.remarks));
          
          csvContent += rowData.join(',') + '\n';
        });

        // 创建Blob对象
        const blob = new Blob(['\uFEFF' + csvContent], { // 添加BOM以正确显示中文
          type: 'text/csv;charset=utf-8'
        });

        // 创建下载链接
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `库存记录_${this.inventoryInfo.code}_${new Date().toISOString().slice(0,10)}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);

        // 显示成功消息
        alert('导出成功！');
      } catch (error) {
        console.error('生成Excel文件失败', error);
        alert('生成导出文件失败：' + (error.message || '未知错误'));
      }
    }
  }
}
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md flex items-center text-sm font-medium;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700;
}
</style>
