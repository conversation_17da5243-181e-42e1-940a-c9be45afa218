const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const productValidation = require('../validations/product.validation');
const productTransactionValidation = require('../validations/productTransaction.validation');
const productController = require('../controllers/product.controller');
const productTransactionController = require('../controllers/productTransaction.controller');

const router = express.Router();

// Product routes
router
  .route('/')
  .post(auth('manageProducts'), validate(productValidation.createProduct), productController.createProduct)
  .get(auth('getProducts'), validate(productValidation.getProducts), productController.getProducts);

router
  .route('/:productId')
  .get(auth('getProducts'), validate(productValidation.getProduct), productController.getProduct)
  .patch(auth('manageProducts'), validate(productValidation.updateProduct), productController.updateProduct)
  .delete(auth('manageProducts'), validate(productValidation.deleteProduct), productController.deleteProduct);

// Product transaction routes for a specific product
router
  .route('/:productId/transactions')
  .get(
    auth('getProducts'),
    validate(productTransactionValidation.getProductTransactionsForProduct),
    productTransactionController.getProductTransactionsForProduct
  );

module.exports = router; 