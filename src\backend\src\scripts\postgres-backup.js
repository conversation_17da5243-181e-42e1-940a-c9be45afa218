/**
 * PostgreSQL Backup Script
 * 
 * This script provides backup and restore functionality for PostgreSQL:
 * - Create backups (using pg_dump)
 * - List backups
 * - Restore from a backup (using pg_restore)
 * - Delete backups
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);
const logger = require('../config/logger');
const { Client } = require('pg');
const moment = require('moment');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Validate environment
if (process.env.DB_DIALECT !== 'postgres') {
  console.error(`${colors.red}Error: This script is intended for PostgreSQL databases only.${colors.reset}`);
  process.exit(1);
}

// Backup directory
const BACKUP_DIR = path.join(__dirname, '../../backups/postgres');

// Ensure backup directory exists
if (!fs.existsSync(BACKUP_DIR)) {
  fs.mkdirSync(BACKUP_DIR, { recursive: true });
}

// Database connection config
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
};

// Tracking database for backup metadata
const TRACKING_DB = path.join(BACKUP_DIR, 'backups.sqlite');
const sqlite3 = require('better-sqlite3');

// Initialize tracking database
function initTrackingDB() {
  const db = new sqlite3(TRACKING_DB);
  
  // Create backups table if it doesn't exist
  db.exec(`
    CREATE TABLE IF NOT EXISTS backups (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      filename TEXT NOT NULL,
      description TEXT,
      createdAt TEXT NOT NULL,
      size INTEGER NOT NULL,
      db_version TEXT
    )
  `);
  
  return db;
}

// Create a backup
async function createBackup(description) {
  try {
    console.log(`${colors.blue}Creating PostgreSQL backup...${colors.reset}`);
    
    // Generate backup filename
    const timestamp = moment().format('YYYYMMDD_HHmmss');
    const filename = `backup_${process.env.DB_NAME}_${timestamp}.dump`;
    const filePath = path.join(BACKUP_DIR, filename);
    
    // Create pg_dump command with proper escaping
    const pgDumpCmd = [
      'pg_dump',
      `--host=${dbConfig.host}`,
      `--port=${dbConfig.port}`,
      `--username=${dbConfig.user}`,
      '--format=custom',
      '--compress=9',
      '--no-owner',
      '--no-acl',
      '--file=' + filePath,
      dbConfig.database
    ].join(' ');
    
    // Set PGPASSWORD environment variable for authentication
    const env = { ...process.env, PGPASSWORD: dbConfig.password };
    
    // Execute pg_dump
    console.log(`${colors.yellow}Running pg_dump...${colors.reset}`);
    await execAsync(pgDumpCmd, { env });
    
    // Get file size
    const stats = fs.statSync(filePath);
    const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
    
    // Get PostgreSQL version
    const client = new Client(dbConfig);
    await client.connect();
    const versionResult = await client.query('SELECT version()');
    const pgVersion = versionResult.rows[0].version;
    await client.end();
    
    // Register backup in tracking database
    const db = initTrackingDB();
    db.prepare(`
      INSERT INTO backups (filename, description, createdAt, size, db_version)
      VALUES (?, ?, ?, ?, ?)
    `).run(
      filename,
      description || 'Backup created on ' + moment().format('YYYY-MM-DD HH:mm:ss'),
      moment().format('YYYY-MM-DD HH:mm:ss'),
      stats.size,
      pgVersion
    );
    db.close();
    
    console.log(`${colors.green}Backup created successfully:${colors.reset} ${filename} (${fileSizeMB} MB)`);
    return { success: true, filename, size: fileSizeMB };
  } catch (error) {
    console.error(`${colors.red}Backup failed:${colors.reset}`, error.message);
    return { success: false, error: error.message };
  }
}

// List all backups
function listBackups() {
  try {
    const db = initTrackingDB();
    const backups = db.prepare('SELECT * FROM backups ORDER BY createdAt DESC').all();
    db.close();
    
    if (backups.length === 0) {
      console.log(`${colors.yellow}No backups found${colors.reset}`);
      return { success: true, backups: [] };
    }
    
    console.log(`${colors.blue}Available Backups:${colors.reset}`);
    console.log('─'.repeat(100));
    console.log(` ID  | Filename                         | Description                  | Created At          | Size       `);
    console.log('─'.repeat(100));
    
    backups.forEach(backup => {
      const fileSizeMB = (backup.size / (1024 * 1024)).toFixed(2);
      console.log(
        ` ${backup.id.toString().padEnd(3)} | ` +
        `${backup.filename.padEnd(33)} | ` +
        `${(backup.description || '').substring(0, 28).padEnd(28)} | ` +
        `${backup.createdAt.padEnd(19)} | ` +
        `${fileSizeMB} MB`
      );
    });
    
    console.log('─'.repeat(100));
    return { success: true, backups };
  } catch (error) {
    console.error(`${colors.red}Error listing backups:${colors.reset}`, error.message);
    return { success: false, error: error.message };
  }
}

// Restore from a backup
async function restoreBackup(backupId) {
  try {
    // Validate backup ID
    if (!backupId) {
      console.error(`${colors.red}Error: Backup ID is required${colors.reset}`);
      return { success: false, error: 'Backup ID is required' };
    }
    
    // Get backup details
    const db = initTrackingDB();
    const backup = db.prepare('SELECT * FROM backups WHERE id = ?').get(backupId);
    db.close();
    
    if (!backup) {
      console.error(`${colors.red}Error: Backup with ID ${backupId} not found${colors.reset}`);
      return { success: false, error: `Backup with ID ${backupId} not found` };
    }
    
    const backupPath = path.join(BACKUP_DIR, backup.filename);
    
    // Confirm backup file exists
    if (!fs.existsSync(backupPath)) {
      console.error(`${colors.red}Error: Backup file not found:${colors.reset} ${backupPath}`);
      return { success: false, error: 'Backup file not found' };
    }
    
    console.log(`${colors.yellow}Warning: This will overwrite the current database!${colors.reset}`);
    console.log(`${colors.blue}Restoring from backup: ${backup.filename}${colors.reset}`);
    
    // Create pg_restore command
    const pgRestoreCmd = [
      'pg_restore',
      `--host=${dbConfig.host}`,
      `--port=${dbConfig.port}`,
      `--username=${dbConfig.user}`,
      '--clean',
      '--if-exists',
      '--no-owner',
      '--no-acl',
      `--dbname=${dbConfig.database}`,
      backupPath
    ].join(' ');
    
    // Set PGPASSWORD environment variable for authentication
    const env = { ...process.env, PGPASSWORD: dbConfig.password };
    
    // Execute pg_restore
    console.log(`${colors.yellow}Running pg_restore...${colors.reset}`);
    await execAsync(pgRestoreCmd, { env });
    
    console.log(`${colors.green}Database restored successfully from backup: ${backup.filename}${colors.reset}`);
    return { success: true, backup };
  } catch (error) {
    console.error(`${colors.red}Restore failed:${colors.reset}`, error.message);
    return { success: false, error: error.message };
  }
}

// Delete a backup
function deleteBackup(backupId) {
  try {
    // Validate backup ID
    if (!backupId) {
      console.error(`${colors.red}Error: Backup ID is required${colors.reset}`);
      return { success: false, error: 'Backup ID is required' };
    }
    
    // Get backup details
    const db = initTrackingDB();
    const backup = db.prepare('SELECT * FROM backups WHERE id = ?').get(backupId);
    
    if (!backup) {
      db.close();
      console.error(`${colors.red}Error: Backup with ID ${backupId} not found${colors.reset}`);
      return { success: false, error: `Backup with ID ${backupId} not found` };
    }
    
    const backupPath = path.join(BACKUP_DIR, backup.filename);
    
    // Check if file exists before attempting to delete
    if (fs.existsSync(backupPath)) {
      // Delete the backup file
      fs.unlinkSync(backupPath);
    } else {
      console.log(`${colors.yellow}Warning: Backup file not found, only removing from tracking database${colors.reset}`);
    }
    
    // Remove from tracking database
    db.prepare('DELETE FROM backups WHERE id = ?').run(backupId);
    db.close();
    
    console.log(`${colors.green}Backup with ID ${backupId} deleted successfully${colors.reset}`);
    return { success: true };
  } catch (error) {
    console.error(`${colors.red}Error deleting backup:${colors.reset}`, error.message);
    return { success: false, error: error.message };
  }
}

// Main function to handle command line arguments
async function main() {
  const command = process.argv[2];
  const args = process.argv.slice(3);
  
  try {
    switch (command) {
      case 'backup':
        await createBackup(args[0]);
        break;
        
      case 'list':
        listBackups();
        break;
        
      case 'restore':
        await restoreBackup(args[0]);
        break;
        
      case 'delete':
        deleteBackup(args[0]);
        break;
        
      default:
        console.log(`
${colors.blue}PostgreSQL Backup Manager${colors.reset}

Usage:
  node postgres-backup.js <command> [args]

Commands:
  backup [description]    Create a new backup (optional description)
  list                    List all backups
  restore <id>            Restore from a backup
  delete <id>             Delete a backup
`);
    }
  } catch (error) {
    console.error(`${colors.red}Command failed with error:${colors.reset}`, error);
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error(`${colors.red}Unexpected error:${colors.reset}`, error);
  process.exit(1);
});
