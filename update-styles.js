const fs = require('fs');
const path = require('path');

// 读取todolist.md文件
const todolistPath = path.join(__dirname, 'todo.md');
const todolist = fs.readFileSync(todolistPath, 'utf8');

// 解析需要更新的文件列表
const filesToUpdate = todolist
  .split('\n')
  .filter(line => line.trim().startsWith('[ ]'))
  .map(line => line.replace('[ ] ', '').trim())
  .filter(file => file.length > 0);

console.log(`找到 ${filesToUpdate.length} 个需要更新的文件`);

// 样式替换规则
const styleReplacements = [
  // 页面容器
  {
    from: /class="max-w-6xl mx-auto px-4 py-6"/g,
    to: 'class="page-container"'
  },
  // 页面头部
  {
    from: /class="flex justify-between items-center mb-8"/g,
    to: 'class="page-header"'
  },
  // 页面标题
  {
    from: /class="text-2xl font-bold text-gray-900"/g,
    to: 'class="page-title"'
  },
  // 返回按钮
  {
    from: /class="text-blue-600 hover:text-blue-800 mr-3 transition-colors duration-200"/g,
    to: 'class="back-button"'
  },
  // 卡片
  {
    from: /class="(?:card )?bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 p-6(?:\s+mb-6)?"/g,
    to: 'class="card"'
  },
  // 卡片标题
  {
    from: /class="text-lg font-semibold text-gray-900 mb-5 flex items-center"/g,
    to: 'class="card-header"'
  },
  // 卡片图标
  {
    from: /class="w-5 h-5 mr-2 text-blue-500"/g,
    to: 'class="card-icon"'
  },
  // 错误提示
  {
    from: /class="mb-6 p-4 bg-red-50 text-red-700 rounded-lg border border-red-100 shadow-sm"/g,
    to: 'class="error-alert"'
  },
  // 加载容器
  {
    from: /class="text-center py-16"/g,
    to: 'class="loading-container"'
  },
  // 加载动画
  {
    from: /class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600"/g,
    to: 'class="loading-spinner"'
  },
  // 加载文本
  {
    from: /class="mt-4 text-gray-600 font-medium"/g,
    to: 'class="loading-text"'
  },
  // 表单标签
  {
    from: /class="block text-sm font-medium text-gray-700 mb-1"/g,
    to: 'class="form-label"'
  },
  // 表单必填标记
  {
    from: /class="text-red-500"/g,
    to: 'class="form-required"'
  },
  // 表单网格
  {
    from: /class="grid grid-cols-1 md:grid-cols-2 gap-5"/g,
    to: 'class="form-grid"'
  },
  // 表单组
  {
    from: /class="space-y-5"/g,
    to: 'class="form-group"'
  }
];

// 更新每个文件
filesToUpdate.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  
  // 检查文件是否存在
  if (!fs.existsSync(fullPath)) {
    console.error(`文件不存在: ${fullPath}`);
    return;
  }
  
  try {
    // 读取文件内容
    let content = fs.readFileSync(fullPath, 'utf8');
    let originalContent = content;
    
    // 应用所有替换规则
    styleReplacements.forEach(({ from, to }) => {
      content = content.replace(from, to);
    });
    
    // 如果内容有变化，则写入文件
    if (content !== originalContent) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`已更新: ${filePath}`);
    } else {
      console.log(`无需更新: ${filePath}`);
    }
  } catch (error) {
    console.error(`处理文件时出错 ${filePath}:`, error);
  }
});

console.log('样式统一更新完成！');
