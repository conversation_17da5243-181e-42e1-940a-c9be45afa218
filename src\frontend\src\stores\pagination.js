import { defineStore } from 'pinia'

export const usePaginationStore = defineStore('pagination', {
  state: () => ({
    currentPage: 1,
    pageSize: 5,
    total: 0
  }),
  getters: {
    getCurrentPage: (state) => state.currentPage,
    getPageSize: (state) => state.pageSize,
    getTotal: (state) => state.total
  },
  actions: {
    setCurrentPage(page) {
      this.currentPage = page
    },
    setPageSize(size) {
      this.pageSize = size
    },
    setTotal(total) {
      this.total = total
    },
    reset() {
      this.currentPage = 1
      this.pageSize = 5
      this.total = 0
    }
  }
}) 