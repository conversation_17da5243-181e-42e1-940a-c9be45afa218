import { defineStore } from 'pinia';
import { ref, computed } from 'vue';

/**
 * Notification store for managing application notifications
 * 
 * This store provides methods for adding, removing, and managing notifications
 * throughout the application.
 */
export const useNotificationStore = defineStore('notification', () => {
  // Array of active notifications
  const notifications = ref([]);
  
  // Computed property to get notifications in reverse order (newest first)
  const sortedNotifications = computed(() => {
    return [...notifications.value].reverse();
  });
  
  /**
   * Add a new notification
   * @param {Object} notification - The notification to add
   * @param {string} notification.type - The type of notification (success, error, info, warning)
   * @param {string} notification.message - The message to display
   * @param {number} notification.timeout - The time in milliseconds before auto-dismissing (default: 5000, 0 for no auto-dismiss)
   * @param {boolean} notification.dismissible - Whether the notification can be manually dismissed (default: true)
   * @returns {string} The ID of the added notification
   */
  function addNotification({ type = 'info', message, timeout = 5000, dismissible = true }) {
    // Validate notification type
    const validTypes = ['success', 'error', 'info', 'warning'];
    if (!validTypes.includes(type)) {
      console.warn(`Invalid notification type: ${type}. Using 'info' instead.`);
      type = 'info';
    }
    
    // Create a unique ID for the notification
    const id = Date.now().toString(36) + Math.random().toString(36).substr(2, 5);
    
    // Add the notification to the array
    notifications.value.push({
      id,
      type,
      message,
      dismissible,
      timestamp: Date.now()
    });
    
    // Set up auto-dismiss if timeout is greater than 0
    if (timeout > 0) {
      setTimeout(() => {
        removeNotification(id);
      }, timeout);
    }
    
    return id;
  }
  
  /**
   * Add a success notification
   * @param {string} message - The message to display
   * @param {number} timeout - The time in milliseconds before auto-dismissing (default: 5000)
   * @returns {string} The ID of the added notification
   */
  function addSuccess(message, timeout = 5000) {
    return addNotification({ type: 'success', message, timeout });
  }
  
  /**
   * Add an error notification
   * @param {string} message - The message to display
   * @param {number} timeout - The time in milliseconds before auto-dismissing (default: 8000)
   * @returns {string} The ID of the added notification
   */
  function addError(message, timeout = 8000) {
    return addNotification({ type: 'error', message, timeout });
  }
  
  /**
   * Add an info notification
   * @param {string} message - The message to display
   * @param {number} timeout - The time in milliseconds before auto-dismissing (default: 5000)
   * @returns {string} The ID of the added notification
   */
  function addInfo(message, timeout = 5000) {
    return addNotification({ type: 'info', message, timeout });
  }
  
  /**
   * Add a warning notification
   * @param {string} message - The message to display
   * @param {number} timeout - The time in milliseconds before auto-dismissing (default: 7000)
   * @returns {string} The ID of the added notification
   */
  function addWarning(message, timeout = 7000) {
    return addNotification({ type: 'warning', message, timeout });
  }
  
  /**
   * Remove a notification by ID
   * @param {string} id - The ID of the notification to remove
   */
  function removeNotification(id) {
    const index = notifications.value.findIndex(notification => notification.id === id);
    if (index !== -1) {
      notifications.value.splice(index, 1);
    }
  }
  
  /**
   * Clear all notifications
   */
  function clearAll() {
    notifications.value = [];
  }
  
  return {
    notifications,
    sortedNotifications,
    addNotification,
    addSuccess,
    addError,
    addInfo,
    addWarning,
    removeNotification,
    clearAll
  };
});
