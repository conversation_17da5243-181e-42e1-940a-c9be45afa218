// @ts-check
import { test, expect } from '@playwright/test';

test('基本页面加载测试', async ({ page }) => {
  // 访问首页
  await page.goto('/');

  // 验证页面标题
  const title = await page.title();
  expect(title).toBeTruthy();

  // 验证页面内容
  const content = await page.textContent('body');
  expect(content).toBeTruthy();
});

test('导航测试', async ({ page }) => {
  // 访问首页
  await page.goto('/');

  // 等待页面加载
  await page.waitForLoadState('networkidle');

  // 检查页面是否成功加载
  expect(await page.isVisible('body')).toBeTruthy();
});
