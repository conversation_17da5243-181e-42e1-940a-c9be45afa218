/* Import component styles */
@import './assets/css/components.css';
@import './assets/css/dark-mode.css';

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Theme variables */
:root {
  /* Default theme (light) */
  --primary-color: #4f46e5; /* indigo-600 */
  --primary-color-rgb: 79, 70, 229; /* indigo-600 RGB values */
  --primary-hover: #4338ca; /* indigo-700 */
  --primary-focus: #6366f1; /* indigo-500 */
  --secondary-color: #ffffff; /* white */
  --secondary-hover: #f9fafb; /* gray-50 */
  --secondary-border: #d1d5db; /* gray-300 */
  --text-primary: #111827; /* gray-900 */
  --text-secondary: #4b5563; /* gray-700 */
  --bg-primary: #f9fafb; /* gray-50 */
  --bg-secondary: #ffffff; /* white */
  --danger-color: #dc2626; /* red-600 */
  --danger-hover: #b91c1c; /* red-700 */
  --danger-focus: #ef4444; /* red-500 */

  /* Status colors */
  --status-planning-bg: #dbeafe; /* blue-100 */
  --status-planning-text: #1e40af; /* blue-800 */
  --status-progress-bg: #dcfce7; /* green-100 */
  --status-progress-text: #166534; /* green-800 */
  --status-completed-bg: #f3e8ff; /* purple-100 */
  --status-completed-text: #6b21a8; /* purple-800 */
  --status-hold-bg: #fef9c3; /* yellow-100 */
  --status-hold-text: #854d0e; /* yellow-800 */
  --status-cancelled-bg: #fee2e2; /* red-100 */
  --status-cancelled-text: #991b1b; /* red-800 */
}

/* Dark theme */
[data-theme="dark"] {
  --primary-color: #6366f1; /* indigo-500 */
  --primary-color-rgb: 99, 102, 241; /* indigo-500 RGB values */
  --primary-hover: #4f46e5; /* indigo-600 */
  --primary-focus: #818cf8; /* indigo-400 */
  --secondary-color: #1f2937; /* gray-800 */
  --secondary-hover: #374151; /* gray-700 */
  --secondary-border: #4b5563; /* gray-600 */
  --text-primary: #f9fafb; /* gray-50 */
  --text-secondary: #e5e7eb; /* gray-200 */
  --bg-primary: #111827; /* gray-900 */
  --bg-secondary: #1f2937; /* gray-800 */
  --danger-color: #ef4444; /* red-500 */
  --danger-hover: #dc2626; /* red-600 */
  --danger-focus: #f87171; /* red-400 */

  /* Status colors in dark mode */
  --status-planning-bg: #1e3a8a; /* blue-900 */
  --status-planning-text: #bfdbfe; /* blue-200 */
  --status-progress-bg: #14532d; /* green-900 */
  --status-progress-text: #bbf7d0; /* green-200 */
  --status-completed-bg: #581c87; /* purple-900 */
  --status-completed-text: #e9d5ff; /* purple-200 */
  --status-hold-bg: #713f12; /* yellow-900 */
  --status-hold-text: #fef08a; /* yellow-200 */
  --status-cancelled-bg: #7f1d1d; /* red-900 */
  --status-cancelled-text: #fecaca; /* red-200 */
}

/* Blue theme */
[data-theme="blue"] {
  --primary-color: #2563eb; /* blue-600 */
  --primary-color-rgb: 37, 99, 235; /* blue-600 RGB values */
  --primary-hover: #1d4ed8; /* blue-700 */
  --primary-focus: #3b82f6; /* blue-500 */
  --secondary-color: #ffffff; /* white */
  --secondary-hover: #f0f9ff; /* blue-50 */
  --secondary-border: #bfdbfe; /* blue-200 */
  --text-primary: #1e3a8a; /* blue-900 */
  --text-secondary: #1e40af; /* blue-800 */
  --bg-primary: #eff6ff; /* blue-50 */
  --bg-secondary: #ffffff; /* white */
  --danger-color: #dc2626; /* red-600 */
  --danger-hover: #b91c1c; /* red-700 */
  --danger-focus: #ef4444; /* red-500 */
}

/* Green theme */
[data-theme="green"] {
  --primary-color: #059669; /* emerald-600 */
  --primary-color-rgb: 5, 150, 105; /* emerald-600 RGB values */
  --primary-hover: #047857; /* emerald-700 */
  --primary-focus: #10b981; /* emerald-500 */
  --secondary-color: #ffffff; /* white */
  --secondary-hover: #ecfdf5; /* emerald-50 */
  --secondary-border: #a7f3d0; /* emerald-200 */
  --text-primary: #064e3b; /* emerald-900 */
  --text-secondary: #065f46; /* emerald-800 */
  --bg-primary: #ecfdf5; /* emerald-50 */
  --bg-secondary: #ffffff; /* white */
  --danger-color: #dc2626; /* red-600 */
  --danger-hover: #b91c1c; /* red-700 */
  --danger-focus: #ef4444; /* red-500 */
}

/* Base styles */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}

#app {
  height: 100%;
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

/* Custom utility classes */
.btn {
  @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
  @apply shadow-sm;
}
.btn-primary:hover {
  background-color: var(--primary-hover);
  @apply shadow;
}
.btn-primary:focus {
  --tw-ring-color: var(--primary-focus);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--text-secondary);
  border: 1px solid var(--secondary-border);
  @apply shadow-sm;
}
.btn-secondary:hover {
  background-color: var(--secondary-hover);
  @apply shadow;
}
.btn-secondary:focus {
  --tw-ring-color: var(--primary-focus);
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
  @apply shadow-sm;
}
.btn-danger:hover {
  background-color: var(--danger-hover);
  @apply shadow;
}
.btn-danger:focus {
  --tw-ring-color: var(--danger-focus);
}

/* Form element styling */
.form-input {
  @apply mt-1 block w-full rounded-md shadow-sm transition-all duration-200;
  border-color: var(--secondary-border);
}
.form-input:focus {
  border-color: var(--primary-focus);
  --tw-ring-color: var(--primary-focus);
  @apply ring;
}

.form-label {
  @apply block text-sm font-medium mb-1;
  color: var(--text-secondary);
}

/* Card styling */
.card {
  background-color: var(--bg-secondary);
  @apply overflow-hidden shadow rounded-lg border border-gray-100;
}

.card-header {
  @apply px-5 py-5 sm:px-6;
  border-bottom: 1px solid var(--secondary-border);
}

.card-body {
  @apply px-5 py-5 sm:p-6;
}

.card-footer {
  @apply px-5 py-4 sm:px-6;
  border-top: 1px solid var(--secondary-border);
}

/* Badge styling */
.badge {
  @apply inline-flex items-center justify-center text-xs font-medium py-1 px-2.5 rounded-full;
}

.badge-blue {
  background-color: var(--status-planning-bg);
  color: var(--status-planning-text);
}

.badge-green {
  background-color: var(--status-progress-bg);
  color: var(--status-progress-text);
}

.badge-purple {
  background-color: var(--status-completed-bg);
  color: var(--status-completed-text);
}

.badge-yellow {
  background-color: var(--status-hold-bg);
  color: var(--status-hold-text);
}

.badge-red {
  background-color: var(--status-cancelled-bg);
  color: var(--status-cancelled-text);
}

.badge-gray {
  @apply bg-gray-100 text-gray-800;
}

/* Page header styling */
.page-header {
  @apply flex justify-between items-center mb-8 bg-white shadow-sm rounded-lg p-6;
}

.page-title {
  @apply text-2xl font-bold text-gray-900;
}

.page-subtitle {
  @apply text-gray-500 mt-1;
}

/* Search and filter card styling */
.filter-card {
  @apply overflow-hidden shadow rounded-lg border border-gray-100 mb-8 p-6 shadow-md;
}

/* Lists and grids */
.item-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.item-list {
  @apply divide-y divide-gray-200;
}

/* Progress bars */
.progress-container {
  @apply w-full bg-gray-100 rounded-full h-2.5;
}

.progress-bar {
  @apply h-2.5 rounded-full;
}

.progress-red {
  @apply bg-red-500;
}

.progress-yellow {
  @apply bg-yellow-500;
}

.progress-blue {
  @apply bg-blue-500;
}

.progress-green {
  @apply bg-green-500;
}