const express = require('express');
const auth = require('../middlewares/auth');
const dashboardController = require('../controllers/dashboard.controller');

const router = express.Router();

/**
 * @swagger
 * /dashboard/stats:
 *   get:
 *     summary: Get comprehensive dashboard statistics
 *     description: Get all dashboard statistics including projects, suppliers, purchases, and inventory
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 projects:
 *                   type: object
 *                 suppliers:
 *                   type: object
 *                 purchases:
 *                   type: object
 *                 inventory:
 *                   type: object
 *                 lastUpdated:
 *                   type: string
 *                   format: date-time
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 */
router
  .route('/stats')
  .get(auth('getDashboard'), dashboardController.getDashboardStats);

/**
 * @swagger
 * /dashboard/projects:
 *   get:
 *     summary: Get project statistics for dashboard
 *     description: Get project statistics including active, completed, and delayed projects
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 active:
 *                   type: integer
 *                 completed:
 *                   type: integer
 *                 delayed:
 *                   type: integer
 *                 total:
 *                   type: integer
 *                 trend:
 *                   type: integer
 *                 lastUpdated:
 *                   type: string
 *                   format: date-time
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 */
router
  .route('/projects')
  .get(auth('getProjects'), dashboardController.getProjectStats);

/**
 * @swagger
 * /dashboard/suppliers:
 *   get:
 *     summary: Get supplier statistics for dashboard
 *     description: Get supplier statistics including total, active, and new suppliers
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total:
 *                   type: integer
 *                 active:
 *                   type: integer
 *                 new:
 *                   type: integer
 *                 trend:
 *                   type: integer
 *                 lastUpdated:
 *                   type: string
 *                   format: date-time
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 */
router
  .route('/suppliers')
  .get(auth('getSuppliers'), dashboardController.getSupplierStats);

/**
 * @swagger
 * /dashboard/purchases:
 *   get:
 *     summary: Get purchase statistics for dashboard
 *     description: Get purchase statistics including monthly, yearly amounts and pending purchases
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 monthly:
 *                   type: number
 *                 yearly:
 *                   type: number
 *                 pending:
 *                   type: integer
 *                 trend:
 *                   type: integer
 *                 lastUpdated:
 *                   type: string
 *                   format: date-time
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 */
router
  .route('/purchases')
  .get(auth('getPurchases'), dashboardController.getPurchaseStats);

/**
 * @swagger
 * /dashboard/inventory:
 *   get:
 *     summary: Get inventory statistics for dashboard
 *     description: Get inventory statistics including total items, low stock items, and total value
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 items:
 *                   type: integer
 *                 lowStock:
 *                   type: integer
 *                 totalValue:
 *                   type: number
 *                 trend:
 *                   type: integer
 *                 lastUpdated:
 *                   type: string
 *                   format: date-time
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 */
router
  .route('/inventory')
  .get(auth('getInventory'), dashboardController.getInventoryStats);

/**
 * @swagger
 * /dashboard/recent-projects:
 *   get:
 *     summary: Get recent projects for dashboard
 *     description: Get a list of recently updated projects
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 20
 *           default: 5
 *         description: Number of projects to return
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   code:
 *                     type: string
 *                   client:
 *                     type: string
 *                   status:
 *                     type: string
 *                   progress:
 *                     type: integer
 *                   deadline:
 *                     type: string
 *                     format: date-time
 *                   manager:
 *                     type: string
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 */
router
  .route('/recent-projects')
  .get(auth('getProjects'), dashboardController.getRecentProjects);

/**
 * @swagger
 * /dashboard/recent-documents:
 *   get:
 *     summary: Get recent documents for dashboard
 *     description: Get a list of recently updated documents
 *     tags: [Dashboard]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 20
 *           default: 5
 *         description: Number of documents to return
 *     responses:
 *       "200":
 *         description: OK
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   title:
 *                     type: string
 *                   category:
 *                     type: string
 *                   excerpt:
 *                     type: string
 *                   updatedAt:
 *                     type: string
 *                     format: date-time
 *                   author:
 *                     type: string
 *       "401":
 *         description: Unauthorized
 *       "403":
 *         description: Forbidden
 */
router
  .route('/recent-documents')
  .get(auth('getDocuments'), dashboardController.getRecentDocuments);

module.exports = router; 