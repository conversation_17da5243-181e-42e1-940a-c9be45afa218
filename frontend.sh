#!/bin/bash

echo "Starting Frontend..."

# 设置控制台标题
echo -en "\033]0;Frontend Starter\007"

# 定义颜色
FRONTEND_COLOR="\033[32m"
RESET_COLOR="\033[0m"

# 检查是否有 status 参数
if [ "$1" = "status" ]; then
    echo -e "${FRONTEND_COLOR}Checking Frontend status...${RESET_COLOR}"
    # 这里添加检查前端状态的命令，例如：
    # curl http://localhost:5173/health
    # 如果上面的命令返回成功，则显示状态
    echo -e "${FRONTEND_COLOR}Frontend is running${RESET_COLOR}"
    exit 0
fi

# 获取当前目录路径
CURRENT_DIR=$(pwd)

# 确定要运行的命令
if [[ "$CURRENT_DIR" == *"-windsurf"* ]]; then
    NPM_COMMAND="npm run dev:windsurf"
    echo -e "${FRONTEND_COLOR}Detected -windsurf in path, using: $NPM_COMMAND${RESET_COLOR}"
elif [[ "$CURRENT_DIR" == *"-cursor"* ]]; then
    NPM_COMMAND="npm run dev:cursor"
    echo -e "${FRONTEND_COLOR}Detected -cursor in path, using: $NPM_COMMAND${RESET_COLOR}"
elif [[ "$CURRENT_DIR" == *"-vscode"* ]]; then
    NPM_COMMAND="npm run dev:vscode"
    echo -e "${FRONTEND_COLOR}Detected -vscode in path, using: $NPM_COMMAND${RESET_COLOR}"
elif [[ "$CURRENT_DIR" == *"-insider"* ]]; then
    NPM_COMMAND="npm run dev:insider"
    echo -e "${FRONTEND_COLOR}Detected -insider in path, using: $NPM_COMMAND${RESET_COLOR}"
elif [[ "$CURRENT_DIR" == *"-trae"* ]]; then
    NPM_COMMAND="npm run dev:trae"
    echo -e "${FRONTEND_COLOR}Detected -trae in path, using: $NPM_COMMAND${RESET_COLOR}"
else
    NPM_COMMAND="npm run dev"
    echo -e "${FRONTEND_COLOR}No special path detected, using default: $NPM_COMMAND${RESET_COLOR}"
fi

# 启动前端
echo -e "${FRONTEND_COLOR}Starting Frontend...${RESET_COLOR}"
cd src/frontend && echo '=== FRONTEND ===' && $NPM_COMMAND

echo -e "Frontend services are starting in separate windows."
echo -e "${FRONTEND_COLOR}Frontend window has green text${RESET_COLOR}"
echo

echo "Press any key to exit this window..."
read -n 1
