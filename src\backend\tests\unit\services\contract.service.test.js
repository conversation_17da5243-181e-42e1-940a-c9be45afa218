const { expect } = require('chai');
const sinon = require('sinon');
const { Contract, Project, User } = require('../../../src/models');
const contractService = require('../../../src/services/contract.service');
const ApiError = require('../../../src/utils/ApiError');

describe('Contract Service', () => {
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('createContract', () => {
    it('should create a contract successfully', async () => {
      // Arrange
      const contractData = {
        contractNumber: 'CT-001',
        name: 'Test Contract',
        projectId: '1',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        status: 'active',
        contractType: 'full-time',
        createdBy: '00000000-0000-0000-0000-000000000000'
      };

      sandbox.stub(Project, 'findByPk').resolves({ id: '1', name: 'Test Project' });
      sandbox.stub(Contract, 'findOne').resolves(null);
      const createStub = sandbox.stub(Contract, 'create').resolves(contractData);

      // Act
      const result = await contractService.createContract(contractData);

      // Assert
      expect(createStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(contractData);
    });

    it('should throw error if project not found', async () => {
      // Arrange
      const contractData = {
        contractNumber: 'CT-001',
        name: 'Test Contract',
        projectId: '999',
        startDate: '2023-01-01',
        endDate: '2023-12-31'
      };

      sandbox.stub(Project, 'findByPk').resolves(null);

      // Act & Assert
      try {
        await contractService.createContract(contractData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('Project not found');
      }
    });

    it('should throw error if contract number already exists', async () => {
      // Arrange
      const contractData = {
        contractNumber: 'CT-001',
        name: 'Test Contract',
        projectId: '1',
        startDate: '2023-01-01',
        endDate: '2023-12-31'
      };

      sandbox.stub(Project, 'findByPk').resolves({ id: '1', name: 'Test Project' });
      sandbox.stub(Contract, 'findOne').resolves({ id: '1', contractNumber: 'CT-001' });

      // Act & Assert
      try {
        await contractService.createContract(contractData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('Contract number already exists');
      }
    });
  });

  describe('getContractById', () => {
    it('should return contract if found', async () => {
      // Arrange
      const contractId = '1';
      const contractData = {
        id: contractId,
        contractNumber: 'CT-001',
        name: 'Test Contract',
        projectId: '1',
        startDate: '2023-01-01',
        endDate: '2023-12-31'
      };

      sandbox.stub(Contract, 'findByPk').resolves(contractData);

      // Act
      const result = await contractService.getContractById(contractId);

      // Assert
      expect(result).to.deep.equal(contractData);
    });

    it('should throw error if contract not found', async () => {
      // Arrange
      const contractId = '999';
      sandbox.stub(Contract, 'findByPk').resolves(null);

      // Act & Assert
      try {
        await contractService.getContractById(contractId);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(404);
        expect(error.message).to.include('Contract not found');
      }
    });
  });

  describe('updateContractById', () => {
    it('should update contract successfully', async () => {
      // Arrange
      const contractId = '1';
      const contractData = {
        id: contractId,
        contractNumber: 'CT-001',
        name: 'Test Contract',
        projectId: '1',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        save: sandbox.stub().resolves()
      };

      const updateData = {
        name: 'Updated Contract',
        endDate: '2024-12-31'
      };

      sandbox.stub(contractService, 'getContractById').resolves(contractData);

      // Act
      const result = await contractService.updateContractById(contractId, updateData);

      // Assert
      expect(result.name).to.equal(updateData.name);
      expect(result.endDate).to.equal(updateData.endDate);
      expect(contractData.save.calledOnce).to.be.true;
    });

    it('should throw error if updating to existing contract number', async () => {
      // Arrange
      const contractId = '1';
      const contractData = {
        id: contractId,
        contractNumber: 'CT-001',
        name: 'Test Contract'
      };

      const updateData = {
        contractNumber: 'CT-002'
      };

      sandbox.stub(contractService, 'getContractById').resolves(contractData);
      sandbox.stub(Contract, 'findOne').resolves({ id: '2', contractNumber: 'CT-002' });

      // Act & Assert
      try {
        await contractService.updateContractById(contractId, updateData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('Contract number already exists');
      }
    });
  });

  describe('deleteContractById', () => {
    it('should delete contract successfully', async () => {
      // Arrange
      const contractId = '1';
      const contractData = {
        id: contractId,
        name: 'Test Contract',
        destroy: sandbox.stub().resolves()
      };

      sandbox.stub(contractService, 'getContractById').resolves(contractData);

      // Act
      await contractService.deleteContractById(contractId);

      // Assert
      expect(contractData.destroy.calledOnce).to.be.true;
    });
  });

  describe('queryContracts', () => {
    it('should return contracts and pagination info', async () => {
      // Arrange
      const filter = { projectId: '1', status: 'active' };
      const options = { 
        page: 1, 
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };
      
      const contracts = [
        { id: '1', name: 'Contract 1', contractNumber: 'CT-001' },
        { id: '2', name: 'Contract 2', contractNumber: 'CT-002' }
      ];
      
      const findAndCountAllStub = sandbox.stub(Contract, 'findAndCountAll').resolves({
        count: 2,
        rows: contracts
      });

      // Act
      const result = await contractService.queryContracts(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      expect(result.results).to.deep.equal(contracts);
      expect(result.pagination.totalResults).to.equal(2);
      
      // Verify filter was applied correctly
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause.projectId).to.equal('1');
      expect(whereClause.status).to.equal('active');
    });

    it('should handle search filter correctly', async () => {
      // Arrange
      const filter = { search: 'test' };
      const options = { 
        page: 1, 
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };
      
      const findAndCountAllStub = sandbox.stub(Contract, 'findAndCountAll').resolves({
        count: 0,
        rows: []
      });

      // Act
      await contractService.queryContracts(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      // Verify that the where clause includes the search condition
      // This is a simplified check, the actual implementation might be more complex
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause[Symbol.for('sequelize.Op.or')]).to.exist;
    });

    it('should handle date range filters correctly', async () => {
      // Arrange
      const filter = { 
        startDateFrom: '2023-01-01',
        startDateTo: '2023-12-31',
        endDateFrom: '2024-01-01',
        endDateTo: '2024-12-31'
      };
      const options = { 
        page: 1, 
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };
      
      const findAndCountAllStub = sandbox.stub(Contract, 'findAndCountAll').resolves({
        count: 0,
        rows: []
      });

      // Act
      await contractService.queryContracts(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      // Verify that the where clause includes the date range conditions
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause.startDate).to.exist;
      expect(whereClause.endDate).to.exist;
    });
  });
});
