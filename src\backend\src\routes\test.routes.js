const express = require('express');
const testAuthRoutes = require('./test-auth.routes');

const router = express.Router();

// Mount test auth routes
router.use('/auth', testAuthRoutes);

/**
 * @route GET /api/test
 * @description Test route that doesn't require authentication
 * @access Public
 */
router.get('/', (req, res) => {
  res.json({
    message: 'Test route is working!',
    timestamp: new Date().toISOString()
  });
});

/**
 * @route GET /api/test/user
 * @description Test route that returns a mock user
 * @access Public
 */
router.get('/user', (req, res) => {
  res.json({
    user: {
      id: 'test-user-id',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user'
    },
    timestamp: new Date().toISOString()
  });
});

module.exports = router;
