const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { supplierService } = require('../services');
const ApiError = require('../utils/ApiError');
const pick = require('../utils/pick');

/**
 * Create a new supplier
 * @route POST /api/suppliers
 */
const createSupplier = catchAsync(async (req, res) => {
  // Add the current user's ID as the creator
  const supplierData = { ...req.body, createdBy: req.user.id };
  const supplier = await supplierService.createSupplier(supplierData);
  res.status(201).send(supplier);
});

/**
 * Get a supplier by ID
 * @route GET /api/suppliers/:supplierId
 */
const getSupplier = catchAsync(async (req, res) => {
  const supplier = await supplierService.getSupplierById(req.params.supplierId);
  if (!supplier) {
    throw new ApiError(404, 'Supplier not found');
  }
  res.send(supplier);
});

/**
 * Update a supplier
 * @route PATCH /api/suppliers/:supplierId
 */
const updateSupplier = catchAsync(async (req, res) => {
  const supplier = await supplierService.updateSupplierById(req.params.supplierId, req.body);
  res.send(supplier);
});

/**
 * Delete a supplier
 * @route DELETE /api/suppliers/:supplierId
 */
const deleteSupplier = catchAsync(async (req, res) => {
  await supplierService.deleteSupplierById(req.params.supplierId);
  // 使用明确的数字状态码，避免可能的undefined问题
  res.status(204).send();
});

/**
 * Get all suppliers with filtering
 * @route GET /api/suppliers
 */
const getSuppliers = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['category', 'rating', 'search', 'tag']);
  const options = pick(req.query, ['sortBy', 'sortOrder', 'page', 'limit']);

  // Parse numeric values
  if (options.page) options.page = parseInt(options.page, 10);
  if (options.limit) options.limit = parseInt(options.limit, 10);
  if (filter.rating) filter.rating = parseFloat(filter.rating);

  // Parse tags if they exist
  if (filter.tags) {
    filter.tags = Array.isArray(filter.tags) ? filter.tags : [filter.tags];
  }

  // For public endpoints, req.user might be undefined
  const result = await supplierService.querySuppliers(filter, options, req.user);
  res.send(result);
});

module.exports = {
  createSupplier,
  getSupplier,
  updateSupplier,
  deleteSupplier,
  getSuppliers
};