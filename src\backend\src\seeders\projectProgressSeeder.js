const { ProjectProgress, Project, User } = require('../models');
const { faker } = require('@faker-js/faker/locale/zh_CN');

const progressTypes = ['设计进度', '开发进度', '测试进度', '验收进度'];
const progressStatuses = ['正常', '延期', '提前', '暂停'];
const stages = ['需求分析', '设计阶段', '开发阶段', '测试阶段', '部署阶段', '维护阶段'];

async function seedProjectProgress() {
  try {
    // Clear existing data
    await ProjectProgress.destroy({ where: {} });

    // Get all projects and a default user for reference
    const projects = await Project.findAll();
    const defaultUser = await User.findOne({ where: { role: 'admin' } });

    if (!projects.length || !defaultUser) {
      console.log('Please seed projects and users first');
      return;
    }

    const progressRecords = [];

    // Create progress records for each project
    for (const project of projects) {
      // Create 4-8 progress records per project
      const recordCount = faker.number.int({ min: 4, max: 8 });
      
      for (let i = 0; i < recordCount; i++) {
        const reportDate = faker.date.between({
          from: project.startDate || '2023-01-01',
          to: project.endDate || '2024-12-31'
        });

        const progress = {
          projectId: project.id,
          stage: faker.helpers.arrayElement(stages),
          type: faker.helpers.arrayElement(progressTypes),
          status: faker.helpers.arrayElement(progressStatuses),
          completionPercentage: faker.number.int({ min: 0, max: 100 }),
          description: faker.lorem.paragraph(),
          challenges: faker.lorem.sentences(2),
          nextSteps: faker.lorem.sentences(2),
          reportDate: reportDate,
          attachments: [],
          notes: faker.lorem.sentence(),
          updatedBy: defaultUser.id,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        progressRecords.push(progress);
      }
    }

    await ProjectProgress.bulkCreate(progressRecords);
    console.log('Project progress seeder executed successfully');
  } catch (error) {
    console.error('Error seeding project progress:', error);
  }
}

module.exports = seedProjectProgress; 