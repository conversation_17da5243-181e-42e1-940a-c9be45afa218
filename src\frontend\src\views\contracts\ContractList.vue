<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">合同管理</h1>
        <p class="page-subtitle">管理和跟踪公司全部合同信息</p>
      </div>
      <div>
        <router-link to="/contracts/create" class="btn btn-primary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          新建合同
        </router-link>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-card">
      <div class="flex justify-between gap-4">
        <div class="flex-1">
          <label for="contract-search" class="form-label font-medium">合同搜索</label>
          <div class="relative">
            <input
              id="contract-search"
              v-model="searchQuery"
              type="text"
              placeholder="搜索合同号或名称"
              class="form-input pl-10 py-2 mt-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
              @input="debouncedFetchContracts"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <label for="contract-type" class="form-label font-medium">合同类型</label>
          <select id="contract-type" v-model="typeFilter" class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200" @change="fetchContracts">
            <option value="">全部类型</option>
            <option value="sales">销售合同</option>
            <option value="purchase">采购合同</option>
            <option value="service">服务合同</option>
            <option value="labor">劳务合同</option>
            <option value="lease">租赁合同</option>
          </select>
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button @click="resetFilters" class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200">重置</button>
        <button @click="fetchContracts" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200">查询</button>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6 p-4 bg-red-100 text-red-700 rounded-lg border border-red-100 shadow-sm">
      <div class="flex">
        <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        {{ error }}
      </div>
      <button @click="fetchContracts" class="mt-2 text-red-700 hover:text-red-900 font-medium underline">
        重试
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="card p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">加载中...</p>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading && contracts.length === 0" class="card p-8 text-center">
      <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无合同记录</h3>
      <p class="text-gray-500 mb-4">
        {{ searchQuery || typeFilter || statusFilter ? '没有找到符合条件的合同记录，请尝试其他搜索条件' : '点击新建合同按钮创建您的第一份合同' }}
      </p>
      <router-link v-if="!searchQuery && !typeFilter && !statusFilter" to="/contracts/create" class="btn btn-primary inline-flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        新建合同
      </router-link>
    </div>

    <!-- 表格区域 -->
    <div v-else class="card overflow-hidden shadow-md">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合同编号</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合同名称</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合同类型</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">签订日期</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合同金额</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="contract in contracts" :key="contract.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{{ contract.contractNumber }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ contract.name }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="badge" :class="{
                  'badge-blue': contract.contractType === 'sales',
                  'badge-indigo': contract.contractType === 'purchase',
                  'badge-purple': contract.contractType === 'service',
                  'badge-green': contract.contractType === 'labor',
                  'badge-yellow': contract.contractType === 'lease'
                }">
                  {{ getContractTypeText(contract.contractType) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDate(contract.startDate) }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <span v-if="contract.amount !== null && contract.amount !== undefined && contract.amount !== '' && !isNaN(Number(contract.amount))">
                  ¥{{ formatCurrency(Number(contract.amount)) }}
                </span>
                <span v-else>-</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="badge" :class="{
                  'badge-gray': contract.status === 'draft',
                  'badge-blue': contract.status === 'reviewing',
                  'badge-success': contract.status === 'active',
                  'badge-warning': contract.status === 'expired',
                  'badge-danger': contract.status === 'terminated'
                }">
                  {{ getContractStatusText(contract.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">                <router-link
                  :to="`/contracts/edit/${contract.id}`"
                  class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                >
                  <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                  </svg>
                  <span class="font-medium">编辑</span>
                </router-link>
                <a
                  @click.prevent="deleteContract(contract.id)"
                  class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group cursor-pointer"
                >
                  <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                  </svg>
                  <span class="font-medium">删除</span>
                </a>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="mt-4 flex justify-between items-center px-6 py-4 border-t border-gray-200">
        <div class="text-sm text-gray-500">
          显示第 <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> 至 <span class="font-medium">{{ Math.min(currentPage * pageSize, totalContracts) }}</span> 条，共 <span class="font-medium">{{ totalContracts }}</span> 条记录
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="prevPage"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm"
            :disabled="currentPage === 1"
            :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <span class="text-sm font-medium text-gray-700">第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
          <button
            @click="nextPage"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm"
            :disabled="currentPage === totalPages"
            :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 使用 script setup 语法糖
import { ref, onMounted, computed } from 'vue';
import axios from 'axios';

// 搜索和筛选状态
const searchQuery = ref('');
const typeFilter = ref('');
const statusFilter = ref('');
const dateRange = ref({
  from: '',
  to: ''
});

// 合同数据
const contracts = ref([]);
const totalContracts = ref(0);
const currentPage = ref(1);
const pageSize = ref(5);
const loading = ref(false);
const error = ref(null);
const successMessage = ref(null);

// 显示成功消息并自动隐藏
const showSuccessMessage = (message, duration = 5000) => {
  successMessage.value = message;
  setTimeout(() => {
    successMessage.value = null;
  }, duration);
};

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(totalContracts.value / pageSize.value) || 1;
});

// 删除合同记录
const deleteContract = async (id) => {
  if (confirm(`确定要删除该合同记录 (${id}) 吗？`)) {
    try {
      await axios.delete(`/api/contracts/${id}`);
      showSuccessMessage('删除成功！合同记录已被成功移除。');
      // 刷新列表
      fetchContracts();
    } catch (err) {
      console.error('删除失败:', err);
      error.value = err.response?.data?.message || '删除失败，请稍后重试。';
    }
  }
};

// 获取合同列表
const fetchContracts = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await axios.get('/api/contracts', {
      params: {
        search: searchQuery.value || undefined,
        type: typeFilter.value || undefined,
        status: statusFilter.value || undefined,
        dateFrom: dateRange.value.from || undefined,
        dateTo: dateRange.value.to || undefined,
        page: currentPage.value,
        limit: pageSize.value
      }
    });

    contracts.value = response.data.results || [];
    totalContracts.value = response.data.total || 0;
  } catch (err) {
    console.error('获取合同列表失败:', err);
    error.value = err.response?.data?.message || '获取合同列表失败，请重试';
    contracts.value = [];
  } finally {
    loading.value = false;
  }
};

// 防抖处理搜索查询
const debouncedFetchContracts = (() => {
  let timeout;
  return () => {
    clearTimeout(timeout);
    timeout = setTimeout(() => {
      fetchContracts();
    }, 300);
  };
})();

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 格式化金额
const formatCurrency = (value) => {
  const num = Number(value);
  if (isNaN(num)) return '0.00';
  return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

// 获取合同类型文本
const getContractTypeText = (type) => {
  const typeMap = {
    'sales': '销售合同',
    'purchase': '采购合同',
    'service': '服务合同',
    'labor': '劳务合同',
    'lease': '租赁合同',
    'full-time': '正式合同',
    'part-time': '兼职合同',
    'other': '其他合同'
    // 如有更多类型，继续补充
  };
  return typeMap[type] || type || '未知类型';
};

// 获取合同状态文本
const getContractStatusText = (status) => {
  const statusMap = {
    'draft': '草稿',
    'reviewing': '审核中',
    'active': '已生效',
    'expired': '已到期',
    'terminated': '已终止'
  };
  return statusMap[status] || status || '未知状态';
};

// 页面加载时获取数据
onMounted(() => {
  fetchContracts();
});

// 分页处理
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchContracts();
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    fetchContracts();
  }
};

// 重置筛选条件
const resetFilters = () => {
  searchQuery.value = '';
  typeFilter.value = '';
  statusFilter.value = '';
  dateRange.value = { from: '', to: '' };
  currentPage.value = 1;
  fetchContracts();
};
</script>

<style scoped>
/* Page transitions */
.page-header, .filter-card, .card {
  animation: fadeSlideIn 0.6s ease-out;
}

.page-header {
  animation-delay: 0s;
}

.filter-card {
  animation-delay: 0.1s;
}

.card {
  animation-delay: 0.2s;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

/* Badge animations */
.badge-success {
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(74, 222, 128, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0);
  }
}
</style>