const dotenv = require('dotenv');
const path = require('path');
const Joi = require('joi');

dotenv.config({ path: path.join(__dirname, '../../.env') });

const envVarsSchema = Joi.object()
  .keys({
    NODE_ENV: Joi.string().valid('production', 'development', 'test', 'windsurf', 'cursor', 'vscode', 'insider', 'trae').required(),
    PORT: Joi.number().default(3005),
    CLIENT_URL: Joi.string().required().description('Client URL'),

    // Database settings
    DB_DIALECT: Joi.string().default('postgres'),
    // Conditional validation based on dialect
    DB_STORAGE: Joi.when('DB_DIALECT', {
      is: 'sqlite',
      then: Joi.string().default('./database.sqlite'),
      otherwise: Joi.optional()
    }),
    DB_HOST: Joi.when('DB_DIALECT', {
      is: Joi.string().valid('postgres'),
      then: Joi.string().required(),
      otherwise: Joi.optional()
    }),
    DB_PORT: Joi.when('DB_DIALECT', {
      is: Joi.string().valid('postgres'),
      then: Joi.number().default(5432),
      otherwise: Joi.optional()
    }),
    DB_USER: Joi.when('DB_DIALECT', {
      is: Joi.string().valid('postgres'),
      then: Joi.string().required(),
      otherwise: Joi.optional()
    }),
    DB_PASSWORD: Joi.when('DB_DIALECT', {
      is: Joi.string().valid('postgres'),
      then: Joi.string().required(),
      otherwise: Joi.optional()
    }),
    DB_NAME: Joi.when('DB_DIALECT', {
      is: Joi.string().valid('postgres'),
      then: Joi.string().required(),
      otherwise: Joi.optional()
    }),

    // JWT settings
    JWT_SECRET: Joi.string().required().description('JWT secret key'),
    JWT_ACCESS_EXPIRATION_MINUTES: Joi.number().default(10080).description('minutes after which access tokens expire'), // 7 days (7*24*60)
    JWT_REFRESH_EXPIRATION_DAYS: Joi.number().default(30).description('days after which refresh tokens expire'),
    JWT_RESET_PASSWORD_EXPIRATION_MINUTES: Joi.number().default(10).description('minutes after which reset password token expires'),
    JWT_VERIFY_EMAIL_EXPIRATION_MINUTES: Joi.number().default(10).description('minutes after which verify email token expires'),

    // Email settings
    SMTP_HOST: Joi.string().description('server that will send the emails'),
    SMTP_PORT: Joi.number().description('port to connect to the email server'),
    SMTP_USERNAME: Joi.string().description('username for email server'),
    SMTP_PASSWORD: Joi.string().description('password for email server'),
    EMAIL_FROM: Joi.string().description('the from field in the emails sent by the app')
  })
  .unknown();

const { value: envVars, error } = envVarsSchema.prefs({ errors: { label: 'key' } }).validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

module.exports = {
  env: envVars.NODE_ENV,
  port: envVars.PORT,
  clientUrl: envVars.CLIENT_URL,

  sequelize: {
    dialect: envVars.DB_DIALECT,
    ...(envVars.DB_DIALECT === 'sqlite'
      ? { storage: envVars.DB_STORAGE }
      : {
          host: envVars.DB_HOST,
          port: envVars.DB_PORT,
          username: envVars.DB_USER,
          password: envVars.DB_PASSWORD,
          database: envVars.DB_NAME,
        }
    ),
  },

  jwt: {
    secret: envVars.JWT_SECRET,
    accessExpirationMinutes: envVars.JWT_ACCESS_EXPIRATION_MINUTES,
    refreshExpirationDays: envVars.JWT_REFRESH_EXPIRATION_DAYS,
    resetPasswordExpirationMinutes: envVars.JWT_RESET_PASSWORD_EXPIRATION_MINUTES,
    verifyEmailExpirationMinutes: envVars.JWT_VERIFY_EMAIL_EXPIRATION_MINUTES,
  },

  email: {
    smtp: {
      host: envVars.SMTP_HOST,
      port: envVars.SMTP_PORT,
      auth: {
        user: envVars.SMTP_USERNAME,
        pass: envVars.SMTP_PASSWORD,
      },
    },
    from: envVars.EMAIL_FROM,
  },

  development: {
    ...(process.env.DB_DIALECT === 'sqlite'
      ? {
          dialect: 'sqlite',
          storage: process.env.DB_STORAGE || './database.sqlite'
        }
      : {
          database: process.env.DB_NAME || 'development_db',
          username: process.env.DB_USER || 'root',
          password: process.env.DB_PASS || '',
          host: process.env.DB_HOST || 'localhost',
          dialect: process.env.DB_DIALECT || 'postgres',
          port: process.env.DB_PORT || 5432
        }
    ),
  },
  test: {
    ...(process.env.DB_DIALECT === 'sqlite'
      ? {
          dialect: 'sqlite',
          storage: process.env.TEST_DB_STORAGE || './test-database.sqlite'
        }
      : {
          database: process.env.TEST_DB_NAME || 'test_db',
          username: process.env.TEST_DB_USER || 'root',
          password: process.env.TEST_DB_PASS || '',
          host: process.env.TEST_DB_HOST || 'localhost',
          dialect: process.env.TEST_DB_DIALECT || 'postgres',
          port: process.env.TEST_DB_PORT || 5432
        }
    ),
  },
  production: {
    ...(process.env.DB_DIALECT === 'sqlite'
      ? {
          dialect: 'sqlite',
          storage: process.env.DB_STORAGE || './production-database.sqlite',
          logging: false
        }
      : {
          database: process.env.DB_NAME,
          username: process.env.DB_USER,
          password: process.env.DB_PASS,
          host: process.env.DB_HOST,
          dialect: process.env.DB_DIALECT || 'postgres',
          port: process.env.DB_PORT || 5432,
          logging: false,
          pool: {
            max: 5,
            min: 0,
            acquire: 30000,
            idle: 10000
          }
        }
    ),
  }
};