<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">{{ isEdit ? '编辑合同工' : '新增合同工' }}</h1>
        <p class="page-subtitle">{{ isEdit ? '修改现有合同工信息' : '添加新的合同工信息' }}</p>
      </div>
      <button @click="goBack" class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        返回列表
      </button>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误提示：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
      <div v-if="error.includes('保存失败')" class="mt-2 text-sm">
        <p>可能的原因：</p>
        <ul class="list-disc ml-5 mt-1">
          <li>您输入的身份证号码可能已存在</li>
          <li>您输入的合同编号可能已被使用</li>
          <li>网络连接暂时中断</li>
          <li>服务器暂时不可用，请稍后再试</li>
        </ul>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">操作成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="card shadow-md p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">正在{{ isEdit.value ? '获取合同工信息' : '准备表单' }}，请稍候...</p>
      <p class="text-gray-500 text-sm mt-2">系统正在{{ isEdit.value ? '从数据库读取合同工详细信息' : '初始化新建合同工表单' }}，这可能需要几秒钟时间</p>
    </div>

    <form v-if="!loading" @submit.prevent="handleSubmit" class="space-y-8 bg-white p-8 rounded-lg shadow">
      <!-- 基本信息 -->
      <div class="form-section">
        <h2 class="form-section-title">基本信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="employeeName" class="form-label">姓名 <span class="form-required">*</span></label>
            <input id="employeeName" type="text" v-model="form.employeeName" required class="form-input" placeholder="请输入合同工姓名" 
              oninvalid="this.setCustomValidity('请输入合同工姓名')" 
              oninput="this.setCustomValidity('')" />
            <span class="text-xs text-gray-500 mt-1 block">请输入合同工的真实姓名，将用于合同签署</span>
          </div>
          <div>
            <label for="idNumber" class="form-label">身份证号 <span class="form-required">*</span></label>
            <input id="idNumber" type="text" v-model="form.idNumber" required class="form-input" placeholder="请输入18位身份证号码" 
              oninvalid="this.setCustomValidity('请输入身份证号码')" 
              oninput="this.setCustomValidity('')" />
            <span class="text-xs text-gray-500 mt-1 block">请确保身份证号码输入正确，系统将验证其有效性</span>
          </div>
          <div>
            <label for="phone" class="form-label">联系电话 <span class="form-required">*</span></label>
            <input id="phone" type="tel" v-model="form.phone" required class="form-input" placeholder="请输入11位手机号码" 
              oninvalid="this.setCustomValidity('请输入联系电话')" 
              oninput="this.setCustomValidity('')" />
            <span class="text-xs text-gray-500 mt-1 block">用于工作通知和紧急联系</span>
          </div>
          <div>
            <label for="joinDate" class="form-label">入职时间 <span class="form-required">*</span></label>
            <input id="joinDate" type="date" v-model="form.joinDate"
            max="3000-12-31"
            required class="form-input" 
            oninvalid="this.setCustomValidity('请选择入职日期')" 
            oninput="this.setCustomValidity('')" />
            <span class="text-xs text-gray-500 mt-1 block">合同起始日期，影响工资计算</span>
          </div>
        </div>
      </div>

      <!-- 合同信息 -->
      <div class="form-section">
        <h2 class="form-section-title">合同信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="workType" class="form-label">工种 <span class="form-required">*</span></label>
            <select id="workType" v-model="form.workType" required class="form-input"
              oninvalid="this.setCustomValidity('请选择工种')" 
              oninput="this.setCustomValidity('')">
              <option value="">请选择工种</option>
              <option value="木工">木工</option>
              <option value="瓦工">瓦工</option>
              <option value="钢筋工">钢筋工</option>
              <option value="电工">电工</option>
              <option value="焊工">焊工</option>
              <option value="油漆工">油漆工</option>
              <option value="普工">普工</option>
            </select>
            <span class="text-xs text-gray-500 mt-1 block">选择合同工的专业技能类型</span>
          </div>
          <div>
            <label for="department" class="form-label">部门 <span class="form-required">*</span></label>
            <select id="department" v-model="form.departmentId" required class="form-input"
              oninvalid="this.setCustomValidity('请选择部门')" 
              oninput="this.setCustomValidity('')">
              <option value="">请选择部门</option>
              <option v-for="dept in departmentOptions" :key="dept.value" :value="dept.value">
                {{ dept.label }}
              </option>
            </select>
            <span class="text-xs text-gray-500 mt-1 block">选择合同工所属的部门单位</span>
          </div>
          <div>
            <label for="dailyWage" class="form-label">日工资 <span class="form-required">*</span></label>
            <input id="dailyWage" type="number" v-model="form.dailyWage" min="0" step="0.01" required class="form-input" placeholder="请输入日工资金额" 
              oninvalid="this.setCustomValidity('请输入日工资金额')" 
              oninput="this.setCustomValidity('')" />
            <span class="text-xs text-gray-500 mt-1 block">每工作日的基本工资，不含加班费和津贴</span>
          </div>
          <div>
            <label for="contractNumber" class="form-label">合同编号 <span class="form-required">*</span></label>
            <input id="contractNumber" type="text" v-model="form.contractNumber" class="form-input" placeholder="自动生成，可手动修改" required />
            <span class="text-xs text-gray-500 mt-1 block">系统自动生成唯一编号，也可手动输入</span>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="form-section">
        <h2 class="form-section-title">备注信息</h2>
        <div>
          <label for="remarks" class="form-label">备注</label>
          <textarea id="remarks" v-model="form.remarks" rows="3" class="form-input" placeholder="请输入合同工的额外信息，如特殊技能、工作经验等"></textarea>
          <span class="text-xs text-gray-500 mt-1 block">可记录合同工的特殊技能、工作经验、过往评价等信息</span>
        </div>
      </div>

      <!-- 表单操作按钮 -->
      <div class="flex justify-end space-x-4 border-t pt-6">
        <button type="button" @click="resetForm" class="btn btn-secondary flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          重置表单
        </button>
        <button type="submit" class="btn btn-primary flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          {{ isEdit ? '保存更改' : '创建合同工' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import axios from 'axios'
import { useDepartmentStore } from '@/stores/department'

const route = useRoute()
const router = useRouter()
const isEdit = computed(() => !!route.params.id)

const loading = ref(false)
const error = ref(null)
const successMessage = ref('')

// 部门存储
const departmentStore = useDepartmentStore()
const departmentOptions = computed(() => departmentStore.getDepartmentOptions)

// 日期格式化函数 - 用于日志和调试
const formatDateForDisplay = (date) => {
  if (!date) return '';
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
};

const parseDate = (dateStr) => {
  if (!dateStr) return '';
  try {
    // 支持多种日期格式
    let year, month, day;

    // 检查是否已经是ISO格式 (YYYY-MM-DD)
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
      return dateStr; // 已经是正确格式，直接返回
    }

    // 处理其他格式
    const parts = dateStr.split(/-|\//gi);
    if (parts.length !== 3) {
      // 尝试使用Date对象解析
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        year = date.getFullYear();
        month = String(date.getMonth() + 1).padStart(2, '0');
        day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      }
      return '';
    }

    // 从分割的部分提取年月日
    [year, month, day] = parts;

    // 确保年月日格式正确
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  } catch (err) {
    console.error('日期解析错误:', err, '原始日期字符串:', dateStr);
    return '';
  }
};

const form = reactive({
  employeeName: '',
  idNumber: '',
  phone: '',
  joinDate: '',
  workType: '',
  dailyWage: 0,
  remarks: '',
  departmentId: '',
  projectId: 'acc9388e-e929-4b77-8d1b-a4e12745b95d',
  contractNumber: 'HT-' + new Date().getTime().toString().slice(-6)
})

onMounted(async () => {
  // 获取部门数据
  departmentStore.fetchDepartments()

  if (isEdit.value) {
    loading.value = true
    try {
      const { data } = await axios.get(`/api/employees/contract-workers/${route.params.id}`)
      console.log('获取到的合同工数据:', data)

      // 记录日期信息，用于调试
      if (data) {
        console.log('原始日期信息:', {
          startDate: data.startDate,
          endDate: data.endDate,
          formattedStartDate: formatDateForDisplay(data.startDate),
          formattedEndDate: formatDateForDisplay(data.endDate)
        })
      }

      // 正确映射数据到表单
      if (data) {
        // 处理员工基本信息
        if (data.employee) {
          form.employeeName = data.employee.name || ''
          form.idNumber = data.employee.idNumber || ''
          form.departmentId = data.employee.departmentId != null ? String(data.employee.departmentId) : ''
          form.workType = data.employee.position || ''

          // 处理联系信息
          if (data.employee.contactInfo) {
            form.phone = data.employee.contactInfo.phone || ''
          }
        }

        // 处理合同信息
        form.contractNumber = data.contractNumber || ''
        form.projectId = data.projectId || 'acc9388e-e929-4b77-8d1b-a4e12745b95d'

        // 正确处理日期格式
        if (data.startDate) {
          // 确保日期格式正确
          const startDate = new Date(data.startDate);
          if (!isNaN(startDate.getTime())) {
            const year = startDate.getFullYear();
            const month = String(startDate.getMonth() + 1).padStart(2, '0');
            const day = String(startDate.getDate()).padStart(2, '0');
            form.joinDate = `${year}-${month}-${day}`;
          }
        }

        if (data.endDate) {
          const endDate = new Date(data.endDate);
          if (!isNaN(endDate.getTime())) {
            const year = endDate.getFullYear();
            const month = String(endDate.getMonth() + 1).padStart(2, '0');
            const day = String(endDate.getDate()).padStart(2, '0');
            form.endDate = `${year}-${month}-${day}`;
          }
        }

        form.dailyWage = data.salary || 0
        form.remarks = data.remarks || ''

        console.log('处理后的表单数据:', form)
      }
    } catch (err) {
      console.error('获取合同工信息失败:', err)
      error.value = '获取合同工信息失败'
    } finally {
      loading.value = false
    }
  }
})

const handleSubmit = async () => {
  loading.value = true;
  error.value = null;
  try {
    // 根据后端要求校验必填项
    if (!form.employeeName) throw new Error('请输入合同工姓名');
    if (!form.idNumber) throw new Error('请输入身份证号码');
    if (!/^\d{17}[\dXx]$/.test(form.idNumber)) throw new Error('身份证号码格式不正确，请输入18位有效身份证号');
    if (!form.phone) throw new Error('请输入联系电话');
    if (!/^1[3-9]\d{9}$/.test(form.phone)) throw new Error('手机号码格式不正确，请输入11位有效手机号');
    if (!form.joinDate) throw new Error('请选择入职日期');
    if (!form.workType) throw new Error('请选择工种类型');
    if (form.dailyWage === null || form.dailyWage === undefined || form.dailyWage === '') throw new Error('请输入日工资金额');
    if (form.dailyWage < 0) throw new Error('日工资不能为负数，请输入有效金额');
    if (!form.departmentId) throw new Error('请选择所属部门');

    // 确保projectId有值
    if (!form.projectId) {
      form.projectId = 'acc9388e-e929-4b77-8d1b-a4e12745b95d';
    }

    // 处理日期格式
    const startDate = parseDate(form.joinDate);
    const endDate = form.endDate ? parseDate(form.endDate) : startDate;

    // 记录处理后的日期，用于调试
    console.log('处理后的日期:', {
      原始入职日期: form.joinDate,
      处理后入职日期: startDate,
      原始结束日期: form.endDate,
      处理后结束日期: endDate
    });

    // 构造只包含后端允许字段的 payload
    const payload = {
      name: form.employeeName,
      idNumber: form.idNumber,
      projectId: form.projectId,
      contractNumber: form.contractNumber,
      startDate: startDate, // 入职日期对应 startDate
      endDate: endDate, // 如果没有结束日期，默认同入职日期
      position: form.workType, // 工种对应 position
      departmentId: form.departmentId, // 部门ID
      department: departmentOptions.value.find(dept => dept.value === form.departmentId)?.label || '未知部门', // 部门名称
      salary: form.dailyWage, // 日薪对应 salary
      status: 'active',
      contactInfo: {
        phone: form.phone,
        email: '',
        address: ''
      }
    };

    console.log('提交的数据:', payload);

    if (isEdit.value) {
      await axios.put(`/api/employees/contract-workers/${route.params.id}`, payload);
      successMessage.value = `已成功更新合同工 "${form.employeeName}" 的信息。\n\n系统将在短暂延迟后返回合同工列表页面。`;
    } else {
      await axios.post('/api/employees/contract-workers', payload);
      successMessage.value = `已成功添加新合同工 "${form.employeeName}"。\n\n系统将在短暂延迟后返回合同工列表页面。`;
    }
    setTimeout(() => {
      router.push('/employees/contract');
    }, 1500);
  } catch (err) {
    console.error('保存失败:', err);
    if (err.response?.data?.message?.includes('ID number already taken')) {
      error.value = '该身份证号码已被注册，请检查后重试。\n\n如果这是同一个合同工，请直接编辑现有记录。';
    } else if (err.response?.data?.message?.includes('Contract number already taken')) {
      error.value = '该合同编号已存在，请使用其他合同编号。\n\n系统会自动生成唯一编号，您也可以手动输入新的编号。';
    } else if (err.response?.data?.message?.includes('projectId')) {
      error.value = '项目ID格式不正确，请联系系统管理员。\n\n系统已尝试使用默认项目ID，请重试保存操作。';
      // 尝试使用另一个有效的项目ID
      form.projectId = 'bdd5371f-80dc-4b0d-a7d6-bc3da860b2e5';
    } else {
      error.value = err.response?.data?.message || err.message || '保存失败，请检查网络连接后重试。\n\n如果问题持续存在，请联系系统管理员。';
    }
  } finally {
    loading.value = false;
  }
}

function resetForm() {
  if (form.employeeName || form.idNumber || form.phone || form.joinDate || form.workType || form.dailyWage > 0 || form.remarks || form.departmentId) {
    if (!confirm('确定要重置表单吗？\n\n所有已填写的内容将被清空，此操作无法撤销。\n\n点击"确定"继续重置，点击"取消"保留当前填写的内容。')) {
      return;
    }
  }
  
  form.employeeName = ''
  form.idNumber = ''
  form.phone = ''
  form.joinDate = ''
  form.workType = ''
  form.dailyWage = 0
  form.remarks = ''
  form.departmentId = ''
  form.projectId = 'acc9388e-e929-4b77-8d1b-a4e12745b95d'
  form.contractNumber = 'HT-' + new Date().getTime().toString().slice(-6)
}

const goBack = () => {
  // 检查表单是否有内容，如果有则确认是否放弃更改
  if (form.employeeName || form.idNumber || form.phone || form.joinDate || form.workType || form.dailyWage > 0 || form.remarks || form.departmentId) {
    if (!confirm('您确定要返回列表页面吗？\n\n当前表单中的所有未保存更改将会丢失。\n\n点击"确定"返回列表页面，点击"取消"继续编辑。')) {
      return;
    }
  }
  router.push('/employees/contract')
}
</script>

<style scoped>
.page-title {
  @apply text-2xl font-bold text-gray-800;
}
.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}
.page-header {
  @apply flex justify-between items-center mb-6;
  animation: fadeSlideIn 0.5s ease-out;
}
.form-section-title {
  @apply text-lg font-semibold text-gray-700 mb-4;
}
.form-section {
  @apply mb-8;
}
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}
.form-input {
  @apply mt-1 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm border-gray-300 rounded-md;
}
.btn {
  @apply inline-flex justify-center py-2 px-4 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2;
}
.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 border-transparent focus:ring-blue-500;
}
.btn-secondary {
  @apply text-gray-700 bg-white hover:bg-gray-50 border-gray-300 focus:ring-blue-500;
}
</style>