const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');


const Reimbursement = sequelize.define('reimbursement', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  projectName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'project',
      key: 'id'
    }
  },
  reimbursementDate: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  reimbursementType: {
    type: DataTypes.STRING,
    allowNull: false
  },
  supplier: {
    type: DataTypes.STRING,
    allowNull: true
  },
  supplierId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'supplier',
      key: 'id'
    }
  },
  reason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  totalAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  taxRate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: false
  },
  invoiceType: {
    type: DataTypes.STRING,
    allowNull: true
  },
  paymentInfo: {
    type: DataTypes.JSON,
    allowNull: true
  },
  status: {
    type: DataTypes.STRING,
    defaultValue: 'pending',
    allowNull: false
  },
  createdBy: {
    type: DataTypes.UUID,
    allowNull: true
  },
  updatedBy: {
    type: DataTypes.UUID,
    allowNull: true
  },
  reimburserId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'user',
      key: 'id'
    }
  }
}, {
  timestamps: true,
  underscored: false,
  paranoid: true,
});

// Define associations
const setupAssociations = (models) => {
  const { Project, Supplier, User, Attachment } = models;

  Reimbursement.belongsTo(Project, {
    foreignKey: 'projectId',
    as: 'project'
  });

  Reimbursement.belongsTo(Supplier, {
    foreignKey: 'supplierId',
    as: 'supplierInfo'
  });

  Reimbursement.belongsTo(User, {
    foreignKey: 'createdBy',
    as: 'creator'
  });

  Reimbursement.belongsTo(User, {
    foreignKey: 'updatedBy',
    as: 'updater'
  });

  Reimbursement.belongsTo(User, {
    foreignKey: 'reimburserId',
    as: 'reimburser'
  });

  // Add the missing hasMany association to Attachment
  if (Attachment) {
    Reimbursement.hasMany(Attachment, {
      foreignKey: 'reimbursementId',
      as: 'attachments'
    });
  }
};

module.exports = { Reimbursement, setupAssociations };
