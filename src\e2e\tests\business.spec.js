import { test, expect } from '@playwright/test';

test.describe('Business Functionality Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 登录系统
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('项目管理测试', async ({ page }) => {
    // 导航到项目页面
    await page.goto('/projects');
    
    // 验证项目列表加载
    await expect(page.locator('.project-list')).toBeVisible();
    
    // 创建新项目
    await page.click('button:has-text("新建项目")');
    await page.fill('input[name="name"]', '测试项目');
    await page.fill('textarea[name="description"]', '这是一个测试项目');
    await page.click('button[type="submit"]');
    
    // 验证项目创建成功
    await expect(page.locator('text=测试项目')).toBeVisible();
  });

  test('合同管理测试', async ({ page }) => {
    // 导航到合同页面
    await page.goto('/contracts');
    
    // 验证合同列表加载
    await expect(page.locator('.contract-list')).toBeVisible();
    
    // 创建新合同
    await page.click('button:has-text("新建合同")');
    await page.fill('input[name="title"]', '测试合同');
    await page.fill('input[name="client"]', '测试客户');
    await page.fill('input[name="amount"]', '10000');
    await page.click('button[type="submit"]');
    
    // 验证合同创建成功
    await expect(page.locator('text=测试合同')).toBeVisible();
  });

  test('员工管理测试', async ({ page }) => {
    // 导航到员工页面
    await page.goto('/employees');
    
    // 验证员工列表加载
    await expect(page.locator('.employee-list')).toBeVisible();
    
    // 添加新员工
    await page.click('button:has-text("添加员工")');
    await page.fill('input[name="name"]', '张三');
    await page.fill('input[name="position"]', '工程师');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    
    // 验证员工添加成功
    await expect(page.locator('text=张三')).toBeVisible();
  });

  test('考勤管理测试', async ({ page }) => {
    // 导航到考勤页面
    await page.goto('/attendance');
    
    // 验证考勤记录加载
    await expect(page.locator('.attendance-list')).toBeVisible();
    
    // 记录考勤
    await page.click('button:has-text("记录考勤")');
    await page.selectOption('select[name="type"]', '上班');
    await page.click('button[type="submit"]');
    
    // 验证考勤记录成功
    await expect(page.locator('.success-message')).toBeVisible();
  });

  test('库存管理测试', async ({ page }) => {
    // 导航到库存页面
    await page.goto('/inventory');
    
    // 验证库存列表加载
    await expect(page.locator('.inventory-list')).toBeVisible();
    
    // 添加新物品
    await page.click('button:has-text("添加物品")');
    await page.fill('input[name="name"]', '测试物品');
    await page.fill('input[name="quantity"]', '100');
    await page.fill('input[name="unit"]', '个');
    await page.click('button[type="submit"]');
    
    // 验证物品添加成功
    await expect(page.locator('text=测试物品')).toBeVisible();
  });
}); 