const { v4: uuidv4 } = require('uuid');
const { Subcontract } = require('../models/subcontract.model');
const { User } = require('../models/user.model');
const { Project } = require('../models/project.model');
const { faker } = require('@faker-js/faker/locale/zh_CN');

const contractTypes = ['劳务分包', '专业分包', '设备分包', '材料分包', '施工分包'];
const contractStatuses = ['draft', 'active', 'completed', 'terminated', 'expired'];

async function seedSubcontracts() {
  try {
    // Check if subcontracts already exist
    const subcontractCount = await Subcontract.count();
    if (subcontractCount > 0) {
      console.log('Subcontracts already exist, skipping subcontract seeding');
      return;
    }

    // Get admin user for createdBy field
    const adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      console.error('No admin user found for subcontract seeding');
      return;
    }

    // Get projects for reference
    const projects = await Project.findAll({ limit: 10 });
    if (projects.length === 0) {
      console.log('No projects found for subcontract seeding, using generic project names');
    }

    // Create 15 subcontracts
    const subcontracts = [];
    for (let i = 0; i < 15; i++) {
      // Generate random dates
      const invoiceDate = faker.date.between({ 
        from: '2023-01-01', 
        to: '2024-05-31' 
      });
      
      const paymentDate = new Date(invoiceDate);
      paymentDate.setDate(paymentDate.getDate() + faker.number.int({ min: 7, max: 30 }));
      
      // Use project name if available, otherwise generate one
      const projectName = projects.length > 0 
        ? projects[i % projects.length].name 
        : `${faker.location.city()}${faker.helpers.arrayElement(['商业中心', '住宅小区', '办公楼', '工业园区', '学校'])}项目`;
      
      const subcontract = {
        projectName,
        contractName: `${projectName}${faker.helpers.arrayElement(contractTypes)}合同`,
        contractCode: `SC${faker.string.numeric(8)}`,
        contractType: faker.helpers.arrayElement(contractTypes),
        contractStatus: faker.helpers.arrayElement(contractStatuses),
        totalAmount: faker.number.float({ min: 50000, max: 2000000, precision: 2 }),
        taxRate: faker.number.float({ min: 3, max: 13, precision: 2 }),
        invoiceDate: invoiceDate,
        paymentDate: paymentDate,
        notes: faker.lorem.paragraph(),
        createdBy: adminUser.id,
        updatedBy: adminUser.id,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      subcontracts.push(subcontract);
    }

    // Bulk create subcontracts
    await Subcontract.bulkCreate(subcontracts);
    console.log('Successfully seeded subcontracts');
  } catch (error) {
    console.error('Error seeding subcontracts:', error);
    throw error;
  }
}

module.exports = seedSubcontracts;
