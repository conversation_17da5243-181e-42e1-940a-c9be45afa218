const { Sequelize } = require('sequelize');
const logger = require('../src/config/logger');
const path = require('path');

// 创建 Sequelize 实例
const sequelize = new Sequelize({
  dialect: 'postgres',
  host: '*************',
  port: 45432,
  username: 'postgres',
  password: 'KIdc4jKN6CTWEQp4vaH49H&vRLdItu7LD97vKSvq',
  database: 'yihe-local',
  logging: console.log
});

async function fixReimbursementForeignKeys() {
  try {
    // 开始事务
    const transaction = await sequelize.transaction();

    try {
      // 删除外键约束
      await sequelize.query(`
        ALTER TABLE reimbursement 
        DROP CONSTRAINT IF EXISTS reimbursement_projectId_fkey,
        DROP CONSTRAINT IF EXISTS reimbursement_supplierId_fkey;
      `, { transaction });

      // 修改列类型
      await sequelize.query(`
        ALTER TABLE reimbursement 
        ALTER COLUMN "projectId" TYPE VARCHAR(255),
        ALTER COLUMN "supplierId" TYPE VARCHAR(255);
      `, { transaction });

      await sequelize.query(`
        UPDATE reimbursement 
        SET "projectId" = CAST(CAST("projectId" AS UUID) AS VARCHAR(255)),
        "supplierId" = CAST(CAST("supplierId" AS UUID) AS VARCHAR(255));
      `, { transaction });

      await sequelize.query(`
        ALTER TABLE reimbursement 
        ALTER COLUMN "projectId" TYPE INTEGER USING "projectId"::INTEGER,
        ALTER COLUMN "supplierId" TYPE INTEGER USING "supplierId"::INTEGER;
      `, { transaction });

      // 重新添加外键约束
      await sequelize.query(`
        ALTER TABLE reimbursement 
        ADD CONSTRAINT reimbursement_projectId_fkey 
        FOREIGN KEY ("projectId") 
        REFERENCES project(id),
        ADD CONSTRAINT reimbursement_supplierId_fkey 
        FOREIGN KEY ("supplierId") 
        REFERENCES supplier(id);
      `, { transaction });

      // 提交事务
      await transaction.commit();
      console.log('Successfully fixed reimbursement foreign key types');
    } catch (error) {
      // 如果出错，回滚事务
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error fixing reimbursement foreign key types:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行修复
fixReimbursementForeignKeys()
  .then(() => {
    console.log('Foreign key fix completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Foreign key fix failed:', error);
    process.exit(1);
  }); 