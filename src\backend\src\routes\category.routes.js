const express = require('express');
const { authenticate } = require('../middlewares/auth.middleware');
const validate = require('../middlewares/validate');
const categoryValidation = require('../validations/category.validation');
const categoryController = require('../controllers/category.controller');

const router = express.Router();

router
  .route('/')
  .post(
    authenticate,
    validate(categoryValidation.createCategorySchema),
    categoryController.createCategory
  )
  .get(
    validate(categoryValidation.getCategoriesSchema),
    categoryController.getCategories
  );

router
  .route('/:categoryId')
  .get(
    authenticate,
    validate(categoryValidation.getCategorySchema),
    categoryController.getCategory
  )
  .patch(
    authenticate,
    validate(categoryValidation.updateCategorySchema),
    categoryController.updateCategory
  )
  .delete(
    authenticate,
    validate(categoryValidation.deleteCategorySchema),
    categoryController.deleteCategory
  );

module.exports = router;