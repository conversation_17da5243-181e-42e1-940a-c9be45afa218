<!-- 面包屑导航组件 -->
<template>
  <nav class="breadcrumb-container">
    <ol class="breadcrumb-list">
      <li v-if="!noHomeIcon" class="breadcrumb-item">
        <router-link to="/dashboard1" class="breadcrumb-link home">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h2a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3"></path>
          </svg>
        </router-link>
      </li>
      <template v-for="(item, index) in items" :key="index">
        <li v-if="index > 0 || !noHomeIcon" class="breadcrumb-separator" aria-hidden="true">
          <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </li>
        <li class="breadcrumb-item">
          <router-link v-if="item.path && index !== items.length - 1" :to="item.path" class="breadcrumb-link">
            {{ item.name }}
          </router-link>
          <span v-else class="breadcrumb-current">{{ item.name }}</span>
        </li>
      </template>
    </ol>
  </nav>
</template>

<script setup>
defineProps({
  items: {
    type: Array,
    required: true
  },
  noHomeIcon: {
    type: Boolean,
    default: false
  }
});
</script>

<style scoped>
.breadcrumb-container {
  @apply bg-white rounded-lg shadow-sm px-4 py-3 mb-4;
}

.breadcrumb-list {
  @apply flex items-center flex-wrap text-sm font-medium;
}

.breadcrumb-item {
  @apply flex items-center;
}

.breadcrumb-separator {
  @apply mx-1;
}

.breadcrumb-link {
  @apply text-blue-600 hover:text-blue-800 transition-colors duration-200;
}

.breadcrumb-link.home {
  @apply text-gray-500 hover:text-gray-700;
}

.breadcrumb-current {
  @apply text-gray-600 font-semibold;
}
</style> 