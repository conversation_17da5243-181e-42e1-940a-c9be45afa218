/**
 * Test helper functions for Playwright tests
 * 增强版 - 支持自动错误检测和修复
 */

const { expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');

// 错误日志路径
const ERROR_LOG_DIR = path.join(__dirname, '../logs');
// 确保日志目录存在
if (!fs.existsSync(ERROR_LOG_DIR)) {
  fs.mkdirSync(ERROR_LOG_DIR, { recursive: true });
}

/**
 * Login to the application
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {string} email - User email
 * @param {string} password - User password
 */
async function login(page, email = '<EMAIL>', password = 'password123') {
  await page.goto('/login');
  await page.fill('input[name="email"]', email);
  await page.fill('input[name="password"]', password);
  await page.click('button[type="submit"]');
  await page.waitForURL('/dashboard');
}

/**
 * Navigate to a specific page and verify it loaded correctly
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {string} url - URL to navigate to
 * @param {string} expectedTitleText - Text expected in the page title
 */
async function navigateTo(page, url, expectedTitleText) {
  await page.goto(url);
  await page.waitForLoadState('networkidle');

  // Check for page title
  if (expectedTitleText) {
    const titleElements = await page.$$('h1, h2');
    let foundTitle = false;

    for (const element of titleElements) {
      const text = await element.textContent();
      if (text.includes(expectedTitleText)) {
        foundTitle = true;
        break;
      }
    }

    expect(foundTitle).toBeTruthy();
  }
}

/**
 * Fill a form with the given data
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {Object} formData - Key-value pairs of form field names and values
 */
async function fillForm(page, formData) {
  for (const [fieldName, value] of Object.entries(formData)) {
    const selector = `input[name="${fieldName}"], textarea[name="${fieldName}"], select[name="${fieldName}"]`;

    // Check if the element exists
    const element = await page.$(selector);
    if (element) {
      // Handle different input types
      const tagName = await element.evaluate(el => el.tagName.toLowerCase());
      const type = await element.evaluate(el => el.type);

      if (tagName === 'select') {
        await page.selectOption(selector, value);
      } else if (type === 'checkbox') {
        if (value) {
          await page.check(selector);
        } else {
          await page.uncheck(selector);
        }
      } else {
        await page.fill(selector, String(value));
      }
    }
  }
}

/**
 * Check for common errors on the page
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @returns {Promise<{hasErrors: boolean, errors: string[]}>} - Error information
 */
async function checkForErrors(page) {
  const errors = [];

  // Check for error messages
  const errorElements = await page.$$('.error-message, .text-red-500, .text-red-600, .text-red-700');
  for (const element of errorElements) {
    const text = await element.textContent();
    if (text && text.trim()) {
      errors.push(text.trim());
    }
  }

  // Check for console errors
  const consoleErrors = [];
  page.on('console', msg => {
    if (msg.type() === 'error') {
      consoleErrors.push(msg.text());
    }
  });

  return {
    hasErrors: errors.length > 0 || consoleErrors.length > 0,
    errors: [...errors, ...consoleErrors]
  };
}

/**
 * Wait for network requests to complete
 * @param {import('@playwright/test').Page} page - Playwright page object
 */
async function waitForNetworkIdle(page) {
  await page.waitForLoadState('networkidle');
}

/**
 * Verify a table contains expected data
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {string} tableSelector - CSS selector for the table
 * @param {Array<string>} expectedColumns - Array of expected column headers
 * @param {boolean} shouldHaveData - Whether the table should have data rows
 */
async function verifyTable(page, tableSelector, expectedColumns, shouldHaveData = true) {
  // Verify table exists
  await page.waitForSelector(tableSelector);

  // Verify column headers
  for (const column of expectedColumns) {
    const headerSelector = `${tableSelector} th:has-text("${column}")`;
    await page.waitForSelector(headerSelector);
  }

  // Verify data rows if expected
  if (shouldHaveData) {
    const rowCount = await page.$$eval(`${tableSelector} tbody tr`, rows => rows.length);
    expect(rowCount).toBeGreaterThan(0);
  }
}

/**
 * 记录错误到日志文件
 * @param {string} testName - 测试名称
 * @param {string} errorType - 错误类型
 * @param {string} errorMessage - 错误消息
 * @param {string} fixApplied - 应用的修复
 */
function logError(testName, errorType, errorMessage, fixApplied = '无') {
  const timestamp = new Date().toISOString();
  const logFile = path.join(ERROR_LOG_DIR, `error-log-${new Date().toISOString().split('T')[0]}.txt`);

  const logEntry = `
时间: ${timestamp}
测试: ${testName}
错误类型: ${errorType}
错误消息: ${errorMessage}
应用修复: ${fixApplied}
-------------------------------------------
`;

  fs.appendFileSync(logFile, logEntry);
  console.log(`[错误日志] ${errorType}: ${errorMessage}`);
}

/**
 * 检查网络请求错误
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @returns {Promise<{hasErrors: boolean, errors: string[]}>} - 错误信息
 */
async function checkNetworkErrors(page) {
  const errors = [];

  // 监听网络请求错误
  page.on('requestfailed', request => {
    errors.push(`请求失败: ${request.url()} (${request.failure().errorText})`);
  });

  // 监听响应错误 (4xx, 5xx)
  page.on('response', response => {
    const status = response.status();
    if (status >= 400) {
      errors.push(`响应错误: ${response.url()} (状态码: ${status})`);
    }
  });

  return {
    hasErrors: errors.length > 0,
    errors
  };
}

/**
 * 截图并保存
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {string} testName - 测试名称
 * @param {string} reason - 截图原因
 */
async function takeScreenshot(page, testName, reason = 'error') {
  const timestamp = new Date().toISOString().replace(/:/g, '-');
  const screenshotDir = path.join(__dirname, '../screenshots');

  if (!fs.existsSync(screenshotDir)) {
    fs.mkdirSync(screenshotDir, { recursive: true });
  }

  const fileName = `${testName.replace(/\s+/g, '-')}_${reason}_${timestamp}.png`;
  const filePath = path.join(screenshotDir, fileName);

  await page.screenshot({ path: filePath, fullPage: true });
  console.log(`[截图] 已保存到: ${filePath}`);

  return filePath;
}

/**
 * 生成随机测试数据
 * @param {string} type - 数据类型
 * @returns {string} - 随机数据
 */
function generateTestData(type) {
  const timestamp = Date.now();

  switch (type) {
    case 'name':
      return `测试用户${timestamp.toString().slice(-4)}`;
    case 'email':
      return `test${timestamp.toString().slice(-4)}@example.com`;
    case 'phone':
      return `138${Math.floor(Math.random() * 100000000).toString().padStart(8, '0')}`;
    case 'idNumber':
      // 生成随机18位身份证号
      const areaCode = '110101'; // 北京市东城区
      const birthYear = Math.floor(1960 + Math.random() * 40).toString();
      const birthMonth = Math.floor(1 + Math.random() * 12).toString().padStart(2, '0');
      const birthDay = Math.floor(1 + Math.random() * 28).toString().padStart(2, '0');
      const sequence = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
      const checkCode = '0123456789X'[Math.floor(Math.random() * 11)];
      return `${areaCode}${birthYear}${birthMonth}${birthDay}${sequence}${checkCode}`;
    case 'date':
      return new Date().toISOString().split('T')[0];
    case 'futureDate':
      const future = new Date();
      future.setDate(future.getDate() + Math.floor(Math.random() * 365));
      return future.toISOString().split('T')[0];
    case 'number':
      return Math.floor(Math.random() * 1000).toString();
    case 'text':
      return `测试文本 ${timestamp}`;
    default:
      return `测试数据 ${timestamp}`;
  }
}

/**
 * 自动修复常见问题
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {string} errorType - 错误类型
 * @param {string} testName - 测试名称
 * @returns {Promise<boolean>} - 修复是否成功
 */
async function autoFix(page, errorType, testName = '未知测试') {
  let fixApplied = '无';
  let success = false;

  try {
    switch (errorType) {
      case 'session-expired':
        // 会话过期时重新登录
        await login(page);
        fixApplied = '重新登录';
        success = true;
        break;

      case 'form-validation':
        // 尝试修复表单验证问题
        const requiredFields = await page.$$('input[required], select[required], textarea[required]');
        for (const field of requiredFields) {
          const value = await field.inputValue();
          if (!value) {
            const type = await field.evaluate(el => el.type);
            const fieldName = await field.evaluate(el => el.name || el.id || '未知字段');

            let newValue = '';

            if (type === 'text' || type === 'textarea') {
              if (fieldName.includes('name') || fieldName.includes('姓名')) {
                newValue = generateTestData('name');
              } else if (fieldName.includes('phone') || fieldName.includes('电话')) {
                newValue = generateTestData('phone');
              } else if (fieldName.includes('id') && fieldName.includes('number')) {
                newValue = generateTestData('idNumber');
              } else {
                newValue = generateTestData('text');
              }
            } else if (type === 'email') {
              newValue = generateTestData('email');
            } else if (type === 'number') {
              newValue = generateTestData('number');
            } else if (type === 'date') {
              newValue = generateTestData('date');
            }

            if (newValue) {
              await field.fill(newValue);
              fixApplied += `填充字段 ${fieldName}: ${newValue}; `;
            }
          }
        }
        success = true;
        break;

      case 'element-not-found':
        // 尝试等待元素出现或使用替代选择器
        await page.waitForTimeout(2000); // 等待页面加载
        fixApplied = '等待页面加载';
        success = true;
        break;

      case 'navigation-timeout':
        // 处理导航超时
        await page.reload();
        await page.waitForLoadState('networkidle');
        fixApplied = '刷新页面';
        success = true;
        break;

      case 'network-error':
        // 处理网络错误
        await page.reload();
        await page.waitForLoadState('networkidle');
        fixApplied = '刷新页面并等待网络空闲';
        success = true;
        break;

      case 'unexpected-dialog':
        // 处理意外对话框
        page.on('dialog', dialog => dialog.accept());
        fixApplied = '自动接受对话框';
        success = true;
        break;

      default:
        fixApplied = '无可用修复';
        success = false;
    }
  } catch (error) {
    console.error(`自动修复失败: ${error.message}`);
    fixApplied += ` (修复过程中出错: ${error.message})`;
    success = false;
  }

  // 记录错误和修复尝试
  logError(testName, errorType, `尝试修复 ${errorType}`, fixApplied);

  // 如果修复失败，截图记录
  if (!success) {
    await takeScreenshot(page, testName, `fix-failed-${errorType}`);
  }

  return success;
}

/**
 * 智能等待元素
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {string} selector - 元素选择器
 * @param {Object} options - 选项
 * @returns {Promise<import('@playwright/test').ElementHandle>} - 元素句柄
 */
async function smartWaitForSelector(page, selector, options = {}) {
  const { timeout = 10000, retries = 3, alternativeSelectors = [] } = options;

  // 尝试主选择器
  try {
    return await page.waitForSelector(selector, { timeout });
  } catch (error) {
    console.log(`无法找到元素: ${selector}，尝试替代选择器...`);

    // 尝试替代选择器
    for (const altSelector of alternativeSelectors) {
      try {
        return await page.waitForSelector(altSelector, { timeout });
      } catch (innerError) {
        console.log(`替代选择器也失败: ${altSelector}`);
      }
    }

    // 如果所有选择器都失败，尝试刷新页面并重试
    if (retries > 0) {
      console.log(`刷新页面并重试 (剩余重试次数: ${retries})...`);
      await page.reload();
      await page.waitForLoadState('networkidle');
      return smartWaitForSelector(page, selector, {
        timeout,
        retries: retries - 1,
        alternativeSelectors
      });
    }

    throw new Error(`无法找到元素: ${selector} 或任何替代选择器`);
  }
}

/**
 * 智能点击元素
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {string} selector - 元素选择器
 * @param {Object} options - 选项
 */
async function smartClick(page, selector, options = {}) {
  const { timeout = 10000, force = false, retries = 3, alternativeSelectors = [] } = options;

  try {
    const element = await smartWaitForSelector(page, selector, {
      timeout,
      retries,
      alternativeSelectors
    });

    await element.click({ force });
  } catch (error) {
    console.error(`点击元素失败: ${selector}`, error);

    // 尝试使用JavaScript点击
    try {
      await page.evaluate((sel) => {
        const element = document.querySelector(sel);
        if (element) element.click();
      }, selector);
      console.log(`使用JavaScript点击元素: ${selector}`);
    } catch (jsError) {
      throw new Error(`无法点击元素: ${selector} - ${error.message}`);
    }
  }
}

/**
 * 检测并处理页面错误
 * @param {import('@playwright/test').Page} page - Playwright page object
 * @param {string} testName - 测试名称
 */
async function detectAndFixPageErrors(page, testName) {
  // 检查页面错误
  const { hasErrors, errors } = await checkForErrors(page);

  if (hasErrors) {
    console.log(`[${testName}] 检测到页面错误:`, errors);

    // 尝试确定错误类型并修复
    let errorType = 'unknown';

    if (errors.some(e => e.includes('登录') || e.includes('认证') || e.includes('session'))) {
      errorType = 'session-expired';
    } else if (errors.some(e => e.includes('验证') || e.includes('必填') || e.includes('required'))) {
      errorType = 'form-validation';
    } else if (errors.some(e => e.includes('网络') || e.includes('连接') || e.includes('network'))) {
      errorType = 'network-error';
    }

    // 应用自动修复
    const fixed = await autoFix(page, errorType, testName);
    if (fixed) {
      console.log(`[${testName}] 成功修复 ${errorType} 错误`);

      // 截图记录修复后的状态
      await takeScreenshot(page, testName, `fixed-${errorType}`);
    } else {
      console.log(`[${testName}] 无法修复错误，继续测试...`);
    }
  }

  // 检查网络错误
  const { hasErrors: hasNetworkErrors, errors: networkErrors } = await checkNetworkErrors(page);

  if (hasNetworkErrors) {
    console.log(`[${testName}] 检测到网络错误:`, networkErrors);
    await autoFix(page, 'network-error', testName);
  }
}

module.exports = {
  login,
  navigateTo,
  fillForm,
  checkForErrors,
  waitForNetworkIdle,
  verifyTable,
  autoFix,
  takeScreenshot,
  generateTestData,
  logError,
  checkNetworkErrors,
  smartWaitForSelector,
  smartClick,
  detectAndFixPageErrors
};
