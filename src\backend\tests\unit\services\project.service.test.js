const { expect } = require('chai');
const sinon = require('sinon');
const { Project, User, sequelize } = require('../../../src/models');
const projectService = require('../../../src/services/project.service');
const ApiError = require('../../../src/utils/ApiError');

describe('Project Service', () => {
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('createProject', () => {
    it('should create a project successfully', async () => {
      // Arrange
      const projectData = {
        name: 'Test Project',
        code: 'TP001',
        description: 'Test project description',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        status: 'active',
        budget: 100000,
        clientId: '1',
        managerId: '1',
        createdBy: '00000000-0000-0000-0000-000000000000'
      };

      sandbox.stub(User, 'findByPk')
        .onFirstCall().resolves({ id: '1', name: 'Test Client' })
        .onSecondCall().resolves({ id: '1', name: 'Test Manager' });
      
      sandbox.stub(Project, 'findOne').resolves(null);
      const createStub = sandbox.stub(Project, 'create').resolves(projectData);

      // Act
      const result = await projectService.createProject(projectData);

      // Assert
      expect(createStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(projectData);
    });

    it('should throw error if project code already exists', async () => {
      // Arrange
      const projectData = {
        name: 'Test Project',
        code: 'TP001',
        description: 'Test project description',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        status: 'active',
        budget: 100000,
        clientId: '1',
        managerId: '1'
      };

      sandbox.stub(User, 'findByPk')
        .onFirstCall().resolves({ id: '1', name: 'Test Client' })
        .onSecondCall().resolves({ id: '1', name: 'Test Manager' });
      
      sandbox.stub(Project, 'findOne').resolves({ id: '1', code: 'TP001' });

      // Act & Assert
      try {
        await projectService.createProject(projectData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('already exists');
      }
    });

    it('should throw error if client not found', async () => {
      // Arrange
      const projectData = {
        name: 'Test Project',
        code: 'TP001',
        description: 'Test project description',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        status: 'active',
        budget: 100000,
        clientId: '999',
        managerId: '1'
      };

      sandbox.stub(User, 'findByPk')
        .onFirstCall().resolves(null)
        .onSecondCall().resolves({ id: '1', name: 'Test Manager' });

      // Act & Assert
      try {
        await projectService.createProject(projectData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('Client not found');
      }
    });

    it('should throw error if manager not found', async () => {
      // Arrange
      const projectData = {
        name: 'Test Project',
        code: 'TP001',
        description: 'Test project description',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        status: 'active',
        budget: 100000,
        clientId: '1',
        managerId: '999'
      };

      sandbox.stub(User, 'findByPk')
        .onFirstCall().resolves({ id: '1', name: 'Test Client' })
        .onSecondCall().resolves(null);

      // Act & Assert
      try {
        await projectService.createProject(projectData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('Manager not found');
      }
    });
  });

  describe('getProjectById', () => {
    it('should return project if found', async () => {
      // Arrange
      const projectId = '1';
      const projectData = {
        id: projectId,
        name: 'Test Project',
        code: 'TP001',
        description: 'Test project description',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        status: 'active',
        budget: 100000,
        clientId: '1',
        managerId: '1'
      };

      sandbox.stub(Project, 'findByPk').resolves(projectData);

      // Act
      const result = await projectService.getProjectById(projectId);

      // Assert
      expect(result).to.deep.equal(projectData);
    });

    it('should throw error if project not found', async () => {
      // Arrange
      const projectId = '999';
      sandbox.stub(Project, 'findByPk').resolves(null);

      // Act & Assert
      try {
        await projectService.getProjectById(projectId);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(404);
        expect(error.message).to.include('not found');
      }
    });
  });

  describe('updateProjectById', () => {
    it('should update project successfully', async () => {
      // Arrange
      const projectId = '1';
      const projectData = {
        id: projectId,
        name: 'Test Project',
        code: 'TP001',
        description: 'Test project description',
        startDate: '2023-01-01',
        endDate: '2023-12-31',
        status: 'active',
        budget: 100000,
        clientId: '1',
        managerId: '1',
        save: sandbox.stub().resolves()
      };

      const updateData = {
        name: 'Updated Project',
        status: 'completed',
        budget: 150000
      };

      sandbox.stub(projectService, 'getProjectById').resolves(projectData);
      sandbox.stub(Project, 'findOne').resolves(null);

      // Act
      const result = await projectService.updateProjectById(projectId, updateData);

      // Assert
      expect(result.name).to.equal(updateData.name);
      expect(result.status).to.equal(updateData.status);
      expect(result.budget).to.equal(updateData.budget);
      expect(projectData.save.calledOnce).to.be.true;
    });

    it('should throw error if updating to existing code', async () => {
      // Arrange
      const projectId = '1';
      const projectData = {
        id: projectId,
        name: 'Test Project',
        code: 'TP001'
      };

      const updateData = {
        code: 'TP002'
      };

      sandbox.stub(projectService, 'getProjectById').resolves(projectData);
      sandbox.stub(Project, 'findOne').resolves({ id: '2', code: 'TP002' });

      // Act & Assert
      try {
        await projectService.updateProjectById(projectId, updateData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('already exists');
      }
    });
  });

  describe('deleteProjectById', () => {
    it('should delete project successfully', async () => {
      // Arrange
      const projectId = '1';
      const projectData = {
        id: projectId,
        name: 'Test Project',
        destroy: sandbox.stub().resolves()
      };

      sandbox.stub(projectService, 'getProjectById').resolves(projectData);

      // Act
      await projectService.deleteProjectById(projectId);

      // Assert
      expect(projectData.destroy.calledOnce).to.be.true;
    });
  });

  describe('queryProjects', () => {
    it('should return projects and pagination info', async () => {
      // Arrange
      const filter = { status: 'active' };
      const options = { 
        page: 1, 
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };
      
      const projects = [
        { id: '1', name: 'Project 1', code: 'P001', status: 'active' },
        { id: '2', name: 'Project 2', code: 'P002', status: 'active' }
      ];
      
      const findAndCountAllStub = sandbox.stub(Project, 'findAndCountAll').resolves({
        count: 2,
        rows: projects
      });

      // Act
      const result = await projectService.queryProjects(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      expect(result.results).to.deep.equal(projects);
      expect(result.pagination.totalResults).to.equal(2);
      
      // Verify filter was applied correctly
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause.status).to.equal('active');
    });

    it('should handle search filter correctly', async () => {
      // Arrange
      const filter = { search: 'test' };
      const options = { 
        page: 1, 
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };
      
      const findAndCountAllStub = sandbox.stub(Project, 'findAndCountAll').resolves({
        count: 0,
        rows: []
      });

      // Act
      await projectService.queryProjects(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      // Verify that the where clause includes the search condition
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause[Symbol.for('sequelize.Op.or')]).to.exist;
    });

    it('should handle date range filters correctly', async () => {
      // Arrange
      const filter = { 
        startDateFrom: '2023-01-01',
        startDateTo: '2023-12-31',
        endDateFrom: '2024-01-01',
        endDateTo: '2024-12-31'
      };
      const options = { 
        page: 1, 
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };
      
      const findAndCountAllStub = sandbox.stub(Project, 'findAndCountAll').resolves({
        count: 0,
        rows: []
      });

      // Act
      await projectService.queryProjects(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      // Verify that the where clause includes the date range conditions
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause.startDate).to.exist;
      expect(whereClause.endDate).to.exist;
    });
  });

  describe('getProjectStats', () => {
    it('should return project statistics', async () => {
      // Arrange
      const stats = {
        totalProjects: 10,
        activeProjects: 5,
        completedProjects: 3,
        onHoldProjects: 2,
        totalBudget: 1000000,
        averageBudget: 100000
      };
      
      sandbox.stub(Project, 'count')
        .onFirstCall().resolves(stats.totalProjects)
        .onSecondCall().resolves(stats.activeProjects)
        .onThirdCall().resolves(stats.completedProjects)
        .onCall(3).resolves(stats.onHoldProjects);
      
      sandbox.stub(Project, 'sum')
        .onFirstCall().resolves(stats.totalBudget);
      
      sandbox.stub(Project, 'findAll').resolves([
        { status: 'active', count: 5 },
        { status: 'completed', count: 3 },
        { status: 'on-hold', count: 2 }
      ]);

      // Act
      const result = await projectService.getProjectStats();

      // Assert
      expect(result.totalProjects).to.equal(stats.totalProjects);
      expect(result.activeProjects).to.equal(stats.activeProjects);
      expect(result.completedProjects).to.equal(stats.completedProjects);
      expect(result.onHoldProjects).to.equal(stats.onHoldProjects);
      expect(result.totalBudget).to.equal(stats.totalBudget);
      expect(result.averageBudget).to.equal(stats.totalBudget / stats.totalProjects);
    });
  });
});
