const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { authService, userService, tokenService, emailService } = require('../services');

/**
 * Get current user profile
 * @route GET /api/auth/me
 */
const getProfile = catchAsync(async (req, res) => {
  const user = await userService.getUserById(req.user.id);
  res.send(user);
});

/**
 * Register a new user
 * @route POST /api/auth/register
 */
const register = catchAsync(async (req, res) => {
  const user = await userService.createUser(req.body);
  const tokens = await tokenService.generateAuthTokens(user);

  // If we have email service configured, we could send verification email
  // const verifyEmailToken = await tokenService.generateVerifyEmailToken(user);
  // await emailService.sendVerificationEmail(user.email, verifyEmailToken);

  res.status(201).send({ user, tokens });
});

/**
 * Login with email and password
 * @route POST /api/auth/login
 */
const login = catchAsync(async (req, res) => {
  const { email, password } = req.body;
  const user = await authService.loginUserWithEmailAndPassword(email, password);

  // Generate tokens with the same secret used for verification
  const tokens = await tokenService.generateAuthTokens(user);

  // Log the token for debugging
  console.log('Generated access token:', tokens.access.token.substring(0, 20) + '...');

  res.send({ user, tokens });
});

/**
 * Logout - invalidate refresh token
 * @route POST /api/auth/logout
 */
const logout = catchAsync(async (req, res) => {
  await authService.logout(req.body.refreshToken);
  // 使用明确的数字状态码，避免可能的undefined问题
  res.status(204).send();
});

/**
 * Refresh auth tokens
 * @route POST /api/auth/refresh-tokens
 */
const refreshTokens = catchAsync(async (req, res) => {
  const tokens = await authService.refreshAuth(req.body.refreshToken);
  res.send({ ...tokens });
});

/**
 * Forgot password - send reset password email
 * @route POST /api/auth/forgot-password
 */
const forgotPassword = catchAsync(async (req, res) => {
  const resetPasswordToken = await tokenService.generateResetPasswordToken(req.body.email);
  // await emailService.sendResetPasswordEmail(req.body.email, resetPasswordToken);
  res.status(204).send();
});

/**
 * Reset password
 * @route POST /api/auth/reset-password
 */
const resetPassword = catchAsync(async (req, res) => {
  await authService.resetPassword(req.query.token, req.body.password);
  res.status(204).send();
});

/**
 * Verify email
 * @route POST /api/auth/verify-email
 */
const verifyEmail = catchAsync(async (req, res) => {
  await authService.verifyEmail(req.query.token);
  res.status(204).send();
});

/**
 * Change password (logged in user)
 * @route POST /api/auth/change-password
 */
const changePassword = catchAsync(async (req, res) => {
  await authService.changePassword(req.user.id, req.body.currentPassword, req.body.newPassword);
  res.status(204).send();
});

module.exports = {
  getProfile,
  register,
  login,
  logout,
  refreshTokens,
  forgotPassword,
  resetPassword,
  verifyEmail,
  changePassword,
};