# Backend Service

This directory contains the backend service for the Information Management application.

## Features

- User authentication and authorization
- Database support for PostgreSQL
- REST API for managing information
- File upload and management
- Email notifications

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- npm or pnpm package manager
- PostgreSQL (optional, for PostgreSQL support)

### Installation

1. Install dependencies:

```bash
npm install
# or
pnpm install
```

2. Configure environment variables:

```bash
cp .env.example .env
```

3. Edit the `.env` file with your configuration.

### Running the Application

Development mode:

```bash
npm run dev
# or
pnpm dev
```

Production mode:

```bash
npm start
# or
pnpm start
```

## Database Support

### PostgreSQL Support

For high-performance production environments, the application supports PostgreSQL. To configure:

1. Update your `.env` file with PostgreSQL credentials (or copy from the example):

```bash
cp .env.postgres.example .env
```

Then edit the `.env` file with your PostgreSQL credentials:

```
DB_DIALECT=postgres
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=yourpassword
DB_NAME=yihe
DB_SCHEMA=public
```

2. Run the PostgreSQL setup script:

```bash
npm run postgres:setup
```

This script will:
- Check connection to the PostgreSQL server
- Create the database if it doesn't exist
- Initialize required extensions
- Run initial migrations

For detailed PostgreSQL documentation, see:
- [PostgreSQL Support Documentation](docs/postgres-support.md)

## Database Migrations

The application includes a migration system for managing database schema changes:

```

### PostgreSQL Migrations

```bash
# Create a migration
npm run postgres:migration:create add-new-field

# Run migrations
npm run postgres:migration:run

# List migrations
npm run postgres:migration:list

# Rollback the last migration
npm run postgres:migration:rollback
```

```

### PostgreSQL Backup and Restore

For PostgreSQL databases, you can create backups and restore from them:

```bash
# Create a backup
npm run postgres:backup "Weekly Backup"

# List backups
npm run postgres:backup:list

# Restore from a backup
npm run postgres:backup:restore 1

# Delete a backup
npm run postgres:backup:delete 1
```

## API Documentation

The API is documented using Swagger/OpenAPI. Access the documentation at:

```
http://localhost:3005/api-docs
```

## Testing

Run tests with:

```bash
npm test
```

## License

This project is licensed under the ISC License. 