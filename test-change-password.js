// 测试用户修改密码功能
async function testChangePassword() {
  const API_BASE_URL = process.env.API_URL || 'http://localhost:3009';
  
  try {
    console.log('🔄 开始测试用户修改密码功能...');
    
    // 1. 使用默认管理员账户登录
    console.log('🔐 管理员登录...');
    const adminLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123' // 假设这是默认密码
      })
    });
    
    if (!adminLoginResponse.ok) {
      const errorData = await adminLoginResponse.text();
      throw new Error(`管理员登录失败: ${adminLoginResponse.status} - ${errorData}`);
    }
    
    const adminLoginData = await adminLoginResponse.json();
    console.log('✅ 管理员登录成功');
    
    const adminToken = adminLoginData.tokens?.access?.token;
    if (!adminToken) {
      throw new Error('未获取到管理员token');
    }
    
    // 2. 创建测试用户
    const testUser = {
      username: 'changetest' + Date.now(),
      email: 'changetest' + Date.now() + '@example.com',
      password: 'originalpassword123',
      lastName: '修改密码测试用户',
      role: 'user'
    };
    
    console.log('📝 创建测试用户:', testUser.username);
    const createResponse = await fetch(`${API_BASE_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify(testUser)
    });
    
    if (!createResponse.ok) {
      const errorData = await createResponse.text();
      throw new Error(`创建用户失败: ${createResponse.status} - ${errorData}`);
    }
    
    const createData = await createResponse.json();
    console.log('✅ 用户创建成功');
    
    // 3. 用新用户登录
    console.log('🔐 新用户登录...');
    const userLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    if (!userLoginResponse.ok) {
      const errorData = await userLoginResponse.text();
      throw new Error(`新用户登录失败: ${userLoginResponse.status} - ${errorData}`);
    }
    
    const userLoginData = await userLoginResponse.json();
    console.log('✅ 新用户登录成功');
    
    const userToken = userLoginData.tokens?.access?.token;
    
    // 4. 修改密码
    const newPassword = 'newpassword456';
    console.log('🔄 修改密码...');
    
    const changePasswordResponse = await fetch(`${API_BASE_URL}/api/auth/change-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken}`
      },
      body: JSON.stringify({
        currentPassword: testUser.password,
        newPassword: newPassword
      })
    });
    
    if (!changePasswordResponse.ok) {
      const errorData = await changePasswordResponse.text();
      throw new Error(`修改密码失败: ${changePasswordResponse.status} - ${errorData}`);
    }
    
    console.log('✅ 密码修改成功');
    
    // 5. 验证原密码不能登录
    console.log('🚫 验证原密码不能登录...');
    const oldPasswordResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    if (oldPasswordResponse.ok) {
      throw new Error('原密码仍然可以登录，密码修改可能失败');
    }
    console.log('✅ 原密码已失效');
    
    // 6. 验证新密码可以登录
    console.log('🔐 验证新密码登录...');
    const newPasswordResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: newPassword
      })
    });
    
    if (!newPasswordResponse.ok) {
      const errorData = await newPasswordResponse.text();
      throw new Error(`新密码登录失败: ${newPasswordResponse.status} - ${errorData}`);
    }
    
    const newLoginData = await newPasswordResponse.json();
    console.log('✅ 新密码登录成功!');
    console.log('👤 用户信息:', {
      id: newLoginData.user?.id,
      username: newLoginData.user?.username,
      email: newLoginData.user?.email
    });
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 运行测试
testChangePassword()
  .then(success => {
    if (success) {
      console.log('\n🎉 用户修改密码功能测试通过！');
    } else {
      console.log('\n💥 用户修改密码功能测试失败。');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 测试执行出错:', error);
    process.exit(1);
  }); 