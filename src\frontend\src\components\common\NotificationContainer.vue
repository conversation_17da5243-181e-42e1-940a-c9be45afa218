<template>
  <div class="fixed top-4 right-4 z-50 space-y-3 max-w-md w-full pointer-events-none">
    <transition-group name="notification">
      <div
        v-for="notification in sortedNotifications"
        :key="notification.id"
        class="notification-item pointer-events-auto rounded-lg shadow-lg overflow-hidden"
        :class="[getNotificationClass(notification.type)]"
      >
        <div class="p-4">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <component :is="getNotificationIcon(notification.type)" class="h-6 w-6" />
            </div>
            <div class="ml-3 w-0 flex-1 pt-0.5">
              <p class="text-sm font-medium">{{ notification.message }}</p>
              <p v-if="notification.description" class="mt-1 text-sm opacity-80">{{ notification.description }}</p>
            </div>
            <div class="ml-4 flex-shrink-0 flex">
              <button
                v-if="notification.dismissible !== false"
                @click="removeNotification(notification.id)"
                class="inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <span class="sr-only">关闭</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div
          v-if="notification.type !== 'error' && notification.timeout > 0"
          class="notification-progress-bar"
          :style="{ animationDuration: `${notification.timeout}ms` }"
        ></div>
      </div>
    </transition-group>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useNotificationStore } from '@/stores/notification';
import notificationService from '../../services/notification.service';

// 使用通知存储
const notificationStore = useNotificationStore();

// 从服务中获取状态 (兼容旧版API)
const notifications = computed(() => notificationService.state.notifications);

// 按时间戳排序的通知列表（最新的显示在最上面）
const sortedNotifications = computed(() => notificationStore.sortedNotifications);

// 移除通知的方法
const removeNotification = (id) => {
  notificationStore.removeNotification(id);
};

// 获取通知类型的CSS类
const getNotificationClass = (type) => {
  switch (type) {
    case 'success':
      return 'bg-green-50 border-l-4 border-green-500';
    case 'error':
      return 'bg-red-50 border-l-4 border-red-500';
    case 'warning':
      return 'bg-yellow-50 border-l-4 border-yellow-500';
    case 'info':
    default:
      return 'bg-blue-50 border-l-4 border-blue-500';
  }
};

// 获取通知类型的图标组件
const getNotificationIcon = (type) => {
  switch (type) {
    case 'success':
      return {
        template: `
          <svg class="text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        `
      };
    case 'error':
      return {
        template: `
          <svg class="text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        `
      };
    case 'warning':
      return {
        template: `
          <svg class="text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        `
      };
    case 'info':
    default:
      return {
        template: `
          <svg class="text-blue-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        `
      };
  }
};
</script>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.notification-item {
  position: relative;
  max-width: 100%;
}

.notification-progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  background-color: rgba(0, 0, 0, 0.1);
  width: 100%;
  animation: progress-bar-shrink 5000ms linear forwards;
}

@keyframes progress-bar-shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
</style>