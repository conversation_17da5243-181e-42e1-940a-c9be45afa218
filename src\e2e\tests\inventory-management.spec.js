// @ts-check
import { test, expect } from '@playwright/test';

test.describe('库存管理流程', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('库存列表页面', async ({ page }) => {
    // 访问库存列表页面
    await page.goto('/inventory');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('库存列表');

    // 验证库存表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加库存按钮存在
    expect(await page.isVisible('a[href="/inventory/create"]')).toBeTruthy();
  });

  test('创建库存项流程', async ({ page }) => {
    // 访问创建库存页面
    await page.goto('/inventory/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('添加库存');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 填写表单
    const itemName = `测试物品 ${Date.now()}`;
    await page.fill('input[name="name"]', itemName);
    
    // 填写SKU编码
    await page.fill('input[name="sku"]', `SKU-${Date.now()}`);
    
    // 选择类别
    if (await page.isVisible('select[name="categoryId"]')) {
      await page.selectOption('select[name="categoryId"]', { index: 1 });
    }
    
    // 设置数量
    await page.fill('input[name="quantity"]', '100');
    
    // 设置单位
    if (await page.isVisible('input[name="unit"]')) {
      await page.fill('input[name="unit"]', '个');
    }
    
    // 设置单价
    if (await page.isVisible('input[name="unitPrice"]')) {
      await page.fill('input[name="unitPrice"]', '99.99');
    }
    
    // 设置位置
    if (await page.isVisible('input[name="location"]')) {
      await page.fill('input[name="location"]', 'A-01-01');
    }
    
    // 添加描述
    if (await page.isVisible('textarea[name="description"]')) {
      await page.fill('textarea[name="description"]', '这是一个由E2E测试创建的测试物品');
    }

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到库存列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/inventory');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
    
    // 验证新物品已添加到列表
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain(itemName);
  });

  test('查看库存详情', async ({ page }) => {
    // 访问库存列表页面
    await page.goto('/inventory');

    // 点击第一个物品的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/inventory/');

    // 验证详情页面内容
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('库存详情');
    expect(await page.isVisible('.inventory-info')).toBeTruthy();
    expect(await page.isVisible('a.edit-button')).toBeTruthy();
  });

  test('编辑库存流程', async ({ page }) => {
    // 访问库存列表页面
    await page.goto('/inventory');

    // 点击第一个编辑按钮
    await page.click('table tbody tr:first-child a.edit-button');

    // 等待页面跳转到编辑页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/inventory/');
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/edit');

    // 验证表单已预填充
    expect(await page.inputValue('input[name="name"]')).toBeTruthy();

    // 修改库存名称
    const updatedName = `更新的物品 ${Date.now()}`;
    await page.fill('input[name="name"]', updatedName);

    // 修改数量
    await page.fill('input[name="quantity"]', '150');

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到库存列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/inventory');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
    
    // 验证物品已更新
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain(updatedName);
  });

  test('库存搜索功能', async ({ page }) => {
    // 访问库存列表页面
    await page.goto('/inventory');

    // 在搜索框中输入关键字
    await page.fill('input[placeholder*="搜索"]', 'SKU-');

    // 点击搜索按钮
    await page.click('button.search-button');

    // 等待搜索结果加载
    await page.waitForTimeout(1000);

    // 验证搜索结果
    const skuCodes = await page.locator('table tbody tr td:nth-child(2)').allTextContents();
    for (const sku of skuCodes) {
      expect(sku).toContain('SKU-');
    }
  });

  test('库存类别筛选功能', async ({ page }) => {
    // 访问库存列表页面
    await page.goto('/inventory');

    // 如果存在类别筛选器
    if (await page.isVisible('select.category-filter')) {
      // 选择类别
      await page.selectOption('select.category-filter', { index: 1 });

      // 等待筛选结果加载
      await page.waitForTimeout(1000);

      // 验证筛选结果存在
      expect(await page.locator('table tbody tr').count()).toBeGreaterThan(0);
    }
  });

  test('库存入库流程', async ({ page }) => {
    // 访问库存列表页面
    await page.goto('/inventory');

    // 点击第一个物品的操作按钮
    await page.click('table tbody tr:first-child button.action-button');

    // 点击入库选项
    await page.click('button:has-text("入库")');

    // 等待入库模态框显示
    await page.waitForSelector('.modal');

    // 填写入库数量
    await page.fill('input[name="quantity"]', '50');

    // 填写备注
    if (await page.isVisible('textarea[name="notes"]')) {
      await page.fill('textarea[name="notes"]', '测试入库操作');
    }

    // 确认入库
    await page.click('button:has-text("确认")');

    // 等待操作完成
    await page.waitForTimeout(1000);

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('库存出库流程', async ({ page }) => {
    // 访问库存列表页面
    await page.goto('/inventory');

    // 点击第一个物品的操作按钮
    await page.click('table tbody tr:first-child button.action-button');

    // 点击出库选项
    await page.click('button:has-text("出库")');

    // 等待出库模态框显示
    await page.waitForSelector('.modal');

    // 填写出库数量
    await page.fill('input[name="quantity"]', '10');

    // 选择项目(如果需要)
    if (await page.isVisible('select[name="projectId"]')) {
      await page.selectOption('select[name="projectId"]', { index: 1 });
    }

    // 填写备注
    if (await page.isVisible('textarea[name="notes"]')) {
      await page.fill('textarea[name="notes"]', '测试出库操作');
    }

    // 确认出库
    await page.click('button:has-text("确认")');

    // 等待操作完成
    await page.waitForTimeout(1000);

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('库存历史记录查看', async ({ page }) => {
    // 访问库存列表页面
    await page.goto('/inventory');

    // 点击第一个物品的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();

    // 点击历史记录选项卡(如果存在)
    if (await page.isVisible('button:has-text("历史记录")')) {
      await page.click('button:has-text("历史记录")');
      
      // 验证历史记录表格存在
      expect(await page.isVisible('.history-table')).toBeTruthy();
      
      // 验证至少有一条历史记录
      expect(await page.locator('.history-table tbody tr').count()).toBeGreaterThan(0);
    }
  });
});
