const Joi = require('joi');
const { objectId, dateFormat } = require('./custom.validation');

const createProductTransaction = {
  body: Joi.object().keys({
    productId: Joi.string().custom(objectId).required().messages({
      'string.empty': '产品ID不能为空',
      'any.required': '产品ID是必填项'
    }),
    projectId: Joi.string().custom(objectId).messages({
      'string.empty': '项目ID不能为空'
    }),
    type: Joi.string().valid('in', 'out', 'adjustment').required().messages({
      'string.empty': '交易类型不能为空',
      'any.only': '交易类型必须是入库(in)、出库(out)或调整(adjustment)',
      'any.required': '交易类型是必填项'
    }),
    quantity: Joi.number().integer().min(1).required().messages({
      'number.base': '数量必须是数字',
      'number.integer': '数量必须是整数',
      'number.min': '数量不能小于{#limit}',
      'any.required': '数量是必填项'
    }),
    unitPrice: Joi.number().min(0).required().messages({
      'number.base': '单价必须是数字',
      'number.min': '单价不能小于{#limit}',
      'any.required': '单价是必填项'
    }),
    reference: Joi.string().allow('', null),
    notes: Joi.string().allow('', null),
    transactionDate: Joi.date().custom(dateFormat).default(new Date()).messages({
      'date.base': '交易日期必须是有效的日期格式',
      'any.custom': '日期必须使用年-月-日格式 (YYYY-MM-DD)'
    })
  })
};

// 入库交易验证规则
const createStockInTransaction = {
  body: Joi.object().keys({
    productId: Joi.string().custom(objectId).required().messages({
      'string.empty': '产品ID不能为空',
      'any.required': '产品ID是必填项'
    }),
    projectId: Joi.string().custom(objectId).messages({
      'string.empty': '项目ID不能为空'
    }),
    quantity: Joi.number().integer().min(1).required().messages({
      'number.base': '入库数量必须是数字',
      'number.integer': '入库数量必须是整数',
      'number.min': '入库数量不能小于{#limit}',
      'any.required': '入库数量是必填项'
    }),
    unitPrice: Joi.number().min(0).required().messages({
      'number.base': '单价必须是数字',
      'number.min': '单价不能小于{#limit}',
      'any.required': '单价是必填项'
    }),
    reference: Joi.string().allow('', null).messages({
      'string.empty': '参考编号不能为空'
    }),
    notes: Joi.string().allow('', null).messages({
      'string.empty': '备注不能为空'
    }),
    transactionDate: Joi.date().custom(dateFormat).default(new Date()).messages({
      'date.base': '入库日期必须是有效的日期格式',
      'any.custom': '日期必须使用年-月-日格式 (YYYY-MM-DD)'
    })
  })
};

// 出库交易验证规则
const createStockOutTransaction = {
  body: Joi.object().keys({
    productId: Joi.string().custom(objectId).required().messages({
      'string.empty': '产品ID不能为空',
      'any.required': '产品ID是必填项'
    }),
    projectId: Joi.string().custom(objectId).messages({
      'string.empty': '项目ID不能为空'
    }),
    quantity: Joi.number().integer().min(1).required().messages({
      'number.base': '出库数量必须是数字',
      'number.integer': '出库数量必须是整数',
      'number.min': '出库数量不能小于{#limit}',
      'any.required': '出库数量是必填项'
    }),
    unitPrice: Joi.number().min(0).required().messages({
      'number.base': '单价必须是数字',
      'number.min': '单价不能小于{#limit}',
      'any.required': '单价是必填项'
    }),
    reference: Joi.string().allow('', null).messages({
      'string.empty': '参考编号不能为空'
    }),
    notes: Joi.string().allow('', null).messages({
      'string.empty': '备注不能为空'
    }),
    transactionDate: Joi.date().custom(dateFormat).default(new Date()).messages({
      'date.base': '出库日期必须是有效的日期格式',
      'any.custom': '日期必须使用年-月-日格式 (YYYY-MM-DD)'
    })
  })
};

const getProductTransaction = {
  params: Joi.object().keys({
    transactionId: Joi.string().custom(objectId).required().messages({
      'string.empty': '交易ID不能为空',
      'any.required': '交易ID是必填项'
    })
  })
};

const getProductTransactions = {
  query: Joi.object().keys({
    productId: Joi.string().custom(objectId).messages({
      'string.empty': '产品ID不能为空'
    }),
    productName: Joi.string().messages({
      'string.empty': '产品名称不能为空'
    }),
    productCode: Joi.string().messages({
      'string.empty': '产品编码不能为空'
    }),
    projectId: Joi.string().custom(objectId).messages({
      'string.empty': '项目ID不能为空'
    }),
    type: Joi.string().valid('in', 'out', 'adjustment').messages({
      'string.empty': '交易类型不能为空',
      'any.only': '交易类型必须是入库(in)、出库(out)或调整(adjustment)'
    }),
    reference: Joi.string().messages({
      'string.empty': '参考编号不能为空'
    }),
    transactionDateFrom: Joi.date().messages({
      'date.base': '开始日期必须是有效的日期格式'
    }),
    transactionDateTo: Joi.date().messages({
      'date.base': '结束日期必须是有效的日期格式'
    }),
    sortBy: Joi.string().default('transactionDate').messages({
      'string.empty': '排序字段不能为空'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    }),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于{#limit}'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    })
  })
};

const getProductTransactionsForProduct = {
  params: Joi.object().keys({
    productId: Joi.string().custom(objectId).required().messages({
      'string.empty': '产品ID不能为空',
      'any.required': '产品ID是必填项'
    })
  }),
  query: Joi.object().keys({
    type: Joi.string().valid('in', 'out', 'adjustment').messages({
      'string.empty': '交易类型不能为空',
      'any.only': '交易类型必须是入库(in)、出库(out)或调整(adjustment)'
    }),
    transactionDateFrom: Joi.date().messages({
      'date.base': '开始日期必须是有效的日期格式'
    }),
    transactionDateTo: Joi.date().messages({
      'date.base': '结束日期必须是有效的日期格式'
    }),
    sortBy: Joi.string().default('transactionDate').messages({
      'string.empty': '排序字段不能为空'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    }),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于{#limit}'
    }),
    limit: Joi.number().integer().min(1).max(100).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    })
  })
};

module.exports = {
  createProductTransaction,
  createStockInTransaction,   // 新增：入库交易验证
  createStockOutTransaction,  // 新增：出库交易验证
  getProductTransaction,
  getProductTransactions,
  getProductTransactionsForProduct
};