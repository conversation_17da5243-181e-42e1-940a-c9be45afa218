const httpStatus = require('http-status');
const { Tag } = require('../models');
const ApiError = require('../utils/ApiError');

/**
 * Create a tag
 * @param {Object} tagBody
 * @returns {Promise<Tag>}
 */
const createTag = async (tagBody) => {
  return Tag.create(tagBody);
};

/**
 * Query for tags
 * @param {Object} filter - Sequelize filter
 * @param {Object} options - Query options
 * @param {string} [options.sortBy] - Sort option in the format: sortField:(asc|desc)
 * @param {number} [options.limit] - Maximum number of results per page
 * @param {number} [options.page] - Current page
 * @returns {Promise<Object>}
 */
const queryTags = async (filter, options) => {
  const { limit, offset } = options;
  
  const tags = await Tag.findAndCountAll({
    where: filter,
    limit,
    offset,
    order: options.sortBy ? [[options.sortBy.split(':')[0], options.sortBy.split(':')[1]]] : [['name', 'ASC']],
  });
  
  return {
    results: tags.rows,
    page: options.page,
    limit: options.limit,
    totalPages: Math.ceil(tags.count / options.limit),
    totalResults: tags.count,
  };
};

/**
 * Get all tags (no pagination)
 * @returns {Promise<Array<Tag>>}
 */
const getAllTags = async () => {
  return Tag.findAll({
    order: [['name', 'ASC']],
  });
};

/**
 * Get tag by id
 * @param {string} id
 * @returns {Promise<Tag>}
 */
const getTagById = async (id) => {
  return Tag.findByPk(id);
};

/**
 * Update tag by id
 * @param {string} tagId
 * @param {Object} updateBody
 * @returns {Promise<Tag>}
 */
const updateTagById = async (tagId, updateBody) => {
  const tag = await getTagById(tagId);
  if (!tag) {
    throw new ApiError(404, 'Tag not found');
  }
  
  Object.assign(tag, updateBody);
  await tag.save();
  return tag;
};

/**
 * Delete tag by id
 * @param {string} tagId
 * @returns {Promise<void>}
 */
const deleteTagById = async (tagId) => {
  const tag = await getTagById(tagId);
  if (!tag) {
    throw new ApiError(404, 'Tag not found');
  }
  await tag.destroy();
};

module.exports = {
  createTag,
  queryTags,
  getAllTags,
  getTagById,
  updateTagById,
  deleteTagById,
};