// @ts-check
import { test, expect } from '@playwright/test';

test.describe('考勤管理流程', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('考勤记录列表页面', async ({ page }) => {
    // 访问考勤记录列表页面
    await page.goto('/attendance');

    // 验证页面标题
    const h1s = await page.$$('h1');
    for (const h1 of h1s) {
      expect(await h1.textContent()).toContain('考勤记录');
    }

    // 验证考勤表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加考勤按钮存在
    expect(await page.isVisible('a[href="/attendance/create"], button:has-text("添加考勤")')).toBeTruthy();
  });

  test('创建考勤记录流程', async ({ page }) => {
    // 访问创建考勤页面
    await page.goto('/attendance/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('添加考勤');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 选择员工
    if (await page.isVisible('select[name="employeeId"]')) {
      await page.selectOption('select[name="employeeId"]', { index: 1 });
    }
    
    // 设置日期
    const attendanceDate = new Date();
    await page.fill('input[name="date"]', attendanceDate.toISOString().split('T')[0]);
    
    // 设置签到时间
    await page.fill('input[name="checkInTime"]', '09:00');
    
    // 设置签退时间
    await page.fill('input[name="checkOutTime"]', '18:00');
    
    // 选择状态
    if (await page.isVisible('select[name="status"]')) {
      await page.selectOption('select[name="status"]', 'normal');
    }
    
    // 添加备注
    if (await page.isVisible('textarea[name="notes"]')) {
      await page.fill('textarea[name="notes"]', '这是一个由E2E测试创建的考勤记录');
    }

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到考勤列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/attendance');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('批量导入考勤记录', async ({ page }) => {
    // 访问考勤记录列表页面
    await page.goto('/attendance');

    // 点击批量导入按钮
    if (await page.isVisible('button:has-text("批量导入")')) {
      await page.click('button:has-text("批量导入")');
      
      // 等待模态框显示
      await page.waitForSelector('.modal');
      
      // 验证上传区域存在
      expect(await page.isVisible('input[type="file"]')).toBeTruthy();
      
      // 验证模板下载链接存在
      expect(await page.isVisible('a:has-text("下载模板")')).toBeTruthy();
    }
  });

  test('考勤统计报表', async ({ page }) => {
    // 访问考勤统计页面
    await page.goto('/attendance/statistics');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('考勤统计');

    // 设置开始日期
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 1);
    await page.fill('input[name="startDate"]', startDate.toISOString().split('T')[0]);
    
    // 设置结束日期
    const endDate = new Date();
    await page.fill('input[name="endDate"]', endDate.toISOString().split('T')[0]);
    
    // 选择部门(如果存在)
    if (await page.isVisible('select[name="departmentId"]')) {
      await page.selectOption('select[name="departmentId"]', { index: 1 });
    }
    
    // 点击查询按钮
    await page.click('button:has-text("查询")');
    
    // 等待结果加载
    await page.waitForTimeout(1000);
    
    // 验证统计结果存在
    expect(await page.isVisible('.statistics-table')).toBeTruthy();
    
    // 验证导出按钮存在
    expect(await page.isVisible('button:has-text("导出报表")')).toBeTruthy();
  });

  test('考勤异常记录查看', async ({ page }) => {
    // 访问考勤异常页面
    await page.goto('/attendance/exceptions');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('考勤异常');

    // 验证异常记录表格存在
    expect(await page.isVisible('table')).toBeTruthy();
    
    // 选择日期范围
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 1);
    await page.fill('input[name="startDate"]', startDate.toISOString().split('T')[0]);
    
    const endDate = new Date();
    await page.fill('input[name="endDate"]', endDate.toISOString().split('T')[0]);
    
    // 点击查询按钮
    await page.click('button:has-text("查询")');
    
    // 等待结果加载
    await page.waitForTimeout(1000);
  });

  test('处理考勤异常', async ({ page }) => {
    // 访问考勤异常页面
    await page.goto('/attendance/exceptions');
    
    // 如果存在异常记录，点击处理按钮
    if (await page.isVisible('table tbody tr')) {
      await page.click('table tbody tr:first-child button.process-button');
      
      // 等待处理模态框显示
      await page.waitForSelector('.modal');
      
      // 选择处理结果
      if (await page.isVisible('select[name="result"]')) {
        await page.selectOption('select[name="result"]', 'approved');
      }
      
      // 添加处理备注
      await page.fill('textarea[name="notes"]', '测试处理考勤异常');
      
      // 确认处理
      await page.click('button:has-text("确认")');
      
      // 等待操作完成
      await page.waitForTimeout(1000);
      
      // 验证成功消息显示
      expect(await page.isVisible('.success-message')).toBeTruthy();
    }
  });

  test('员工考勤申请流程', async ({ page }) => {
    // 访问考勤申请页面
    await page.goto('/attendance/applications');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('考勤申请');

    // 点击创建申请按钮
    await page.click('button:has-text("创建申请")');
    
    // 等待申请表单显示
    await page.waitForSelector('form');
    
    // 选择申请类型
    if (await page.isVisible('select[name="type"]')) {
      await page.selectOption('select[name="type"]', 'leave');
    }
    
    // 设置开始日期时间
    await page.fill('input[name="startDate"]', new Date().toISOString().split('T')[0]);
    await page.fill('input[name="startTime"]', '09:00');
    
    // 设置结束日期时间
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 1);
    await page.fill('input[name="endDate"]', endDate.toISOString().split('T')[0]);
    await page.fill('input[name="endTime"]', '18:00');
    
    // 选择请假类型(如果存在)
    if (await page.isVisible('select[name="leaveType"]')) {
      await page.selectOption('select[name="leaveType"]', 'annual');
    }
    
    // 添加申请原因
    await page.fill('textarea[name="reason"]', '测试考勤申请流程');
    
    // 提交申请
    await page.click('button[type="submit"]');
    
    // 等待操作完成
    await page.waitForTimeout(1000);
    
    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('考勤申请审批流程', async ({ page }) => {
    // 访问考勤审批页面
    await page.goto('/attendance/approvals');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('考勤审批');

    // 验证审批表格存在
    expect(await page.isVisible('table')).toBeTruthy();
    
    // 如果存在待审批记录，点击审批按钮
    if (await page.isVisible('table tbody tr button.approve-button')) {
      await page.click('table tbody tr:first-child button.approve-button');
      
      // 等待审批模态框显示
      await page.waitForSelector('.modal');
      
      // 选择审批结果
      if (await page.isVisible('select[name="result"]')) {
        await page.selectOption('select[name="result"]', 'approved');
      }
      
      // 添加审批意见
      await page.fill('textarea[name="comment"]', '测试审批通过');
      
      // 确认审批
      await page.click('button:has-text("确认")');
      
      // 等待操作完成
      await page.waitForTimeout(1000);
      
      // 验证成功消息显示
      expect(await page.isVisible('.success-message')).toBeTruthy();
    }
  });

  test('考勤设置页面', async ({ page }) => {
    // 访问考勤设置页面
    await page.goto('/attendance/settings');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('考勤设置');

    // 验证设置表单存在
    expect(await page.isVisible('form')).toBeTruthy();
    
    // 验证工作时间设置存在
    expect(await page.isVisible('input[name="workStartTime"]')).toBeTruthy();
    expect(await page.isVisible('input[name="workEndTime"]')).toBeTruthy();
    
    // 验证保存按钮存在
    expect(await page.isVisible('button[type="submit"]')).toBeTruthy();
  });
});
