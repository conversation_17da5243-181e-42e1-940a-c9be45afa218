<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Console Checker</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
      background-color: #f9f9f9;
    }
    .button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    .button.secondary {
      background-color: #2196F3;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    .error {
      color: #d32f2f;
    }
    .warning {
      color: #f57c00;
    }
    .info {
      color: #0288d1;
    }
    .log {
      color: #333;
    }
  </style>
</head>
<body>
  <h1>Console Checker</h1>
  
  <div class="card">
    <h2>Current Data Source</h2>
    <p>Current data source information:</p>
    <pre id="dataSourceInfo">Checking...</pre>
  </div>
  
  <div class="card">
    <h2>Console Logs</h2>
    <p>Click the button below to check console logs:</p>
    <button id="checkConsole" class="button">Check Console Logs</button>
    <pre id="consoleResult">Results will appear here...</pre>
  </div>
  
  <div class="card">
    <h2>Network Requests</h2>
    <p>Click the button below to check network requests:</p>
    <button id="checkNetwork" class="button">Check Network Requests</button>
    <pre id="networkResult">Results will appear here...</pre>
  </div>
  
  <script>
    // Function to check data source
    function checkDataSource() {
      const dataSourceInfoElement = document.getElementById('dataSourceInfo');
      
      try {
        // Try to get data source from localStorage
        const dataSource = localStorage.getItem('app_data_source') || 'default (API)';
        
        // Try to get API URL from environment variables
        let apiUrl = '';
        try {
          apiUrl = window.parent.axios?.defaults?.baseURL || 'Unknown';
        } catch (e) {
          apiUrl = 'Could not access parent window';
        }
        
        dataSourceInfoElement.textContent = `Data Source: ${dataSource}\nAPI URL: ${apiUrl}`;
        
        // Try to get window.__currentDataSource if available
        try {
          const currentDataSource = window.parent.__currentDataSource;
          if (currentDataSource) {
            dataSourceInfoElement.textContent += `\nCurrent Data Source (from global variable): ${currentDataSource}`;
          }
        } catch (e) {
          // Ignore errors
        }
      } catch (e) {
        dataSourceInfoElement.textContent = `Error checking data source: ${e.message}`;
      }
    }
    
    // Function to check console logs
    document.getElementById('checkConsole').addEventListener('click', function() {
      const resultElement = document.getElementById('consoleResult');
      resultElement.textContent = 'Checking console logs...';
      
      // Create a new console logger
      const logs = [];
      const originalConsoleLog = console.log;
      const originalConsoleError = console.error;
      const originalConsoleWarn = console.warn;
      const originalConsoleInfo = console.info;
      
      // Override console methods to capture logs
      console.log = function() {
        logs.push({ type: 'log', args: Array.from(arguments) });
        originalConsoleLog.apply(console, arguments);
      };
      
      console.error = function() {
        logs.push({ type: 'error', args: Array.from(arguments) });
        originalConsoleError.apply(console, arguments);
      };
      
      console.warn = function() {
        logs.push({ type: 'warn', args: Array.from(arguments) });
        originalConsoleWarn.apply(console, arguments);
      };
      
      console.info = function() {
        logs.push({ type: 'info', args: Array.from(arguments) });
        originalConsoleInfo.apply(console, arguments);
      };
      
      // Force some console logs to test
      console.log('Console logger is working');
      console.error('This is a test error');
      console.warn('This is a test warning');
      console.info('This is test info');
      
      // Format logs for display
      let formattedLogs = '';
      logs.forEach(log => {
        try {
          const args = log.args.map(arg => {
            if (typeof arg === 'object') {
              return JSON.stringify(arg, null, 2);
            }
            return String(arg);
          }).join(' ');
          
          formattedLogs += `<span class="${log.type}">[${log.type.toUpperCase()}] ${args}</span>\n`;
        } catch (e) {
          formattedLogs += `<span class="${log.type}">[${log.type.toUpperCase()}] [Could not format log]</span>\n`;
        }
      });
      
      // Restore original console methods
      console.log = originalConsoleLog;
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
      console.info = originalConsoleInfo;
      
      resultElement.innerHTML = formattedLogs || 'No console logs found';
    });
    
    // Function to check network requests
    document.getElementById('checkNetwork').addEventListener('click', function() {
      const resultElement = document.getElementById('networkResult');
      resultElement.textContent = 'Checking network requests...';
      
      // Create a wrapper for the fetch API to log all requests
      const requests = [];
      const originalFetch = window.fetch;
      
      window.fetch = function(url, options) {
        const request = { url, options, timestamp: new Date().toISOString() };
        requests.push(request);
        
        return originalFetch.apply(this, arguments)
          .then(response => {
            request.status = response.status;
            request.ok = response.ok;
            return response;
          })
          .catch(error => {
            request.error = error.message;
            throw error;
          });
      };
      
      // Make a test request
      fetch('/api/health-check')
        .then(response => response.text())
        .catch(error => console.error('Test request failed:', error))
        .finally(() => {
          // Format requests for display
          let formattedRequests = '';
          requests.forEach(request => {
            formattedRequests += `URL: ${request.url}\n`;
            formattedRequests += `Timestamp: ${request.timestamp}\n`;
            formattedRequests += `Method: ${request.options?.method || 'GET'}\n`;
            
            if (request.status) {
              formattedRequests += `Status: ${request.status} (${request.ok ? 'OK' : 'Error'})\n`;
            }
            
            if (request.error) {
              formattedRequests += `Error: ${request.error}\n`;
            }
            
            formattedRequests += '-------------------\n';
          });
          
          // Restore original fetch
          window.fetch = originalFetch;
          
          resultElement.textContent = formattedRequests || 'No network requests found';
        });
    });
    
    // Check data source on page load
    window.addEventListener('DOMContentLoaded', checkDataSource);
  </script>
</body>
</html>
