<template>
  <div class="page-container">
    <!-- 页面标题和操作按钮同一行 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center">
        <router-link to="/departments" class="back-button">

        </router-link>
        <h1 class="page-title text-2xl font-bold text-gray-800">创建新部门</h1>
      </div>
      <div class="flex space-x-4">
        <router-link to="/departments" class="btn btn-secondary flex items-center">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          返回列表
        </router-link>
      </div>
    </div>
    <p class="page-subtitle text-sm text-gray-500 mb-6">创建新的部门记录</p>

    <!-- 错误提示 -->
    <div v-if="formError" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ formError }}</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="card shadow-md p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">加载中...</p>
    </div>

    <!-- 表单区域 -->
    <div v-else class="card shadow-md">
      <form id="department-form" @submit.prevent="submitForm" class="space-y-6">
        <!-- 基本信息 -->
        <div class="form-section">
          <h2 class="form-section-title">基本信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="name" class="form-label">部门名称 <span class="form-required">*</span></label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                class="form-input"
                :class="{ 'border-red-500': errors.name }"
                placeholder="请输入部门名称"
                required
                oninvalid="this.setCustomValidity('请输入部门名称')"
                oninput="this.setCustomValidity('')"
              />
              <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
            </div>

            <div>
              <label for="code" class="form-label">部门代码 <span class="form-required">*</span></label>
              <input
                id="code"
                v-model="form.code"
                type="text"
                class="form-input"
                :class="{ 'border-red-500': errors.code }"
                placeholder="如：HR、IT、FIN等"
                required
                oninvalid="this.setCustomValidity('请输入部门代码')"
                oninput="this.setCustomValidity('')"
              />
              <p v-if="errors.code" class="mt-1 text-sm text-red-600">{{ errors.code }}</p>
              <p class="mt-1 text-sm text-gray-500">请使用简短的唯一代码标识此部门（必填项）</p>
            </div>
          </div>
        </div>

        <!-- 部门详情 -->
        <div class="form-section">
          <h2 class="form-section-title">部门详情</h2>
          <div class="grid grid-cols-1 gap-6">
            <div>
              <label for="description" class="form-label">部门描述</label>
              <textarea
                id="description"
                v-model="form.description"
                rows="3"
                class="form-input"
                placeholder="请输入部门职责描述"
              ></textarea>
            </div>

            <div>
              <label for="parentId" class="form-label">上级部门</label>
              <select
                id="parentId"
                v-model="form.parentId"
                class="form-input"
              >
                <option value="">无上级部门</option>
                <option v-for="dept in departments" :key="dept.id" :value="dept.id">
                  {{ dept.name }}
                </option>
              </select>
            </div>

            <div>
              <label for="status" class="form-label">状态</label>
              <select
                id="status"
                v-model="form.status"
                class="form-input"
              >
                <option value="active">活跃</option>
                <option value="inactive">禁用</option>
              </select>
            </div>
          </div>
        </div>
        
        <!-- Form buttons -->
        <div class="flex justify-end space-x-4 mt-6">
          <button type="button" @click="resetForm" class="btn btn-secondary">重置</button>
          <button type="submit" class="btn btn-primary">提交部门</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useDepartmentStore } from '@/stores/department';
import axios from 'axios';
import { useAuthStore } from '@/stores/auth';
import { useToast } from 'vue-toastification';

const router = useRouter();
const toast = useToast();
const departmentStore = useDepartmentStore();
const authStore = useAuthStore();
const API_URL = import.meta.env.VITE_API_URL || '';

// Form state
const form = reactive({
  name: '',
  code: '',
  description: '',
  parentId: '',
  status: 'active',
});

// UI state
const loading = ref(false);
const formError = ref('');
const errors = reactive({
  name: '',
  code: '',
});
const departments = computed(() => departmentStore.getDepartments);

// Methods
function validateForm() {
  let isValid = true;
  errors.name = '';
  errors.code = '';

  if (!form.name.trim()) {
    errors.name = '部门名称为必填项，请输入部门名称';
    isValid = false;
  } else if (form.name.trim().length > 50) {
    errors.name = '部门名称不能超过50个字符';
    isValid = false;
  }

  if (!form.code.trim()) {
    errors.code = '部门代码为必填项，请输入部门代码';
    isValid = false;
  } else if (!/^[A-Za-z0-9_-]+$/.test(form.code)) {
    errors.code = '部门代码格式不正确，只能包含字母、数字、下划线和连字符';
    isValid = false;
  } else if (form.code.trim().length > 20) {
    errors.code = '部门代码不能超过20个字符';
    isValid = false;
  }

  return isValid;
}

async function submitForm() {
  if (!validateForm()) return;

  loading.value = true;
  formError.value = '';

  try {
    // Prepare data
    const departmentData = {
      ...form,
      parentId: form.parentId || null,
    };

    // Create department
    await departmentStore.createDepartment(departmentData);
    
    // 显示创建成功的提示信息
    toast.success(`部门 ${departmentData.name} 创建成功`);
    
    // Navigate to departments list
    router.push('/departments');
  } catch (error) {
    console.error('Error creating department:', error);
    formError.value = error.response?.data?.message || '创建部门时发生错误';
    toast.error(formError.value);
  } finally {
    loading.value = false;
  }
}

function resetForm() {
  if (form.name || form.code || form.description || form.parentId || form.status !== 'active') {
    if (!confirm('确定要重置表单吗？\n\n所有已填写的内容将被清空，此操作无法撤销。\n\n点击"确定"继续重置，点击"取消"保留当前填写的内容。')) {
      return;
    }
  }
  
  form.name = '';
  form.code = '';
  form.description = '';
  form.parentId = '';
  form.status = 'active';
  errors.name = '';
  errors.code = '';
  formError.value = '';
}

function goBack() {
  // 检查表单是否有内容，如果有则确认是否放弃更改
  if (form.name || form.code || form.description || form.parentId || form.status !== 'active') {
    if (!confirm('您确定要返回列表页面吗？\n\n当前表单中的所有未保存更改将会丢失。\n\n点击"确定"返回列表页面，点击"取消"继续编辑。')) {
      return;
    }
  }
  router.push('/departments');
}

// Lifecycle hooks
onMounted(async () => {
  // Fetch departments
  await departmentStore.fetchDepartments();
});
</script>

<style scoped>
.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.page-header {
  @apply mb-6;
  animation: fadeSlideIn 0.5s ease-out;
}

.card {
  @apply bg-white rounded-xl p-6 mb-6;
  animation: fadeSlideIn 0.6s ease-out;
}

.form-section {
  @apply border-b border-gray-200 pb-6 mb-6 last:border-0 last:pb-0 last:mb-0;
}

.form-section-title {
  @apply text-lg font-semibold text-gray-700 mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50;
}

.form-required {
  @apply text-red-600;
}

.btn {
  @apply py-2 px-4 rounded-lg focus:outline-none focus:ring transition-all duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-200;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-gray-200;
}

.back-button {
  @apply mr-3 text-gray-600 hover:text-gray-900 transition-colors duration-200;
}
</style>