<template>
  <div class="page-container">
    <!-- 页面标题和按钮 -->
    <div class="page-header">
      <div class="flex items-center">
        <router-link to="/inventory" class="back-button">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
        </router-link>
        <h1 class="page-title">{{ isEdit ? '编辑产品' : '新增产品' }}</h1>
      </div>
      <div class="flex space-x-3">
        <button @click="submitForm" class="btn btn-primary shadow-sm hover:shadow-md transition-all duration-200" :disabled="loading">
          <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ isEdit ? '保存' : '创建' }}
        </button>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2">{{ error }}</p>
    </div>

    <!-- 表单区域 -->
    <div class="card shadow-md">
      <form @submit.prevent="submitForm" class="p-6 space-y-6">
        <div class="form-section">
          <h2 class="form-section-title">基本信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 产品名称 -->
            <div>
              <label for="name" class="form-label">产品名称</label>
              <input
                type="text"
                id="name"
                v-model="form.name"
                class="form-input"
                required
              />
            </div>

            <!-- 产品编码 -->
            <div>
              <label for="code" class="form-label">产品编码</label>
              <input
                type="text"
                id="code"
                v-model="form.code"
                class="form-input"
              />
              <p class="text-xs text-gray-500 mt-1">留空将自动生成编码</p>
            </div>

            <!-- 规格型号 -->
            <div>
              <label for="specification" class="form-label">规格型号</label>
              <input
                type="text"
                id="specification"
                v-model="form.specification"
                class="form-input"
              />
            </div>

            <!-- 单位 -->
            <div>
              <label for="unit" class="form-label">单位</label>
              <input
                type="text"
                id="unit"
                v-model="form.unit"
                class="form-input"
                required
              />
            </div>

            <!-- 供应商 -->
            <div>
              <label for="supplier" class="form-label">供应商</label>
              <input
                type="text"
                id="supplier"
                v-model="form.supplier"
                class="form-input"
              />
            </div>

            <!-- 产品分类 -->
            <div>
              <label for="category" class="form-label">产品分类</label>
              <select
                id="category"
                v-model="form.category"
                class="form-input"
              >
                <option value="原材料">原材料</option>
                <option value="半成品">半成品</option>
                <option value="成品">成品</option>
                <option value="办公用品">办公用品</option>
                <option value="其他">其他</option>
              </select>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h2 class="form-section-title">库存信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 初始库存 -->
            <div>
              <label for="quantity" class="form-label">初始库存数量</label>
              <input
                type="number"
                id="quantity"
                v-model.number="form.quantity"
                min="0"
                class="form-input"
              />
            </div>

            <!-- 最低库存 -->
            <div>
              <label for="minStock" class="form-label">最低库存</label>
              <input
                type="number"
                id="minStock"
                v-model.number="form.minStock"
                min="0"
                class="form-input"
              />
            </div>

            <!-- 最高库存 -->
            <div>
              <label for="maxStock" class="form-label">最高库存</label>
              <input
                type="number"
                id="maxStock"
                v-model.number="form.maxStock"
                min="0"
                class="form-input"
              />
            </div>

            <!-- 安全库存 -->
            <div>
              <label for="safetyStock" class="form-label">安全库存</label>
              <input
                type="number"
                id="safetyStock"
                v-model.number="form.safetyStock"
                min="0"
                class="form-input"
              />
            </div>
          </div>
        </div>

        <div class="form-section">
          <h2 class="form-section-title">存放信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- 仓库 -->
            <div>
              <label for="warehouse" class="form-label">仓库</label>
              <input
                type="text"
                id="warehouse"
                v-model="form.location.warehouse"
                class="form-input"
              />
            </div>

            <!-- 区域 -->
            <div>
              <label for="area" class="form-label">区域</label>
              <input
                type="text"
                id="area"
                v-model="form.location.area"
                class="form-input"
              />
            </div>

            <!-- 货架 -->
            <div>
              <label for="shelf" class="form-label">货架</label>
              <input
                type="text"
                id="shelf"
                v-model="form.location.shelf"
                class="form-input"
              />
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';

const router = useRouter();
const route = useRoute();

// 判断是编辑模式还是新增模式
const isEdit = computed(() => !!route.params.id);
const inventoryId = computed(() => route.params.id);

// 加载和错误状态
const loading = ref(false);
const error = ref(null);

// 表单数据
const form = ref({
  name: '',
  code: '',
  specification: '',
  unit: '个',
  supplier: '',
  category: '原材料',
  quantity: 0,
  minStock: 0,
  maxStock: 0,
  safetyStock: 0,
  location: {
    warehouse: '主仓库',
    area: 'A区',
    shelf: ''
  }
});

// 如果是编辑模式，获取产品详情
onMounted(async () => {
  if (isEdit.value) {
    await fetchInventoryDetail();
  }
});

// 获取产品详情
const fetchInventoryDetail = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await axios.get(`/api/inventory/${inventoryId.value}`);
    const inventoryData = response.data;

    // 填充表单数据
    form.value = {
      name: inventoryData.name || '',
      code: inventoryData.code || '',
      specification: inventoryData.specification || '',
      unit: inventoryData.unit || '个',
      supplier: inventoryData.supplier || '',
      category: inventoryData.category || '原材料',
      quantity: inventoryData.quantity || 0,
      minStock: inventoryData.minStock || 0,
      maxStock: inventoryData.maxStock || 0,
      safetyStock: inventoryData.safetyStock || 0,
      location: {
        warehouse: inventoryData.locations && inventoryData.locations[0] ? inventoryData.locations[0].warehouse : '主仓库',
        area: inventoryData.locations && inventoryData.locations[0] ? inventoryData.locations[0].area : 'A区',
        shelf: inventoryData.locations && inventoryData.locations[0] ? inventoryData.locations[0].shelf : ''
      }
    };
  } catch (err) {
    console.error('获取产品详情失败:', err);
    error.value = err.response?.data?.message || '获取产品详情失败，请重试';
  } finally {
    loading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  if (isEdit.value) {
    // 如果是编辑模式，重新获取产品详情
    fetchInventoryDetail();
  } else {
    // 如果是新增模式，重置为默认值
    form.value = {
      name: '',
      code: '',
      specification: '',
      unit: '个',
      supplier: '',
      category: '原材料',
      quantity: 0,
      minStock: 0,
      maxStock: 0,
      safetyStock: 0,
      location: {
        warehouse: '主仓库',
        area: 'A区',
        shelf: ''
      }
    };
  }
};

// 提交表单
const submitForm = async () => {
  loading.value = true;
  error.value = null;

  try {
    console.log('提交表单数据:', form.value);

    if (isEdit.value) {
      // 编辑模式：调用更新API
      await axios.patch(`/api/inventory/${inventoryId.value}`, form.value);
      console.log('更新产品成功!');
    } else {
      // 新增模式：调用创建API
      await axios.post('/api/inventory', form.value);
      console.log('添加产品成功!');
    }

    // 跳转回列表页面
    router.push('/inventory');
  } catch (err) {
    console.error(isEdit.value ? '更新产品失败:' : '添加产品失败:', err);
    error.value = err.response?.data?.message || (isEdit.value ? '更新产品失败，请重试' : '添加产品失败，请重试');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.page-header {
  @apply flex justify-between items-center mb-6;
}

.page-title {
  @apply text-2xl font-bold text-gray-900;
}

.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.card {
  @apply bg-white rounded-lg overflow-hidden;
}

.form-section {
  @apply mb-6;
}

.form-section-title {
  @apply text-lg font-medium text-gray-900 mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm
         focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50
         transition-all duration-200;
}

.btn {
  @apply px-4 py-2 rounded-md flex items-center text-sm font-medium transition-all duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 shadow;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300;
}
</style>