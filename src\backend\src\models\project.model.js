const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { User } = require('./user.model');

// Define Project model with graceful fallbacks for missing fields
const Project = sequelize.define('project', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  code: {
    type: DataTypes.STRING,
    unique: true,
    allowNull: false,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },

  manager: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  constructionUnit: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  designUnit: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  contractorUnit: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  supervisorUnit: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  reviewUnit: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  address: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  startDate: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'startDate'
  },
  endDate: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'endDate'
  },
  archiveNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'archiveNumber'
  },
  engineeringNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'engineeringNumber'
  },
  usageType: {
    type: DataTypes.ENUM('商业', '住宅', '工业', '公共建筑'),
    allowNull: false,
    field: 'usageType'
  },
  engineeringType: {
    type: DataTypes.ENUM('建筑工程', '消防工程', '年度检测', '竣工检测', '消防安全评估', '消防维保'),
    allowNull: false,
    field: 'engineeringType'
  },
  constructionPermitNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'constructionPermitNumber'
  },
  area: {
    type: DataTypes.FLOAT,
    allowNull: true,
    field: 'area'
  },
  specialSystem: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'specialSystem'
  },
  companyManager: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'companyManager'
  },
  status: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'planning',
    validate: {
      isIn: [['planning', 'planned', 'in_progress', 'completed', 'on_hold', 'cancelled', 'not_started']]
    },
    field: 'status'
  },
  projectNumber: {
    type: DataTypes.STRING,
    allowNull: true,
    unique: true,
    field: 'projectNumber'
  },
  progress: {
    type: DataTypes.INTEGER,
    validate: {
      min: 0,
      max: 100
    },
    defaultValue: 0,
    field: 'progress'
  },
  clientContact: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'clientContact'
  },
  tags: {
    type: DataTypes.ARRAY(DataTypes.STRING),
    defaultValue: [],
    field: 'tag'
  },
  members: {
    type: DataTypes.ARRAY(DataTypes.UUID),
    defaultValue: [],
    field: 'members'
  },
  createdBy: {
    type: DataTypes.UUID,
    allowNull: true, // Make this field optional to handle cases where it doesn't exist
    field: 'createdBy'
  }
}, {
  timestamps: true,
});

// Add Project 与 User 的关联，支持 as: 'Creator'
Project.belongsTo(User, { as: 'Creator', foreignKey: 'createdBy' });

module.exports = { Project };