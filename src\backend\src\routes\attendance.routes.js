const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const attendanceController = require('../controllers/attendance.controller');
const attendanceValidation = require('../validations/attendance.validation');

const router = express.Router();

router
  .route('/')
  .post(auth('manageAttendances'), validate(attendanceValidation.createAttendance), attendanceController.createAttendance)
  .get(auth('getAttendances'), validate(attendanceValidation.getAttendances), attendanceController.getAttendances);

router
  .route('/export')
  .get(auth('getAttendances'), validate(attendanceValidation.getAttendances), attendanceController.exportAttendances);

router
  .route('/:id')
  .get(auth('getAttendances'), validate(attendanceValidation.getAttendance), attendanceController.getAttendance)
  .patch(auth('manageAttendances'), validate(attendanceValidation.updateAttendance), attendanceController.updateAttendance)
  .delete(auth('manageAttendances'), validate(attendanceValidation.deleteAttendance), attendanceController.deleteAttendance);

module.exports = router; 