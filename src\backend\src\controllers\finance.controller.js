const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { financeService } = require('../services');

/**
 * Create a new finance record
 * @route POST /api/finances
 */
const createFinance = catchAsync(async (req, res) => {
  const finance = await financeService.createFinance({
    ...req.body,
    createdBy: req.user.id
  });
  res.status(201).send(finance);
});

/**
 * Get a finance record by ID
 * @route GET /api/finances/:financeId
 */
const getFinance = catchAsync(async (req, res) => {
  const finance = await financeService.getFinanceById(req.params.financeId);
  res.send(finance);
});

/**
 * Update a finance record
 * @route PATCH /api/finances/:financeId
 */
const updateFinance = catchAsync(async (req, res) => {
  const finance = await financeService.updateFinanceById(req.params.financeId, req.body);
  res.send(finance);
});

/**
 * Delete a finance record
 * @route DELETE /api/finances/:financeId
 */
const deleteFinance = catchAsync(async (req, res) => {
  await financeService.deleteFinanceById(req.params.financeId);
  res.status(204).send();
});

/**
 * Get all finance records with filtering
 * @route GET /api/finances
 */
const getFinances = catchAsync(async (req, res) => {
  const filter = {
    projectId: req.query.projectId,
    supplierId: req.query.supplierId,
    type: req.query.type,
    category: req.query.category,
    status: req.query.status,
    search: req.query.search,
    dateFrom: req.query.dateFrom,
    dateTo: req.query.dateTo,
    minAmount: req.query.minAmount,
    maxAmount: req.query.maxAmount
  };

  const options = {
    sortBy: req.query.sortBy || 'date',
    sortOrder: req.query.sortOrder || 'desc',
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10
  };

  const result = await financeService.queryFinances(filter, options);
  res.send(result);
});

/**
 * Get finance statistics
 * @route GET /api/finance/stats
 */
const getFinanceStats = catchAsync(async (req, res) => {
  // Get current date and calculate date 12 months ago
  const currentDate = new Date();
  const startDate = new Date();
  startDate.setMonth(currentDate.getMonth() - 12);

  // Query all finances within the date range
  const filter = {
    dateFrom: req.query.dateFrom || startDate.toISOString().split('T')[0],
    dateTo: req.query.dateTo || currentDate.toISOString().split('T')[0]
  };

  const stats = await financeService.getFinanceStats(filter);
  res.send(stats);
});

module.exports = {
  createFinance,
  getFinance,
  updateFinance,
  deleteFinance,
  getFinances,
  getFinanceStats
};