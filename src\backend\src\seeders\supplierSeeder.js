const { v4: uuidv4 } = require('uuid');
const { Supplier } = require('../models/supplier.model');
const { User } = require('../models/user.model');

async function seedSuppliers() {
  try {
    // Check if suppliers already exist
    const supplierCount = await Supplier.count();
    if (supplierCount > 0) {
      console.log('Suppliers already exist, skipping supplier seeding');
      return;
    }

    // Get a default admin user for createdBy field
    const adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      console.error('No admin user found for supplier seeding');
      return;
    }

    const suppliers = [
      {
        id: uuidv4(),
        name: '上海原材料有限公司',
        code: 'S20220001',
        contactName: '李经理',
        contactTitle: '销售总监',
        phone: '13800138001',
        contactPhone: '13800138001',
        contactEmail: '<EMAIL>',
        category: 'material',
        address: '上海市浦东新区张江高科技园区',
        rating: 4,
        products: [],
        tags: ['原材料', '上海'],
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '北京设备制造有限公司',
        code: 'S20220002',
        contactName: '王总',
        contactTitle: '总经理',
        phone: '13900139002',
        contactPhone: '13900139002',
        contactEmail: '<EMAIL>',
        category: 'equipment',
        address: '北京市海淀区中关村科技园',
        rating: 5,
        products: [],
        tags: ['设备', '北京'],
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '广州物流服务有限公司',
        code: 'S20220003',
        contactName: '张经理',
        contactTitle: '客户经理',
        phone: '13700137003',
        contactPhone: '13700137003',
        contactEmail: '<EMAIL>',
        category: 'logistics',
        address: '广州市黄埔区科学城',
        rating: 4,
        products: [],
        tags: ['物流', '广州'],
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '深圳技术服务有限公司',
        code: 'S20220004',
        contactName: '赵工',
        contactTitle: '技术总监',
        phone: '13600136004',
        contactPhone: '13600136004',
        contactEmail: '<EMAIL>',
        category: 'service',
        address: '深圳市南山区科技园',
        rating: 3,
        products: [],
        tags: ['技术服务', '深圳'],
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '杭州包装材料有限公司',
        code: 'S20220005',
        contactName: '陈经理',
        contactTitle: '销售经理',
        phone: '13500135005',
        contactPhone: '13500135005',
        contactEmail: '<EMAIL>',
        category: 'material',
        address: '杭州市滨江区科技园区',
        rating: 4,
        products: [],
        tags: ['包装材料', '杭州'],
        createdBy: adminUser.id
      }
    ];

    for (const supplier of suppliers) {
      await Supplier.create(supplier);
    }

    console.log('Supplier seeding completed successfully');
  } catch (error) {
    console.error('Error seeding suppliers:', error);
  }
}

module.exports = seedSuppliers;
