const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Document = sequelize.define('document', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [1, 255]
    }
  },
  content: {
    type: DataTypes.TEXT('long'),
    allowNull: true
  },
  summary: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  categoryId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'category',
      key: 'id'
    },
    field: 'categoryId'
  },
  authorId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'user',
      key: 'id'
    },
    field: 'authorId'
  },
  status: {
    type: DataTypes.ENUM('draft', 'published', 'archived', 'deleted'),
    defaultValue: 'draft',
    allowNull: false
  },
  viewCount: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'viewCount'
  },
  isPublic: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    field: 'isPublic'
  },
  publishDate: {
    type: DataTypes.DATE,
    allowNull: true,
    field: 'publishDate'
  },
  lastModifierId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'user',
      key: 'id'
    },
    field: 'lastModifierId'
  },
  documentType: {
    type: DataTypes.ENUM('article', 'procedure', 'policy', 'guide', 'faq', 'other'),
    defaultValue: 'article',
    allowNull: false,
    field: 'documentType'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium',
    allowNull: false
  },
  version: {
    type: DataTypes.STRING,
    defaultValue: '1.0'
  },
  metaData: {
    type: DataTypes.JSON,
    allowNull: true,
    field: 'metaData'
  }
}, {
  underscored: false,
  paranoid: true,
  timestamps: true,
});

const setupAssociations = (db) => {
  // Document belongs to User (author)
  db.Document.belongsTo(db.User, {
    foreignKey: 'authorId',
    as: 'author'
  });

  // Document belongs to User (lastModifier)
  db.Document.belongsTo(db.User, {
    foreignKey: 'lastModifierId',
    as: 'lastModifier'
  });

  // Document belongs to Category
  db.Document.belongsTo(db.Category, {
    foreignKey: 'categoryId',
    as: 'category'
  });

  // Document has many Attachments
  db.Document.hasMany(db.Attachment, {
    foreignKey: 'documentId'
  });

  // Document has many Comments
  db.Document.hasMany(db.Comment, {
    foreignKey: 'documentId'
  });

  // Document belongs to many Tags through DocumentTag
  db.Document.belongsToMany(db.Tag, {
    through: db.DocumentTag,
    foreignKey: 'documentId'
  });
};

module.exports = { Document, setupAssociations };