<template>
  <div class="max-w-6xl mx-auto px-4 py-6">    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">合同工管理</h1>
        <p class="page-subtitle">管理和跟踪合同工信息，包括个人信息、工种、工资和合同状态</p>
      </div>
      <div class="flex space-x-2">
        <button @click="goToAdd" class="btn btn-primary flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          添加合同工
        </button>
      </div>
    </div>    <!-- 查询区域 -->
    <div class="filter-card mb-6">
      <div class="flex justify-between gap-4">
        <div class="flex-1">
          <label for="search" class="form-label font-medium">查询条件</label>
          <div class="relative">
            <input
              type="text"
              id="search"
              v-model="filters.search"
              class="form-input pl-10 py-2 mt-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
              placeholder="请输入姓名、身份证号或电话号码进行搜索"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <span class="text-xs text-gray-500 mt-1 block">支持模糊搜索，可输入部分信息进行查询</span>
        </div>

      </div>
      <div class="flex justify-end mt-6">
        <button 
          @click="resetFilters" 
          class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200"
          :disabled="loading"
        >
          重置
        </button>
        <button 
          @click="handleFilterChange" 
          class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200"
          :disabled="loading"
        >
          <span v-if="loading" class="inline-block animate-spin mr-1">↻</span>
          {{ loading ? '加载中...' : '查询' }}
        </button>
      </div>
    </div>    <!-- 成功提示 -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">操作成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-4 p-4 bg-red-100 text-red-700 rounded-lg shadow-sm flex items-center justify-between">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">提示：</span>
        <span class="ml-1 whitespace-pre-line">{{ error }}</span>
      </div>
      <button @click="fetchContractWorkers" class="text-red-700 hover:text-red-900 font-medium flex items-center">
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        重新加载
      </button>
    </div>    <!-- 加载状态 -->
    <div v-if="loading" class="bg-white shadow overflow-hidden sm:rounded-lg p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">正在加载合同工数据...</p>
      <p class="text-gray-500 text-sm mt-2">系统正在从数据库读取合同工信息，请稍候</p>
    </div>

    <!-- 空数据状态 -->
    <div v-else-if="!loading && filteredRecords.length === 0" class="bg-white shadow overflow-hidden sm:rounded-lg p-8 text-center">
      <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"></path>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">{{ filters.search ? '未找到符合条件的合同工' : '暂无合同工数据' }}</h3>
      <p class="text-gray-500 mb-4">
        {{ filters.search ? '未能找到符合您查询条件的合同工记录。\n请尝试使用其他搜索条件，或清空查询条件查看所有记录。' : '系统中尚未录入任何合同工信息。\n点击上方的"添加合同工"按钮开始创建新的合同工记录。' }}
      </p>
      <div class="mt-4 flex justify-center">
        <button v-if="filters.search" @click="resetFilters" class="btn btn-secondary">
          重置
        </button>
        <button v-else @click="goToAdd" class="btn btn-primary flex items-center">
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          添加合同工
        </button>
      </div>
    </div>    <!-- 数据表格 -->
    <div v-else class="bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">身份证号</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">入职时间</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工种</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="record in filteredRecords" :key="record.id" class="hover:bg-gray-50 transition-colors duration-150">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ record.employeeName || (record.employee && record.employee.name) || '未设置' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ record.idNumber || (record.employee && record.employee.idNumber) || '未设置' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ record.phone || (record.employee && record.employee.contactInfo && record.employee.contactInfo.phone) || '未设置' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(record.joinDate || record.startDate) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ record.workType || (record.employee && record.employee.position) || '未设置' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <button
                  @click="goToEdit(record)"
                  class="text-blue-600 hover:text-blue-900 mr-3 flex items-center"
                  title="编辑合同工信息"
                >
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                  编辑
                </button>
                <button
                  @click="deleteRecord(record)"
                  class="text-red-600 hover:text-red-900 flex items-center"
                  title="删除合同工信息"
                >
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'

const router = useRouter()
const filters = ref({
  search: ''
})

const records = ref([])
const loading = ref(false)
const error = ref(null)
const successMessage = ref(null)

// 显示成功消息并自动隐藏
const showSuccessMessage = (message, duration = 5000) => {
  successMessage.value = message;
  setTimeout(() => {
    successMessage.value = null;
  }, duration);
}

// Add date formatter
const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  return `${year}/${month}/${day}`;
};

// 获取合同工数据
const fetchContractWorkers = async () => {
  loading.value = true;
  error.value = null;

  try {
    const params = {};

    if (filters.value.search) {
      params.search = filters.value.search;
    }

    // 检查是否需要重新登录
    try {
      await axios.get('/api/auth/me');
    } catch (authErr) {
      if (authErr.response?.status === 401) {
        error.value = '尊敬的用户，您的登录状态已过期。\n\n为了保护您的账户安全和数据完整性，系统要求您重新登录后才能继续使用。\n\n系统将在5秒后为您跳转到登录页面。登录成功后，您将返回此页面继续操作。';
        setTimeout(() => {
          window.location.href = '/login';
        }, 5000);
        return;
      }
    }

    const response = await axios.get('/api/employees/contract-workers', { params });
    records.value = response.data.results || [];
    
    // 如果开启了过滤但结果为空，显示提示信息
    if (filters.value.search && records.value.length === 0) {
      console.log('查询条件下无结果:', filters.value.search);
    }
  } catch (err) {
    console.error('获取合同工数据失败:', err);
    if (err.response?.status === 401) {
      error.value = '尊敬的用户，您的登录状态已过期。\n\n为了保护您的账户安全和数据完整性，系统要求您重新登录后才能继续使用。\n\n系统将在5秒后为您跳转到登录页面。登录成功后，您将返回此页面继续操作。';
      setTimeout(() => {
        window.location.href = '/login';
      }, 5000);
      return;
    }
    error.value = err.response?.data?.message || '❌ 很抱歉，系统暂时无法获取合同工数据。\n\n可能的原因：\n· 您的网络连接可能不稳定\n· 服务器可能正在维护中\n· 系统临时故障\n\n建议操作：\n1. 检查网络连接\n2. 刷新页面后重试\n3. 稍后再次尝试\n\n如果问题持续存在，请联系系统管理员获取帮助。我们将尽快解决此问题，感谢您的理解与耐心。';
    records.value = [];
  } finally {
    loading.value = false;
  }
};

// 监听筛选条件变化
const handleFilterChange = () => {
  if (!filters.value.search.trim()) {
    showSuccessMessage('已清空搜索条件，显示所有合同工数据。');
  } else {
    // 避免过于频繁的提示，这里不显示消息
    console.log('正在使用以下条件过滤：', filters.value.search);
  }
  fetchContractWorkers();
};

// 在组件挂载时获取数据
onMounted(() => {
  fetchContractWorkers();
});

// 过滤记录
const filteredRecords = computed(() => {
  return records.value.filter(record => {
    // 如果没有搜索条件，返回所有记录
    if (!filters.value.search) {
      return true;
    }

    // 获取员工信息，可能在record或record.employee中
    const employeeName = record.employeeName || (record.employee && record.employee.name) || '';
    const idNumber = record.idNumber || (record.employee && record.employee.idNumber) || '';
    const phone = record.phone || (record.employee && record.employee.contactInfo && record.employee.contactInfo.phone) || '';

    // 转换搜索条件为小写
    const searchLower = filters.value.search.toLowerCase();

    // 检查是否匹配
    return employeeName.toLowerCase().includes(searchLower) ||
           idNumber.includes(filters.value.search) ||
           phone.includes(filters.value.search);
  })
})

// 删除记录
async function deleteRecord(record) {
  const employeeName = record.employeeName || (record.employee && record.employee.name) || '此合同工';
  if (confirm(`您确定要删除"${employeeName}"的合同工记录吗？\n\n⚠️ 请注意以下重要影响：\n· 删除后将无法恢复此记录\n· 相关的合同信息将被永久删除\n· 工资计算数据将被删除\n· 考勤记录可能会受到影响\n· 历史工时统计将不再包含此员工数据\n\n如果您确定要删除，请点击"确定"按钮继续操作。如需保留记录，请点击"取消"按钮。`)) {
    try {
      // 检查是否需要重新登录
      try {
        await axios.get('/api/auth/me');
      } catch (authErr) {
        if (authErr.response?.status === 401) {
          error.value = '尊敬的用户，您的登录状态已过期。\n\n为了保护您的账户安全和数据完整性，系统要求您重新登录后才能继续此操作。\n\n系统将在5秒后为您跳转到登录页面。登录成功后，您可以继续之前的操作。';
          setTimeout(() => {
            window.location.href = '/login';
          }, 5000);
          return;
        }
      }

      await axios.delete(`/api/employees/contract-workers/${record.id}`);
      // 显示成功消息
      showSuccessMessage(`✅ 已成功删除"${employeeName}"的合同工记录。\n\n所有相关联的数据已被成功移除，此操作已完成。`);
      // 刷新数据
      fetchContractWorkers();
    } catch (err) {
      console.error('删除记录失败:', err);
      if (err.response?.status === 401) {
        error.value = '尊敬的用户，您的登录状态已过期。\n\n为了保护您的账户安全和数据完整性，系统要求您重新登录后才能继续此操作。\n\n系统将在5秒后为您跳转到登录页面。登录成功后，您可以继续之前的操作。';
        setTimeout(() => {
          window.location.href = '/login';
        }, 5000);
        return;
      }
      error.value = err.response?.data?.message || '❌ 很抱歉，删除合同工记录时遇到了问题。\n\n可能的原因：\n· 网络连接暂时中断\n· 系统临时故障\n· 该记录可能已被关联到其他重要数据（如正在进行的项目）\n· 您可能没有足够的权限执行此操作\n\n建议您：\n1. 检查网络连接\n2. 确认该员工没有未完成的工作任务\n3. 稍后再次尝试\n\n如果问题持续存在，请联系系统管理员获取帮助。';
    }
  }
}

// 重置过滤条件
const resetFilters = () => {
  const hadFilters = !!filters.value.search.trim();
  filters.value.search = '';
  fetchContractWorkers();
  
  if (hadFilters) {
    showSuccessMessage('已重置所有筛选条件，现在显示全部合同工数据。');
  }
};

function goToAdd() {
  console.log('正在跳转到添加合同工页面...');
  router.push('/employees/contract/add')
}

function goToEdit(record) {
  const employeeName = record.employeeName || (record.employee && record.employee.name) || '合同工';
  console.log(`正在跳转到编辑"${employeeName}"的详细信息页面...`);
  router.push(`/employees/contract/edit/${record.id}`)
}
</script>