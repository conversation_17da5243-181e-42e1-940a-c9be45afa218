const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ProductTransaction = sequelize.define(
  'producttransaction',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    productId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'product',
        key: 'id',
      },
    },
    projectId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'project',
        key: 'id',
      },
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isIn: [['in', 'out', 'adjustment']],
      },
    },
    quantity: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    unitPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    totalPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    reference: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    transactionDate: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'user',
        key: 'id',
      },
    },
  },
  {
    timestamps: true,
  }
);

// Define associations
const setupAssociations = (db) => {
  if (db.Product) {
    ProductTransaction.belongsTo(db.Product, {
      foreignKey: 'productId',
    });
  }

  if (db.Project) {
    ProductTransaction.belongsTo(db.Project, {
      foreignKey: 'projectId',
    });
  }

  if (db.User) {
    ProductTransaction.belongsTo(db.User, {
      foreignKey: 'createdBy',
      as: 'Creator',
    });
  }
};

module.exports = {
  ProductTransaction,
  setupAssociations,
};