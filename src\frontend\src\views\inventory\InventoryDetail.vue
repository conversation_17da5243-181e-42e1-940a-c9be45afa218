<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2">{{ error }}</p>
      <div class="mt-3 flex space-x-3">
        <button @click="fetchInventoryDetail()" class="text-red-700 hover:text-red-900 font-medium underline">
          重试
        </button>
        <button @click="backToList()" class="text-blue-700 hover:text-blue-900 font-medium underline">
          返回列表
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="bg-white rounded-lg shadow p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">正在加载产品详情...</p>
    </div>

    <!-- 产品详情 -->
    <div v-else-if="!error" class="bg-white rounded-lg shadow overflow-hidden">
      <!-- 产品头部信息 -->
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h1 class="text-2xl font-bold text-gray-900">库存详情</h1>
          <div class="flex space-x-2">
            <!-- 编辑按钮 -->
            <router-link
              :to="`/inventory/`"
              class="btn btn-secondary flex items-center"
            >
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              返回
            </router-link>

            <!-- 入库按钮 -->
            <button @click="showInboundModal = true" class="btn btn-success">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              入库
            </button>

            <!-- 出库按钮 -->
            <button @click="showOutboundModal = true" class="btn btn-danger">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
              </svg>
              出库
            </button>

          </div>
        </div>
      </div>

      <!-- 产品基本信息 -->
      <div class="px-6 py-4 bg-gray-50">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-500">产品编码</p>
            <p class="mt-1 text-lg font-semibold">{{ inventory.code }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">产品名称</p>
            <p class="mt-1">{{ inventory.name }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">规格型号</p>
            <p class="mt-1">{{ inventory.specification }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">单位</p>
            <p class="mt-1">{{ inventory.unit }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">供应商</p>
            <p class="mt-1">{{ inventory.supplier }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">产品分类</p>
            <p class="mt-1">{{ inventory.category }}</p>
          </div>
        </div>
      </div>

      <!-- 库存信息 -->
      <div class="px-6 py-4 border-t border-gray-200">
        <h2 class="text-lg font-semibold mb-3">库存信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-500">库存数量</p>
            <p class="mt-1 text-xl font-bold">{{ inventory.stock }} <span class="text-sm text-gray-500">{{ inventory.unit }}</span></p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">库存状态</p>
            <p class="mt-1">
              <span
                :class="{
                  'bg-green-100 text-green-800': inventory.stockStatus === '正常',
                  'bg-yellow-100 text-yellow-800': inventory.stockStatus === '偏低',
                  'bg-red-100 text-red-800': inventory.stockStatus === '紧急'
                }"
                class="px-2 py-1 rounded-full text-xs font-medium">
                {{ inventory.stockStatus }}
              </span>
            </p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">最后入库时间</p>
            <p class="mt-1">{{ inventory.lastInboundDate }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">最低库存</p>
            <p class="mt-1">{{ inventory.minStock }} {{ inventory.unit }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">最高库存</p>
            <p class="mt-1">{{ inventory.maxStock }} {{ inventory.unit }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">安全库存</p>
            <p class="mt-1">{{ inventory.minStock }} {{ inventory.unit }}</p>
          </div>
        </div>
      </div>

      <!-- 存放位置 -->
      <div class="px-6 py-4 border-t border-gray-200">
        <h2 class="text-lg font-semibold mb-3">存放位置</h2>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">区域</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">货架</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="(location, index) in inventory.locations" :key="index">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ location.warehouse }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ location.area }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ location.shelf }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ location.quantity }} {{ inventory.unit }}</td>
              </tr>
              <tr v-if="inventory.locations.length === 0">
                <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">
                  暂无存放位置数据
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 近期库存记录 -->
      <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">近期库存记录</h2>
          <router-link :to="`/inventory/${inventory.id}/transactions`" class="text-blue-600 hover:text-blue-800 text-sm">
            查看全部
          </router-link>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">流水号</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作类型</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作员</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">关联信息</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="(transaction, index) in inventory.recentTransactions" :key="index">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ transaction.code || transaction.reference || '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm">
                  <span
                    :class="{
                      'bg-green-100 text-green-800': transaction.type === '入库' || transaction.type === 'in',
                      'bg-red-100 text-red-800': transaction.type === '出库' || transaction.type === 'out',
                      'bg-yellow-100 text-yellow-800': transaction.type === '调整' || transaction.type === 'adjust'
                    }"
                    class="px-2 py-1 rounded-full text-xs font-medium">
                    {{ transaction.type === 'in' ? '入库' : transaction.type === 'out' ? '出库' : transaction.type }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm" :class="{
                  'text-green-600': transaction.type === '入库' || transaction.type === 'in',
                  'text-red-600': transaction.type === '出库' || transaction.type === 'out'
                }">
                  {{ transaction.type === '入库' || transaction.type === 'in' ? '+' : transaction.type === '出库' || transaction.type === 'out' ? '-' : '' }}
                  {{ transaction.quantity }} {{ inventory.unit }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ transaction.date || (transaction.transactionDate ? new Date(transaction.transactionDate).toLocaleDateString() : '-') }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ transaction.operator || transaction.createdBy || '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span v-if="transaction.projectName || (transaction.projectId && findProjectName(transaction.projectId))">
                    项目: {{ transaction.projectName || findProjectName(transaction.projectId) }}
                  </span>
                  <span v-else-if="transaction.notes" class="text-xs max-w-xs truncate block">{{ transaction.notes }}</span>
                  <span v-else>-</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- 入库模态框 -->
  <div v-if="showInboundModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-lg mx-4">
      <div class="p-5 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">库存入库</h3>
          <button @click="showInboundModal = false" class="text-gray-400 hover:text-gray-500">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      <StockInForm 
        :item="inventory" 
        :loading="inboundLoading" 
        @submit="handleInboundSubmit" 
        @cancel="showInboundModal = false" 
      />
    </div>
  </div>

  <!-- 出库模态框 -->
  <div v-if="showOutboundModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-lg mx-4">
      <div class="p-5 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">库存出库</h3>
          <button @click="showOutboundModal = false" class="text-gray-400 hover:text-gray-500">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      <StockOutForm 
        :item="inventory" 
        :loading="outboundLoading" 
        @submit="handleOutboundSubmit" 
        @cancel="showOutboundModal = false" 
      />
    </div>
  </div>
</template>

<script>
import Breadcrumb from '@/components/Breadcrumb.vue';
import axios from 'axios';
import StockInForm from '@/components/inventory/StockInForm.vue';
import StockOutForm from '@/components/inventory/StockOutForm.vue';

export default {
  name: 'InventoryDetail',
  components: {
    Breadcrumb,
    StockInForm,
    StockOutForm
  },
  data() {
    return {
      loading: true,
      error: null,
      inventory: {
        id: '',
        code: '',
        name: '',
        specification: '',
        unit: '',
        supplier: '',
        category: '',
        stock: 0,
        stockStatus: '',
        lastInboundDate: '',
        minStock: 0,
        maxStock: 0,
        locations: [],
        recentTransactions: []
      },
      showInboundModal: false,
      showOutboundModal: false,
      inboundLoading: false,
      outboundLoading: false,
      inboundForm: {
        quantity: 1,
        warehouseLocation: '',
        supplier: '',
        batchNumber: '',
        remarks: ''
      },
      outboundForm: {
        quantity: 1,
        projectId: '',
        warehouseLocation: '',
        purpose: '',
        recipient: '',
        remarks: ''
      },
      projects: []
    }
  },  created() {
    this.fetchInventoryDetail();
    this.fetchProjects();
  },
  computed: {
    itemId() {
      return this.$route.params.id;
    }
  },
  methods: {
    // 返回库存列表页面
    backToList() {
      this.$router.push('/inventory');
    },
    
    async fetchInventoryDetail() {
      try {
        this.loading = true;
        this.error = null;
        const id = this.itemId;

        // 修正API调用路径 - 从inventory改为products
        const response = await axios.get(`/api/products/${id}`);
        this.inventory = response.data;
        
        // 确保_id字段存在，以便在后续操作中使用
        if (!this.inventory._id && this.inventory.id) {
          this.inventory._id = this.inventory.id;
        }

        // 确保locations字段存在
        if (!this.inventory.locations) {
          this.inventory.locations = [];
        }
        
        // 如果没有存放位置数据，添加一些默认数据以便显示
        if (this.inventory.locations.length === 0 && this.inventory.stock > 0) {
          // 根据库存量添加默认位置
          this.inventory.locations = [
            {
              warehouse: '主仓库',
              area: 'A区',
              shelf: 'A01',
              quantity: this.inventory.stock
            }
          ];
        }

        // 处理库存状态
        this.updateStockStatus();

        // 使用正确的API路径获取库存记录
        try {
          const transactionsResponse = await axios.get(`/api/products/${id}/transactions`, {
            params: {
              limit: 5,  // 只获取5条最近记录
              sortBy: 'transactionDate',
              sortOrder: 'desc'
            }
          });

          if (transactionsResponse.data && Array.isArray(transactionsResponse.data.results)) {
            this.inventory.recentTransactions = transactionsResponse.data.results;
          }
        } catch (txError) {
          console.error('获取库存记录失败', txError);
          // 库存记录获取失败不影响产品主信息展示
          this.inventory.recentTransactions = [];
        }
      } catch (err) {
        console.error('获取产品详情失败', err);
        this.error = err.response?.data?.message || '获取产品详情失败，请重试。可能是产品ID不存在或者API路径不正确。';
      } finally {
        this.loading = false;
      }
    },

    // 获取项目列表
    async fetchProjects() {
      try {
        // 调用真实API
        const response = await axios.get('/api/projects', {
          params: {
            status: 'in_progress',  // 只获取进行中的项目
            limit: 100  // 获取足够多的项目
          }
        });

        if (response.data && Array.isArray(response.data.results)) {
          this.projects = response.data.results.map(project => ({
            id: project.id,
            name: project.name
          }));
        }
      } catch (err) {
        console.error('获取项目列表失败', err);
        // 如果获取项目列表失败，不应阻止用户查看产品详情
        // 只需要在控制台记录错误即可
        this.projects = [];
      }
    },

    // 处理StockInForm组件的提交事件
    async handleInboundSubmit(formData) {
      try {
        this.inboundLoading = true;

        // 构建入库数据
        const inboundData = {
          productId: this.inventory._id || this.inventory.id,
          quantity: formData.quantity,
          unitPrice: Number(this.inventory.price || 0),
          reference: `IN-${new Date().toISOString().slice(0,10).replace(/-/g, '')}`,
          notes: `供应商: ${formData.supplierName || '未指定'}, 库位: ${formData.warehouseLocation}${formData.batchNumber ? ', 批次: ' + formData.batchNumber : ''}${formData.remarks ? ', ' + formData.remarks : ''}`,
          transactionDate: new Date().toISOString(),
          type: 'in' // 显式设置交易类型为入库
        };

        // 使用正确的API路径进行入库操作 - 尝试多个可能的路径
        let response;
        try {
          // 首先尝试主要API路径
          response = await axios.post('/api/product-transactions/in', inboundData);
        } catch (err) {
          if (err.response && err.response.status === 404) {
            // 如果404，尝试备用路径
            response = await axios.post('/api/inventory/stock-in', inboundData);
          } else {
            throw err; // 如果是其他错误，则继续抛出
          }
        }

        // 更新库存数量
        this.inventory.stock += formData.quantity;

        // 更新库存位置信息
        const locationParts = formData.warehouseLocation.split('-');
        const warehouseName = locationParts[0];
        const areaName = locationParts[1] ? locationParts[1] : '';
        const shelfName = locationParts[2] ? locationParts[2] : '';

        // 查找是否已存在相同位置
        const existingLocationIndex = this.inventory.locations.findIndex(loc => 
          loc.warehouse === warehouseName && 
          loc.area === areaName && 
          loc.shelf === shelfName
        );

        if (existingLocationIndex !== -1) {
          // 如果位置已存在，更新数量
          this.inventory.locations[existingLocationIndex].quantity += formData.quantity;
        } else {
          // 如果位置不存在，添加新位置
          this.inventory.locations.push({
            warehouse: warehouseName,
            area: areaName,
            shelf: shelfName,
            quantity: formData.quantity
          });
        }

        // 关闭模态框
        this.showInboundModal = false;

        // 显示成功消息
        alert('入库操作成功完成！');

        // 刷新库存详情
        await this.fetchInventoryDetail();
      } catch (err) {
        console.error('入库操作失败', err);
        alert('入库操作失败: ' + (err.response?.data?.message || err.message || '未知错误'));
      } finally {
        this.inboundLoading = false;
      }
    },

    // 处理StockOutForm组件的提交事件
    async handleOutboundSubmit(formData) {
      if (formData.quantity > this.inventory.stock) {
        alert('出库数量不能超过当前库存');
        return;
      }

      try {
        this.outboundLoading = true;

        // 构建出库数据
        const outboundData = {
          productId: this.inventory._id || this.inventory.id,
          projectId: formData.projectId,
          quantity: formData.quantity,
          unitPrice: Number(this.inventory.price || 0),
          reference: `OUT-${new Date().toISOString().slice(0,10).replace(/-/g, '')}`,
          notes: `领用人: ${formData.recipient}, 用途: ${formData.purpose}, 库位: ${formData.warehouseLocation}${formData.remarks ? ', ' + formData.remarks : ''}`,
          transactionDate: new Date().toISOString(),
          type: 'out'
        };

        // 使用正确的API路径进行出库操作 - 尝试多个可能的路径
        let response;
        try {
          // 首先尝试主要API路径
          response = await axios.post('/api/product-transactions/out', outboundData);
        } catch (err) {
          if (err.response && err.response.status === 404) {
            // 如果404，尝试备用路径
            response = await axios.post('/api/inventory/stock-out', outboundData);
          } else {
            throw err; // 如果是其他错误，则继续抛出
          }
        }

        // 更新库存数量
        this.inventory.stock -= formData.quantity;

        // 查找出库位置
        const locationIndex = this.inventory.locations.findIndex(
          loc => `${loc.warehouse}-${loc.area}-${loc.shelf}` === formData.warehouseLocation
        );

        if (locationIndex >= 0) {
          // 减少位置的数量
          this.inventory.locations[locationIndex].quantity -= formData.quantity;

          // 如果数量为0，可以选择移除该位置
          if (this.inventory.locations[locationIndex].quantity <= 0) {
            this.inventory.locations.splice(locationIndex, 1);
          }
        }

        // 获取返回的库存记录
        let newTransaction = response.data;
        
        // 如果API没有返回库存记录，则手动创建一个用于前端显示
        if (!newTransaction) {
          newTransaction = {
            id: Date.now().toString(),
            code: `OUT${new Date().toISOString().slice(0,10).replace(/-/g, '')}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`,
            type: '出库',
            quantity: formData.quantity,
            date: new Date().toISOString().slice(0,10),
            operator: '当前用户', // 实际应用中应从登录用户获取
            projectId: formData.projectId,
            projectName: formData.projectName || ''
          };
        }

        // 添加新的库存记录到前端显示
        this.inventory.recentTransactions.unshift(newTransaction);

        // 关闭模态框
        this.showOutboundModal = false;

        // 检查库存状态并更新
        this.updateStockStatus();

        // 显示成功消息
        alert('出库操作成功！');

        // 刷新库存详情
        await this.fetchInventoryDetail();

      } catch (error) {
        console.error('出库操作失败', error);
        alert(error.response?.data?.message || `出库操作失败: ${error.message || '未知错误'}`);
      } finally {
        this.outboundLoading = false;
      }
    },

    // 更新库存状态
    updateStockStatus() {
      if (this.inventory.stock <= 0) {
        this.inventory.stockStatus = '无库存';
      } else if (this.inventory.stock < this.inventory.minStock) {
        this.inventory.stockStatus = '紧急';
      } else if (this.inventory.stock < this.inventory.minStock * 1.5) {
        this.inventory.stockStatus = '偏低';
      } else {
        this.inventory.stockStatus = '正常';
      }
    },

    // 重置入库表单
    resetInboundForm() {
      this.inboundForm = {
        quantity: 1,
        warehouseLocation: '',
        supplier: '',
        batchNumber: '',
        remarks: ''
      };
    },

    // 重置出库表单
    resetOutboundForm() {
      this.outboundForm = {
        quantity: 1,
        projectId: '',
        warehouseLocation: '',
        purpose: '',
        recipient: '',
        remarks: ''
      };
    },

    // 查找项目名称
    findProjectName(projectId) {
      const project = this.projects.find(p => p.id === projectId);
      return project ? project.name : '未知项目';
    }
  }
}
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md flex items-center text-sm font-medium;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700;
}
</style>