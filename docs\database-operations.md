# Database Operations Guide

This guide provides examples of common database operations using Sequelize ORM in our application.

## Table of Contents

1. [Connecting to Database](#connecting-to-database)
2. [Basic CRUD Operations](#basic-crud-operations)
   - [Create](#create)
   - [Read](#read)
   - [Update](#update)
   - [Delete](#delete)
3. [Transactions](#transactions)
4. [Querying](#querying)
5. [Associations](#associations)

## Connecting to Database

The application automatically connects to the configured database (SQLite or MySQL) on startup. The connection is handled in `src/config/database.js`.

## Basic CRUD Operations

### Create

Creating a new record:

```javascript
// Create a single record
const user = await User.create({
  name: '<PERSON>',
  email: '<EMAIL>',
  password: 'hashedPassword123',
  role: 'user'
});

// Create multiple records
const users = await User.bulkCreate([
  { name: '<PERSON>', email: '<EMAIL>', role: 'user' },
  { name: '<PERSON>', email: '<EMAIL>', role: 'admin' }
]);
```

### Read

Reading records:

```javascript
// Find by primary key
const user = await User.findByPk(1);

// Find one record
const user = await User.findOne({
  where: { email: '<EMAIL>' }
});

// Find all records matching criteria
const admins = await User.findAll({
  where: { role: 'admin' }
});

// Find with pagination
const users = await User.findAll({
  limit: 10,
  offset: 0,
  order: [['createdAt', 'DESC']]
});

// Count records
const count = await User.count({
  where: { status: 'active' }
});
```

### Update

Updating records:

```javascript
// Method 1: Find and update
const user = await User.findByPk(1);
user.name = 'Updated Name';
await user.save();

// Method 2: Direct update
const [updatedCount] = await User.update(
  { status: 'inactive' },
  { where: { role: 'guest' } }
);

// Method 3: Update or create (upsert)
await User.upsert({
  id: 1,  // If this exists, update; if not, create
  name: 'John Doe',
  email: '<EMAIL>'
});
```

Update with field increment/decrement:

```javascript
// Increment a value
await User.increment('loginCount', { 
  by: 1,
  where: { id: userId }
});

// Decrement a value
await Post.decrement('viewCount', {
  by: 5,
  where: { id: postId }
});
```

### Delete

Deleting records:

```javascript
// Delete by instance
const user = await User.findByPk(1);
await user.destroy();

// Delete by criteria
const deletedCount = await User.destroy({
  where: { status: 'inactive' }
});

// Soft delete (if model has paranoid option enabled)
await User.destroy({
  where: { id: userId }
});

// Hard delete (even for paranoid models)
await User.destroy({
  where: { id: userId },
  force: true
});
```

## Transactions

Transactions ensure that multiple database operations either all succeed or all fail together.

```javascript
// Using managed transaction
const transaction = await sequelize.transaction();

try {
  // Perform operations within the transaction
  const user = await User.create({
    name: 'John Doe',
    email: '<EMAIL>'
  }, { transaction });
  
  await Profile.create({
    userId: user.id,
    bio: 'User bio'
  }, { transaction });
  
  // Commit the transaction
  await transaction.commit();
} catch (error) {
  // Rollback the transaction if any operation fails
  await transaction.rollback();
  throw error;
}
```

Using an auto-managed transaction:

```javascript
await sequelize.transaction(async (t) => {
  // All operations inside this callback will be part of the transaction
  const user = await User.create({
    name: 'John Doe',
    email: '<EMAIL>'
  }, { transaction: t });
  
  await Profile.create({
    userId: user.id,
    bio: 'User bio'
  }, { transaction: t });
  
  // Transaction automatically commits if no errors are thrown
  // and rolls back if an error is thrown
});
```

## Querying

Advanced querying with Sequelize:

```javascript
// Using operators
const { Op } = require('sequelize');

const users = await User.findAll({
  where: {
    [Op.or]: [
      { role: 'admin' },
      { role: 'manager' }
    ],
    createdAt: {
      [Op.gte]: new Date(new Date() - 24 * 60 * 60 * 1000) // Last 24 hours
    }
  }
});

// Complex where conditions
const posts = await Post.findAll({
  where: {
    title: {
      [Op.like]: '%sequelize%'
    },
    [Op.or]: [
      {
        viewCount: {
          [Op.gte]: 1000
        }
      },
      {
        commentCount: {
          [Op.gte]: 100
        }
      }
    ]
  }
});

// Raw queries
const [results, metadata] = await sequelize.query(
  'UPDATE users SET status = :status WHERE role = :role',
  {
    replacements: { status: 'active', role: 'member' },
    type: sequelize.QueryTypes.UPDATE
  }
);
```

## Associations

Working with associated models:

```javascript
// Define associations in models
User.hasOne(Profile);
Profile.belongsTo(User);

User.hasMany(Post);
Post.belongsTo(User);

// Include associated data in queries
const userWithProfile = await User.findByPk(1, {
  include: [Profile]
});

const userWithPosts = await User.findByPk(1, {
  include: [
    {
      model: Post,
      where: { status: 'published' }
    }
  ]
});

// Eager loading with multiple associations
const userWithAll = await User.findByPk(1, {
  include: [Profile, Post, Comment]
});

// Nested eager loading
const userWithPostsAndComments = await User.findByPk(1, {
  include: [
    {
      model: Post,
      include: [
        {
          model: Comment,
          include: [User] // Comments with their authors
        }
      ]
    }
  ]
}); 