const { expect } = require('chai');
const sinon = require('sinon');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const authService = require('../../../src/services/auth.service');
const userService = require('../../../src/services/user.service');
const ApiError = require('../../../src/utils/ApiError');
const config = require('../../../src/config/config');

describe('Auth Service', () => {
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('login', () => {
    it('should login user successfully with correct credentials', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'Password123';
      
      const user = {
        id: '1',
        name: 'Test User',
        email,
        password: 'hashedPassword',
        role: 'user'
      };
      
      sandbox.stub(userService, 'getUserByEmail').resolves(user);
      sandbox.stub(bcrypt, 'compare').resolves(true);
      
      const token = 'jwt-token';
      sandbox.stub(jwt, 'sign').returns(token);

      // Act
      const result = await authService.login(email, password);

      // Assert
      expect(result).to.have.property('user');
      expect(result).to.have.property('token');
      expect(result.user.id).to.equal(user.id);
      expect(result.user.email).to.equal(user.email);
      expect(result.token).to.equal(token);
      expect(result.user).to.not.have.property('password'); // 密码不应该返回
    });

    it('should throw error if user not found', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'Password123';
      
      sandbox.stub(userService, 'getUserByEmail').resolves(null);

      // Act & Assert
      try {
        await authService.login(email, password);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(401);
        expect(error.message).to.include('Incorrect email or password');
      }
    });

    it('should throw error if password is incorrect', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'WrongPassword';
      
      const user = {
        id: '1',
        name: 'Test User',
        email,
        password: 'hashedPassword',
        role: 'user'
      };
      
      sandbox.stub(userService, 'getUserByEmail').resolves(user);
      sandbox.stub(bcrypt, 'compare').resolves(false);

      // Act & Assert
      try {
        await authService.login(email, password);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(401);
        expect(error.message).to.include('Incorrect email or password');
      }
    });
  });

  describe('register', () => {
    it('should register user successfully', async () => {
      // Arrange
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123'
      };
      
      const createdUser = {
        id: '1',
        name: userData.name,
        email: userData.email,
        role: 'user'
      };
      
      sandbox.stub(userService, 'createUser').resolves(createdUser);
      
      const token = 'jwt-token';
      sandbox.stub(jwt, 'sign').returns(token);

      // Act
      const result = await authService.register(userData);

      // Assert
      expect(result).to.have.property('user');
      expect(result).to.have.property('token');
      expect(result.user.id).to.equal(createdUser.id);
      expect(result.user.email).to.equal(createdUser.email);
      expect(result.token).to.equal(token);
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      // Arrange
      const userId = '1';
      const user = {
        id: userId,
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user'
      };
      
      sandbox.stub(userService, 'getUserById').resolves(user);
      
      const token = 'new-jwt-token';
      sandbox.stub(jwt, 'sign').returns(token);

      // Act
      const result = await authService.refreshToken(userId);

      // Assert
      expect(result).to.equal(token);
    });

    it('should throw error if user not found', async () => {
      // Arrange
      const userId = '999';
      
      sandbox.stub(userService, 'getUserById').rejects(new ApiError(404, 'User not found'));

      // Act & Assert
      try {
        await authService.refreshToken(userId);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(404);
        expect(error.message).to.include('User not found');
      }
    });
  });

  describe('verifyToken', () => {
    it('should verify token successfully', () => {
      // Arrange
      const token = 'valid-jwt-token';
      const decoded = {
        sub: '1',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600
      };
      
      sandbox.stub(jwt, 'verify').returns(decoded);

      // Act
      const result = authService.verifyToken(token);

      // Assert
      expect(result).to.deep.equal(decoded);
    });

    it('should throw error if token is invalid', () => {
      // Arrange
      const token = 'invalid-jwt-token';
      
      sandbox.stub(jwt, 'verify').throws(new Error('Invalid token'));

      // Act & Assert
      try {
        authService.verifyToken(token);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(401);
        expect(error.message).to.include('Please authenticate');
      }
    });
  });

  describe('generateToken', () => {
    it('should generate token correctly', () => {
      // Arrange
      const userId = '1';
      const expires = '1h';
      const type = 'access';
      const secret = 'test-secret';
      
      const token = 'jwt-token';
      const jwtSignStub = sandbox.stub(jwt, 'sign').returns(token);

      // Act
      const result = authService.generateToken(userId, expires, type, secret);

      // Assert
      expect(result).to.equal(token);
      expect(jwtSignStub.calledOnce).to.be.true;
      
      const signArgs = jwtSignStub.firstCall.args;
      expect(signArgs[0]).to.deep.equal({ sub: userId, type });
      expect(signArgs[1]).to.equal(secret || config.jwt.secret);
      expect(signArgs[2].expiresIn).to.equal(expires);
    });
  });
});
