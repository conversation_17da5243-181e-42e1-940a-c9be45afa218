const express = require('express');
const { authenticate, authorize } = require('../middlewares/auth.middleware');
const validate = require('../middlewares/validate');
const userController = require('../controllers/user.controller');
const userValidation = require('../validations/user.validation');

const router = express.Router();

/**
 * @route POST /api/users
 * @desc Create a new user
 * @access Private/Admin
 */
router
  .route('/')
  .post(
    authenticate,
    authorize(['manageUsers']),
    validate(userValidation.createUser),
    userController.createUser
  )
  .get(
    authenticate,
    authorize(['getUsers']),
    validate(userValidation.getUsers),
    userController.getUsers
  );

/**
 * @route GET /api/users/me
 * @desc Get current user profile
 * @access Private
 */
router
  .route('/me')
  .get(authenticate, userController.getMe);

/**
 * @route GET /api/users/me/preferences
 * @desc Get current user preferences
 * @access Private
 */
router
  .route('/me/preferences')
  .get(authenticate, userController.getMyPreferences);

/**
 * @route GET /api/users/dropdown
 * @desc Get users for dropdown selection
 * @access Private
 */
router
  .route('/dropdown')
  .get(authenticate, userController.getUsersForDropdown);

/**
 * @route PATCH /api/users/profile
 * @desc Update current user profile
 * @access Private
 */
router
  .route('/profile')
  .patch(
    authenticate,
    validate(userValidation.updateMyProfile), // Assumes userValidation.updateMyProfile schema exists for request body
    userController.updateMyProfile
  );

/**
 * @route POST /api/users/:userId/reset-password
 * @desc Admin reset user password
 * @access Private/Admin
 */
router
  .route('/:userId/reset-password')
  .post(
    authenticate,
    authorize(['manageUsers']),
    validate(userValidation.resetUserPassword),
    userController.resetUserPassword
  );

/**
 * @route GET/PATCH/DELETE /api/users/:userId
 * @desc Get/Update/Delete user by ID
 * @access Private/Admin
 */
router
  .route('/:userId')
  .get(
    authenticate,
    authorize(['getUsers']),
    validate(userValidation.getUser),
    userController.getUser
  )
  .patch(
    authenticate,
    authorize(['manageUsers']),
    validate(userValidation.updateUser),
    userController.updateUser
  )
  .delete(
    authenticate,
    authorize(['manageUsers']),
    validate(userValidation.deleteUser),
    userController.deleteUser
  );

module.exports = router;