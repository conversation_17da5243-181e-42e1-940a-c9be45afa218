<template>
  <div class="data-source-switcher">
    <div class="switch-container">
      <span class="label">数据源:</span>
      <div
        class="switch"
        :class="{ 'is-mock': isMockActive, 'disabled': isProd }"
        @click="toggleSource"
      >
        <div class="switch-button"></div>
        <span class="switch-text api-text">API</span>
        <span class="switch-text mock-text">Mock</span>
      </div>
      <div class="status-indicator" :class="{'status-online': apiStatus === 'online', 'status-offline': apiStatus === 'offline', 'status-unknown': apiStatus === 'unknown'}">
        <span class="status-dot"></span>
        <span class="status-text">{{ getStatusText }}</span>
      </div>
      <div class="data-source-info">
        <span class="info-text">{{ dataSourceInfo }}</span>
      </div>
    </div>
    <div v-if="showMessage" class="message">
      <div class="message-content">
        <span>{{ message }}</span>
        <button @click="reloadPage" class="reload-btn">刷新页面</button>
      </div>
    </div>
  </div>
</template>

<script>
import { DataSourceType, getCurrentDataSource, toggleDataSource, resetToDefaultDataSource } from '@/services/dataSourceConfig';
import { ref, computed, onMounted, onUnmounted } from 'vue';

export default {
  name: 'DataSourceSwitcher',

  setup() {
    const isMockActive = ref(getCurrentDataSource() === DataSourceType.MOCK);
    const showMessage = ref(false);
    const message = ref('');
    const isProd = ref(false);
    const apiStatus = ref('unknown'); // 'online', 'offline', 'unknown'
    let checkInterval = null;

    try {
      isProd.value = import.meta.env.PROD === true;
    } catch (err) {
      console.warn('无法检测环境变量，假定为非生产环境');
    }

    // 计算属性
    const getStatusText = computed(() => {
      if (isMockActive.value) return '模拟数据';

      if (apiStatus.value === 'online') return 'API在线';
      if (apiStatus.value === 'offline') return 'API离线';
      return 'API状态未知';
    });

    // 数据源详细信息
    const dataSourceInfo = computed(() => {
      const type = isMockActive.value ? 'MOCK' : 'API';
      const baseUrl = import.meta.env.VITE_API_URL || '';
      return `当前数据源: ${type} | 基础URL: ${baseUrl}`;
    });

    // 检查API状态
    const checkApiStatus = async () => {
      if (isMockActive.value) return;

      try {
        const testEndpoint = '/api/health' // 假设有健康检查端点，若没有可以改为任意有效API
        const response = await fetch(testEndpoint, {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          // 设置超时
          signal: AbortSignal.timeout(3005)
        });
        apiStatus.value = response.ok ? 'online' : 'offline';
      } catch (error) {
        console.error('API状态检查失败:', error);
        apiStatus.value = 'offline';
      }
    };

    onMounted(() => {
      // 在组件创建时确保生产环境下使用API
      if (isProd.value && isMockActive.value) {
        resetToDefaultDataSource();
        isMockActive.value = false;
      }

      // 立即检查一次API状态
      checkApiStatus();

      // 每60秒检查一次API状态
      checkInterval = setInterval(checkApiStatus, 60000);
    });

    onUnmounted(() => {
      // 清除定时器
      if (checkInterval) {
        clearInterval(checkInterval);
      }
    });

    const toggleSource = () => {
      // 生产环境不允许切换
      if (isProd.value) {
        message.value = '生产环境下仅允许使用API数据源';
        showMessage.value = true;
        setTimeout(() => {
          showMessage.value = false;
        }, 3005);
        return;
      }

      const newType = toggleDataSource();
      isMockActive.value = newType === DataSourceType.MOCK;

      // 显示切换消息
      message.value = isMockActive.value
        ? '已切换到模拟数据，点击刷新应用更改'
        : '已切换到实际API，点击刷新应用更改';
      showMessage.value = true;

      // 自动隐藏消息
      setTimeout(() => {
        showMessage.value = false;
      }, 8000);

      // 发布全局事件，通知数据源已切换
      window.dispatchEvent(new CustomEvent('datasource-changed', {
        detail: { type: newType }
      }));
    };

    const reloadPage = () => {
      window.location.reload();
    };

    return {
      isMockActive,
      showMessage,
      message,
      isProd,
      apiStatus,
      getStatusText,
      dataSourceInfo,
      toggleSource,
      reloadPage
    };
  }
};
</script>

<style scoped>
.data-source-switcher {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin: 10px;
  position: relative;
}

.switch-container {
  display: flex;
  align-items: center;
}

.label {
  margin-right: 10px;
  font-size: 14px;
  color: #606266;
}

.switch {
  position: relative;
  display: inline-flex;
  align-items: center;
  width: 100px;
  height: 30px;
  border-radius: 15px;
  background-color: #13c2c2;
  cursor: pointer;
  transition: background-color 0.3s;
  padding: 0 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.switch.is-mock {
  background-color: #722ed1;
}

.switch.disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.switch-button {
  position: absolute;
  left: 3px;
  width: 45px;
  height: 24px;
  border-radius: 12px;
  background-color: #fff;
  transition: transform 0.3s;
}

.switch.is-mock .switch-button {
  transform: translateX(47px);
}

.switch-text {
  z-index: 1;
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: bold;
  transition: color 0.3s;
}

.api-text {
  margin-left: 8px;
}

.mock-text {
  margin-left: auto;
  margin-right: 8px;
}

.switch:not(.is-mock) .api-text,
.switch.is-mock .mock-text {
  color: rgba(255, 255, 255, 1);
}

.message {
  position: absolute;
  top: 40px;
  right: 0;
  background-color: #f0f9ff;
  border: 1px solid #bae7ff;
  border-radius: 4px;
  padding: 5px 10px;
  width: 250px;
  font-size: 12px;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.reload-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 2px 8px;
  font-size: 12px;
  cursor: pointer;
  margin-left: 10px;
}

.reload-btn:hover {
  background-color: #40a9ff;
}

.data-source-info {
  margin-left: 10px;
  padding: 4px 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
  color: #333;
}

.info-text {
  font-family: monospace;
}

/* API状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  margin-left: 10px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  background-color: #f5f5f5;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}

.status-online {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.status-online .status-dot {
  background-color: #52c41a;
}

.status-offline {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

.status-offline .status-dot {
  background-color: #ff4d4f;
}

.status-unknown {
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
}

.status-unknown .status-dot {
  background-color: #bfbfbf;
}
</style>