const db = require('../models');
const Subcontract = db.Subcontract;
const Attachment = db.Attachment;
const User = db.User;
const Project = db.Project;
const { Op } = require('sequelize');
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });

// 获取所有分包合同（带分页和筛选）
exports.getSubcontracts = async (req, res) => {
  try {
    const { page = 1, limit = 10, projectName, contractName, contractCode, contractType, contractStatus } = req.query;

    // 构建筛选条件
    const where = {};
    if (projectName) where.projectName = { [Op.like]: `%${projectName}%` };
    if (contractName) where.contractName = { [Op.like]: `%${contractName}%` };
    if (contractCode) where.contractCode = { [Op.like]: `%${contractCode}%` };
    if (contractType) where.contractType = contractType;
    if (contractStatus) where.contractStatus = contractStatus;

    // 分页查询
    const offset = (page - 1) * limit;
    const { count, rows } = await Subcontract.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      include: [
        {
          model: Attachment,
          as: 'attachments'
        },
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'code']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    return res.json({
      results: rows,
      totalResults: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    });
  } catch (error) {
    console.error('获取分包合同失败:', error);
    return res.status(500).json({ message: '获取分包合同失败' });
  }
};

// 获取单个分包合同
exports.getSubcontract = async (req, res) => {
  try {
    const { id } = req.params;

    const subcontract = await Subcontract.findByPk(id, {
      include: [
        {
          model: Attachment,
          as: 'attachments'
        },
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'code']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    if (!subcontract) {
      return res.status(404).json({ message: '分包合同不存在' });
    }

    return res.json(subcontract);
  } catch (error) {
    console.error('获取分包合同失败:', error);
    return res.status(500).json({ message: '获取分包合同失败' });
  }
};

// 创建新的分包合同
exports.createSubcontract = async (req, res) => {
  try {
    const subcontractData = req.body;
    
    // 处理日期字段，确保无效日期不会导致数据库错误
    if (subcontractData.invoiceDate === 'Invalid date' || subcontractData.invoiceDate === '') {
      subcontractData.invoiceDate = null;
    }
    
    if (subcontractData.paymentDate === 'Invalid date' || subcontractData.paymentDate === '') {
      subcontractData.paymentDate = null;
    }

    // 处理文件上传（如果有）
    const attachments = [];
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        const attachment = {
          fileName: file.originalname,
          originalName: file.originalname,
          mimeType: file.mimetype,
          filePath: file.path,
          fileSize: file.size,
          uploadedBy: req.user ? req.user.id : null,
          isPublic: false
        };
        attachments.push(attachment);
      }
    }

    // 设置创建者
    if (req.user) {
      subcontractData.createdBy = req.user.id;
    }

    // 创建分包合同
    const subcontract = await Subcontract.create(subcontractData);

    // 创建附件记录
    if (attachments.length > 0) {
      for (const attachment of attachments) {
        attachment.subcontractId = subcontract.id;
        await Attachment.create(attachment);
      }
    }

    return res.status(201).json(subcontract);
  } catch (error) {
    console.error('创建分包合同失败:', error);
    console.error('错误详情:', JSON.stringify(error, null, 2));
    console.error('请求数据:', JSON.stringify(req.body, null, 2));
    
    // 检查是否是唯一约束冲突
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(400).json({ 
        message: '合同编号已存在，请使用不同的编号',
        field: 'contractCode'
      });
    }
    
    // 检查是否是验证错误
    if (error.name === 'SequelizeValidationError') {
      const validationErrors = error.errors.map(err => ({
        field: err.path,
        message: err.message
      }));
      return res.status(400).json({ 
        message: '表单验证失败',
        errors: validationErrors
      });
    }
    
    return res.status(500).json({ 
      message: '创建分包合同失败', 
      error: error.message || '未知错误',
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
};

// 更新分包合同
exports.updateSubcontract = async (req, res) => {
  try {
    const { id } = req.params;
    const subcontractData = req.body;
    
    // 处理日期字段，确保无效日期不会导致数据库错误
    if (subcontractData.invoiceDate === 'Invalid date' || subcontractData.invoiceDate === '') {
      subcontractData.invoiceDate = null;
    }
    
    if (subcontractData.paymentDate === 'Invalid date' || subcontractData.paymentDate === '') {
      subcontractData.paymentDate = null;
    }

    // 查找分包合同
    const subcontract = await Subcontract.findByPk(id);

    if (!subcontract) {
      return res.status(404).json({ message: '分包合同不存在' });
    }

    // 设置更新者
    if (req.user) {
      subcontractData.updatedBy = req.user.id;
    }

    // 更新分包合同
    await subcontract.update(subcontractData);

    // 处理文件上传（如果有）
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        const attachment = {
          fileName: file.originalname,
          originalName: file.originalname,
          mimeType: file.mimetype,
          filePath: file.path,
          fileSize: file.size,
          subcontractId: subcontract.id,
          uploadedBy: req.user ? req.user.id : null,
          isPublic: false
        };
        await Attachment.create(attachment);
      }
    }

    // 处理现有附件（如果有）
    if (subcontractData.existingAttachments) {
      const existingIds = subcontractData.existingAttachments.map(id => parseInt(id));

      // 删除不在列表中的附件
      const attachments = await Attachment.findAll({
        where: {
          subcontractId: subcontract.id
        }
      });

      for (const attachment of attachments) {
        if (!existingIds.includes(attachment.id)) {
          // 删除文件
          if (attachment.filePath && fs.existsSync(attachment.filePath)) {
            fs.unlinkSync(attachment.filePath);
          }
          // 删除记录
          await attachment.destroy();
        }
      }
    }

    // 获取更新后的完整记录
    const updatedSubcontract = await Subcontract.findByPk(id, {
      include: [
        {
          model: Attachment,
          as: 'attachments'
        },
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'code']
        },
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: User,
          as: 'updater',
          attributes: ['id', 'username', 'email']
        }
      ]
    });

    return res.json(updatedSubcontract);
  } catch (error) {
    console.error('更新分包合同失败:', error);
    return res.status(500).json({ message: '更新分包合同失败' });
  }
};

// 删除分包合同
exports.deleteSubcontract = async (req, res) => {
  try {
    const { id } = req.params;

    const subcontract = await Subcontract.findByPk(id, {
      include: [
        {
          model: Attachment,
          as: 'attachments'
        }
      ]
    });

    if (!subcontract) {
      return res.status(404).json({ message: '分包合同不存在' });
    }

    // 删除相关附件文件
    if (subcontract.attachments && subcontract.attachments.length > 0) {
      for (const attachment of subcontract.attachments) {
        if (attachment.filePath && fs.existsSync(attachment.filePath)) {
          fs.unlinkSync(attachment.filePath);
        }
      }
    }

    // 删除分包合同（级联删除附件记录）
    await subcontract.destroy();

    return res.json({ message: '分包合同删除成功' });
  } catch (error) {
    console.error('删除分包合同失败:', error);
    return res.status(500).json({ message: '删除分包合同失败' });
  }
};
