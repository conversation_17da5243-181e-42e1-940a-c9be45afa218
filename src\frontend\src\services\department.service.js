import axios from 'axios';
import { useAuthStore } from '@/stores/auth';

const API_URL = import.meta.env.VITE_API_URL || '';

/**
 * 创建一个带有Auth头部的axios实例
 * @returns {Object} Axios实例
 */
const createAuthAxios = () => {
  const authStore = useAuthStore();
  const token = authStore.token;
  
  const instance = axios.create({
    baseURL: API_URL,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': token ? `Bearer ${token}` : ''
    }
  });
  
  // 添加响应拦截器处理401错误
  instance.interceptors.response.use(
    response => response,
    error => {
      if (error.response && error.response.status === 401) {
        // 处理未授权错误，可能需要重定向到登录页
        console.error('未授权访问，请先登录');
        // 可以在这里触发重定向
        // window.location.href = '/login';
      }
      return Promise.reject(error);
    }
  );
  
  return instance;
};

/**
 * Service for department management API calls
 */
export const departmentService = {
  /**
   * Get all departments
   * @returns {Promise} Promise with departments data
   */
  getAllDepartments: async () => {
    try {
      const axiosInstance = createAuthAxios();
      const response = await axiosInstance.get('/api/departments');
      return response.data;
    } catch (error) {
      console.error('Error fetching departments:', error);
      // 返回一个模拟的空结果，以便前端不报错
      return { success: false, data: [], message: error.response?.data?.message || 'Failed to fetch departments' };
    }
  },

  /**
   * Get department by ID
   * @param {number} id - Department ID
   * @returns {Promise} Promise with department data
   */
  getDepartmentById: async (id) => {
    try {
      const axiosInstance = createAuthAxios();
      const response = await axiosInstance.get(`/api/departments/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching department with ID ${id}:`, error);
      return { success: false, data: null, message: error.response?.data?.message || `Failed to fetch department with ID ${id}` };
    }
  },

  /**
   * Create new department
   * @param {Object} departmentData - Department data
   * @returns {Promise} Promise with created department data
   */
  createDepartment: async (departmentData) => {
    try {
      const axiosInstance = createAuthAxios();
      const response = await axiosInstance.post('/api/departments', departmentData);
      return response.data;
    } catch (error) {
      console.error('Error creating department:', error);
      throw error;
    }
  },

  /**
   * Update department
   * @param {number} id - Department ID
   * @param {Object} departmentData - Updated department data
   * @returns {Promise} Promise with updated department data
   */
  updateDepartment: async (id, departmentData) => {
    try {
      const axiosInstance = createAuthAxios();
      const response = await axiosInstance.put(`/api/departments/${id}`, departmentData);
      return response.data;
    } catch (error) {
      console.error(`Error updating department with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete department
   * @param {number} id - Department ID
   * @returns {Promise} Promise with deletion status
   */
  deleteDepartment: async (id) => {
    try {
      const axiosInstance = createAuthAxios();
      const response = await axiosInstance.delete(`/api/departments/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting department with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Get department hierarchy
   * @returns {Promise} Promise with department hierarchy data
   */
  getDepartmentHierarchy: async () => {
    try {
      const axiosInstance = createAuthAxios();
      const response = await axiosInstance.get('/api/departments/hierarchy');
      return response.data;
    } catch (error) {
      console.error('Error fetching department hierarchy:', error);
      return { success: false, data: [], message: error.response?.data?.message || 'Failed to fetch department hierarchy' };
    }
  },
}; 