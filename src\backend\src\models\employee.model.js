const { DataTypes, Op } = require('sequelize');
const { sequelize } = require('../config/database');

// Base Employee Model
const Employee = sequelize.define('employee', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  idNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  position: {
    type: DataTypes.STRING,
    allowNull: false
  },
  department: {
    type: DataTypes.STRING,
    allowNull: false
  },
  departmentId: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive'),
    defaultValue: 'active'
  },
  employeeType: {
    type: DataTypes.ENUM('contract', 'temporary'),
    allowNull: false
  }
}, {
  timestamps: true,
  paranoid: true
});

// Contact Info Model
const ContactInfo = sequelize.define('contactinfo', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  phone: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  email: {
    type: DataTypes.STRING,
    allowNull: true,

  },
  address: {
    type: DataTypes.STRING,
    allowNull: true,
  },
}, {
  timestamps: true,
});

// Contract Worker Model
const ContractWorker = sequelize.define('contractworker', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  employeeId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'employee',
      key: 'id'
    }
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'project',
      key: 'id',
    },
  },
  contractNumber: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  month:{
    type: DataTypes.STRING,
    allowNull: true,
  },
  startDate: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  endDate: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  salary: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    validate: {
      min: 0,
    },
  }
}, {
  timestamps: true,
});

// Temporary Worker Model
const TemporaryWorker = sequelize.define('temporaryworker', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  employeeId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'employee',
      key: 'id'
    }
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'project',
      key: 'id',
    },
  },
  month: {
    type: DataTypes.STRING,
    allowNull: true,
  },
  startDate: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  endDate: {
    type: DataTypes.DATE,
    allowNull: true,
  },
  dailyRate: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0,
    },
  }
}, {
  timestamps: true,
});

// Define Associations
Employee.hasOne(ContactInfo, {
  foreignKey: 'employeeId',
  as: 'contactInfo'
});
ContactInfo.belongsTo(Employee);

Employee.hasOne(ContractWorker, {
  foreignKey: 'employeeId',
  as: 'contractDetails'
});
ContractWorker.belongsTo(Employee, {
  foreignKey: 'employeeId',
  as: 'employee'
});

Employee.hasOne(TemporaryWorker, {
  foreignKey: 'employeeId',
  as: 'temporaryDetails'
});
TemporaryWorker.belongsTo(Employee, {
  foreignKey: 'employeeId',
  as: 'employee'
});

// Instance Methods
Employee.prototype.toJSON = function() {
  const values = { ...this.get() };
  delete values.deletedAt;
  return values;
};

// Static Methods
Employee.isIdNumberTaken = async function(idNumber, excludeId) {
  const employee = await this.findOne({
    where: {
      idNumber,
      ...(excludeId && { id: { [Op.ne]: excludeId } }),
    },
  });
  return !!employee;
};

// Contract Worker Static Methods
ContractWorker.isContractNumberTaken = async function(contractNumber, excludeId) {
  const worker = await this.findOne({
    where: {
      contractNumber,
      ...(excludeId && { id: { [Op.ne]: excludeId } }),
    },
  });
  return !!worker;
};

// 添加 isIdNumberTaken 方法到 ContractWorker 模型
ContractWorker.isIdNumberTaken = async function(idNumber, excludeId) {
  if (excludeId) {
    // 首先通过contractWorkerId获取对应的employeeId
    const contractWorker = await this.findByPk(excludeId);
    if (contractWorker) {
      // 然后查询其他不同employeeId但相同idNumber的员工
      const worker = await this.findOne({
        include: [{
          model: Employee,
          as: 'employee',
          where: {
            idNumber,
            id: { [Op.ne]: contractWorker.employeeId }
          },
        }],
      });
      return !!worker;
    }
  }
  
  // 如果没有excludeId或找不到对应的contractWorker，则直接查询是否存在相同idNumber
  const worker = await this.findOne({
    include: [{
      model: Employee,
      as: 'employee',
      where: { idNumber },
    }],
  });
  return !!worker;
};

// 添加 isIdNumberTaken 方法到 TemporaryWorker 模型
TemporaryWorker.isIdNumberTaken = async function(idNumber, excludeId) {
  if (excludeId) {
    // 首先通过temporaryWorkerId获取对应的employeeId
    const temporaryWorker = await this.findByPk(excludeId);
    if (temporaryWorker) {
      // 然后查询其他不同employeeId但相同idNumber的员工
      const worker = await this.findOne({
        include: [{
          model: Employee,
          as: 'employee',
          where: {
            idNumber,
            id: { [Op.ne]: temporaryWorker.employeeId }
          },
        }],
      });
      return !!worker;
    }
  }
  
  // 如果没有excludeId或找不到对应的temporaryWorker，则直接查询是否存在相同idNumber
  const worker = await this.findOne({
    include: [{
      model: Employee,
      as: 'employee',
      where: { idNumber },
    }],
  });
  return !!worker;
};

module.exports = {
  Employee,
  ContractWorker,
  TemporaryWorker,
  ContactInfo
};