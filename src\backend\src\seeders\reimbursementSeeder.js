const { v4: uuidv4 } = require('uuid');
const { Reimbursement } = require('../models/reimbursement.model');
const { User } = require('../models/user.model');
const { Project } = require('../models/project.model');
const { faker } = require('@faker-js/faker/locale/zh_CN');

const reimbursementTypes = ['差旅费', '办公用品', '交通费', '餐饮费', '通讯费', '培训费', '其他'];
const invoiceTypes = ['增值税专用发票', '增值税普通发票', '电子发票', '无发票'];
const statusTypes = ['pending', 'approved', 'rejected', 'completed'];

async function seedReimbursements() {
  try {
    // Check if reimbursements already exist
    const reimbursementCount = await Reimbursement.count();
    if (reimbursementCount > 0) {
      console.log('Reimbursements already exist, skipping reimbursement seeding');
      return;
    }

    // Use a default admin ID for createdBy field
    // Since we're using integer IDs for createdBy but User model uses UUID
    const adminId = 1; // Using a default admin ID

    // Get projects for reference
    const projects = await Project.findAll({ limit: 10 });
    if (projects.length === 0) {
      console.log('No projects found for reimbursement seeding, using generic project names');
    }

    // Create 20 reimbursements
    const reimbursements = [];
    for (let i = 0; i < 20; i++) {
      // Generate random date in the past year
      const reimbursementDate = faker.date.between({
        from: '2023-01-01',
        to: '2024-05-31'
      });

      // Use project name if available, otherwise generate one
      const projectName = projects.length > 0
        ? projects[i % projects.length].name
        : `${faker.location.city()}${faker.helpers.arrayElement(['商业中心', '住宅小区', '办公楼', '工业园区', '学校'])}项目`;

      const totalAmount = faker.number.float({ min: 100, max: 10000, precision: 2 });
      const taxRate = faker.helpers.arrayElement([0, 3, 6, 9, 13]);

      const reimbursement = {
        projectName,
        reimbursementDate,
        reimbursementType: faker.helpers.arrayElement(reimbursementTypes),
        supplier: faker.company.name(),
        reason: faker.lorem.sentence(),
        totalAmount,
        taxRate,
        invoiceType: faker.helpers.arrayElement(invoiceTypes),
        paymentInfo: {
          bankName: faker.helpers.arrayElement(['中国银行', '工商银行', '建设银行', '农业银行', '招商银行']),
          accountNumber: faker.finance.accountNumber(16),
          accountName: faker.person.fullName()
        },
        status: faker.helpers.arrayElement(statusTypes),
        createdBy: adminId,
        updatedBy: adminId,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      reimbursements.push(reimbursement);
    }

    // Bulk create reimbursements
    await Reimbursement.bulkCreate(reimbursements);
    console.log('Successfully seeded reimbursements');
  } catch (error) {
    console.error('Error seeding reimbursements:', error);
    throw error;
  }
}

module.exports = seedReimbursements;
