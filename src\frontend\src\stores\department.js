import { defineStore } from 'pinia';
import { departmentService } from '@/services/department.service';
import { ref, computed } from 'vue';

export const useDepartmentStore = defineStore('department', () => {
  // State
  const departments = ref([]);
  const currentDepartment = ref(null);
  const departmentHierarchy = ref([]);
  const loading = ref(false);
  const error = ref(null);

  // Getters
  const getDepartments = computed(() => departments.value);
  const getCurrentDepartment = computed(() => currentDepartment.value);
  const getDepartmentHierarchy = computed(() => departmentHierarchy.value);
  const isLoading = computed(() => loading.value);
  const getError = computed(() => error.value);
  
  // Active departments
  const getActiveDepartments = computed(() => 
    departments.value.filter(dept => dept.status === 'active')
  );

  // Department options for select
  const getDepartmentOptions = computed(() => 
    departments.value.map(dept => ({
      value: String(dept.id),
      label: dept.name
    }))
  );

  // Actions
  /**
   * Fetch all departments
   */
  async function fetchDepartments() {
    loading.value = true;
    error.value = null;
    try {
      const response = await departmentService.getAllDepartments();
      // 处理不同格式的API响应
      if (response.data) {
        departments.value = response.data;
      } else if (response.results) {
        departments.value = response.results;
      } else if (Array.isArray(response)) {
        departments.value = response;
      } else {
        departments.value = [];
        console.warn('未知的部门数据格式', response);
      }
      
      // 确保至少有一个默认部门
      if (departments.value.length === 0) {
        departments.value = [
          { id: 'default-dept', name: '默认部门', status: 'active' }
        ];
        console.warn('未找到部门数据，已添加默认部门');
      }
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to fetch departments';
      console.error('Error in fetchDepartments:', err);
      
      // 在错误情况下也确保有默认部门
      departments.value = [
        { id: 'default-dept', name: '默认部门', status: 'active' }
      ];
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch department by ID
   * @param {number} id - Department ID
   */
  async function fetchDepartment(id) {
    loading.value = true;
    error.value = null;
    try {
      const response = await departmentService.getDepartmentById(id);
      // 处理不同格式的API响应
      if (response.data) {
        currentDepartment.value = response.data;
      } else if (Array.isArray(response) && response.length > 0) {
        currentDepartment.value = response[0];
      } else {
        currentDepartment.value = response || null;
      }
    } catch (err) {
      error.value = err.response?.data?.message || `Failed to fetch department with ID ${id}`;
      console.error('Error in fetchDepartment:', err);
    } finally {
      loading.value = false;
    }
  }

  /**
   * Create new department
   * @param {Object} departmentData - Department data
   */
  async function createDepartment(departmentData) {
    loading.value = true;
    error.value = null;
    try {
      const response = await departmentService.createDepartment(departmentData);
      departments.value.push(response.data);
      return response.data;
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to create department';
      console.error('Error in createDepartment:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Update department
   * @param {number} id - Department ID
   * @param {Object} departmentData - Updated department data
   */
  async function updateDepartment(id, departmentData) {
    loading.value = true;
    error.value = null;
    try {
      const response = await departmentService.updateDepartment(id, departmentData);
      
      // Update in departments array
      const index = departments.value.findIndex(d => d.id === id);
      if (index !== -1) {
        departments.value[index] = response.data;
      }
      
      // Update current department if it's the one being edited
      if (currentDepartment.value && currentDepartment.value.id === id) {
        currentDepartment.value = response.data;
      }
      
      return response.data;
    } catch (err) {
      error.value = err.response?.data?.message || `Failed to update department with ID ${id}`;
      console.error('Error in updateDepartment:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Delete department
   * @param {number} id - Department ID
   */
  async function deleteDepartment(id) {
    loading.value = true;
    error.value = null;
    try {
      await departmentService.deleteDepartment(id);
      
      // Remove from departments array
      departments.value = departments.value.filter(d => d.id !== id);
      
      // Clear current department if it's the one being deleted
      if (currentDepartment.value && currentDepartment.value.id === id) {
        currentDepartment.value = null;
      }
      
      return true;
    } catch (err) {
      error.value = err.response?.data?.message || `Failed to delete department with ID ${id}`;
      console.error('Error in deleteDepartment:', err);
      throw err;
    } finally {
      loading.value = false;
    }
  }

  /**
   * Fetch department hierarchy
   */
  async function fetchDepartmentHierarchy() {
    loading.value = true;
    error.value = null;
    try {
      const response = await departmentService.getDepartmentHierarchy();
      departmentHierarchy.value = response.data;
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to fetch department hierarchy';
      console.error('Error in fetchDepartmentHierarchy:', err);
    } finally {
      loading.value = false;
    }
  }

  /**
   * Reset current department
   */
  function resetCurrentDepartment() {
    currentDepartment.value = null;
  }

  /**
   * Reset error
   */
  function resetError() {
    error.value = null;
  }

  return {
    // State
    departments,
    currentDepartment,
    departmentHierarchy,
    loading,
    error,
    
    // Getters
    getDepartments,
    getCurrentDepartment,
    getDepartmentHierarchy,
    isLoading,
    getError,
    getActiveDepartments,
    getDepartmentOptions,
    
    // Actions
    fetchDepartments,
    fetchDepartment,
    createDepartment,
    updateDepartment,
    deleteDepartment,
    fetchDepartmentHierarchy,
    resetCurrentDepartment,
    resetError
  };
}); 