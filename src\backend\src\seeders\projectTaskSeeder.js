const { ProjectTask } = require('../models');
const { Project } = require('../models/project.model');
const { Employee } = require('../models/employee.model');
const { faker } = require('@faker-js/faker/locale/zh_CN');

const taskTypes = ['design', 'development', 'testing', 'deployment', 'maintenance', 'documentation'];
const priorities = ['low', 'medium', 'high', 'urgent'];
const statuses = ['not_started', 'in_progress', 'completed', 'on_hold', 'cancelled'];

async function seedProjectTasks() {
  try {
    console.log('Starting to seed project tasks...');

    // Clear existing data
    await ProjectTask.destroy({ where: {} });
    console.log('Cleared existing project tasks');

    // Get all projects and employees for reference
    const projects = await Project.findAll();
    const employees = await Employee.findAll();

    if (!projects.length) {
      console.log('No projects found. Please seed projects first');
      return;
    }

    if (!employees.length) {
      console.log('No employees found. Please seed employees first');
      return;
    }

    console.log(`Found ${projects.length} projects and ${employees.length} employees`);

    // Create parent tasks (10 tasks)
    const parentTasks = [];
    for (let i = 0; i < 10; i++) {
      const assignee = faker.helpers.arrayElement(employees);
      const startDate = faker.date.between({ 
        from: '2023-01-01', 
        to: '2024-12-31' 
      });
      
      const endDate = faker.date.between({
        from: startDate,
        to: new Date(startDate.getTime() + 90 * 24 * 60 * 60 * 1000)
      });

      const task = {
        projectId: faker.helpers.arrayElement(projects).id,
        name: faker.helpers.arrayElement([
          'Requirements Analysis',
          'UI Design',
          'Database Design',
          'API Development',
          'Frontend Development',
          'Unit Testing',
          'Integration Testing',
          'Performance Optimization',
          'Code Review',
          'Documentation'
        ]) + ' - ' + faker.lorem.words(2),
        description: faker.lorem.paragraph(),
        taskType: faker.helpers.arrayElement(taskTypes),
        priority: faker.helpers.arrayElement(priorities),
        status: faker.helpers.arrayElement(statuses),
        assigneeId: assignee.id,
        plannedStartDate: startDate,
        plannedEndDate: endDate,
        actualStartDate: faker.helpers.arrayElement([startDate, null]),
        actualEndDate: null,
        remainingWork: faker.number.int({ min: 8, max: 160 }),
        createdBy: assignee.id, // Using the same employee as both assignee and creator for simplicity
        createdAt: new Date(),
        updatedAt: new Date()
      };
      parentTasks.push(task);
    }

    // Create parent tasks and get their IDs
    const createdParentTasks = await ProjectTask.bulkCreate(parentTasks);
    console.log(`Created ${createdParentTasks.length} parent tasks`);

    // Now create child tasks (20 tasks)
    const childTasks = [];
    for (let i = 0; i < 20; i++) {
      const assignee = faker.helpers.arrayElement(employees);
      const startDate = faker.date.between({ 
        from: '2023-01-01', 
        to: '2024-12-31' 
      });
      
      const endDate = faker.date.between({
        from: startDate,
        to: new Date(startDate.getTime() + 90 * 24 * 60 * 60 * 1000)
      });

      const task = {
        projectId: faker.helpers.arrayElement(projects).id,
        name: faker.helpers.arrayElement([
          'Subtask - Requirements Analysis',
          'Subtask - UI Design',
          'Subtask - Database Design',
          'Subtask - API Development',
          'Subtask - Frontend Development',
          'Subtask - Unit Testing',
          'Subtask - Integration Testing',
          'Subtask - Performance Optimization',
          'Subtask - Code Review',
          'Subtask - Documentation'
        ]) + ' - ' + faker.lorem.words(2),
        description: faker.lorem.paragraph(),
        taskType: faker.helpers.arrayElement(taskTypes),
        priority: faker.helpers.arrayElement(priorities),
        status: faker.helpers.arrayElement(statuses),
        assigneeId: assignee.id,
        plannedStartDate: startDate,
        plannedEndDate: endDate,
        actualStartDate: faker.helpers.arrayElement([startDate, null]),
        actualEndDate: null,
        remainingWork: faker.number.int({ min: 4, max: 80 }),
        createdBy: assignee.id, // Using the same employee as both assignee and creator for simplicity
        createdAt: new Date(),
        updatedAt: new Date()
      };
      childTasks.push(task);
    }

    // Create child tasks
    await ProjectTask.bulkCreate(childTasks);
    console.log(`Created ${childTasks.length} child tasks`);
    console.log('Project tasks seeder executed successfully');
  } catch (error) {
    console.error('Error seeding project tasks:', error);
    throw error; // 重新抛出错误以便上层捕获
  }
}

module.exports = seedProjectTasks; 