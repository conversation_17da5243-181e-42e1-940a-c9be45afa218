<template>
  <div>
    <h1 class="text-2xl font-bold mb-6">组织架构</h1>

    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg mb-6">
      <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
          <h2 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
            部门组织结构图
          </h2>
          <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
            公司的部门架构和组织关系
          </p>
        </div>
        <div>
          <button
            @click="expandAll"
            class="mr-2 inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            展开全部
          </button>
          <button
            @click="collapseAll"
            class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            折叠全部
          </button>
        </div>
      </div>

      <div class="p-6">
        <!-- 组织架构树 -->
        <div v-if="loading" class="flex justify-center items-center py-10">
          <div class="loader"></div>
        </div>
        <div v-else-if="error" class="text-center py-10 text-red-500">
          {{ error }}
        </div>
        <div v-else-if="departments.length === 0" class="text-center py-10">
          暂无部门数据，请先创建部门
        </div>
        <div v-else class="org-chart-container">
          <div class="org-chart">
            <div class="node root-node">
              <div class="node-content">
                <h3 class="font-medium">公司架构</h3>
              </div>

              <div class="children">
                <template v-for="(dept, index) in topLevelDepartments" :key="dept.id">
                  <DepartmentNode 
                    :department="dept" 
                    :all-departments="departments"
                    :expanded-departments="expandedDepartments"
                    @toggle-expand="toggleExpand"
                  />
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 员工统计信息 -->
    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h2 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
          部门员工统计
        </h2>
        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
          各部门人员配置情况
        </p>
      </div>
      <div class="px-4 py-5 sm:p-6">
        <div v-if="loading" class="flex justify-center items-center py-10">
          <div class="loader"></div>
        </div>
        <div v-else-if="error" class="text-center py-10 text-red-500">
          {{ error }}
        </div>
        <div v-else-if="departments.length === 0" class="text-center py-10">
          暂无部门数据，请先创建部门
        </div>
        <div v-else>
          <!-- 部门员工统计表格 -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-900">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    部门名称
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    员工人数
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    创建时间
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                <tr v-for="department in departments" :key="department.id">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                    {{ department.name }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {{ department.employeeCount || 0 }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {{ formatDate(department.createdAt) }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { departmentService } from '@/services/department.service';
import DepartmentNode from '@/components/departments/DepartmentNode.vue';

// 状态变量
const departments = ref([]);
const loading = ref(true);
const error = ref(null);
const expandedDepartments = ref([]);

// 计算属性：顶级部门（没有父部门的部门）
const topLevelDepartments = computed(() => {
  return departments.value.filter(dept => !dept.parentId && !dept.parent_id);
});

// 生命周期钩子
onMounted(async () => {
  await fetchDepartments();
});

// 方法
const fetchDepartments = async () => {
  try {
    loading.value = true;
    // 使用departmentService获取部门数据
    const response = await departmentService.getAllDepartments();
    
    // 处理不同格式的API响应
    if (response.data) {
      departments.value = response.data;
    } else if (response.results) {
      departments.value = response.results;
    } else if (Array.isArray(response)) {
      departments.value = response;
    } else {
      departments.value = [];
      console.warn('未知的部门数据格式', response);
    }
    
    // 默认展开第一级部门
    expandedDepartments.value = topLevelDepartments.value.map(dept => dept.id);
  } catch (err) {
    console.error('Failed to fetch departments:', err);
    error.value = '获取部门数据失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

const toggleExpand = (departmentId) => {
  const index = expandedDepartments.value.indexOf(departmentId);
  if (index === -1) {
    expandedDepartments.value.push(departmentId);
  } else {
    expandedDepartments.value.splice(index, 1);
  }
};

const expandAll = () => {
  expandedDepartments.value = departments.value.map(dept => dept.id);
};

const collapseAll = () => {
  expandedDepartments.value = [];
};

const formatDate = (dateString) => {
  if (!dateString) return '未知';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'numeric',
    day: 'numeric'
  });
};
</script>

<style scoped>
.org-chart-container {
  overflow: auto;
  padding: 20px 0;
}

.org-chart {
  display: flex;
  justify-content: center;
}

.node {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.node-content {
  border: 1px solid #e5e7eb;
  padding: 10px 20px;
  border-radius: 8px;
  background-color: #f9fafb;
  min-width: 150px;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.dark .node-content {
  background-color: #1f2937;
  border-color: #374151;
}

.root-node > .node-content {
  background-color: #93c5fd;
  color: #1e3a8a;
  border-color: #60a5fa;
}

.dark .root-node > .node-content {
  background-color: #2563eb;
  color: white;
  border-color: #3b82f6;
}

.children {
  display: flex;
  margin-top: 30px;
  position: relative;
}

.children:before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  width: 1px;
  height: 20px;
  background-color: #e5e7eb;
}

.dark .children:before {
  background-color: #374151;
}

.loader {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #3b82f6;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dark .loader {
  border-color: rgba(255, 255, 255, 0.1);
  border-left-color: #3b82f6;
}
</style> 