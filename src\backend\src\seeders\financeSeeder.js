const { v4: uuidv4 } = require('uuid');
const { User, Project, Supplier } = require('../models');
const { Finance } = require('../models/finance.model');

async function seedFinances() {
  try {
    // Check if finances already exist
    const financeCount = await Finance.count();
    if (financeCount > 0) {
      console.log('Finances already exist, skipping finance seeding');
      return;
    }

    // Get or create a default admin user for createdBy field
    let adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      adminUser = await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin'
      });
    }

    // Get existing projects
    const projects = await Project.findAll({ limit: 10 });
    if (projects.length === 0) {
      console.log('No projects found for finance seeding');
      return;
    }

    // Get existing suppliers
    const suppliers = await Supplier.findAll({ limit: 10 });
    if (suppliers.length === 0) {
      console.log('No suppliers found for finance seeding');
      return;
    }

    // Transaction types
    const transactionTypes = ['income', 'expense', 'invoice', 'payment'];

    // Categories
    const incomeCategories = ['项目收入', '投资收入', '其他收入'];
    const expenseCategories = ['材料费', '人工费', '设备费', '运输费', '管理费', '其他费用'];

    // Payment methods
    const paymentMethods = ['银行转账', '现金', '支票', '微信支付', '支付宝'];

    // Status options
    const statusOptions = ['pending', 'completed', 'cancelled'];

    // Generate random finances
    const finances = [];

    for (let i = 0; i < 50; i++) {
      const transactionType = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
      const project = projects[Math.floor(Math.random() * projects.length)];
      const supplier = suppliers[Math.floor(Math.random() * suppliers.length)];

      // Generate random date within the last year
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 365));

      // Generate random amount between 1000 and 100000
      const amount = Math.floor(Math.random() * 99000) + 1000;

      // Select category based on transaction type
      let category;
      if (transactionType === 'income') {
        category = incomeCategories[Math.floor(Math.random() * incomeCategories.length)];
      } else {
        category = expenseCategories[Math.floor(Math.random() * expenseCategories.length)];
      }

      // Generate random invoice number
      const invoiceNumber = `INV-${String(i + 1).padStart(6, '0')}`;

      // Select payment method
      const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];

      // Select status
      const status = statusOptions[Math.floor(Math.random() * statusOptions.length)];

      finances.push({
        id: uuidv4(),
        type: transactionType,
        amount: amount,
        currency: 'CNY',
        date: date,
        description: `${transactionType === 'income' ? '收入' : '支出'}：${category} - ${i + 1}`,
        category: category,
        projectId: project.id,
        supplierId: supplier.id,
        reference: invoiceNumber,
        paymentMethod: paymentMethod,
        status: status,
        createdBy: adminUser.id
      });
    }

    // Bulk create finances
    await Finance.bulkCreate(finances);
    console.log('Successfully seeded finances');
  } catch (error) {
    console.error('Error seeding finances:', error);
    throw error;
  }
}

module.exports = seedFinances;
