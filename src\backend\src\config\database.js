const { Sequelize } = require('sequelize');
const logger = require('./logger');
require('dotenv').config();
const config = require('./config');

// Default configurations PostgreSQL
const defaultConfig = {
  postgres: {
    host: 'localhost',
    port: 5432,
    pool: {
      max: 5,
      min: 0,
      acquire: 30000,
      idle: 10000,
      evict: 1000,
    },
    // PostgreSQL-specific default options
    dialectOptions: {
      statement_timeout: 10000, // 10 seconds
      idle_in_transaction_session_timeout: 60000, // 1 minute
      application_name: '<PERSON>heApp'
    }
  }
};

// Create Sequelize instance
let sequelizeConfig = {
  dialect: process.env.DB_DIALECT || 'postgres',
  logging: process.env.DB_LOGGING === 'true' ? (msg) => logger.debug(msg) : false,
  pool: {
    max: parseInt(process.env.DB_POOL_MAX || defaultConfig.postgres.pool.max, 10),
    min: parseInt(process.env.DB_POOL_MIN || defaultConfig.postgres.pool.min, 10),
    acquire: parseInt(process.env.DB_POOL_ACQUIRE || defaultConfig.postgres.pool.acquire, 10),
    idle: parseInt(process.env.DB_POOL_IDLE || defaultConfig.postgres.pool.idle, 10),
    evict: parseInt(process.env.DB_POOL_EVICT || defaultConfig.postgres.pool.evict, 10),
  },
  retry: {
    max: 3, // Maximum number of connection retry attempts
    match: [
      /SequelizeConnectionRefusedError/,
      /SequelizeConnectionError/,
      /SequelizeConnectionTimedOutError/,
      /SequelizeHostNotFoundError/,
      /SequelizeHostNotReachableError/
    ]
  },
  benchmark: process.env.NODE_ENV === 'development', // Show query execution time in development
  define: {
    timestamps: true, // 自动添加 createdAt 和 updatedAt 字段
    underscored: false, // 下划线命名
    underscoredAll: false, // 所有字段都下划线命名
    freezeTableName: true, // 冻结表名
    paranoid: true, // 软删除
  }
};

// Add specific configuration based on dialect
if (process.env.DB_DIALECT === 'sqlite') {
  sequelizeConfig.storage = process.env.DB_STORAGE || defaultConfig.sqlite.storage;
} else if (process.env.DB_DIALECT === 'mysql') {
  // MySQL specific configurations
  sequelizeConfig.host = process.env.DB_HOST || defaultConfig.mysql.host;
  sequelizeConfig.port = parseInt(process.env.DB_PORT || defaultConfig.mysql.port, 10);
  sequelizeConfig.database = process.env.DB_NAME;
  sequelizeConfig.username = process.env.DB_USER;
  sequelizeConfig.password = process.env.DB_PASSWORD;
  sequelizeConfig.dialectOptions = {
    connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT || defaultConfig.mysql.dialectOptions.connectTimeout, 10),
    charset: process.env.DB_CHARSET || defaultConfig.mysql.dialectOptions.charset,
    supportBigNumbers: true,
    bigNumberStrings: true,
    dateStrings: true
  };
  
  // MySQL timezone handling
  if (process.env.DB_TIMEZONE) {
    sequelizeConfig.timezone = process.env.DB_TIMEZONE;
  }
  
  // SSL Configuration for MySQL if needed
  if (process.env.DB_SSL === 'true') {
    sequelizeConfig.dialectOptions.ssl = {
      rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
    };
    
    if (process.env.DB_SSL_CA) {
      sequelizeConfig.dialectOptions.ssl.ca = process.env.DB_SSL_CA;
    }
  }
} else if (process.env.DB_DIALECT === 'postgres') {
  // PostgreSQL specific configurations
  sequelizeConfig.host = process.env.DB_HOST || defaultConfig.postgres.host;
  sequelizeConfig.port = parseInt(process.env.DB_PORT || defaultConfig.postgres.port, 10);
  sequelizeConfig.database = process.env.DB_NAME;
  sequelizeConfig.username = process.env.DB_USER;
  sequelizeConfig.password = process.env.DB_PASSWORD;
  
  console.log('sequelizeConfig.password', sequelizeConfig.password);
  console.log('sequelizeConfig.username', sequelizeConfig.username);
  console.log('sequelizeConfig.database', sequelizeConfig.database);
  console.log('sequelizeConfig.host', sequelizeConfig.host);
  console.log('sequelizeConfig.port', sequelizeConfig.port);
  // PostgreSQL specific dialect options
  sequelizeConfig.dialectOptions = {
    application_name: process.env.APP_NAME || defaultConfig.postgres.dialectOptions.application_name,
    statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT || 
                              defaultConfig.postgres.dialectOptions.statement_timeout, 10),
    idle_in_transaction_session_timeout: parseInt(process.env.DB_IDLE_TIMEOUT || 
                                             defaultConfig.postgres.dialectOptions.idle_in_transaction_session_timeout, 10)
  };
  
  // PostgreSQL schema support
  if (process.env.DB_SCHEMA) {
    sequelizeConfig.schema = process.env.DB_SCHEMA;
  }
  
  // SSL Configuration for PostgreSQL
  if (process.env.DB_SSL === 'true') {
    sequelizeConfig.dialectOptions.ssl = {
      rejectUnauthorized: process.env.DB_SSL_REJECT_UNAUTHORIZED !== 'false',
    };
    
    if (process.env.DB_SSL_CA) {
      sequelizeConfig.dialectOptions.ssl.ca = process.env.DB_SSL_CA;
    }
  }
} else {
  // For other database types
  sequelizeConfig.host = process.env.DB_HOST;
  sequelizeConfig.port = process.env.DB_PORT;
}

// Create the Sequelize instance
let sequelize;
try {
  sequelize = process.env.DB_DIALECT === 'sqlite' 
    ? new Sequelize({
        dialect: 'sqlite',
        storage: sequelizeConfig.storage,
        logging: sequelizeConfig.logging,
        pool: sequelizeConfig.pool,
        retry: sequelizeConfig.retry,
        benchmark: sequelizeConfig.benchmark
      })
    : new Sequelize(
        process.env.DB_NAME,
        process.env.DB_USER,
        process.env.DB_PASSWORD,
        sequelizeConfig
      );

  logger.info(`Sequelize initialized with ${process.env.DB_DIALECT || 'sqlite'} dialect`);
} catch (error) {
  logger.error('Error initializing Sequelize:', error);
  throw error;
}

// Test database connection
async function testConnection() {
  try {
    await sequelize.authenticate();
    logger.info('Database connection has been established successfully.');
    
    // Get server information based on dialect
    if (process.env.DB_DIALECT === 'mysql') {
      const [results] = await sequelize.query('SELECT VERSION() as version');
      if (results && results[0]) {
        logger.info(`Connected to MySQL server version: ${results[0].version}`);
      }
    } else if (process.env.DB_DIALECT === 'postgres') {
      const [results] = await sequelize.query('SELECT version()');
      if (results && results[0]) {
        logger.info(`Connected to PostgreSQL server: ${results[0].version}`);
      }
    }
    
    return true;
  } catch (error) {
    logger.error('Unable to connect to the database:', error.message);
    
    // Provide more helpful error messages based on error type
    if (error.name === 'SequelizeConnectionRefusedError') {
      logger.error('Make sure your database server is running and accessible.');
    } else if (error.name === 'SequelizeHostNotFoundError') {
      logger.error(`Could not resolve host: ${sequelizeConfig.host}`);
    } else if (error.name === 'SequelizeAccessDeniedError') {
      logger.error('Access denied. Check your username and password.');
    } else if (error.name === 'SequelizeDatabaseError') {
      logger.error(`Database error: ${error.message}`);
    } else if (error.name === 'SequelizeConnectionTimedOutError') {
      logger.error('Connection timed out. Check network and firewall settings.');
    }
    
    return false;
  }
}

// Close database connection
async function closeConnection() {
  try {
    await sequelize.close();
    logger.info('Database connection closed successfully.');
    return true;
  } catch (error) {
    logger.error('Error closing database connection:', error);
    return false;
  }
}

module.exports = { sequelize, testConnection, closeConnection };