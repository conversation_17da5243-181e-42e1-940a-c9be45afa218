const { Employee, Con<PERSON><PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON><PERSON><PERSON><PERSON>, ContactInfo } = require('../models/employee.model');
const { Project } = require('../models/project.model');
const { faker } = require('@faker-js/faker/locale/zh_CN');
const { v4: uuidv4 } = require('uuid');

const departments = ['研发部', '销售部', '人事部', '财务部', '运营部', '技术支持部'];
const positions = ['经理', '主管', '高级工程师', '工程师', '专员', '助理'];

async function seedEmployees() {
  try {
    // Clear existing data
    await ContactInfo.destroy({ where: {} });
    await ContractWorker.destroy({ where: {} });
    await TemporaryWorker.destroy({ where: {} });
    await Employee.destroy({ where: {} });

    // Get all projects for temporary workers
    const projects = await Project.findAll();

    if (!projects.length) {
      console.log('Please seed projects first');
      return;
    }

    // Create 30 contract workers
    const contractWorkers = [];
    const contractEmployees = [];
    for (let i = 0; i < 30; i++) {
      const startDate = faker.date.between({ from: '2015-01-01', to: '2024-01-01' });
      const endDate = new Date(startDate);
      endDate.setFullYear(endDate.getFullYear() + 3); // 3-year contract

      const employeeId = uuidv4();
      
      const employee = {
        id: employeeId,
        name: faker.person.fullName(),
        idNumber: faker.string.numeric(18),
        position: faker.helpers.arrayElement(positions),
        department: faker.helpers.arrayElement(departments),
        status: faker.helpers.arrayElement(['active', 'inactive']),
        employeeType: 'contract'
      };
      
      const worker = {
        id: uuidv4(),
        employeeId: employeeId,
        contractNumber: `CTR${faker.string.numeric(8)}`,
        startDate: startDate,
        endDate: endDate,
        salary: faker.number.int({ min: 5000, max: 30000 })
      };

      contractEmployees.push(employee);
      contractWorkers.push(worker);
    }

    // Create 20 temporary workers
    const temporaryWorkers = [];
    const temporaryEmployees = [];
    for (let i = 0; i < 20; i++) {
      const startDate = faker.date.between({ from: '2024-01-01', to: '2024-12-31' });
      const endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + faker.number.int({ min: 1, max: 6 })); // 1-6 months contract

      const employeeId = uuidv4();

      const employee = {
        id: employeeId,
        name: faker.person.fullName(),
        idNumber: faker.string.numeric(18),
        position: faker.helpers.arrayElement(positions),
        department: faker.helpers.arrayElement(departments),
        status: faker.helpers.arrayElement(['active', 'inactive']),
        employeeType: 'temporary'
      };

      const worker = {
        id: uuidv4(),
        employeeId: employeeId,
        projectId: faker.helpers.arrayElement(projects).id,
        startDate: startDate,
        endDate: endDate,
        dailyRate: faker.number.int({ min: 200, max: 1000 })
      };

      temporaryEmployees.push(employee);
      temporaryWorkers.push(worker);
    }

    // Create employees first
    const createdContractEmployees = await Employee.bulkCreate(contractEmployees);
    const createdTemporaryEmployees = await Employee.bulkCreate(temporaryEmployees);

    // Then create workers
    const createdContractWorkers = await ContractWorker.bulkCreate(contractWorkers);
    const createdTemporaryWorkers = await TemporaryWorker.bulkCreate(temporaryWorkers);

    // Create contact info for all employees
    const contactInfos = [...createdContractEmployees, ...createdTemporaryEmployees].map(employee => ({
      id: uuidv4(),
      employeeId: employee.id,
      phone: faker.phone.number('1##########'),
      email: faker.internet.email(),
      address: faker.location.streetAddress(true)
    }));

    // Create contact info
    await ContactInfo.bulkCreate(contactInfos);

    console.log('Employee seeder executed successfully');
  } catch (error) {
    console.error('Error seeding employees:', error);
    throw error; // Re-throw to see full error stack
  }
}

module.exports = seedEmployees; 