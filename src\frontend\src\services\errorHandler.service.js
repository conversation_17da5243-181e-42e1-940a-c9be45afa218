/**
 * 错误处理服务
 * 提供统一的错误处理和用户反馈功能
 */
import notificationService from './notification.service';

/**
 * 处理API错误并返回格式化的错误消息
 * @param {Error} err - 错误对象
 * @param {string} action - 操作名称，如"创建"、"更新"等
 * @param {string} entityName - 实体名称，如"用户"、"产品"等
 * @param {boolean} showNotification - 是否显示通知，默认为true
 * @returns {string} 格式化的错误消息
 */
const handleApiError = (err, action, entityName, showNotification = true) => {
  console.error(`${action}${entityName}失败:`, err);

  let errorMessage = '';

  // 处理不同类型的错误
  if (!err.response || !err.response.data) {
    // 网络错误或其他非HTTP错误
    if (err.message?.includes('Network Error')) {
      errorMessage = '网络错误: 无法连接到服务器，请检查网络连接';
    } else if (err.code === 'ECONNABORTED') {
      errorMessage = '请求超时: 服务器响应时间过长，请稍后重试';
    } else {
      errorMessage = err.message || `${action}${entityName}失败，请重试`;
    }
  } else {
    // HTTP错误
    const { status, data } = err.response;
    
    // 根据HTTP状态码提供具体错误信息
    switch (status) {
      case 400:
        errorMessage = `请求错误: ${data.message || '提供的数据无效'}`;
        break;
      case 401:
        errorMessage = '未授权: 请登录后重试';
        break;
      case 403:
        errorMessage = '禁止访问: 您没有权限执行此操作';
        break;
      case 404:
        errorMessage = `资源不存在: 请求的${entityName}不存在`;
        break;
      case 409:
        errorMessage = `冲突: ${data.message || `${entityName}数据冲突`}`;
        break;
      case 422:
        // 处理验证错误
        if (data.errors) {
          const validationErrors = Object.values(data.errors).flat();
          errorMessage = `验证错误: ${validationErrors.join(', ')}`;
        } else {
          errorMessage = `验证错误: ${data.message || '提供的数据无效'}`;
        }
        break;
      case 500:
      case 502:
      case 503:
        errorMessage = `服务器错误: ${data.message || '服务器暂时不可用，请稍后重试'}`;
        break;
      default:
        errorMessage = data.message || `${action}${entityName}失败，请重试`;
    }
  }

  // 显示通知
  if (showNotification) {
    notificationService.error(`${action}${entityName}失败`, errorMessage);
  }

  return errorMessage;
};

/**
 * 处理成功操作并显示通知
 * @param {string} action - 操作名称，如"创建"、"更新"等
 * @param {string} entityName - 实体名称，如"用户"、"产品"等
 * @param {string} itemName - 项目名称，如"用户名"、"产品名"等
 * @param {boolean} showNotification - 是否显示通知，默认为true
 * @returns {string} 成功消息
 */
const handleSuccess = (action, entityName, itemName, showNotification = true) => {
  const successMessage = `${entityName}${action}成功`;
  const detailMessage = itemName ? `${entityName} "${itemName}" 已成功${action}` : `${entityName}已成功${action}`;
  
  if (showNotification) {
    notificationService.success(successMessage, detailMessage);
  }
  
  return successMessage;
};

/**
 * 验证表单数据并返回错误信息
 * @param {Object} formData - 表单数据
 * @param {Object} validationRules - 验证规则
 * @returns {Object} 包含isValid和errors的对象
 */
const validateForm = (formData, validationRules) => {
  const errors = {};
  let isValid = true;

  // 遍历验证规则
  for (const field in validationRules) {
    const rules = validationRules[field];
    const value = formData[field];

    // 检查必填字段
    if (rules.required && (value === undefined || value === null || value === '')) {
      errors[field] = rules.requiredMessage || `请填写${field}`;
      isValid = false;
      continue;
    }

    // 如果字段为空且不是必填，则跳过其他验证
    if (value === undefined || value === null || value === '') {
      continue;
    }

    // 检查最小长度
    if (rules.minLength && String(value).length < rules.minLength) {
      errors[field] = rules.minLengthMessage || `${field}长度不能小于${rules.minLength}个字符`;
      isValid = false;
      continue;
    }

    // 检查最大长度
    if (rules.maxLength && String(value).length > rules.maxLength) {
      errors[field] = rules.maxLengthMessage || `${field}长度不能超过${rules.maxLength}个字符`;
      isValid = false;
      continue;
    }

    // 检查数字类型
    if (rules.number && isNaN(Number(value))) {
      errors[field] = rules.numberMessage || `${field}必须是数字`;
      isValid = false;
      continue;
    }

    // 检查最小值
    if (rules.min !== undefined && Number(value) < rules.min) {
      errors[field] = rules.minMessage || `${field}不能小于${rules.min}`;
      isValid = false;
      continue;
    }

    // 检查最大值
    if (rules.max !== undefined && Number(value) > rules.max) {
      errors[field] = rules.maxMessage || `${field}不能大于${rules.max}`;
      isValid = false;
      continue;
    }

    // 检查邮箱格式
    if (rules.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      errors[field] = rules.emailMessage || `${field}必须是有效的邮箱地址`;
      isValid = false;
      continue;
    }

    // 检查自定义验证函数
    if (rules.validator && typeof rules.validator === 'function') {
      const validationResult = rules.validator(value, formData);
      if (validationResult !== true) {
        errors[field] = validationResult || rules.validatorMessage || `${field}验证失败`;
        isValid = false;
      }
    }
  }

  return { isValid, errors };
};

export default {
  handleApiError,
  handleSuccess,
  validateForm
};
