/**
 * 运行增强版 Playwright 测试
 * 支持自动错误检测和修复
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const { generateTestData } = require('./utils/test-helpers');

// 配置
const config = {
  testDir: path.join(__dirname, 'tests'),
  resultsDir: path.join(__dirname, 'test-results'),
  reportDir: path.join(__dirname, 'test-report'),
  logsDir: path.join(__dirname, 'logs'),
  screenshotsDir: path.join(__dirname, 'screenshots'),
  maxRetries: 3,
  autoFix: true,
  browsers: ['chromium'], // 可以是 'chromium', 'firefox', 'webkit'
  testPattern: '*-enhanced.spec.js', // 只运行增强版测试
  parallel: 1, // 并行运行的测试数量
  timeout: 60000, // 测试超时时间 (ms)
  slowMo: 0, // 放慢测试执行速度 (ms)
  headless: false, // 是否使用无头模式
  video: true, // 是否录制视频
  trace: 'on-first-retry', // 是否记录跟踪
};

// 确保目录存在
[
  config.resultsDir, 
  config.reportDir, 
  config.logsDir, 
  config.screenshotsDir
].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// 获取所有测试文件
const getTestFiles = () => {
  return fs.readdirSync(config.testDir)
    .filter(file => file.endsWith(config.testPattern))
    .map(file => path.join(config.testDir, file));
};

// 生成测试报告
const generateReport = (results) => {
  const reportFile = path.join(config.reportDir, 'test-report.html');
  
  // 创建简单的HTML报告
  const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>测试报告</title>
  <style>
    body { font-family: Arial, sans-serif; line-height: 1.6; margin: 0; padding: 20px; color: #333; }
    h1 { color: #2c3e50; border-bottom: 2px solid #eee; padding-bottom: 10px; }
    .summary { background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
    .test-case { margin-bottom: 15px; padding: 15px; border-radius: 5px; border: 1px solid #ddd; }
    .test-case h3 { margin-top: 0; }
    .passed { border-left: 5px solid #28a745; }
    .failed { border-left: 5px solid #dc3545; }
    .warning { background-color: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .error { background-color: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .fix { background-color: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
    .details { margin-top: 10px; }
    .screenshot { max-width: 100%; height: auto; border: 1px solid #ddd; margin-top: 10px; }
    .timestamp { color: #6c757d; font-size: 0.9em; }
  </style>
</head>
<body>
  <h1>自动化测试报告</h1>
  
  <div class="summary">
    <h2>测试摘要</h2>
    <p>运行时间: ${new Date().toLocaleString()}</p>
    <p>总测试数: ${results.totalTests}</p>
    <p>通过: ${results.passed} (${Math.round(results.passed / results.totalTests * 100)}%)</p>
    <p>失败: ${results.failed}</p>
    <p>自动修复: ${results.autoFixed}</p>
  </div>
  
  <h2>测试详情</h2>
  
  ${results.tests.map(test => `
    <div class="test-case ${test.status === 'passed' ? 'passed' : 'failed'}">
      <h3>${test.name}</h3>
      <p>状态: ${test.status === 'passed' ? '通过 ✅' : '失败 ❌'}</p>
      <p>耗时: ${test.duration}ms</p>
      
      ${test.errors.length > 0 ? `
        <div class="details">
          <h4>错误 (${test.errors.length})</h4>
          ${test.errors.map(error => `
            <div class="error">
              <p>${error.message}</p>
              <p class="timestamp">时间: ${new Date(error.timestamp).toLocaleString()}</p>
            </div>
          `).join('')}
        </div>
      ` : ''}
      
      ${test.autoFixes.length > 0 ? `
        <div class="details">
          <h4>自动修复 (${test.autoFixes.length})</h4>
          ${test.autoFixes.map(fix => `
            <div class="fix">
              <p>类型: ${fix.type}</p>
              <p>修复: ${fix.fix}</p>
              <p class="timestamp">时间: ${new Date(fix.timestamp).toLocaleString()}</p>
            </div>
          `).join('')}
        </div>
      ` : ''}
      
      ${test.screenshots.length > 0 ? `
        <div class="details">
          <h4>截图 (${test.screenshots.length})</h4>
          ${test.screenshots.map(screenshot => `
            <div>
              <p>${screenshot.reason}</p>
              <img class="screenshot" src="${screenshot.path}" alt="${screenshot.reason}">
              <p class="timestamp">时间: ${new Date(screenshot.timestamp).toLocaleString()}</p>
            </div>
          `).join('')}
        </div>
      ` : ''}
    </div>
  `).join('')}
</body>
</html>
  `;
  
  fs.writeFileSync(reportFile, html);
  console.log(`📊 测试报告已生成: ${reportFile}`);
  
  return reportFile;
};

// 运行测试
const runTests = async () => {
  console.log('🚀 开始运行增强版 Playwright 测试...');
  
  const testFiles = getTestFiles();
  console.log(`📋 找到 ${testFiles.length} 个增强版测试文件`);
  
  if (testFiles.length === 0) {
    console.log('❌ 未找到匹配的测试文件');
    return;
  }
  
  // 测试结果
  const results = {
    totalTests: 0,
    passed: 0,
    failed: 0,
    autoFixed: 0,
    tests: []
  };
  
  // 构建命令行参数
  const args = [
    'npx',
    'playwright',
    'test',
    '--config=playwright.mcp.config.js',
    `--reporter=json,line`,
    ...config.browsers.map(browser => `--project=${browser}`),
    `--workers=${config.parallel}`,
    `--timeout=${config.timeout}`,
    config.headless ? '' : '--headed',
    config.slowMo > 0 ? `--slow-mo=${config.slowMo}` : '',
    config.video ? '--video=on' : '',
    `--trace=${config.trace}`,
    ...testFiles
  ].filter(Boolean);
  
  console.log(`\n📋 执行命令: ${args.join(' ')}\n`);
  
  // 运行测试
  const startTime = Date.now();
  
  const playwright = spawn('npx', args.slice(1), {
    stdio: 'inherit',
    shell: true
  });
  
  // 等待测试完成
  const exitCode = await new Promise(resolve => {
    playwright.on('close', code => {
      console.log(`\n${code === 0 ? '✅ 测试成功完成' : '❌ 测试失败'} (退出码: ${code})`);
      resolve(code);
    });
  });
  
  const endTime = Date.now();
  const duration = endTime - startTime;
  
  // 解析测试结果
  const resultsFile = path.join(config.resultsDir, 'test-results.json');
  if (fs.existsSync(resultsFile)) {
    try {
      const rawResults = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
      
      // 处理测试结果
      if (rawResults.suites) {
        processResults(rawResults, results);
      }
    } catch (error) {
      console.error('解析测试结果失败:', error);
    }
  }
  
  // 生成报告
  const reportFile = generateReport(results);
  
  console.log('\n📊 测试统计:');
  console.log(`总测试数: ${results.totalTests}`);
  console.log(`通过: ${results.passed} (${Math.round(results.passed / results.totalTests * 100)}%)`);
  console.log(`失败: ${results.failed}`);
  console.log(`自动修复: ${results.autoFixed}`);
  console.log(`总耗时: ${(duration / 1000).toFixed(2)}秒`);
  
  // 打开报告
  console.log(`\n🌐 正在打开测试报告...`);
  spawn('start', [reportFile], { shell: true });
  
  return exitCode;
};

// 处理测试结果
const processResults = (rawResults, results) => {
  const processSpec = (spec) => {
    if (spec.suites) {
      spec.suites.forEach(processSpec);
    }
    
    if (spec.specs) {
      spec.specs.forEach(test => {
        if (test.tests) {
          test.tests.forEach(testRun => {
            results.totalTests++;
            
            const testResult = {
              name: testRun.title,
              status: 'passed',
              duration: testRun.results.reduce((sum, r) => sum + r.duration, 0),
              errors: [],
              autoFixes: [],
              screenshots: []
            };
            
            // 检查测试状态
            if (testRun.results.some(r => r.status === 'failed')) {
              testResult.status = 'failed';
              results.failed++;
            } else {
              results.passed++;
            }
            
            // 收集错误和自动修复信息
            // 这里需要从日志文件中读取，因为 Playwright 的 JSON 报告中没有这些信息
            
            // 收集截图
            const screenshotPattern = new RegExp(`${testRun.title.replace(/[^a-zA-Z0-9]/g, '-')}.*\\.png$`);
            if (fs.existsSync(config.screenshotsDir)) {
              fs.readdirSync(config.screenshotsDir)
                .filter(file => screenshotPattern.test(file))
                .forEach(file => {
                  testResult.screenshots.push({
                    path: path.join('screenshots', file),
                    reason: file.split('_')[1] || '未知原因',
                    timestamp: new Date()
                  });
                });
            }
            
            results.tests.push(testResult);
          });
        }
      });
    }
  };
  
  rawResults.suites.forEach(processSpec);
};

// 运行测试
runTests().catch(error => {
  console.error('运行测试时出错:', error);
  process.exit(1);
});
