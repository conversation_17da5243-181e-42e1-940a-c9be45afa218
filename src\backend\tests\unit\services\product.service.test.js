const { expect } = require('chai');
const sinon = require('sinon');
const { Product, User, sequelize } = require('../../../src/models');
const productService = require('../../../src/services/product.service');
const ApiError = require('../../../src/utils/ApiError');

describe('Product Service', () => {
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('createProduct', () => {
    it('should create a product successfully', async () => {
      // Arrange
      const productData = {
        name: 'Test Product',
        code: 'TP001',
        category: 'electronics',
        price: 100,
        unit: '个',
        stock: 10
      };

      const findOneStub = sandbox.stub(Product, 'findOne').resolves(null);
      const createStub = sandbox.stub(Product, 'create').resolves(productData);

      // Act
      const result = await productService.createProduct(productData);

      // Assert
      expect(findOneStub.calledOnce).to.be.true;
      expect(createStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(productData);
    });

    it('should throw error if product with same code already exists', async () => {
      // Arrange
      const productData = {
        name: 'Test Product',
        code: 'TP001',
        category: 'electronics',
        price: 100,
        unit: '个',
        stock: 10
      };

      const existingProduct = { ...productData, id: '1' };
      sandbox.stub(Product, 'findOne').resolves(existingProduct);

      // Act & Assert
      try {
        await productService.createProduct(productData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('already exists');
      }
    });
  });

  describe('getProductById', () => {
    it('should return product if found', async () => {
      // Arrange
      const productId = '1';
      const productData = {
        id: productId,
        name: 'Test Product',
        code: 'TP001',
        category: 'electronics',
        price: 100,
        unit: '个',
        stock: 10
      };

      sandbox.stub(Product, 'findByPk').resolves(productData);

      // Act
      const result = await productService.getProductById(productId);

      // Assert
      expect(result).to.deep.equal(productData);
    });

    it('should throw error if product not found', async () => {
      // Arrange
      const productId = '999';
      sandbox.stub(Product, 'findByPk').resolves(null);

      // Act & Assert
      try {
        await productService.getProductById(productId);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(404);
        expect(error.message).to.include('not found');
      }
    });
  });

  describe('updateProductById', () => {
    it('should update product successfully', async () => {
      // Arrange
      const productId = '1';
      const productData = {
        id: productId,
        name: 'Test Product',
        code: 'TP001',
        category: 'electronics',
        price: 100,
        unit: '个',
        stock: 10,
        save: sandbox.stub().resolves()
      };

      const updateData = {
        name: 'Updated Product',
        price: 150
      };

      sandbox.stub(productService, 'getProductById').resolves(productData);

      // Act
      const result = await productService.updateProductById(productId, updateData);

      // Assert
      expect(result.name).to.equal(updateData.name);
      expect(result.price).to.equal(updateData.price);
      expect(productData.save.calledOnce).to.be.true;
    });
  });

  describe('deleteProductById', () => {
    it('should delete product successfully', async () => {
      // Arrange
      const productId = '1';
      const productData = {
        id: productId,
        name: 'Test Product',
        destroy: sandbox.stub().resolves()
      };

      sandbox.stub(productService, 'getProductById').resolves(productData);

      // Act
      await productService.deleteProductById(productId);

      // Assert
      expect(productData.destroy.calledOnce).to.be.true;
    });
  });

  describe('queryProducts', () => {
    it('should return products and pagination info', async () => {
      // Arrange
      const filter = { category: 'electronics' };
      const options = { page: 1, limit: 10 };
      
      const products = [
        { id: '1', name: 'Product 1' },
        { id: '2', name: 'Product 2' }
      ];
      
      const findAndCountAllStub = sandbox.stub(Product, 'findAndCountAll').resolves({
        count: 2,
        rows: products
      });

      // Act
      const result = await productService.queryProducts(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      expect(result.results).to.deep.equal(products);
      expect(result.pagination.totalResults).to.equal(2);
    });
  });

  describe('getInventoryOverview', () => {
    it('should return inventory statistics', async () => {
      // Arrange
      const stats = [{
        dataValues: {
          totalProducts: 10,
          totalStock: 100,
          totalValue: 10000
        }
      }];
      
      const categoryStats = [
        { category: 'electronics', productCount: 5, totalStock: 50, totalValue: 5000 },
        { category: 'furniture', productCount: 5, totalStock: 50, totalValue: 5000 }
      ];

      sandbox.stub(Product, 'findAll')
        .onFirstCall().resolves(stats)
        .onSecondCall().resolves(categoryStats);
      
      sandbox.stub(Product, 'count').resolves(3);

      // Act
      const result = await productService.getInventoryOverview();

      // Assert
      expect(result.summary).to.deep.equal(stats[0].dataValues);
      expect(result.lowStockCount).to.equal(3);
      expect(result.categoryBreakdown).to.deep.equal(categoryStats);
    });
  });

  describe('getStockAlerts', () => {
    it('should return products with low stock', async () => {
      // Arrange
      const lowStockProducts = [
        { id: '1', name: 'Product 1', stock: 5, minStock: 10 },
        { id: '2', name: 'Product 2', stock: 3, minStock: 5 }
      ];

      sandbox.stub(Product, 'findAll').resolves(lowStockProducts);

      // Act
      const result = await productService.getStockAlerts();

      // Assert
      expect(result).to.deep.equal(lowStockProducts);
    });
  });

  describe('getStockLevelReport', () => {
    it('should return stock level report', async () => {
      // Arrange
      const filter = { category: 'electronics' };
      
      const products = [
        { id: '1', name: 'Product 1', stock: 10, price: 100, minStock: 5 },
        { id: '2', name: 'Product 2', stock: 5, price: 200, minStock: 10 }
      ];

      sandbox.stub(Product, 'findAll').resolves(products);

      // Act
      const result = await productService.getStockLevelReport(filter);

      // Assert
      expect(result.products).to.deep.equal(products);
      expect(result.summary.totalProducts).to.equal(2);
      expect(result.summary.totalStock).to.equal(15);
      expect(result.summary.totalValue).to.equal(2000);
      expect(result.summary.lowStockCount).to.equal(1);
    });
  });
});
