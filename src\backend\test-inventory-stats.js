const { dashboardService } = require('./src/services');

async function testInventoryStats() {
  try {
    console.log('🔄 测试库存统计功能...');
    
    const stats = await dashboardService.getInventoryStats();
    
    console.log('✅ 库存统计结果:');
    console.log(`   - 总物品数量: ${stats.items}`);
    console.log(`   - 库存不足物品: ${stats.lowStock}`);
    console.log(`   - 库存总值: ¥${stats.totalValue.toFixed(2)}`);
    console.log(`   - 趋势: ${stats.trend}%`);
    console.log(`   - 最后更新: ${stats.lastUpdated}`);
    
    if (stats.items > 0) {
      console.log('✅ 库存统计功能正常工作！');
    } else {
      console.log('⚠️  没有找到库存数据，请确保数据库中有产品数据');
    }
    
  } catch (error) {
    console.error('❌ 库存统计测试失败:', error.message);
    console.error('错误详情:', error);
  }
}

testInventoryStats(); 