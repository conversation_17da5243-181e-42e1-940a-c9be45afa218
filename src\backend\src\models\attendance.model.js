const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Attendance = sequelize.define(
  'attendance',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    projectId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'project',
        key: 'id',
      },
    },
    employeeName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    employeeId: {
      type: DataTypes.STRING,
      allowNull: true,
    },


    date: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    timeIn: {
      type: DataTypes.TIME,
      allowNull: false,
    },
    timeOut: {
      type: DataTypes.TIME,
      allowNull: true,
    },
    hoursWorked: {
      type: DataTypes.FLOAT,
      allowNull: true,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'present',
      validate: {
        isIn: [['present', 'absent', 'late', 'half-day']],
      },
    },
    // 新增字段 - 签到位置
    location: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: '签到位置信息',
    },
    // 新增字段 - 签到纬度
    latitude: {
      type: DataTypes.DECIMAL(10, 7),
      allowNull: true,
      comment: '签到纬度',
    },
    // 新增字段 - 签到经度
    longitude: {
      type: DataTypes.DECIMAL(10, 7),
      allowNull: true,
      comment: '签到经度',
    },
    // 新增字段 - 签到方式 - 修改为 STRING 类型而不是 ENUM 以避免 PostgreSQL 同步问题
    checkInMethod: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: 'manual',
      validate: {
        isIn: [['manual', 'gps', 'wifi', 'beacon']],
      },
      comment: '签到方式：手动/GPS/WIFI/信标',
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'user',
        key: 'id',
      },
    },
  },
  {
    timestamps: true,
  }
);

// Define associations
const setupAssociations = (models) => {
  const { Project, User } = models;

  Attendance.belongsTo(Project, {
    foreignKey: 'projectId',
  });

  Attendance.belongsTo(User, {
    foreignKey: 'createdBy',
    as: 'Creator',
  });
  
  // 添加与员工的关联，用于获取部门信息
  Attendance.belongsTo(User, {
    foreignKey: 'employeeId',
    as: 'Employee',
    constraints: false, // 避免外键约束问题
  });
};

module.exports = {
  Attendance,
  setupAssociations,
};