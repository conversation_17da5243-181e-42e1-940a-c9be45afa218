const httpStatus = require('http-status');
const config = require('../config/config');
const logger = require('../config/logger');
const ApiError = require('../utils/ApiError');

/**
 * Convert error object to ApiError if needed
 */
const errorConverter = (err, req, res, next) => {
  let error = err;

  // Convert non-ApiError to ApiError
  if (!(error instanceof ApiError)) {
    // 使用明确的数字状态码，避免可能的undefined问题
    let statusCode = error.statusCode || (error instanceof SyntaxError ? 400 : 500);
    let message = error.message || (statusCode === 400 ? '请求无效' :
                                   statusCode === 401 ? '未授权访问' :
                                   statusCode === 403 ? '禁止访问' :
                                   statusCode === 404 ? '未找到资源' :
                                   statusCode === 500 ? '服务器内部错误' :
                                   '未知错误');

    // 处理特定类型的错误
    if (error.name === 'SequelizeValidationError') {
      statusCode = 400;
      message = error.errors?.[0]?.message || '数据验证错误';
    }
    else if (error.name === 'SequelizeUniqueConstraintError') {
      statusCode = 400;
      const field = Object.keys(error.fields)[0];
      message = `${field}已存在`;
    }
    else if (error.name === 'SequelizeForeignKeyConstraintError') {
      statusCode = 400;
      message = '引用的记录不存在';
    }
    else if (error.name === 'JsonWebTokenError') {
      statusCode = 401;
      message = '无效的令牌';
    }
    else if (error.name === 'TokenExpiredError') {
      statusCode = 401;
      message = '令牌已过期';
    }

    error = new ApiError(statusCode, message, false, err.stack);

    // 保留原始错误的关键属性
    error.original = err;
    error.fields = err.fields;
    error.parent = err.parent;
    error.errors = err.errors;
  }

  // Log error for debugging
  logger.error(`Error being processed: ${error.message}`, {
    statusCode: error.statusCode,
    stack: error.stack,
    isOperational: error.isOperational
  });

  next(error);
};

/**
 * Handle API errors and send appropriate response
 */
const errorHandler = (err, req, res, next) => {
  let { statusCode, message } = err;

  // Ensure status code is valid
  if (!statusCode || typeof statusCode !== 'number' || statusCode < 100 || statusCode > 599) {
    logger.warn(`Invalid status code detected: ${statusCode}, defaulting to 500`);
    statusCode = 500;
  }

  // Handle production mode
  if (config.env === 'production' && !err.isOperational) {
    statusCode = 500;
    message = '服务器内部错误';
  }

  // Set locals for error handling
  res.locals.errorMessage = message;

  // Prepare response object
  const response = {
    code: statusCode,
    message,
    success: false,
    ...(config.env === 'development' && {
      stack: err.stack,
      isOperational: err.isOperational
    })
  };

  // 添加数据库错误的详细信息
  if (err.name && (err.name.includes('Sequelize') || err.parent)) {
    response.error = {
      name: err.name,
      code: err.parent?.code,
      detail: err.parent?.detail
    };

    // 处理唯一约束错误
    if (err.name === 'SequelizeUniqueConstraintError' && err.fields) {
      const field = Object.keys(err.fields)[0];
      const value = err.fields[field];
      response.message = `${field} '${value}' 已存在`;
      response.error.fields = err.fields;
    }
  }

  // Log error in development
  if (config.env === 'development') {
    logger.error('Error response being sent:', {
      statusCode,
      message,
      stack: err.stack
    });
  }

  // Send error response
  res.status(statusCode).json(response);
};

module.exports = {
  errorConverter,
  errorHandler
};