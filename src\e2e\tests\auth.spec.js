import { test, expect } from '@playwright/test';

test.describe('Authentication Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
  });

  test('登录功能测试', async ({ page }) => {
    // 输入登录信息
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    
    // 点击登录按钮
    await page.click('button[type="submit"]');
    
    // 验证登录成功
    await expect(page).toHaveURL('/dashboard');
  });

  test('注册功能测试', async ({ page }) => {
    // 点击注册链接
    await page.click('a[href="/register"]');
    
    // 填写注册信息
    await page.fill('input[name="name"]', 'Test User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.fill('input[name="password_confirmation"]', 'password123');
    
    // 提交注册
    await page.click('button[type="submit"]');
    
    // 验证注册成功
    await expect(page).toHaveURL('/dashboard');
  });

  test('忘记密码功能测试', async ({ page }) => {
    // 点击忘记密码链接
    await page.click('a[href="/forgot-password"]');
    
    // 输入邮箱
    await page.fill('input[name="email"]', '<EMAIL>');
    
    // 提交
    await page.click('button[type="submit"]');
    
    // 验证提示信息
    await expect(page.locator('.success-message')).toBeVisible();
  });
}); 