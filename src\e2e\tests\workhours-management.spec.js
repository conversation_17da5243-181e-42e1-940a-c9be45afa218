// @ts-check
import { test, expect } from '@playwright/test';

test.describe('工时管理流程', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('工时记录列表页面', async ({ page }) => {
    // 访问工时记录列表页面
    await page.goto('/workhours');

    // 验证页面标题
    const h2s = await page.$$('h2');
    for (const h2 of h2s) {
      const text = await h2.textContent();
      expect(text).toContain('工时管理');
    }

    // 验证工时表格存在
    await page.waitForSelector('table');
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加工时按钮存在
    expect(await page.isVisible('button:has-text("记工")')).toBeTruthy();
  });

  test('创建工时记录流程', async ({ page }) => {
    // 访问工时列表页面
    await page.goto('/workhours');

    // 等待页面加载
    await page.waitForTimeout(2000);

    // 检查是否有记工按钮
    const hasRecordButton = await page.isVisible('button:has-text("记工")');

    if (hasRecordButton) {
      // 点击记工按钮
      await page.click('button:has-text("记工")');

      // 等待一段时间，看是否有模态框出现
      await page.waitForTimeout(2000);

      // 检查是否有表单元素
      const hasForm = await page.isVisible('form') || await page.isVisible('.modal form');

      if (hasForm) {
        // 尝试填写表单
        // 填写员工信息
        const employeeInput = await page.$('input[placeholder="员工姓名"], input[name="employeeName"]');
        if (employeeInput) {
          await employeeInput.fill('测试员工');
        }

        // 设置日期
        const dateInput = await page.$('input[type="date"], input[name="date"]');
        if (dateInput) {
          await dateInput.fill(new Date().toISOString().split('T')[0]);
        }

        // 设置工时
        const hoursInput = await page.$('input[type="number"], input[name="workHours"]');
        if (hoursInput) {
          await hoursInput.fill('8');
        }

        // 填写项目名称
        const projectInput = await page.$('input[placeholder="项目名称"], input[name="projectName"]');
        if (projectInput) {
          await projectInput.fill('测试项目');
        }

        // 添加备注
        const remarksInput = await page.$('textarea, textarea[name="remarks"]');
        if (remarksInput) {
          await remarksInput.fill('这是一个由E2E测试创建的工时记录');
        }

        // 查找保存按钮
        const saveButton = await page.$('button:has-text("保存"), button[type="submit"]');
        if (saveButton) {
          await saveButton.click();
        }
      }
    }

    // 等待操作完成
    await page.waitForTimeout(2000);
  });

  test('工时统计功能', async ({ page }) => {
    // 访问工时列表页面
    await page.goto('/workhours');

    // 等待页面加载
    await page.waitForTimeout(2000);

    // 检查是否有记工统计按钮
    const hasStatsButton = await page.isVisible('button:has-text("记工统计")');

    if (hasStatsButton) {
      // 点击记工统计按钮
      await page.click('button:has-text("记工统计")');

      // 等待一段时间，看是否有页面跳转
      await page.waitForTimeout(2000);

      // 直接访问统计页面
      await page.goto('/workhours/statistics');

      // 等待页面加载
      await page.waitForTimeout(2000);

      // 验证页面URL
      expect(page.url()).toContain('/workhours/statistics');
    } else {
      // 如果没有按钮，直接访问统计页面
      await page.goto('/workhours/statistics');

      // 等待页面加载
      await page.waitForTimeout(2000);
    }
  });

  test('工时日历视图', async ({ page }) => {
    // 访问工时日历页面
    await page.goto('/workhours/calendar');

    // 等待页面加载
    await page.waitForTimeout(2000);

    // 验证页面URL
    expect(page.url()).toContain('/workhours/calendar');

    // 尝试选择月份
    const currentMonth = new Date().toISOString().slice(0, 7);
    const monthInput = await page.$('input[type="month"]');
    if (monthInput) {
      await monthInput.fill(currentMonth);
    }

    // 等待一段时间
    await page.waitForTimeout(1000);
  });

  test('筛选工时记录', async ({ page }) => {
    // 访问工时列表页面
    await page.goto('/workhours');

    // 等待页面加载
    await page.waitForSelector('table');

    // 使用筛选功能
    if (await page.isVisible('input[placeholder="搜索..."]')) {
      await page.fill('input[placeholder="搜索..."]', '测试');

      // 点击搜索按钮
      if (await page.isVisible('button:has-text("搜索")')) {
        await page.click('button:has-text("搜索")');
      }

      // 等待结果加载
      await page.waitForTimeout(1000);
    }

    // 使用月份筛选
    if (await page.isVisible('select[name="month"]')) {
      await page.selectOption('select[name="month"]', { index: 1 });

      // 等待结果加载
      await page.waitForTimeout(1000);
    }
  });
});
