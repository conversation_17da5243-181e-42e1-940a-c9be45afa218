// @ts-check
import { test, expect } from '@playwright/test';

test.describe('用户管理流程', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('用户列表页面', async ({ page }) => {
    // 访问用户列表页面
    await page.goto('/admin/users');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) {
      expect(await h1.textContent()).toContain('用户列表');
    }
    // 验证用户表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加用户按钮存在
    expect(await page.isVisible('a[href="/users/create"]')).toBeTruthy();
  });

  test('创建用户流程', async ({ page }) => {
    // 访问创建用户页面
    await page.goto('/admin/users/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('创建用户');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 生成随机邮箱以避免冲突
    const randomEmail = `test${Date.now()}@example.com`;

    // 填写表单
    await page.fill('input[name="name"]', '测试用户');
    await page.fill('input[name="email"]', randomEmail);
    
    // 填写密码
    await page.fill('input[name="password"]', 'Password123!');
    
    // 确认密码
    if (await page.isVisible('input[name="confirmPassword"]')) {
      await page.fill('input[name="confirmPassword"]', 'Password123!');
    }
    
    // 选择角色
    if (await page.isVisible('select[name="role"]')) {
      await page.selectOption('select[name="role"]', 'user');
    }
    
    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到用户列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/admin/users');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
    
    // 验证新用户已添加到列表
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain(randomEmail);
  });

  test('编辑用户流程', async ({ page }) => {
    // 访问用户列表页面
    await page.goto('/admin/users');

    // 点击第一个编辑按钮
    await page.click('table tbody tr:first-child a.edit-button');

    // 等待页面跳转到编辑页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/admin/users/');
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/admin/edit');

    // 验证表单已预填充
    expect(await page.inputValue('input[name="name"]')).toBeTruthy();

    // 修改用户名称
    const updatedName = `更新的用户 ${Date.now()}`;
    await page.fill('input[name="name"]', updatedName);

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到用户列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/users');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('用户搜索功能', async ({ page }) => {
    // 访问用户列表页面
    await page.goto('/users');

    // 在搜索框中输入关键字
    await page.fill('input[placeholder*="搜索"]', 'admin');

    // 点击搜索按钮
    await page.click('button.search-button');

    // 等待搜索结果加载
    await page.waitForTimeout(1000);

    // 验证搜索结果
    const userEmails = await page.locator('table tbody tr td:nth-child(2)').allTextContents();
    expect(userEmails.some(email => email.includes('admin'))).toBeTruthy();
  });

  test('用户角色筛选功能', async ({ page }) => {
    // 访问用户列表页面
    await page.goto('/users');

    // 如果存在角色筛选器
    if (await page.isVisible('select.role-filter')) {
      // 选择角色
      await page.selectOption('select.role-filter', 'admin');

      // 等待筛选结果加载
      await page.waitForTimeout(1000);

      // 验证筛选结果存在
      expect(await page.locator('table tbody tr').count()).toBeGreaterThan(0);
    }
  });

  test('禁用/启用用户功能', async ({ page }) => {
    // 访问用户列表页面
    await page.goto('/users');

    // 找到非管理员用户行
    const rows = await page.locator('table tbody tr').count();
    let targetRow = 0;
    
    for (let i = 0; i < rows; i++) {
      const role = await page.locator(`table tbody tr:nth-child(${i + 1}) td:nth-child(3)`).textContent();
      if (role && role.trim().toLowerCase() !== 'admin') {
        targetRow = i + 1;
        break;
      }
    }
    
    if (targetRow > 0) {
      // 获取当前状态
      const initialStatus = await page.locator(`table tbody tr:nth-child(${targetRow}) td:nth-child(4)`).textContent();
      
      // 点击状态切换按钮
      await page.click(`table tbody tr:nth-child(${targetRow}) button.status-toggle`);
      
      // 确认操作
      if (await page.isVisible('button.confirm-action')) {
        await page.click('button.confirm-action');
      }
      
      // 等待操作完成
      await page.waitForTimeout(1000);
      
      // 验证状态已更改
      const newStatus = await page.locator(`table tbody tr:nth-child(${targetRow}) td:nth-child(4)`).textContent();
      expect(newStatus).not.toEqual(initialStatus);
    }
  });

  test('重置用户密码功能', async ({ page }) => {
    // 访问用户列表页面
    await page.goto('/users');

    // 点击第一个用户的操作菜单
    await page.click('table tbody tr:first-child button.action-menu');

    // 点击重置密码选项
    await page.click('button:has-text("重置密码")');

    // 等待重置密码模态框显示
    await page.waitForSelector('.modal');

    // 填写新密码
    await page.fill('input[name="newPassword"]', 'NewPassword123!');
    
    // 确认新密码
    if (await page.isVisible('input[name="confirmPassword"]')) {
      await page.fill('input[name="confirmPassword"]', 'NewPassword123!');
    }

    // 确认重置
    await page.click('button:has-text("确认")');

    // 等待操作完成
    await page.waitForTimeout(1000);

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });
});
