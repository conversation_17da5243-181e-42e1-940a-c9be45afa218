const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const clientValidation = require('../validations/client.validation');
const clientController = require('../controllers/client.controller');

const router = express.Router();

/**
 * @route GET /api/clients/public
 * @desc Get all clients (public access)
 * @access Public
 */
router
  .route('/public')
  .get(validate(clientValidation.getClients), clientController.getClients);

/**
 * @route POST /api/clients
 * @desc Create a new client
 * @access Private
 */
router
  .route('/')
  .post(auth('manageCustomers'), validate(clientValidation.createClient), clientController.createClient)
  .get(auth('getCustomers'), validate(clientValidation.getClients), clientController.getClients);

/**
 * @route GET/PATCH/DELETE /api/clients/:id
 * @desc Get/Update/Delete client by ID
 * @access Private
 */
router
  .route('/:id')
  .get(auth('getCustomers'), validate(clientValidation.getClient), clientController.getClient)
  .patch(auth('manageCustomers'), validate(clientValidation.updateClient), clientController.updateClient)
  .delete(auth('manageCustomers'), validate(clientValidation.deleteClient), clientController.deleteClient);

module.exports = router;
