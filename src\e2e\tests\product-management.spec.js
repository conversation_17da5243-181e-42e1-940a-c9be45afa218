// @ts-check
import { test, expect } from '@playwright/test';

test.describe('产品管理流程', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('产品列表页面', async ({ page }) => {
    // 访问产品列表页面
    await page.goto('/products');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('产品列表');

    // 验证产品表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加产品按钮存在
    expect(await page.isVisible('a[href="/products/create"]')).toBeTruthy();
  });

  test('创建产品流程', async ({ page }) => {
    // 访问创建产品页面
    await page.goto('/products/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('创建产品');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 填写表单
    await page.fill('input[name="name"]', 'E2E Test Product');
    await page.fill('input[name="code"]', `TP-${Date.now()}`);
    await page.selectOption('select[name="category"]', 'electronics');
    await page.fill('textarea[name="description"]', 'This is a test product created by E2E test');
    await page.fill('input[name="price"]', '100');
    await page.fill('input[name="unit"]', '个');
    await page.fill('input[name="stock"]', '10');
    await page.fill('input[name="minStock"]', '5');

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到产品列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/products');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('编辑产品流程', async ({ page }) => {
    // 访问产品列表页面
    await page.goto('/products');

    // 点击第一个编辑按钮
    await page.click('table tbody tr:first-child a.edit-button');

    // 等待页面跳转到编辑页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/products/');
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/edit');

    // 验证表单已预填充
    expect(await page.inputValue('input[name="name"]')).toBeTruthy();

    // 修改产品名称
    const newName = `Updated Product ${Date.now()}`;
    await page.fill('input[name="name"]', newName);

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到产品列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/products');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('删除产品流程', async ({ page }) => {
    // 访问产品列表页面
    await page.goto('/products');

    // 获取产品数量
    const initialProductCount = await page.locator('table tbody tr').count();

    // 点击第一个删除按钮
    await page.click('table tbody tr:first-child button.delete-button');

    // 确认删除
    await page.click('button.confirm-delete');

    // 等待页面刷新
    await page.waitForTimeout(1000);

    // 验证产品数量减少
    const newProductCount = await page.locator('table tbody tr').count();
    expect(newProductCount).toBeLessThan(initialProductCount);

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('产品搜索功能', async ({ page }) => {
    // 访问产品列表页面
    await page.goto('/products');

    // 在搜索框中输入关键字
    await page.fill('input[placeholder*="搜索"]', 'Test');

    // 点击搜索按钮
    await page.click('button.search-button');

    // 等待搜索结果加载
    await page.waitForTimeout(1000);

    // 验证搜索结果
    const productNames = await page.locator('table tbody tr td:nth-child(2)').allTextContents();
    for (const name of productNames) {
      expect(name.toLowerCase()).toContain('test');
    }
  });

  test('产品分类筛选功能', async ({ page }) => {
    // 访问产品列表页面
    await page.goto('/products');

    // 选择分类
    await page.selectOption('select.category-filter', 'electronics');

    // 等待筛选结果加载
    await page.waitForTimeout(1000);

    // 验证筛选结果
    const productCategories = await page.locator('table tbody tr td:nth-child(3)').allTextContents();
    for (const category of productCategories) {
      expect(category.toLowerCase()).toContain('electronics');
    }
  });
});
