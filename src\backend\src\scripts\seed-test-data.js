/**
 * Seed Test Data Script
 *
 * This script dynamically scans the seeders directory and executes seeders in the correct dependency order.
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { sequelize } = require('../config/database');
const logger = require('../config/logger');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Update seeder dependencies before running
require('./generate-seeder-dependencies');

// SEEDER_DEPENDENCIES will be automatically updated by generate-seeder-dependencies.js
const SEEDER_DEPENDENCIES = {
  "attachmentSeeder": [
    "userSeeder"
  ],
  "attendanceSeeder": [
    "projectSeeder",
    "userSeeder"
  ],
  "categorySeeder": [],
  "clientSeeder": [
    "userSeeder"
  ],
  "commentSeeder": [],
  "contractSeeder": [
    "projectSeeder",
    "userSeeder"
  ],
  "customerSeeder": [],
  "documentSeeder": [
    "categorySeeder",
    "userSeeder",
    "tagSeeder"
  ],
  "employeeSeeder": [],
  "financeSeeder": [
    "projectSeeder",
    "supplierSeeder",
    "userSeeder"
  ],
  "permissionSeeder": [
    "roleSeeder"
  ],
  "procurementSeeder": [],
  "productSeeder": [
    "userSeeder"
  ],
  "productTransactionSeeder": [
    "productSeeder",
    "projectSeeder",
    "userSeeder"
  ],
  "projectSeeder": [],
  "projectFollowUpSeeder": [
    "projectSeeder",
    "employeeSeeder"
  ],
  "projectProgressSeeder": [
    "projectSeeder",
    "userSeeder"
  ],
  "projectTaskSeeder": [
    "projectSeeder"
  ],
  "purchaseSeeder": [
    "projectSeeder",
    "supplierSeeder",
    "userSeeder"
  ],
  "reimbursementSeeder": [],
  "roleSeeder": [
    "userSeeder"
  ],
  "subcontractSeeder": [],
  "supplierSeeder": [
    "userSeeder"
  ],
  "tagSeeder": [],
  "taskSeeder": [
    "userSeeder",
    "projectSeeder"
  ],
  "tokenSeeder": [
    "userSeeder"
  ],
  "userSeeder": [],
  "workhourSeeder": []
};

// Function to get all seeder files
function getSeedersFromDirectory() {
  const seedersDir = path.join(__dirname, '../seeders');
  return fs.readdirSync(seedersDir)
    .filter(file => 
      file.endsWith('.js') && 
      !file.startsWith('2024') && // Exclude migration files
      file !== 'index.js' // Exclude index.js
    )
    .map(file => ({
      name: file.replace('.js', ''),
      path: path.join(seedersDir, file)
    }));
}

// Function to sort seeders based on dependencies
function sortSeedersByDependencies(seeders) {
  const visited = new Set();
  const sorted = [];
  const visiting = new Set();

  function visit(seederName) {
    if (visiting.has(seederName)) {
      throw new Error(`Circular dependency detected for seeder: ${seederName}`);
    }
    if (visited.has(seederName)) {
      return;
    }

    visiting.add(seederName);

    const dependencies = SEEDER_DEPENDENCIES[seederName] || [];
    for (const dep of dependencies) {
      visit(dep);
    }

    visiting.delete(seederName);
    visited.add(seederName);
    sorted.push(seederName);
  }

  // Get all seeder names
  const seederNames = seeders.map(s => s.name);
  
  // Visit each seeder
  for (const seederName of seederNames) {
    if (!visited.has(seederName)) {
      visit(seederName);
    }
  }

  // Map sorted names back to full seeder objects
  return sorted.map(name => 
    seeders.find(s => s.name === name)
  ).filter(Boolean);
}

async function seedAll() {
  try {
    // Test database connection
    await sequelize.authenticate();
    console.log(`${colors.green}Database connection established successfully${colors.reset}`);

    // Get all seeders
    const seeders = getSeedersFromDirectory();
    console.log(`${colors.blue}Found ${seeders.length} seeders${colors.reset}`);

    // Sort seeders by dependencies
    const sortedSeeders = sortSeedersByDependencies(seeders);
    console.log(`${colors.blue}Sorted seeders based on dependencies${colors.reset}`);

    // Execute seeders in order
    console.log(`${colors.blue}Starting to seed test data...${colors.reset}`);
    
    for (const seeder of sortedSeeders) {
      console.log(`${colors.yellow}Seeding ${seeder.name}...${colors.reset}`);
      try {
        const seederModule = require(seeder.path);
        // Handle both default exports and named exports
        const seederFunction = typeof seederModule === 'function' ? seederModule : seederModule[0];
        await seederFunction();
        // console.log('seederModule.default', typeof seederFunction);
      } catch (error) {
        console.error(`${colors.red}Error in ${seeder.name}:${seeder.path} ${colors.reset}`, error);
        throw error;
      }
    }

    console.log(`${colors.green}All test data seeded successfully!${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}Error seeding test data:${colors.reset}`, error);
    process.exit(1);
  } finally {
    // Close database connection
    await sequelize.close();
  }
}

// Run the seeding process
seedAll().catch(error => {
  console.error(`${colors.red}Unexpected error:${colors.reset}`, error);
  process.exit(1);
});
