const { test, expect } = require('@playwright/test');

test.describe('Settings Page', () => {
  test('should load settings page', async ({ page }) => {
    await page.goto('/settings');
    await expect(page).toHaveURL(/.*settings/);
    await expect(page.locator('.settings-container')).toBeVisible();
  });

  test('should update a setting', async ({ page }) => {
    await page.goto('/settings');
    await page.fill('input[name="siteName"]', '新站点名');
    await page.click('button[type="submit"]');
    await expect(page.locator('.success-message')).toBeVisible();
  });
}); 