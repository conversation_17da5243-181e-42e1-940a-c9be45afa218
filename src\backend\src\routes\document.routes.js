const express = require('express');
const { authenticate } = require('../middlewares/auth.middleware');
const validate = require('../middlewares/validate');
const documentValidation = require('../validations/document.validation');
const documentController = require('../controllers/document.controller');
const commentController = require('../controllers/comment.controller');

const router = express.Router();

router
  .route('/')
  .post(
    authenticate,
    validate(documentValidation.createDocument),
    documentController.createDocument
  )
  .get(
    authenticate,
    validate(documentValidation.getDocuments),
    documentController.getDocuments
  );

router
  .route('/:documentId')
  .get(
    authenticate,
    validate(documentValidation.getDocument),
    documentController.getDocument
  )
  .patch(
    authenticate,
    validate(documentValidation.updateDocument),
    documentController.updateDocument
  )
  .delete(
    authenticate,
    validate(documentValidation.deleteDocument),
    documentController.deleteDocument
  );

// Tags related routes
router
  .route('/:documentId/tags')
  .post(
    authenticate,
    validate(documentValidation.addTagsSchema),
    documentController.addTags
  )
  .delete(
    authenticate,
    validate(documentValidation.removeTagsSchema),
    documentController.removeTags
  );

// Document comments routes
router
  .route('/:documentId/comments')
  .get(
    authenticate,
    validate(documentValidation.getDocumentCommentsSchema),
    commentController.getDocumentComments
  );

module.exports = router;