const nodemailer = require('nodemailer');
const config = require('../config/config');
const logger = require('../config/logger');

/**
 * Create a nodemailer transport instance
 * @returns {nodemailer.Transporter}
 */
let transport;

// Only create transport if SMTP settings are provided
if (config.email.smtp && config.email.smtp.host) {
  transport = nodemailer.createTransport(config.email.smtp);
  
  // Verify transport configuration in non-production environments
  if (config.env !== 'production') {
    transport
      .verify()
      .then(() => logger.info('Connected to email server'))
      .catch((error) => logger.warn('Unable to connect to email server. Make sure you have configured the SMTP options:', error));
  }
} else {
  logger.info('Email service disabled - SMTP configuration not provided');
  // Create a mock transport for development that logs instead of sending
  if (config.env === 'development') {
    transport = {
      sendMail: (msg) => {
        logger.info('Email sending skipped (SMTP not configured):', {
          to: msg.to,
          subject: msg.subject
        });
        return Promise.resolve();
      },
      verify: () => Promise.resolve()
    };
  }
}

/**
 * Send an email
 * @param {string} to
 * @param {string} subject
 * @param {string} text
 * @param {string} html
 * @returns {Promise<void>}
 */
const sendEmail = async (to, subject, text, html) => {
  if (!transport) {
    logger.warn('Email sending skipped - SMTP not configured');
    return;
  }
  
  const msg = {
    from: config.email.from,
    to,
    subject,
    text,
    html,
  };
  await transport.sendMail(msg);
};

/**
 * Send reset password email
 * @param {string} to
 * @param {string} token
 * @returns {Promise<void>}
 */
const sendResetPasswordEmail = async (to, token) => {
  const subject = 'Reset Password';
  const resetPasswordUrl = `${config.clientUrl}/reset-password?token=${token}`;
  const text = `Dear user,
To reset your password, click on this link: ${resetPasswordUrl}
If you did not request any password resets, then ignore this email.`;
  const html = `<div>
    <p>Dear user,</p>
    <p>To reset your password, click <a href="${resetPasswordUrl}">here</a></p>
    <p>If you did not request any password resets, please ignore this email.</p>
  </div>`;

  await sendEmail(to, subject, text, html);
};

/**
 * Send verification email
 * @param {string} to
 * @param {string} token
 * @returns {Promise<void>}
 */
const sendVerificationEmail = async (to, token) => {
  const subject = 'Email Verification';
  const verificationEmailUrl = `${config.clientUrl}/verify-email?token=${token}`;
  const text = `Dear user,
To verify your email, click on this link: ${verificationEmailUrl}
If you did not create an account, then ignore this email.`;
  const html = `<div>
    <p>Dear user,</p>
    <p>To verify your email, click <a href="${verificationEmailUrl}">here</a></p>
    <p>If you did not create an account, please ignore this email.</p>
  </div>`;

  await sendEmail(to, subject, text, html);
};

module.exports = {
  transport,
  sendEmail,
  sendResetPasswordEmail,
  sendVerificationEmail,
};