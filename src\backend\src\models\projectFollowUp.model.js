const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ProjectFollowUp = sequelize.define('projectfollowup', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'project',
      key: 'id'
    }
  },
  followUpDate: {
    type: DataTypes.DATE,
    allowNull: false
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  createdBy: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'employee',
      key: 'id'
    }
  }
}, {
  timestamps: true,
});

// 模型关联将在 index.js 中定义
module.exports = { ProjectFollowUp };