const { test, expect } = require('@playwright/test');

test.describe('Forgot Password Page', () => {
  test('should load forgot password page', async ({ page }) => {
    await page.goto('/forgot-password');
    await expect(page).toHaveURL(/.*forgot-password/);
    await expect(page.locator('form')).toBeVisible();
    await expect(page.locator('input[type="email"]')).toBeVisible();
  });

  test('should show error for invalid email', async ({ page }) => {
    await page.goto('/forgot-password');
    await page.fill('input[type="email"]', 'invalid-email');
    await page.click('button[type="submit"]');
    await expect(page.locator('.error-message')).toBeVisible();
  });

  test('should show success for valid email', async ({ page }) => {
    await page.goto('/forgot-password');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    await expect(page.locator('.success-message')).toBeVisible();
  });
}); 