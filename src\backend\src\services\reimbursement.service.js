const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { Reimbursement, Project, Supplier, Attachment, User } = require('../models');

/**
 * Create a reimbursement
 * @param {Object} reimbursementBody
 * @param {Array} attachments
 * @param {Object} user
 * @returns {Promise<Reimbursement>}
 */
const createReimbursement = async (reimbursementBody, attachments = [], user) => {
  // Set created by
  if (user) {
    reimbursementBody.createdBy = user.id;
  }

  // If projectId is provided, fetch project data to set projectName
  if (reimbursementBody.projectId) {
    const project = await Project.findByPk(reimbursementBody.projectId);
    if (project) {
      reimbursementBody.projectName = project.name;
    }
  }

  // If supplierId is provided, fetch supplier data to set supplier name
  if (reimbursementBody.supplierId) {
    const supplier = await Supplier.findByPk(reimbursementBody.supplierId);
    if (supplier) {
      reimbursementBody.supplier = supplier.name;
    }
  }

  // 处理报销人信息
  if (reimbursementBody.reimburserId) {
    const reimburser = await User.findByPk(reimbursementBody.reimburserId);
    if (reimburser) {
      reimbursementBody.reimburserName = reimburser.username;
    }
  }

  // Create reimbursement record
  const reimbursement = await Reimbursement.create(reimbursementBody);

  // Create attachment records
  if (attachments.length > 0) {
    for (const attachment of attachments) {
      attachment.reimbursementId = reimbursement.id;
      await Attachment.create(attachment);
    }
  }

  return reimbursement;
};

/**
 * Get reimbursement by id
 * @param {string} id
 * @returns {Promise<Reimbursement>}
 */
const getReimbursementById = async (id) => {
  const reimbursement = await Reimbursement.findByPk(id, {
    include: [
      {
        model: Attachment,
        as: 'attachments'
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'username', 'email']
      },
      {
        model: Project,
        as: 'project',
        attributes: ['id', 'name', 'code']
      },
      {
        model: Supplier,
        as: 'supplierInfo',
        attributes: ['id', 'name', 'category']
      },
      {
        model: User,
        as: 'reimburser',
        attributes: ['id', 'username', 'email']
      }
    ]
  });

  if (!reimbursement) {
    // 使用明确的数字状态码，避免可能的undefined问题
    throw new ApiError(404, 'Reimbursement not found');
  }

  return reimbursement;
};

/**
 * Update reimbursement by id
 * @param {string} reimbursementId
 * @param {Object} updateBody
 * @param {Array} newAttachments
 * @param {Array} existingAttachmentIds
 * @param {Object} user
 * @returns {Promise<Reimbursement>}
 */
const updateReimbursementById = async (reimbursementId, updateBody, newAttachments = [], existingAttachmentIds = [], user) => {
  const reimbursement = await getReimbursementById(reimbursementId);

  // Set updated by
  if (user) {
    updateBody.updatedBy = user.id;
  }

  // If projectId is provided, fetch project data to set projectName
  if (updateBody.projectId) {
    const project = await Project.findByPk(updateBody.projectId);
    if (project) {
      updateBody.projectName = project.name;
    }
  }

  // If supplierId is provided, fetch supplier data to set supplier name
  if (updateBody.supplierId) {
    const supplier = await Supplier.findByPk(updateBody.supplierId);
    if (supplier) {
      updateBody.supplier = supplier.name;
    }
  }

  // 处理报销人信息
  if (updateBody.reimburserId) {
    const reimburser = await User.findByPk(updateBody.reimburserId);
    if (reimburser) {
      updateBody.reimburserName = reimburser.username;
    }
  }

  // Update reimbursement record
  Object.assign(reimbursement, updateBody);
  await reimbursement.save();

  // Add new attachments
  if (newAttachments.length > 0) {
    for (const attachment of newAttachments) {
      attachment.reimbursementId = reimbursement.id;
      await Attachment.create(attachment);
    }
  }

  // Handle existing attachments (delete ones not in the list)
  if (existingAttachmentIds && existingAttachmentIds.length > 0) {
    // 确保 existingAttachmentIds 是一个数组
    const attachmentIds = Array.isArray(existingAttachmentIds)
      ? existingAttachmentIds
      : [existingAttachmentIds];

    await Attachment.destroy({
      where: {
        reimbursementId: reimbursement.id,
        id: { [Op.notIn]: attachmentIds }
      }
    });
  }

  return getReimbursementById(reimbursementId);
};

/**
 * Delete reimbursement by id
 * @param {string} reimbursementId
 * @returns {Promise<void>}
 */
const deleteReimbursementById = async (reimbursementId) => {
  try {
    // 直接查询数据库，不使用getReimbursementById以避免抛出404错误
    const reimbursement = await Reimbursement.findByPk(reimbursementId);

    // 如果记录不存在，返回明确的错误
    if (!reimbursement) {
      throw new ApiError(404, '报销记录不存在或已被删除');
    }

    // 删除记录
    await reimbursement.destroy();
  } catch (error) {
    // 如果不是ApiError，则重新抛出
    if (!(error instanceof ApiError)) {
      console.error(`删除报销记录时发生错误: ${error.message}`, error);
      throw new ApiError(500, '删除报销记录失败，请稍后重试');
    }
    throw error;
  }
};

/**
 * Query for reimbursements
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Reimbursements and pagination info
 */
const queryReimbursements = async (filter, options) => {
  const { projectName, reimbursementType, supplier, startDate, endDate } = filter;
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;
  const offset = (page - 1) * limit;

  // Build where clause
  const whereClause = {};
  if (projectName) whereClause.projectName = { [Op.like]: `%${projectName}%` };
  if (reimbursementType) whereClause.reimbursementType = reimbursementType;
  if (supplier) whereClause.supplier = { [Op.like]: `%${supplier}%` };

  // Date range filtering
  if (startDate || endDate) {
    whereClause.reimbursementDate = {};
    if (startDate) whereClause.reimbursementDate[Op.gte] = startDate;
    if (endDate) whereClause.reimbursementDate[Op.lte] = endDate;
  }

  // Execute query
  const { count, rows } = await Reimbursement.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Attachment,
        as: 'attachments'
      },
      {
        model: User,
        as: 'creator',
        attributes: ['id', 'username', 'email']
      },
      {
        model: Project,
        as: 'project',
        attributes: ['id', 'name', 'code']
      },
      {
        model: Supplier,
        as: 'supplierInfo',
        attributes: ['id', 'name', 'category']
      },
      {
        model: User,
        as: 'reimburser',
        attributes: ['id', 'username', 'email']
      }
    ],
    order: [[sortBy, sortOrder]],
    limit,
    offset,
    distinct: true
  });

  return {
    results: rows,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

/**
 * Get projects for dropdown menu
 * @returns {Promise<Array>} - Array of project options
 */
const getProjectsForDropdown = async () => {
  const projects = await Project.findAll({
    attributes: ['id', 'name', 'code'],
    order: [['name', 'ASC']]
  });

  return projects.map(project => ({
    value: project.id,
    label: `${project.name} (${project.code})`,
    projectName: project.name
  }));
};

/**
 * Get suppliers for dropdown menu
 * @returns {Promise<Array>} - Array of supplier options
 */
const getSuppliersForDropdown = async () => {
  const suppliers = await Supplier.findAll({
    attributes: ['id', 'name', 'category'],
    order: [['name', 'ASC']]
  });

  return suppliers.map(supplier => ({
    value: supplier.id,
    label: `${supplier.name} (${supplier.category})`,
    supplierName: supplier.name
  }));
};

/**
 * Get users for dropdown menu
 * @returns {Promise<Array>} - Array of user options
 */
const getUsersForDropdown = async () => {
  const users = await User.findAll({
    attributes: ['id', 'username', 'email'],
    where: { isActive: true },
    order: [['username', 'ASC']]
  });

  return users.map(user => ({
    value: user.id,
    label: user.username,
    email: user.email
  }));
};

module.exports = {
  createReimbursement,
  getReimbursementById,
  updateReimbursementById,
  deleteReimbursementById,
  queryReimbursements,
  getProjectsForDropdown,
  getSuppliersForDropdown,
  getUsersForDropdown
};