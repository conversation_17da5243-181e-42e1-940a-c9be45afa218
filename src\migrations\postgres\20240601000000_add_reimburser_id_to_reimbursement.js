/**
 * Migration: Add reimburserId to reimbursement table
 * Created at: 2024-06-01
 */
'use strict';

/** @type {import('sequelize').QueryInterface} */
module.exports = {
  // Apply the migration
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('reimbursement', 'reimburserId', {
      type: Sequelize.DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'user',
        key: 'id'
      }
    });
  },

  // Revert the migration
  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('reimbursement', 'reimburserId');
  }
};
