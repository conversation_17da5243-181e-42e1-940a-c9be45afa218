const Joi = require('joi');
const { password } = require('./custom.validation');

const createUser = {
  body: Joi.object().keys({
    username: Joi.string().required().min(3).max(50).messages({
      'string.empty': '用户名不能为空',
      'any.required': '用户名是必填项',
      'string.min': '用户名至少需要3个字符',
      'string.max': '用户名不能超过50个字符'
    }),
    email: Joi.string().required().email().messages({
      'string.email': '必须是有效的电子邮箱地址',
      'string.empty': '电子邮箱不能为空',
      'any.required': '电子邮箱是必填项'
    }),
    password: Joi.string().required().custom(password).messages({
      'string.empty': '密码不能为空',
      'any.required': '密码是必填项'
    }),
    lastName: Joi.string().required().messages({
      'string.empty': '姓名不能为空',
      'any.required': '姓名是必填项'
    }),
    role: Joi.string().valid('user', 'admin').messages({
      'any.only': '角色必须是有效的类型'
    }),
    status: Joi.string().valid('active', 'inactive', 'pending').messages({
      'any.only': '状态必须是有效的类型'
    }),
    position: Joi.string().allow('', null),
    department: Joi.string().allow('', null),
    departmentId: Joi.number().allow(null),
    contactInfo: Joi.object().allow(null),
    phoneNumber: Joi.string().allow('', null),
    phone: Joi.string().allow('', null),
    gender: Joi.string().allow('', null),
    idNumber: Joi.string().allow('', null),
    address: Joi.string().allow('', null),
    avatarUrl: Joi.string().allow('', null),
    isActive: Joi.boolean(),
    isAdmin: Joi.boolean(),
    notes: Joi.string().allow('', null)
  }),
};

const getUsers = {
  query: Joi.object().keys({
    name: Joi.string().allow(null),
    role: Joi.string().allow(null),
    search: Joi.string().allow(null, ''),
    status: Joi.string().allow(null),
    department: Joi.string().allow(null),
    isActive: Joi.string().allow(null),
    sortBy: Joi.string().allow(null),
    sortOrder: Joi.string().valid('asc', 'desc').allow(null),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    }),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    }),
  }),
};

const getUser = {
  params: Joi.object().keys({
    userId: Joi.string().required().messages({
      'string.empty': '用户ID不能为空',
      'any.required': '用户ID是必填项'
    }),
  }),
};

const updateUser = {
  params: Joi.object().keys({
    userId: Joi.string().required().messages({
      'string.empty': '用户ID不能为空',
      'any.required': '用户ID是必填项'
    }),
  }),
  body: Joi.object()
    .keys({
      username: Joi.string().min(3).max(50).messages({
        'string.min': '用户名至少需要3个字符',
        'string.max': '用户名不能超过50个字符'
      }),
      email: Joi.string().email().messages({
        'string.email': '必须是有效的电子邮箱地址'
      }),
      password: Joi.string().custom(password),
      name: Joi.string().allow(null),
      lastName: Joi.string().allow('', null),
      role: Joi.string().valid('user', 'admin').messages({
        'any.only': '角色必须是有效的类型'
      }),
      status: Joi.string().valid('active', 'inactive', 'pending').messages({
        'any.only': '状态必须是有效的类型'
      }),
      position: Joi.string().allow('', null),
      department: Joi.string().allow('', null),
      departmentId: Joi.number().allow(null),
      contactInfo: Joi.object().allow(null),
      phoneNumber: Joi.string().allow('', null),
      phone: Joi.string().allow('', null),
      gender: Joi.string().allow('', null),
      idNumber: Joi.string().allow('', null),
      address: Joi.string().allow('', null),
      avatarUrl: Joi.string().allow('', null),
      isActive: Joi.boolean(),
      isAdmin: Joi.boolean(),
      notes: Joi.string().allow('', null)
    })
    .min(1)
    .messages({
      'object.min': '至少需要提供一个要更新的字段'
    }),
};

const updateBatchUsers = {
  body: Joi.object().keys({
    criteria: Joi.object().required().min(1).messages({
      'object.min': '查询条件至少需要一个字段',
      'any.required': '查询条件是必填项'
    })
      .description('匹配要更新的用户的条件，例如 { role: "user" }'),
    updateData: Joi.object().required().min(1).messages({
      'object.min': '更新数据至少需要一个字段',
      'any.required': '更新数据是必填项'
    })
      .keys({
        name: Joi.string().allow(null),
        role: Joi.string().valid('user', 'admin').messages({
          'any.only': '角色必须是有效的类型'
        }),
        status: Joi.string().valid('active', 'inactive', 'pending').messages({
          'any.only': '状态必须是有效的类型'
        }),
        // Don't allow email or password in batch updates for security
      })
      .description('要更新的字段'),
  }),
};

const resetUserPassword = {
  params: Joi.object().keys({
    userId: Joi.string().required().messages({
      'string.empty': '用户ID不能为空',
      'any.required': '用户ID是必填项'
    }),
  }),
  body: Joi.object().keys({
    newPassword: Joi.string().required().custom(password).messages({
      'string.empty': '新密码不能为空',
      'any.required': '新密码是必填项'
    }),
  }),
};

const deleteUser = {
  params: Joi.object().keys({
    userId: Joi.string().required().messages({
      'string.empty': '用户ID不能为空',
      'any.required': '用户ID是必填项'
    }),
  }),
};

module.exports = {
  createUser,
  getUsers,
  getUser,
  updateUser,
  updateBatchUsers,
  resetUserPassword,
  deleteUser,
};