const { Op } = require('sequelize');
const db = require('../models');
const ApiError = require('../utils/ApiError');
const { getPagination, getPagingData } = require('../utils/pagination');

/**
 * Create a purchase
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Purchase>}
 */
const createPurchase = async (req, res) => {
  const purchaseData = {
    ...req.body,
    createdBy: req.user.id
  };
  const purchase = await db.Purchase.create(purchaseData);
  res.status(201).json(purchase);
};

/**
 * Get all purchases
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Purchase[]>}
 */
const getPurchases = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const { limit: limitValue, offset } = getPagination(page, limit);

    const condition = {};
    if (req.query.purchaseNumber) {
      condition.purchaseNumber = { [Op.like]: `%${req.query.purchaseNumber}%` };
    }
    if (req.query.status) {
      condition.status = req.query.status;
    }
    if (req.query.projectId) {
      condition.projectId = req.query.projectId;
    }
    if (req.query.supplierId) {
      condition.supplierId = req.query.supplierId;
    }
    if (req.query.startDate && req.query.endDate) {
      condition.purchaseDate = {
        [Op.between]: [new Date(req.query.startDate), new Date(req.query.endDate)]
      };
    }

    const data = await db.Purchase.findAndCountAll({
      where: condition,
      limit: limitValue,
      offset,
      include: [
        {
          model: db.User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: db.Project,
          as: 'project',
          attributes: ['id', 'name', 'code', 'projectNumber']
        },
        {
          model: db.Supplier,
          as: 'supplier',
          attributes: ['id', 'name', 'category', 'contactName', 'contactPhone', 'contactEmail']
        }
      ],
      order: [['createdAt', 'DESC']]
    });

    const response = getPagingData(data, page, limitValue);
    return res.status(200).json(response);
  } catch (error) {
    console.error('Error in getPurchases:', error);
    return res.status(500).json({
      code: 500,
      message: 'Error retrieving purchases',
      error: error.message
    });
  }
};

/**
 * Get purchase by id
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Purchase>}
 */
const getPurchase = async (req, res) => {
  try {
    const purchase = await db.Purchase.findByPk(req.params.purchaseId, {
      include: [
        {
          model: db.User,
          as: 'creator',
          attributes: ['id', 'username', 'email']
        },
        {
          model: db.Project,
          as: 'project',
          attributes: ['id', 'name', 'code']
        },
        {
          model: db.Supplier,
          as: 'supplier',
          attributes: ['id', 'name', 'code']
        }
      ]
    });
    if (!purchase) {
      return res.status(404).json({
        code: 404,
        message: 'Purchase not found'
      });
    }
    return res.status(200).json(purchase);
  } catch (error) {
    console.error('Error in getPurchase:', error);
    return res.status(500).json({
      code: 500,
      message: 'Error retrieving purchase'
    });
  }
};

/**
 * Update purchase by id
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Purchase>}
 */
const updatePurchase = async (req, res) => {
  try {
    const purchase = await db.Purchase.findByPk(req.params.purchaseId);
    if (!purchase) {
      return res.status(404).json({
        code: 404,
        message: 'Purchase not found'
      });
    }

    // Only allow updates if the purchase is in draft or pending status
    if (!['draft', 'pending'].includes(purchase.status)) {
      return res.status(400).json({
        code: 400,
        message: 'Cannot update purchase in current status'
      });
    }

    Object.assign(purchase, req.body);
    await purchase.save();
    return res.status(200).json(purchase);
  } catch (error) {
    console.error('Error in updatePurchase:', error);
    return res.status(500).json({
      code: 500,
      message: 'Error updating purchase'
    });
  }
};

/**
 * Delete purchase by id
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Purchase>}
 */
const deletePurchase = async (req, res) => {
  try {
    const purchase = await db.Purchase.findByPk(req.params.purchaseId);
    if (!purchase) {
      return res.status(404).json({
        code: 404,
        message: 'Purchase not found'
      });
    }

    // Only allow deletion if the purchase is in draft status
    if (purchase.status !== 'draft') {
      return res.status(400).json({
        code: 400,
        message: 'Can only delete purchases in draft status'
      });
    }

    await purchase.destroy();
    // 使用明确的数字状态码，避免可能的undefined问题
    return res.status(204).send();
  } catch (error) {
    console.error('Error in deletePurchase:', error);
    return res.status(500).json({
      code: 500,
      message: 'Error deleting purchase'
    });
  }
};

/**
 * Approve purchase
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Purchase>}
 */
const approvePurchase = async (req, res) => {
  try {
    const purchase = await db.Purchase.findByPk(req.params.purchaseId);
    if (!purchase) {
      return res.status(404).json({
        code: 404,
        message: 'Purchase not found'
      });
    }

    if (purchase.status !== 'pending') {
      return res.status(400).json({
        code: 400,
        message: 'Can only approve pending purchases'
      });
    }

    purchase.status = 'approved';
    purchase.approvedBy = req.user.id;
    purchase.approvedAt = new Date();
    await purchase.save();

    return res.status(200).json(purchase);
  } catch (error) {
    console.error('Error in approvePurchase:', error);
    return res.status(500).json({
      code: 500,
      message: 'Error approving purchase'
    });
  }
};

/**
 * Reject purchase
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Purchase>}
 */
const rejectPurchase = async (req, res) => {
  try {
    const purchase = await db.Purchase.findByPk(req.params.purchaseId);
    if (!purchase) {
      return res.status(404).json({
        code: 404,
        message: 'Purchase not found'
      });
    }

    if (purchase.status !== 'pending') {
      return res.status(400).json({
        code: 400,
        message: 'Can only reject pending purchases'
      });
    }

    purchase.status = 'rejected';
    purchase.notes = req.body.reason;
    await purchase.save();

    return res.status(200).json(purchase);
  } catch (error) {
    console.error('Error in rejectPurchase:', error);
    return res.status(500).json({
      code: 500,
      message: 'Error rejecting purchase'
    });
  }
};

module.exports = {
  createPurchase,
  getPurchases,
  getPurchase,
  updatePurchase,
  deletePurchase,
  approvePurchase,
  rejectPurchase
};