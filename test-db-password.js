// 直接测试数据库密码操作
const path = require('path');

// 设置环境变量
process.env.NODE_ENV = 'development';

async function testDatabasePassword() {
  try {
    // 动态导入后端模块
    const { User } = require('./src/backend/src/models');
    const bcrypt = require('bcryptjs');
    
    console.log('🔄 开始测试数据库密码操作...');
    
    // 创建测试用户
    const testUser = {
      username: 'dbtest' + Date.now(),
      email: 'dbtest' + Date.now() + '@example.com',
      password: 'originalpassword123',
      lastName: '数据库测试用户',
      role: 'user'
    };
    
    console.log('📝 创建测试用户:', testUser.username);
    const user = await User.create(testUser);
    console.log('✅ 用户创建成功, ID:', user.id);
    
    // 验证原始密码
    const originalPasswordValid = await bcrypt.compare(testUser.password, user.password);
    console.log('🔐 原始密码验证:', originalPasswordValid ? '通过' : '失败');
    
    if (!originalPasswordValid) {
      throw new Error('原始密码验证失败');
    }
    
    // 重置密码
    const newPassword = 'newpassword456';
    console.log('🔄 重置密码...');
    
    user.password = newPassword;
    await user.save();
    
    console.log('✅ 密码重置完成');
    
    // 重新获取用户以确保数据库中的数据是最新的
    const updatedUser = await User.findByPk(user.id);
    
    // 验证新密码
    const newPasswordValid = await bcrypt.compare(newPassword, updatedUser.password);
    console.log('🔐 新密码验证:', newPasswordValid ? '通过' : '失败');
    
    // 验证原始密码不再有效
    const oldPasswordStillValid = await bcrypt.compare(testUser.password, updatedUser.password);
    console.log('🚫 原始密码失效检查:', oldPasswordStillValid ? '失败(仍然有效)' : '通过(已失效)');
    
    // 清理测试数据
    await user.destroy();
    console.log('🗑️ 测试数据已清理');
    
    if (newPasswordValid && !oldPasswordStillValid) {
      console.log('\n🎉 数据库密码操作测试通过！');
      return true;
    } else {
      console.log('\n❌ 数据库密码操作测试失败！');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
    return false;
  }
}

// 运行测试
testDatabasePassword()
  .then(success => {
    console.log(success ? '\n✅ 所有测试通过' : '\n❌ 测试失败');
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 测试执行出错:', error);
    process.exit(1);
  }); 