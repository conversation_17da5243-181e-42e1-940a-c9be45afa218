const { v4: uuidv4 } = require('uuid');
const { User, Project } = require('../models');
const { Product } = require('../models/product.model');
const { ProductTransaction } = require('../models/productTransaction.model');

/**
 * Helper function to determine the transaction type
 * @param {string} transactionType - The original transaction type
 * @returns {string} - The processed transaction type
 */
function determineTransactionType(transactionType) {
  if (transactionType === 'return') {
    return 'in';
  } else if (transactionType === 'adjustment') {
    return 'adjustment';
  }
  return transactionType;
}

async function seedProductTransactions() {
  try {
    // Check if product transactions already exist
    const transactionCount = await ProductTransaction.count();
    if (transactionCount > 0) {
      console.log('Product transactions already exist, skipping product transaction seeding');
      return;
    }

    // Get or create a default admin user for createdBy field
    let adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      adminUser = await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin'
      });
    }

    // Get existing projects
    const projects = await Project.findAll({ limit: 10 });
    if (projects.length === 0) {
      console.log('No projects found for product transaction seeding');
      return;
    }

    // Get existing products
    const products = await Product.findAll();
    if (products.length === 0) {
      console.log('No products found for product transaction seeding');
      return;
    }

    // Transaction types
    const transactionTypes = ['in', 'out', 'return', 'adjustment'];

    // Status options - commented out as not currently used
    // const statusOptions = ['pending', 'completed', 'cancelled'];

    // Generate random product transactions
    const productTransactions = [];

    for (let i = 0; i < 100; i++) {
      const transactionType = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
      const project = projects[Math.floor(Math.random() * projects.length)];
      const product = products[Math.floor(Math.random() * products.length)];

      // Generate random date within the last year
      const transactionDate = new Date();
      transactionDate.setDate(transactionDate.getDate() - Math.floor(Math.random() * 365));

      // Generate random quantity between 1 and 20
      const quantity = Math.floor(Math.random() * 20) + 1;

      // We'll use these variables later if needed
      // const status = statusOptions[Math.floor(Math.random() * statusOptions.length)];
      // const transactionNumber = `TRX-${String(i + 1).padStart(6, '0')}`;

      productTransactions.push({
        id: uuidv4(),
        // Determine the transaction type
        type: determineTransactionType(transactionType),
        productId: product.id,
        projectId: project.id,
        quantity: quantity,
        unitPrice: product.price,
        totalPrice: product.price * quantity,
        transactionDate: transactionDate,
        reference: `REF-${String(i + 1).padStart(6, '0')}`,
        notes: `测试产品库存记录 ${i + 1}`,
        createdBy: adminUser.id
      });
    }

    // Bulk create product transactions
    await ProductTransaction.bulkCreate(productTransactions);
    console.log('Successfully seeded product transactions');
  } catch (error) {
    console.error('Error seeding product transactions:', error);
    throw error;
  }
}

module.exports = seedProductTransactions;
