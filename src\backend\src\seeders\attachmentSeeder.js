const { Attachment, User } = require('../models');
const { faker } = require('@faker-js/faker/locale/zh_CN');

const fileTypes = [
  { ext: '.pdf', type: 'application/pdf' },
  { ext: '.doc', type: 'application/msword' },
  { ext: '.docx', type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' },
  { ext: '.xls', type: 'application/vnd.ms-excel' },
  { ext: '.xlsx', type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' },
  { ext: '.jpg', type: 'image/jpeg' },
  { ext: '.png', type: 'image/png' },
  { ext: '.zip', type: 'application/zip' }
];

const entityTypes = ['Project', 'Document', 'Contract', 'Comment'];

async function seedAttachments() {
  try {
    // Clear existing data
    await Attachment.destroy({ where: {} });

    // Get all users for reference
    const users = await User.findAll();

    if (!users.length) {
      console.log('Please seed users first');
      return;
    }

    const attachments = [];

    // Create 100 attachments
    for (let i = 0; i < 100; i++) {
      const fileType = faker.helpers.arrayElement(fileTypes);
      const fileName = faker.system.fileName() + fileType.ext;
      const entityType = faker.helpers.arrayElement(entityTypes);
      
      // Generate a realistic entity ID range based on entity type
      let entityIdMax;
      switch (entityType) {
        case 'Project':
          entityIdMax = 50;
          break;
        case 'Document':
          entityIdMax = 100;
          break;
        case 'Contract':
          entityIdMax = 50;
          break;
        case 'Comment':
          entityIdMax = 200;
          break;
        default:
          entityIdMax = 50;
      }

      const attachment = {
        fileName: fileName,
        originalName: fileName,
        fileSize: faker.number.int({ min: 1024, max: 10485760 }), // 1KB to 10MB
        mimeType: fileType.type,
        filePath: `/uploads/${faker.date.recent().getFullYear()}/${faker.date.recent().getMonth() + 1}/${fileName}`,
        entityType: entityType,
        entityId: faker.number.int({ min: 1, max: entityIdMax }),
        uploadedBy: faker.helpers.arrayElement(users).id,
        description: faker.lorem.sentence(),
        tags: faker.helpers.arrayElements(['重要', '合同', '报告', '图片', '文档'], faker.number.int({ min: 0, max: 2 })),
        createdAt: new Date(),
        updatedAt: new Date()
      };
      attachments.push(attachment);
    }

    await Attachment.bulkCreate(attachments);
    console.log('Attachments seeder executed successfully');
  } catch (error) {
    console.error('Error seeding attachments:', error);
  }
}

module.exports = seedAttachments; 