const { Op, Sequelize } = require('sequelize');
const { 
  Project, 
  Supplier, 
  Purchase, 
  Document, 
  User,
  Finance,
  Product
} = require('../models');

/**
 * Get comprehensive dashboard statistics
 * @returns {Promise<Object>} - Dashboard statistics
 */
const getDashboardStats = async () => {
  const [
    projectStats,
    supplierStats,
    purchaseStats,
    inventoryStats
  ] = await Promise.all([
    getProjectStats(),
    getSupplierStats(),
    getPurchaseStats(),
    getInventoryStats()
  ]);

  return {
    projects: projectStats,
    suppliers: supplierStats,
    purchases: purchaseStats,
    inventory: inventoryStats,
    lastUpdated: new Date()
  };
};

/**
 * Get project statistics
 * @returns {Promise<Object>} - Project statistics
 */
const getProjectStats = async () => {
  try {
    // Get current date and previous month for trend calculation
    const currentDate = new Date();
    const previousMonth = new Date();
    previousMonth.setMonth(currentDate.getMonth() - 1);

    // Get project counts by status
    const projectCounts = await Project.findAll({
      attributes: [
        'status',
        [Sequelize.fn('COUNT', Sequelize.col('id')), 'count']
      ],
      group: ['status'],
      raw: true
    });

    // Get projects created this month vs last month for trend
    const thisMonthProjects = await Project.count({
      where: {
        createdAt: {
          [Op.gte]: new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
        }
      }
    });

    const lastMonthProjects = await Project.count({
      where: {
        createdAt: {
          [Op.gte]: new Date(previousMonth.getFullYear(), previousMonth.getMonth(), 1),
          [Op.lt]: new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
        }
      }
    });

    // Calculate trend percentage
    const trend = lastMonthProjects > 0 
      ? Math.round(((thisMonthProjects - lastMonthProjects) / lastMonthProjects) * 100)
      : thisMonthProjects > 0 ? 100 : 0;

    // Process project counts
    const statusCounts = {};
    projectCounts.forEach(item => {
      statusCounts[item.status] = parseInt(item.count);
    });

    const active = (statusCounts['in_progress'] || 0) + (statusCounts['planning'] || 0);
    const completed = statusCounts['completed'] || 0;
    const delayed = statusCounts['delayed'] || 0;

    return {
      active,
      completed,
      delayed,
      total: active + completed + delayed,
      trend,
      lastUpdated: new Date()
    };
  } catch (error) {
    console.error('Error getting project stats:', error);
    return {
      active: 0,
      completed: 0,
      delayed: 0,
      total: 0,
      trend: 0,
      lastUpdated: new Date()
    };
  }
};

/**
 * Get supplier statistics
 * @returns {Promise<Object>} - Supplier statistics
 */
const getSupplierStats = async () => {
  try {
    const currentDate = new Date();
    const previousMonth = new Date();
    previousMonth.setMonth(currentDate.getMonth() - 1);

    // Get total suppliers
    const total = await Supplier.count();

    // Get active suppliers (those with recent purchases)
    const active = await Supplier.count({
      include: [{
        model: Purchase,
        where: {
          createdAt: {
            [Op.gte]: new Date(currentDate.getFullYear(), currentDate.getMonth() - 3, 1) // Last 3 months
          }
        },
        required: true
      }]
    });

    // Get new suppliers this month
    const newThisMonth = await Supplier.count({
      where: {
        createdAt: {
          [Op.gte]: new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
        }
      }
    });

    // Get new suppliers last month for trend
    const newLastMonth = await Supplier.count({
      where: {
        createdAt: {
          [Op.gte]: new Date(previousMonth.getFullYear(), previousMonth.getMonth(), 1),
          [Op.lt]: new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
        }
      }
    });

    const trend = newLastMonth > 0 
      ? Math.round(((newThisMonth - newLastMonth) / newLastMonth) * 100)
      : newThisMonth > 0 ? 100 : 0;

    return {
      total,
      active,
      new: newThisMonth,
      trend,
      lastUpdated: new Date()
    };
  } catch (error) {
    console.error('Error getting supplier stats:', error);
    return {
      total: 0,
      active: 0,
      new: 0,
      trend: 0,
      lastUpdated: new Date()
    };
  }
};

/**
 * Get purchase statistics
 * @returns {Promise<Object>} - Purchase statistics
 */
const getPurchaseStats = async () => {
  try {
    const currentDate = new Date();
    const currentMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const currentYear = new Date(currentDate.getFullYear(), 0, 1);
    const previousMonth = new Date();
    previousMonth.setMonth(currentDate.getMonth() - 1);
    const lastMonth = new Date(previousMonth.getFullYear(), previousMonth.getMonth(), 1);

    // Get monthly purchase amount
    const monthlyPurchases = await Purchase.findAll({
      attributes: [
        [Sequelize.fn('SUM', Sequelize.col('totalAmount')), 'total']
      ],
      where: {
        createdAt: {
          [Op.gte]: currentMonth
        }
      },
      raw: true
    });

    // Get yearly purchase amount
    const yearlyPurchases = await Purchase.findAll({
      attributes: [
        [Sequelize.fn('SUM', Sequelize.col('totalAmount')), 'total']
      ],
      where: {
        createdAt: {
          [Op.gte]: currentYear
        }
      },
      raw: true
    });

    // Get last month's purchases for trend
    const lastMonthPurchases = await Purchase.findAll({
      attributes: [
        [Sequelize.fn('SUM', Sequelize.col('totalAmount')), 'total']
      ],
      where: {
        createdAt: {
          [Op.gte]: lastMonth,
          [Op.lt]: currentMonth
        }
      },
      raw: true
    });

    // Get pending purchases
    const pending = await Purchase.count({
      where: {
        status: 'pending'
      }
    });

    const monthly = parseFloat(monthlyPurchases[0]?.total || 0);
    const yearly = parseFloat(yearlyPurchases[0]?.total || 0);
    const lastMonthTotal = parseFloat(lastMonthPurchases[0]?.total || 0);

    const trend = lastMonthTotal > 0 
      ? Math.round(((monthly - lastMonthTotal) / lastMonthTotal) * 100)
      : monthly > 0 ? 100 : 0;

    return {
      monthly,
      yearly,
      pending,
      trend,
      lastUpdated: new Date()
    };
  } catch (error) {
    console.error('Error getting purchase stats:', error);
    return {
      monthly: 0,
      yearly: 0,
      pending: 0,
      trend: 0,
      lastUpdated: new Date()
    };
  }
};

/**
 * Get inventory statistics
 * @returns {Promise<Object>} - Inventory statistics
 */
const getInventoryStats = async () => {
  try {
    // Get total inventory items
    const items = await Product.count();

    // Get low stock items (stock <= minStock)
    const lowStock = await Product.count({
      where: {
        stock: {
          [Op.lte]: Sequelize.col('minStock')
        }
      }
    });

    // Get total inventory value
    const inventoryValue = await Product.findAll({
      attributes: [
        [Sequelize.fn('SUM', 
          Sequelize.literal('stock * price')
        ), 'totalValue']
      ],
      raw: true
    });

    const totalValue = parseFloat(inventoryValue[0]?.totalValue || 0);

    // Calculate trend (simplified - could be based on value changes over time)
    const trend = 3; // Placeholder positive trend

    return {
      items,
      lowStock,
      totalValue,
      trend,
      lastUpdated: new Date()
    };
  } catch (error) {
    console.error('Error getting inventory stats:', error);
    return {
      items: 0,
      lowStock: 0,
      totalValue: 0,
      trend: 0,
      lastUpdated: new Date()
    };
  }
};

/**
 * Get recent projects for dashboard
 * @param {number} limit - Number of projects to return
 * @returns {Promise<Array>} - Recent projects
 */
const getRecentProjects = async (limit = 5) => {
  try {
    const projects = await Project.findAll({
      limit,
      order: [['updatedAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'Manager',
          attributes: ['id', 'username', 'firstName', 'lastName']
        }
      ],
      attributes: [
        'id',
        'name',
        'projectNumber',
        'status',
        'progress',
        'startDate',
        'endDate',
        'budget',
        'description'
      ]
    });

    return projects.map(project => ({
      id: project.id,
      name: project.name,
      code: project.projectNumber,
      client: project.clientName || '未指定客户',
      status: project.status,
      progress: project.progress || 0,
      deadline: project.endDate,
      manager: project.Manager ? 
        `${project.Manager.firstName || ''} ${project.Manager.lastName || ''}`.trim() || 
        project.Manager.username : '未指定'
    }));
  } catch (error) {
    console.error('Error getting recent projects:', error);
    return [];
  }
};

/**
 * Get recent documents for dashboard
 * @param {number} limit - Number of documents to return
 * @returns {Promise<Array>} - Recent documents
 */
const getRecentDocuments = async (limit = 5) => {
  try {
    const documents = await Document.findAll({
      limit,
      order: [['updatedAt', 'DESC']],
      include: [
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'username', 'firstName', 'lastName']
        }
      ],
      attributes: [
        'id',
        'title',
        'category',
        'description',
        'updatedAt'
      ]
    });

    return documents.map(doc => ({
      id: doc.id,
      title: doc.title,
      category: doc.category || 'General',
      excerpt: doc.description ? 
        doc.description.substring(0, 100) + (doc.description.length > 100 ? '...' : '') :
        '无描述',
      updatedAt: doc.updatedAt,
      author: doc.Creator ? 
        `${doc.Creator.firstName || ''} ${doc.Creator.lastName || ''}`.trim() || 
        doc.Creator.username : '未知'
    }));
  } catch (error) {
    console.error('Error getting recent documents:', error);
    return [];
  }
};

module.exports = {
  getDashboardStats,
  getProjectStats,
  getSupplierStats,
  getPurchaseStats,
  getInventoryStats,
  getRecentProjects,
  getRecentDocuments
}; 