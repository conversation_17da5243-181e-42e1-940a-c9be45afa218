const { expect } = require('chai');
const sinon = require('sinon');
const { User } = require('../../../src/models');
const userService = require('../../../src/services/user.service');
const ApiError = require('../../../src/utils/ApiError');
const bcrypt = require('bcryptjs');

describe('User Service', () => {
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('createUser', () => {
    it('should create a user successfully', async () => {
      // Arrange
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123',
        role: 'user'
      };

      const hashedPassword = 'hashedPassword123';
      sandbox.stub(bcrypt, 'hash').resolves(hashedPassword);
      sandbox.stub(User, 'findOne').resolves(null);
      
      const createdUser = {
        id: '1',
        name: userData.name,
        email: userData.email,
        role: userData.role,
        password: hashedPassword
      };
      
      const createStub = sandbox.stub(User, 'create').resolves(createdUser);

      // Act
      const result = await userService.createUser(userData);

      // Assert
      expect(createStub.calledOnce).to.be.true;
      expect(result).to.have.property('id');
      expect(result.name).to.equal(userData.name);
      expect(result.email).to.equal(userData.email);
      expect(result.role).to.equal(userData.role);
      expect(result).to.not.have.property('password'); // 密码不应该返回
    });

    it('should throw error if email already exists', async () => {
      // Arrange
      const userData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'Password123',
        role: 'user'
      };

      const existingUser = { id: '1', email: userData.email };
      sandbox.stub(User, 'findOne').resolves(existingUser);

      // Act & Assert
      try {
        await userService.createUser(userData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('already exists');
      }
    });
  });

  describe('getUserById', () => {
    it('should return user if found', async () => {
      // Arrange
      const userId = '1';
      const userData = {
        id: userId,
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user'
      };

      sandbox.stub(User, 'findByPk').resolves(userData);

      // Act
      const result = await userService.getUserById(userId);

      // Assert
      expect(result).to.deep.equal(userData);
    });

    it('should throw error if user not found', async () => {
      // Arrange
      const userId = '999';
      sandbox.stub(User, 'findByPk').resolves(null);

      // Act & Assert
      try {
        await userService.getUserById(userId);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(404);
        expect(error.message).to.include('not found');
      }
    });
  });

  describe('updateUserById', () => {
    it('should update user successfully', async () => {
      // Arrange
      const userId = '1';
      const userData = {
        id: userId,
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        save: sandbox.stub().resolves()
      };

      const updateData = {
        name: 'Updated User',
        role: 'admin'
      };

      sandbox.stub(userService, 'getUserById').resolves(userData);
      sandbox.stub(User, 'findOne').resolves(null);

      // Act
      const result = await userService.updateUserById(userId, updateData);

      // Assert
      expect(result.name).to.equal(updateData.name);
      expect(result.role).to.equal(updateData.role);
      expect(userData.save.calledOnce).to.be.true;
    });

    it('should throw error if updating to existing email', async () => {
      // Arrange
      const userId = '1';
      const userData = {
        id: userId,
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user'
      };

      const updateData = {
        email: '<EMAIL>'
      };

      sandbox.stub(userService, 'getUserById').resolves(userData);
      sandbox.stub(User, 'findOne').resolves({ id: '2', email: '<EMAIL>' });

      // Act & Assert
      try {
        await userService.updateUserById(userId, updateData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('already exists');
      }
    });

    it('should update password if provided', async () => {
      // Arrange
      const userId = '1';
      const userData = {
        id: userId,
        name: 'Test User',
        email: '<EMAIL>',
        role: 'user',
        password: 'oldHashedPassword',
        save: sandbox.stub().resolves()
      };

      const updateData = {
        password: 'NewPassword123'
      };

      const hashedPassword = 'newHashedPassword123';
      sandbox.stub(bcrypt, 'hash').resolves(hashedPassword);
      sandbox.stub(userService, 'getUserById').resolves(userData);
      sandbox.stub(User, 'findOne').resolves(null);

      // Act
      const result = await userService.updateUserById(userId, updateData);

      // Assert
      expect(result.password).to.equal(hashedPassword);
      expect(userData.save.calledOnce).to.be.true;
    });
  });

  describe('deleteUserById', () => {
    it('should delete user successfully', async () => {
      // Arrange
      const userId = '1';
      const userData = {
        id: userId,
        name: 'Test User',
        destroy: sandbox.stub().resolves()
      };

      sandbox.stub(userService, 'getUserById').resolves(userData);

      // Act
      await userService.deleteUserById(userId);

      // Assert
      expect(userData.destroy.calledOnce).to.be.true;
    });
  });

  describe('queryUsers', () => {
    it('should return users and pagination info', async () => {
      // Arrange
      const filter = { role: 'user' };
      const options = { page: 1, limit: 10 };
      
      const users = [
        { id: '1', name: 'User 1', email: '<EMAIL>', role: 'user' },
        { id: '2', name: 'User 2', email: '<EMAIL>', role: 'user' }
      ];
      
      const findAndCountAllStub = sandbox.stub(User, 'findAndCountAll').resolves({
        count: 2,
        rows: users
      });

      // Act
      const result = await userService.queryUsers(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      expect(result.results).to.deep.equal(users);
      expect(result.pagination.totalResults).to.equal(2);
      
      // Verify filter was applied correctly
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause.role).to.equal('user');
    });

    it('should handle search filter correctly', async () => {
      // Arrange
      const filter = { search: 'test' };
      const options = { page: 1, limit: 10 };
      
      const findAndCountAllStub = sandbox.stub(User, 'findAndCountAll').resolves({
        count: 0,
        rows: []
      });

      // Act
      await userService.queryUsers(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      // Verify that the where clause includes the search condition
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause[Symbol.for('sequelize.Op.or')]).to.exist;
    });
  });

  describe('getUserByEmail', () => {
    it('should return user if found', async () => {
      // Arrange
      const email = '<EMAIL>';
      const userData = {
        id: '1',
        name: 'Test User',
        email: email,
        role: 'user'
      };

      sandbox.stub(User, 'findOne').resolves(userData);

      // Act
      const result = await userService.getUserByEmail(email);

      // Assert
      expect(result).to.deep.equal(userData);
    });

    it('should return null if user not found', async () => {
      // Arrange
      const email = '<EMAIL>';
      sandbox.stub(User, 'findOne').resolves(null);

      // Act
      const result = await userService.getUserByEmail(email);

      // Assert
      expect(result).to.be.null;
    });
  });
});
