/**
 * Export Service - Provides functionality for exporting data in various formats
 */

import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import notificationService from './notification.service';

/**
 * Convert an array of objects to CSV format
 * @param {Array} data - Array of objects to convert
 * @param {Array} headers - Array of header objects with title and key properties
 * @returns {string} - CSV formatted string
 */
const convertToCSV = (data, headers) => {
  if (!data || !data.length) return '';
  
  // Create header row
  const headerRow = headers.map(header => `"${header.title}"`).join(',');
  
  // Create data rows
  const rows = data.map(item => {
    return headers.map(header => {
      const value = item[header.key];
      // Handle different data types
      if (value === null || value === undefined) return '""';
      if (typeof value === 'string') return `"${value.replace(/"/g, '""')}"`;
      if (typeof value === 'object' && value instanceof Date) {
        return `"${format(value, 'yyyy-MM-dd HH:mm:ss')}"`;
      }
      if (typeof value === 'object') return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
      return `"${value}"`;
    }).join(',');
  });
  
  // Combine header and data rows
  return [headerRow, ...rows].join('\n');
};

/**
 * Export data as CSV file
 * @param {Array} data - Array of objects to export
 * @param {Array} headers - Array of header objects with title and key properties
 * @param {string} filename - Name of the file to download (without extension)
 */
const exportCSV = (data, headers, filename) => {
  try {
    // Convert data to CSV
    const csv = convertToCSV(data, headers);
    
    // Create blob and download
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    saveAs(blob, `${filename}.csv`);
    
    // Show success notification
    notificationService.success('导出成功', `数据已成功导出为 ${filename}.csv`);
  } catch (error) {
    console.error('CSV export error:', error);
    notificationService.error('导出失败', error.message || '导出CSV文件时发生错误');
  }
};

/**
 * Export data as Excel file
 * @param {Array} data - Array of objects to export
 * @param {Array} headers - Array of header objects with title and key properties
 * @param {string} filename - Name of the file to download (without extension)
 * @param {string} sheetName - Name of the worksheet (default: 'Sheet1')
 */
const exportExcel = (data, headers, filename, sheetName = 'Sheet1') => {
  try {
    // Prepare data for Excel format
    const excelData = data.map(item => {
      const row = {};
      headers.forEach(header => {
        const value = item[header.key];
        if (value instanceof Date) {
          row[header.title] = format(value, 'yyyy-MM-dd HH:mm:ss');
        } else if (value === null || value === undefined) {
          row[header.title] = '';
        } else if (typeof value === 'object') {
          row[header.title] = JSON.stringify(value);
        } else {
          row[header.title] = value;
        }
      });
      return row;
    });
    
    // Create workbook and worksheet
    const worksheet = XLSX.utils.json_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    
    // Generate Excel file and download
    XLSX.writeFile(workbook, `${filename}.xlsx`);
    
    // Show success notification
    notificationService.success('导出成功', `数据已成功导出为 ${filename}.xlsx`);
  } catch (error) {
    console.error('Excel export error:', error);
    notificationService.error('导出失败', error.message || '导出Excel文件时发生错误');
  }
};

/**
 * Export data as JSON file
 * @param {Array} data - Array of objects to export
 * @param {string} filename - Name of the file to download (without extension)
 */
const exportJSON = (data, filename) => {
  try {
    // Convert data to JSON string
    const jsonString = JSON.stringify(data, null, 2);
    
    // Create blob and download
    const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8;' });
    saveAs(blob, `${filename}.json`);
    
    // Show success notification
    notificationService.success('导出成功', `数据已成功导出为 ${filename}.json`);
  } catch (error) {
    console.error('JSON export error:', error);
    notificationService.error('导出失败', error.message || '导出JSON文件时发生错误');
  }
};

/**
 * Export data as PDF file (placeholder - requires additional library)
 * @param {Array} data - Array of objects to export
 * @param {Array} headers - Array of header objects with title and key properties
 * @param {string} filename - Name of the file to download (without extension)
 * @param {Object} options - Additional options for PDF generation
 */
const exportPDF = (data, headers, filename, options = {}) => {
  // This is a placeholder. PDF export requires additional libraries like jsPDF
  notificationService.info('功能开发中', 'PDF导出功能正在开发中，敬请期待');
};

/**
 * Get file extension from format string
 * @param {string} format - Export format (csv, excel, json, pdf)
 * @returns {string} - File extension
 */
const getFileExtension = (format) => {
  const extensions = {
    csv: 'csv',
    excel: 'xlsx',
    json: 'json',
    pdf: 'pdf'
  };
  return extensions[format] || format;
};

/**
 * Generate a default filename based on entity name and current date
 * @param {string} entityName - Name of the entity being exported
 * @returns {string} - Generated filename
 */
const generateFilename = (entityName) => {
  const date = format(new Date(), 'yyyyMMdd_HHmmss');
  return `${entityName}_${date}`;
};

/**
 * Export data in the specified format
 * @param {Object} options - Export options
 * @param {Array} options.data - Array of objects to export
 * @param {Array} options.headers - Array of header objects with title and key properties
 * @param {string} options.format - Export format (csv, excel, json, pdf)
 * @param {string} options.filename - Name of the file to download (without extension)
 * @param {string} options.entityName - Name of the entity being exported (used if filename not provided)
 * @param {Object} options.additionalOptions - Additional options for specific export formats
 */
const exportData = ({ data, headers, format, filename, entityName, additionalOptions = {} }) => {
  // Validate input
  if (!data || !Array.isArray(data)) {
    notificationService.error('导出失败', '没有可导出的数据');
    return;
  }
  
  if (!format) {
    notificationService.error('导出失败', '未指定导出格式');
    return;
  }
  
  // Generate filename if not provided
  const exportFilename = filename || generateFilename(entityName || 'export');
  
  // Export based on format
  switch (format.toLowerCase()) {
    case 'csv':
      exportCSV(data, headers, exportFilename);
      break;
    case 'excel':
      exportExcel(data, headers, exportFilename, additionalOptions.sheetName);
      break;
    case 'json':
      exportJSON(data, exportFilename);
      break;
    case 'pdf':
      exportPDF(data, headers, exportFilename, additionalOptions);
      break;
    default:
      notificationService.error('导出失败', `不支持的导出格式: ${format}`);
  }
};

export default {
  exportData,
  exportCSV,
  exportExcel,
  exportJSON,
  exportPDF,
  generateFilename,
  getFileExtension
};
