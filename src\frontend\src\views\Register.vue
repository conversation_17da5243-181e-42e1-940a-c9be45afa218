<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Create your account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Or
          <router-link to="/login" class="font-medium text-indigo-600 hover:text-indigo-500">
            sign in to your existing account
          </router-link>
        </p>
      </div>

      <form class="mt-8 space-y-6" @submit.prevent="handleRegister">
        <div v-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
          {{ error }}
        </div>

        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="name" class="sr-only">Full Name <span class="text-red-500">*</span></label>
            <input id="name" name="name" type="text" autocomplete="name" required
                  v-model="name"
                  class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  placeholder="Full Name">
          </div>
          <div>
            <label for="email-address" class="sr-only">Email address <span class="text-red-500">*</span></label>
            <input id="email-address" name="email" type="email" autocomplete="email" required
                  v-model="email"
                  class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  placeholder="Email address">
          </div>
          <div>
            <label for="password" class="sr-only">Password <span class="text-red-500">*</span></label>
            <input id="password" name="password" type="password" autocomplete="new-password" required
                  v-model="password"
                  class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  placeholder="Password">
          </div>
          <div>
            <label for="confirm-password" class="sr-only">Confirm Password <span class="text-red-500">*</span></label>
            <input id="confirm-password" name="confirmPassword" type="password" autocomplete="new-password" required
                  v-model="confirmPassword"
                  class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  placeholder="Confirm Password">
          </div>
        </div>

        <div>
          <button type="submit" :disabled="loading || !isValid"
                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed">
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg v-if="!loading" class="h-5 w-5 text-indigo-500 group-hover:text-indigo-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
              </svg>
              <svg v-else class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
              </svg>
            </span>
            {{ loading ? 'Creating account...' : 'Create account' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

export default {
  name: 'RegisterView',
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()

    const name = ref('')
    const email = ref('')
    const password = ref('')
    const confirmPassword = ref('')
    const loading = ref(false)
    const error = ref('')

    // Validation
    const isValid = computed(() => {
      return name.value.trim() !== '' &&
             email.value.trim() !== '' &&
             password.value.trim() !== '' &&
             password.value === confirmPassword.value &&
             password.value.length >= 8
    })

    const handleRegister = async () => {
      if (!isValid.value) {
        error.value = 'Please fill in all fields and ensure passwords match.'
        return
      }

      try {
        error.value = ''
        loading.value = true

        // 按照后端要求构造payload
        const payload = {
          email: email.value,
          password: password.value,
          username: name.value, // 使用name作为username
          lastName: name.value.split(' ').slice(-1)[0] || name.value // 尝试从全名中提取姓氏，如果没有空格则使用全名
        }

        // Call the real API for registration
        await authStore.register(payload)

        router.push({
          path: '/login',
          query: { registered: 'success' }
        })
      } catch (err) {
        error.value = err.message || 'Failed to register. Please try again.'
      } finally {
        loading.value = false
      }
    }

    return {
      name,
      email,
      password,
      confirmPassword,
      loading,
      error,
      isValid,
      handleRegister
    }
  }
}
</script>