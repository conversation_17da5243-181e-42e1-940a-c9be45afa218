// @ts-check
import { test, expect } from '@playwright/test';

test.describe('仪表盘测试', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // 确认已登录到仪表盘
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/dashboard');
  });

  test('仪表盘页面显示正确', async ({ page }) => {
    // 验证页面标题和欢迎信息
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('仪表盘');
    const str = await page.textContent('p')

    expect(await page.textContent('p.text-gray-500')).toContain('欢迎回来');
    
    // 验证统计卡片存在
    const statCards = await page.locator('.bg-white.overflow-hidden.shadow.rounded-lg').count();
    expect(statCards).toBeGreaterThanOrEqual(4);
    
    // 验证项目卡片
    expect(await page.textContent('text=活跃项目')).toBeTruthy();
    
    // 验证供应商卡片
    expect(await page.textContent('text=供应商')).toBeTruthy();
    
    // 验证采购卡片
    expect(await page.textContent('text=查看采购记录')).toBeTruthy();
    
    // 验证库存卡片
    expect(await page.textContent('text=查看库存')).toBeTruthy();
  });

  test('仪表盘项目列表显示正确', async ({ page }) => {
    // 验证近期项目标题
    const h2s = await page.locator('h2').allTextContents();
    expect(h2s.some(t => t.includes('近期项目'))).toBeTruthy();
    
    // 验证项目表格存在
    expect(await page.isVisible('table')).toBeTruthy();
    
    // 验证表格标题行
    const headers = await page.locator('thead th').allTextContents();
    expect(headers).toContain('项目名称');
    expect(headers).toContain('状态');
    expect(headers).toContain('进度');
    
    // 验证至少有一个项目行
    const projectRows = await page.locator('tbody tr').count();
    expect(projectRows).toBeGreaterThan(0);
  });

  test('仪表盘导航链接工作正常', async ({ page }) => {
    // 点击"查看所有项目"链接
    await page.click('text=查看所有项目');
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/projects');
    
    // 返回仪表盘
    await page.goto('/dashboard');
    
    // 点击"管理供应商"链接
    await page.click('text=管理供应商');
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/suppliers');
    
    // 返回仪表盘
    await page.goto('/dashboard');
    
    // 点击"查看采购记录"链接
    await page.click('text=查看采购记录');
    await page.waitForNavigation({timeout:1000});
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/purchases');
    
    // 返回仪表盘
    await page.goto('/dashboard');
    
    // 点击"查看库存"链接
    await page.click('text=查看库存');
    await page.waitForNavigation({timeout:1000});
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/inventory');
  });

  test('创建项目按钮工作正常', async ({ page }) => {
    // 点击"创建项目"按钮
    await page.click('text=创建项目');
    await page.waitForNavigation();
    
    // 验证跳转到创建项目页面
    await new Promise(resolve => setTimeout(resolve, 1000));
    expect(page.url()).toContain('/projects/create');
  });
});
