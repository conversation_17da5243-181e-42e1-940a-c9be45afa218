const Joi = require('joi');
const { objectId } = require('./custom.validation');

const createTask = {
  body: Joi.object().keys({
    title: Joi.string().required().min(1).max(255).messages({
      'string.empty': '任务标题不能为空',
      'string.min': '任务标题长度不能小于{#limit}个字符',
      'string.max': '任务标题长度不能超过{#limit}个字符',
      'any.required': '任务标题是必填项'
    }),
    description: Joi.string().allow('', null),
    status: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled').messages({
      'any.only': '状态必须是待处理(pending)、进行中(in_progress)、已完成(completed)或已取消(cancelled)'
    }),
    priority: Joi.string().valid('low', 'medium', 'high', 'urgent').messages({
      'any.only': '优先级必须是低(low)、中(medium)、高(high)或紧急(urgent)'
    }),
    dueDate: Joi.date().allow(null).messages({
      'date.base': '截止日期格式不正确'
    }),
    assignedTo: Joi.string().custom(objectId).allow(null).messages({
      'string.empty': '指派人ID不能为空',
      'any.custom': '指派人ID必须是有效的ObjectID格式'
    }),
    projectId: Joi.string().custom(objectId).allow(null).messages({
      'string.empty': '项目ID不能为空',
      'any.custom': '项目ID必须是有效的ObjectID格式'
    })
  })
};

const getTasks = {
  query: Joi.object().keys({
    title: Joi.string().allow(null),
    status: Joi.string().allow(null),
    priority: Joi.string().allow(null),
    assignedTo: Joi.string().custom(objectId).messages({
      'any.custom': '指派人ID必须是有效的ObjectID格式'
    }),
    projectId: Joi.string().custom(objectId).messages({
      'any.custom': '项目ID必须是有效的ObjectID格式'
    }),
    sortBy: Joi.string().allow(null),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    }),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    })
  })
};

const getTask = {
  params: Joi.object().keys({
    taskId: Joi.string().custom(objectId).required().messages({
      'string.empty': '任务ID不能为空',
      'any.required': '任务ID是必填项',
      'any.custom': '任务ID必须是有效的ObjectID格式'
    })
  })
};

const updateTask = {
  params: Joi.object().keys({
    taskId: Joi.string().custom(objectId).required().messages({
      'string.empty': '任务ID不能为空',
      'any.required': '任务ID是必填项',
      'any.custom': '任务ID必须是有效的ObjectID格式'
    })
  }),
  body: Joi.object().keys({
    title: Joi.string().min(1).max(255).messages({
      'string.empty': '任务标题不能为空',
      'string.min': '任务标题长度不能小于{#limit}个字符',
      'string.max': '任务标题长度不能超过{#limit}个字符'
    }),
    description: Joi.string().allow('', null),
    status: Joi.string().valid('pending', 'in_progress', 'completed', 'cancelled').messages({
      'any.only': '状态必须是待处理(pending)、进行中(in_progress)、已完成(completed)或已取消(cancelled)'
    }),
    priority: Joi.string().valid('low', 'medium', 'high', 'urgent').messages({
      'any.only': '优先级必须是低(low)、中(medium)、高(high)或紧急(urgent)'
    }),
    dueDate: Joi.date().allow(null).messages({
      'date.base': '截止日期格式不正确'
    }),
    assignedTo: Joi.string().custom(objectId).allow(null).messages({
      'any.custom': '指派人ID必须是有效的ObjectID格式'
    }),
    projectId: Joi.string().custom(objectId).allow(null).messages({
      'any.custom': '项目ID必须是有效的ObjectID格式'
    })
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deleteTask = {
  params: Joi.object().keys({
    taskId: Joi.string().custom(objectId).required().messages({
      'string.empty': '任务ID不能为空',
      'any.required': '任务ID是必填项',
      'any.custom': '任务ID必须是有效的ObjectID格式'
    })
  })
};

module.exports = {
  createTask,
  getTasks,
  getTask,
  updateTask,
  deleteTask
};