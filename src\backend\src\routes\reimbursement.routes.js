const express = require('express');
const router = express.Router();
const reimbursementController = require('../controllers/reimbursement.controller');
const multer = require('multer');
const path = require('path');
const { authorize } = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const reimbursementValidation = require('../validations/reimbursement.validation');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/reimbursements/');
  },
  filename: function (req, file, cb) {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 限制文件大小为10MB
});

// 获取下拉选项
router.get('/project-options', reimbursementController.getProjectOptions);
router.get('/supplier-options', reimbursementController.getSupplierOptions);
router.get('/user-options', reimbursementController.getUserOptions);

// 获取所有报销记录
router.get('/', validate(reimbursementValidation.getReimbursements), reimbursementController.getReimbursements);

// 获取单个报销记录
router.get('/:id', validate(reimbursementValidation.getReimbursement), reimbursementController.getReimbursement);

// 创建新的报销记录
router.post('/', upload.array('attachments', 5), validate(reimbursementValidation.createReimbursement), reimbursementController.createReimbursement);

// 更新报销记录
router.put('/:id', upload.array('attachments', 5), validate(reimbursementValidation.updateReimbursement), reimbursementController.updateReimbursement);

// 删除报销记录
router.delete('/:id', validate(reimbursementValidation.deleteReimbursement), reimbursementController.deleteReimbursement);

module.exports = router;
