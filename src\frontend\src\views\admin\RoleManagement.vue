<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">角色管理</h1>
      <button
        @click="openCreateModal"
        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
      >
        创建角色
      </button>
    </div>

    <!-- 角色列表 -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色名称</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="role in roles" :key="role.id">
            <td class="px-6 py-4 whitespace-nowrap">{{ role.name }}</td>
            <td class="px-6 py-4">{{ role.description }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(role.createdAt) }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <button
                @click="openEditModal(role)"
                class="text-indigo-600 hover:text-indigo-900 mr-4"
              >
                编辑
              </button>
              <button
                @click="confirmDelete(role)"
                class="text-red-600 hover:text-red-900"
              >
                删除
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 创建/编辑角色模态框 -->
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div class="bg-white p-6 rounded-lg w-full max-w-md">
        <h2 class="text-xl font-bold mb-4">{{ isEditing ? '编辑角色' : '创建角色' }}</h2>
        <form @submit.prevent="handleSubmit">
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">
              角色名称
            </label>
            <input
              v-model="formData.name"
              type="text"
              class="w-full px-3 py-2 border rounded"
              required
            />
          </div>
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">
              描述
            </label>
            <textarea
              v-model="formData.description"
              class="w-full px-3 py-2 border rounded"
              rows="3"
            ></textarea>
          </div>
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2">
              权限
            </label>
            <div class="space-y-2">
              <div v-for="permission in permissions" :key="permission.id" class="flex items-center">
                <input
                  type="checkbox"
                  :value="permission.id"
                  v-model="formData.permissions"
                  class="mr-2"
                />
                <span>{{ permission.name }}</span>
              </div>
            </div>
          </div>
          <div class="flex justify-end space-x-4">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 border rounded"
            >
              取消
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              保存
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useToast } from 'vue-toastification'
import { useRoleStore } from '@/stores/role'
import { formatDate } from '@/utils/date'

const toast = useToast()
const roleStore = useRoleStore()

const roles = ref([])
const permissions = ref([])
const showModal = ref(false)
const isEditing = ref(false)
const formData = ref({
  id: null,
  name: '',
  description: '',
  permissions: []
})

// 获取角色列表
const fetchRoles = async () => {
  try {
    roles.value = await roleStore.getRoles()
  } catch (error) {
    toast.error('获取角色列表失败')
  }
}

// 获取权限列表
const fetchPermissions = async () => {
  try {
    permissions.value = await roleStore.getPermissions()
  } catch (error) {
    toast.error('获取权限列表失败')
  }
}

// 打开创建模态框
const openCreateModal = () => {
  isEditing.value = false
  formData.value = {
    id: null,
    name: '',
    description: '',
    permissions: []
  }
  showModal.value = true
}

// 打开编辑模态框
const openEditModal = (role) => {
  isEditing.value = true
  formData.value = {
    id: role.id,
    name: role.name,
    description: role.description,
    permissions: role.permissions.map(p => p.id)
  }
  showModal.value = true
}

// 关闭模态框
const closeModal = () => {
  showModal.value = false
  formData.value = {
    id: null,
    name: '',
    description: '',
    permissions: []
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    if (isEditing.value) {
      await roleStore.updateRole(formData.value)
      toast.success('角色更新成功')
    } else {
      await roleStore.createRole(formData.value)
      toast.success('角色创建成功')
    }
    closeModal()
    fetchRoles()
  } catch (error) {
    toast.error(isEditing.value ? '更新角色失败' : '创建角色失败')
  }
}

// 确认删除
const confirmDelete = async (role) => {
  if (confirm(`确定要删除角色 "${role.name}" 吗？`)) {
    try {
      await roleStore.deleteRole(role.id)
      toast.success('角色删除成功')
      fetchRoles()
    } catch (error) {
      toast.error('删除角色失败')
    }
  }
}

onMounted(() => {
  fetchRoles()
  fetchPermissions()
})
</script>
