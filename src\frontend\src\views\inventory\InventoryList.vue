<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">库存管理</h1>
        <p class="page-subtitle">管理和跟踪产品库存信息</p>
      </div>
      <router-link to="/inventory/records" class="btn btn-primary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
        </svg>
        库存记录
      </router-link>
      
    </div>

    <!-- 筛选区域 -->
    <div class="filter-card">
      <div class="flex flex-wrap gap-6">
        <div class="flex-grow min-w-[200px]">
          <label for="material-name" class="form-label font-medium">产品名称</label>
          <div class="relative mt-2">
            <input
              type="text"
              id="material-name"
              v-model="filters.name"
              class="form-input pl-10 py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
              placeholder="请输入产品名称"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-grow min-w-[200px]">
          <label for="material-code" class="form-label font-medium">产品编码</label>
          <div class="relative mt-2">
            <input
              type="text"
              id="material-code"
              v-model="filters.code"
              class="form-input pl-10 py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
              placeholder="请输入产品编码"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
          </div>
        </div>
        <div class="min-w-[150px]">
          <label for="stock-status" class="form-label font-medium">库存状态</label>
          <select id="stock-status" v-model="filters.status" class="form-input mt-2 py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200">
            <option value="">全部状态</option>
            <option>库存充足</option>
            <option>库存不足</option>
            <option>库存预警</option>
          </select>
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button 
          @click="resetFilters" 
          class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200"
          :disabled="loading"
        >
          重置
        </button>
        <button 
          @click="handleSearch" 
          class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200"
          :disabled="loading"
        >
          <span v-if="loading" class="inline-block animate-spin mr-1">↻</span>
          {{ loading ? '加载中...' : '查询' }}
        </button>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2">{{ error }}</p>
      <button @click="fetchInventory()" class="mt-2 text-red-700 hover:text-red-900 font-medium underline">
        重试
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="card overflow-hidden shadow-md p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">正在加载库存数据...</p>
    </div>

    <!-- 空数据状态 -->
    <div v-else-if="!loading && inventoryItems.length === 0" class="card overflow-hidden shadow-md p-8 text-center">
      <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无库存产品</h3>
      <p class="text-gray-500 mb-4">点击"库存记录"按钮查看历史出入库记录</p>
      <router-link to="/inventory/records" class="btn btn-primary">库存记录</router-link>
    </div>

    <!-- 表格区域 -->
    <div v-else class="card overflow-hidden shadow-md">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品编码</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品名称</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格型号</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">当前库存</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">安全库存</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in inventoryItems" :key="item.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{{ item.code }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.name }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.specification }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.unit }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.stock }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.minStock }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="badge" :class="{
                  'badge-success': item.stock > item.minStock,
                  'badge-warning': item.stock === item.minStock,
                  'badge-danger': item.stock < item.minStock,
                }">
                  {{ item.stock > item.minStock ? '库存充足' :
                     item.stock === item.minStock ? '库存预警' : '库存不足' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">                <router-link
                  :to="`/inventory/${item.id || item._id}`"
                  class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                >
                  <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                  </svg>
                  <span class="font-medium">详情</span>
                </router-link>
          
                <button @click="showInboundModal(item)" class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-800 group mr-3">
                  <svg class="w-4 h-4 mr-1 text-green-500 group-hover:text-green-700 transition-colors duration-200" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4v16m8-8H4"/>
                  </svg>
                  <span class="font-medium">入库</span>
                </button>
                <button @click="showOutboundModal(item)" class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-orange-50 text-orange-600 hover:bg-orange-100 hover:text-orange-800 group">
                  <svg class="w-4 h-4 mr-1 text-orange-500 group-hover:text-orange-700 transition-colors duration-200" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 12h16m-8-8v16"/>
                  </svg>
                  <span class="font-medium">出库</span>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="mt-4 flex justify-between items-center px-6 py-4 border-t border-gray-200">
        <div class="text-sm text-gray-500">
          显示第 <span class="font-medium">{{ (pagination.currentPage - 1) * pagination.pageSize + 1 }}</span> 至
          <span class="font-medium">{{ Math.min(pagination.currentPage * pagination.pageSize, pagination.total) }}</span> 条，共
          <span class="font-medium">{{ pagination.total }}</span> 条记录
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="changePage(pagination.currentPage - 1)"
            :disabled="pagination.currentPage <= 1"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <span class="text-sm font-medium text-gray-700">第 {{ pagination.currentPage }} 页</span>
          <button
            @click="changePage(pagination.currentPage + 1)"
            :disabled="pagination.currentPage >= Math.ceil(pagination.total / pagination.pageSize)"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认模态框 -->
    <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 animate-fadeIn">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all duration-300 ease-in-out" :class="{'scale-100 opacity-100': showDeleteModal, 'scale-95 opacity-0': !showDeleteModal}">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-1">确认删除</h3>
          <p class="text-gray-500">
            您确定要删除产品 "{{ itemToDelete?.name || '' }}" 吗？请注意，此操作执行后将无法恢复，相关的库存数据将被永久移除。删除前请确认该产品不再需要使用或已有替代品。
          </p>

          <div class="mt-6 flex justify-center space-x-3">
            <button
              type="button"
              @click="closeDeleteModal"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="button"
              @click="performDelete"
              class="btn btn-danger"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                删除中...
              </span>
              <span v-else>确认删除</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作结果通知 -->
    <div v-if="notification.show"
      class="fixed bottom-4 right-4 p-4 rounded-lg shadow-lg transform transition-all duration-500 max-w-md animate-slideInUp"
      :class="{
        'bg-green-50 text-green-800 border border-green-200': notification.type === 'success',
        'bg-red-50 text-red-800 border border-red-200': notification.type === 'error'
      }">
      <div class="flex">
        <div v-if="notification.type === 'success'" class="flex-shrink-0">
          <svg class="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div v-else class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium">{{ notification.message }}</p>
        </div>
        <div class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button
              @click="notification.show = false"
              class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2"
              :class="{
                'text-green-500 hover:bg-green-100 focus:ring-green-600 focus:ring-offset-green-50': notification.type === 'success',
                'text-red-500 hover:bg-red-100 focus:ring-red-600 focus:ring-offset-red-50': notification.type === 'error'
              }">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 入库模态框 -->
    <div v-if="inboundModalVisible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-lg mx-4">
        <div class="p-5 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">库存入库</h3>
            <button @click="inboundModalVisible = false" class="text-gray-400 hover:text-gray-500">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <StockInForm 
          :item="selectedItem" 
          :loading="inboundLoading" 
          @submit="handleInboundSubmit" 
          @cancel="inboundModalVisible = false" 
        />
      </div>
    </div>

    <!-- 出库模态框 -->
    <div v-if="outboundModalVisible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-lg mx-4">
        <div class="p-5 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">库存出库</h3>
            <button @click="outboundModalVisible = false" class="text-gray-400 hover:text-gray-500">
              <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div class="p-5">
          <form @submit.prevent="submitOutbound">
            <div class="space-y-4">
              <!-- 产品信息展示 -->
              <div class="bg-gray-50 p-3 rounded-lg">
                <div class="flex items-center">
                  <div class="flex-1">
                    <p class="text-sm font-medium text-gray-900">{{ selectedItem?.name || '' }}</p>
                    <p class="text-xs text-gray-500">{{ selectedItem?.specification || '' }}</p>
                  </div>
                  <div class="text-right">
                    <p class="text-sm font-medium text-gray-500">当前库存</p>
                    <p class="text-lg font-bold">{{ selectedItem?.stock || 0 }} <span class="text-xs">{{ selectedItem?.unit || '' }}</span></p>
                  </div>
                </div>
              </div>

              <!-- 出库数量 -->
              <div>
                <label for="outbound-quantity" class="block text-sm font-medium text-gray-700 mb-1">出库数量</label>
                <div class="flex">
                  <input
                    id="outbound-quantity"
                    v-model.number="outboundForm.quantity"
                    type="number"
                    min="1"
                    :max="selectedItem?.stock || 0"
                    class="flex-1 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                    required
                  >
                  <span class="ml-2 inline-flex items-center px-3 rounded-md border border-gray-300 bg-gray-50 text-gray-500">
                    {{ selectedItem?.unit || '' }}
                  </span>
                </div>
                <p v-if="outboundForm.quantity > (selectedItem?.stock || 0)" class="mt-1 text-sm text-red-600">
                  出库数量不能超过当前库存
                </p>
              </div>

              <!-- 关联项目 -->
              <div>
                <label for="project" class="block text-sm font-medium text-gray-700 mb-1">关联项目 <span class="text-red-500">*</span></label>
                <select
                  id="project"
                  v-model="outboundForm.projectId"
                  class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  required
                >
                  <option value="">选择项目</option>
                  <option v-for="project in projects" :key="project.id" :value="project.id">
                    {{ project.name }}
                  </option>
                </select>
              </div>

              <!-- 仓库位置 -->
              <div>
                <label for="outbound-warehouse" class="block text-sm font-medium text-gray-700 mb-1">出库仓位</label>
                <select
                  id="outbound-warehouse"
                  v-model="outboundForm.warehouseLocation"
                  class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  required
                >
                  <option value="">选择仓库位置</option>
                  <option value="主仓库-A区-A01">主仓库-A区-A01</option>
                  <option value="主仓库-A区-A02">主仓库-A区-A02</option>
                  <option value="主仓库-B区-B01">主仓库-B区-B01</option>
                  <option value="分仓库-C区-C01">分仓库-C区-C01</option>
                </select>
              </div>

              <!-- 用途 -->
              <div>
                <label for="purpose" class="block text-sm font-medium text-gray-700 mb-1">用途</label>
                <select
                  id="purpose"
                  v-model="outboundForm.purpose"
                  class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  required
                >
                  <option value="">选择用途</option>
                  <option value="生产">生产</option>
                  <option value="维修">维修</option>
                  <option value="样品">样品</option>
                  <option value="销售">销售</option>
                  <option value="其他">其他</option>
                </select>
              </div>

              <!-- 领用人 -->
              <div>
                <label for="recipient" class="block text-sm font-medium text-gray-700 mb-1">领用人</label>
                <input
                  id="recipient"
                  v-model="outboundForm.recipient"
                  type="text"
                  class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                  required
                >
              </div>

              <!-- 备注 -->
              <div>
                <label for="outbound-remarks" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                <textarea
                  id="outbound-remarks"
                  v-model="outboundForm.remarks"
                  rows="2"
                  class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                ></textarea>
              </div>
            </div>

            <div class="mt-6 flex justify-end space-x-3">
              <button
                type="button"
                @click="outboundModalVisible = false"
                class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                type="submit"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                :disabled="outboundLoading || outboundForm.quantity > (selectedItem?.stock || 0)"
              >
                {{ outboundLoading ? '处理中...' : '确认出库' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import axios from 'axios';
import { useRouter } from 'vue-router';
import apiService from '@/services/apiService';
import StockInForm from '@/components/inventory/StockInForm.vue';

const router = useRouter();

// 状态变量
const loading = ref(false);
const error = ref(null);

// 库存产品列表
const inventoryItems = ref([]);

// 分页参数
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 筛选条件
const filters = reactive({
  name: '',
  code: '',
  status: ''
});

// 删除相关状态
const showDeleteModal = ref(false);
const itemToDelete = ref(null);
const isDeleting = ref(false);

// 入库相关状态
const inboundModalVisible = ref(false);
const inboundLoading = ref(false);
const selectedItem = ref(null);
const supplierOptions = ref([]);
const inboundForm = reactive({
  quantity: 1,
  warehouseLocation: '',
  supplier: '',
  batchNumber: '',
  remarks: ''
});

// 出库相关状态
const outboundModalVisible = ref(false);
const outboundLoading = ref(false);
const projects = ref([]);
const outboundForm = reactive({
  quantity: 1,
  projectId: '',
  warehouseLocation: '',
  purpose: '',
  recipient: '',
  remarks: ''
});

// 通知状态
const notification = ref({
  show: false,
  message: '',
  type: 'success',
  timeout: null
});

// 显示通知
const showNotification = (message, type = 'success') => {
  // 清除之前的超时计时器
  if (notification.value.timeout) {
    clearTimeout(notification.value.timeout);
  }

  // 设置新的通知
  notification.value = {
    show: true,
    message,
    type,
    timeout: setTimeout(() => {
      notification.value.show = false;
    }, 5000)  // 5秒后自动关闭
  };
};

// 打开删除确认模态框
const confirmDelete = (item) => {
  itemToDelete.value = item;
  showDeleteModal.value = true;
};

// 关闭删除确认模态框
const closeDeleteModal = () => {
  showDeleteModal.value = false;
  setTimeout(() => {
    itemToDelete.value = null;
  }, 300); // 等待动画完成后再重置
};

// 执行删除操作
const performDelete = async () => {
  if (!itemToDelete.value) return;

  isDeleting.value = true;
  const id = itemToDelete.value.id;

  try {
    // 调用API删除库存产品
    await axios.delete(`/api/inventory/${id}`);

    // 刷新库存列表
    await fetchInventory();

    // 关闭模态框
    closeDeleteModal();

    // 显示成功通知
    showNotification('恭喜！产品已成功删除。系统库存数据已更新，您可以在列表中查看最新库存状态。');
  } catch (err) {
    console.error('删除失败:', err);
    // 显示错误通知
    showNotification(err.response?.data?.message || '很抱歉，删除产品时遇到了问题。这可能是由于网络连接问题或系统临时故障导致的。请稍后再次尝试，如果问题持续存在，请联系系统管理员获取帮助。', 'error');
  } finally {
    isDeleting.value = false;
  }
};

// 删除库存产品
const deleteInventory = (id) => {
  const item = inventoryItems.value.find(item => item.id === id);
  if (item) {
    confirmDelete(item);
  }
};

// 显示入库模态框
const showInboundModal = async (item) => {
  selectedItem.value = item;
  // 重置入库表单
  inboundForm.quantity = 1;
  inboundForm.warehouseLocation = '';
  inboundForm.supplier = '';
  inboundForm.batchNumber = '';
  inboundForm.remarks = '';
  
  // 显示模态框
  inboundModalVisible.value = true;
  
  // 加载供应商列表
  await fetchSuppliers();
};

// 显示出库模态框
const showOutboundModal = (item) => {
  selectedItem.value = item;
  // 重置出库表单
  outboundForm.quantity = 1;
  outboundForm.projectId = '';
  outboundForm.warehouseLocation = '';
  outboundForm.purpose = '';
  outboundForm.recipient = '';
  outboundForm.remarks = '';
  outboundModalVisible.value = true;
  // 加载项目列表
  fetchProjects();
};

// 获取项目列表
const fetchProjects = async () => {
  try {
    const response = await axios.get('/api/projects', {
      params: {
        status: 'in_progress',  // 只获取进行中的项目
        limit: 100  // 获取足够多的项目
      }
    });

    if (response.data && Array.isArray(response.data.results)) {
      projects.value = response.data.results.map(project => ({
        id: project.id,
        name: project.name
      }));
    }
  } catch (err) {
    console.error('获取项目列表失败', err);
    projects.value = [];
  }
};

// 获取供应商列表
const fetchSuppliers = async () => {
  try {
    console.log('开始获取供应商列表...');
    const response = await axios.get('/api/suppliers');
    console.log('供应商API响应:', response);
    
    // 处理可能的各种响应格式
    let suppliersData = [];
    
    if (response.data && Array.isArray(response.data.results)) {
      // 标准分页格式: {results: [...]}
      suppliersData = response.data.results;
    } else if (response.data && Array.isArray(response.data)) {
      // 直接数组格式
      suppliersData = response.data;
    } else if (response.data && typeof response.data === 'object') {
      // 尝试从对象中提取数据
      const possibleArrays = Object.values(response.data).filter(val => Array.isArray(val));
      if (possibleArrays.length > 0) {
        // 使用第一个找到的数组
        suppliersData = possibleArrays[0];
      }
    }
    
    if (suppliersData.length === 0) {
      console.warn('未找到供应商数据');
      // 添加一些测试数据以便继续测试
      supplierOptions.value = [
        { id: 'test1', name: '南京市海能电子有限公司' },
        { id: 'test2', name: '上海维宏电子科技股份有限公司' },
        { id: 'test3', name: '北京欧倍尔软件技术开发有限公司' }
      ];
      return;
    }
    
    // 规范化供应商数据
    supplierOptions.value = suppliersData.map(supplier => {
      // 尝试获取id（可能是_id, id或其他键）
      const id = supplier._id || supplier.id || supplier.supplierId || supplier.supplier_id || supplier.value || '';
      
      // 尝试获取name（可能是name, title, label或其他键）
      const name = supplier.name || supplier.title || supplier.label || supplier.supplierName || supplier.supplier_name || supplier.text || '';
      
      return { id, name };
    });
    
    console.log('处理后的供应商选项:', supplierOptions.value);
  } catch (err) {
    console.error('获取供应商列表失败', err);
    
    // 添加一些测试数据以便继续测试
    supplierOptions.value = [
      { id: 'test1', name: '南京市海能电子有限公司' },
      { id: 'test2', name: '上海维宏电子科技股份有限公司' },
      { id: 'test3', name: '北京欧倍尔软件技术开发有限公司' }
    ];
  }
};

// 提交入库操作
const submitInbound = async () => {
  if (!selectedItem.value || !inboundForm.quantity || !inboundForm.warehouseLocation || !inboundForm.supplier) {
    showNotification('请填写完整的入库信息', 'error');
    return;
  }

  try {
    inboundLoading.value = true;
    
    // 查找选中的供应商
    const supplier = supplierOptions.value.find(s => s.id === inboundForm.supplier);
    const supplierName = supplier ? supplier.name : inboundForm.supplier;

    // 构建入库数据 - 只包含后端验证允许的字段
    const inboundData = {
      productId: selectedItem.value._id || selectedItem.value.id,
      quantity: inboundForm.quantity,
      unitPrice: selectedItem.value.price || 0,
      reference: `IN-${new Date().toISOString().slice(0,10).replace(/-/g, '')}`,
      notes: `库位: ${inboundForm.warehouseLocation}${inboundForm.batchNumber ? ', 批次: ' + inboundForm.batchNumber : ''}${inboundForm.remarks ? ', ' + inboundForm.remarks : ''}`,
      transactionDate: new Date().toISOString(),
      // 仅使用supplier字段保存供应商名称
      supplier: supplierName
    };

    console.log('提交入库数据:', inboundData);

    // 调用入库API
    await axios.post('/api/inventory/stock-in', inboundData);

    // 刷新库存列表
    await fetchInventory();

    // 关闭模态框
    inboundModalVisible.value = false;
    selectedItem.value = null;

    // 显示成功通知
    showNotification('入库操作成功完成！系统库存已更新。');
  } catch (err) {
    console.error('入库操作失败', err);
    showNotification(err.response?.data?.message || '入库操作失败，请重试', 'error');
  } finally {
    inboundLoading.value = false;
  }
};

// 处理StockInForm组件的提交事件
const handleInboundSubmit = async (formData) => {
  if (!selectedItem.value) {
    showNotification('未选择产品', 'error');
    return;
  }

  try {
    inboundLoading.value = true;

    // 构建入库数据 - 只包含后端验证允许的字段
    const inboundData = {
      productId: selectedItem.value._id || selectedItem.value.id,
      quantity: formData.quantity,
      unitPrice: selectedItem.value.price || 0,
      reference: `IN-${new Date().toISOString().slice(0,10).replace(/-/g, '')}`,
      notes: `库位: ${formData.warehouseLocation}${formData.batchNumber ? ', 批次: ' + formData.batchNumber : ''}${formData.remarks ? ', ' + formData.remarks : ''}`,
      transactionDate: new Date().toISOString(),
      supplier: formData.supplierName // 使用组件传来的供应商名称
    };

    console.log('提交入库数据:', inboundData);

    // 调用入库API
    await axios.post('/api/inventory/stock-in', inboundData);

    // 刷新库存列表
    await fetchInventory();

    // 关闭模态框
    inboundModalVisible.value = false;
    selectedItem.value = null;

    // 显示成功通知
    showNotification('入库操作成功完成！系统库存已更新。');
  } catch (err) {
    console.error('入库操作失败', err);
    showNotification(err.response?.data?.message || '入库操作失败，请重试', 'error');
  } finally {
    inboundLoading.value = false;
  }
};

// 提交出库操作
const submitOutbound = async () => {
  if (!selectedItem.value || !outboundForm.quantity || !outboundForm.projectId ||
      !outboundForm.warehouseLocation || !outboundForm.purpose || !outboundForm.recipient) {
    showNotification('请填写完整的出库信息', 'error');
    return;
  }

  if (outboundForm.quantity > selectedItem.value.stock) {
    showNotification('出库数量不能超过当前库存', 'error');
    return;
  }

  try {
    outboundLoading.value = true;

    // 查找关联的项目
    const project = projects.value.find(p => p.id === outboundForm.projectId);

    // 构建出库数据 - 只包含后端验证允许的字段
    const outboundData = {
      productId: selectedItem.value._id || selectedItem.value.id,
      projectId: outboundForm.projectId,
      quantity: outboundForm.quantity,
      unitPrice: selectedItem.value.price || 0,
      reference: `OUT-${new Date().toISOString().slice(0,10).replace(/-/g, '')}`,
      notes: `领用人: ${outboundForm.recipient}, 用途: ${outboundForm.purpose}, 库位: ${outboundForm.warehouseLocation}${outboundForm.remarks ? ', ' + outboundForm.remarks : ''}`,
      transactionDate: new Date().toISOString()
    };

    // 调用出库API - 使用正确的API路径
    await axios.post('/api/inventory/stock-out', outboundData);

    // 刷新库存列表
    await fetchInventory();

    // 关闭模态框
    outboundModalVisible.value = false;
    selectedItem.value = null;

    // 显示成功通知
    showNotification('出库操作成功完成！系统库存已更新。');
  } catch (err) {
    console.error('出库操作失败', err);
    showNotification(err.response?.data?.message || '出库操作失败，请重试', 'error');
  } finally {
    outboundLoading.value = false;
  }
};

// 获取库存列表
const fetchInventory = async () => {
  loading.value = true;
  error.value = null;

  try {
    const response = await axios.get('/api/inventory', {
      params: {
        page: pagination.currentPage,
        limit: pagination.pageSize,
        name: filters.name || undefined,
        code: filters.code || undefined,
        status: filters.status || undefined
      }
    });

    inventoryItems.value = response.data.results || [];
    pagination.total = response.data.total || 0;
  } catch (err) {
    console.error('获取库存列表失败:', err);
    error.value = err.response?.data?.message || '很抱歉，获取库存列表时遇到了问题。这可能是由于网络连接问题或系统临时故障导致的。请点击下方"重试"按钮再次尝试，如果问题持续存在，请联系系统管理员获取帮助。';
  } finally {
    loading.value = false;
  }
};

// 处理筛选条件变化
const handleSearch = () => {
  pagination.currentPage = 1; // 重置到第一页
  fetchInventory();
};

// 重置筛选条件
const resetFilters = () => {
  filters.name = '';
  filters.code = '';
  filters.status = '';
  handleSearch();
};

// 切换页码
const changePage = (page) => {
  pagination.currentPage = page;
  fetchInventory();
};

// 页面加载时获取数据
onMounted(() => {
  fetchInventory();
});
</script>

<style scoped>
/* Page transitions */
.page-header, .filter-card, .card {
  animation: fadeSlideIn 0.6s ease-out;
}

.page-header {
  animation-delay: 0s;
}

.filter-card {
  animation-delay: 0.1s;
}

.card {
  animation-delay: 0.2s;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

/* Badge animations */
.badge-success {
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(74, 222, 128, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0);
  }
}

/* 操作按钮样式 */
.badge {
  @apply inline-flex text-xs font-medium px-2 py-1 rounded-full;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.page-header {
  @apply flex justify-between items-center mb-6;
}

.page-title {
  @apply text-2xl font-bold text-gray-900;
}

.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.filter-card {
  @apply bg-white rounded-lg shadow-md p-6 mb-6;
}

.card {
  @apply bg-white rounded-lg shadow-md;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-input {
  @apply block w-full transition-all duration-200;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
}

.btn-primary {
  @apply border-transparent bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500;
}

.btn-danger {
  @apply border-transparent bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

@keyframes animate-fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: animate-fadeIn 0.3s ease-out;
}

@keyframes animate-slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slideInUp {
  animation: animate-slideInUp 0.4s ease-out;
}
</style>