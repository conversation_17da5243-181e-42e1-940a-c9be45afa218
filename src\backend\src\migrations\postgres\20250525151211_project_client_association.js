/**
 * Migration: project_client_association
 * Created at: 2025-05-25T15:12:11.783Z
 */
'use strict';

/** @type {import('sequelize').QueryInterface} */
module.exports = {
  // Apply the migration
  async up(queryInterface, Sequelize) {
    // Create project_client_associations table for many-to-many relationship
    await queryInterface.createTable('project_client_associations', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: Sequelize.DataTypes.UUIDV4,
        primaryKey: true
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'project',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      clientId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'client',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      role: {
        type: Sequelize.DataTypes.STRING,
        allowNull: true,
        comment: 'Role of the client in the project (e.g., main_client, partner, consultant)'
      },
      notes: {
        type: Sequelize.DataTypes.TEXT,
        allowNull: true
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint to prevent duplicate associations
    await queryInterface.addIndex('project_client_associations', {
      fields: ['projectId', 'clientId'],
      unique: true,
      name: 'unique_project_client_association'
    });

    // Add indexes for better query performance
    await queryInterface.addIndex('project_client_associations', ['projectId']);
    await queryInterface.addIndex('project_client_associations', ['clientId']);
  },

  // Revert the migration
  async down(queryInterface, Sequelize) {
    // Drop indexes first
    await queryInterface.removeIndex('project_client_associations', 'unique_project_client_association');
    await queryInterface.removeIndex('project_client_associations', ['projectId']);
    await queryInterface.removeIndex('project_client_associations', ['clientId']);
    
    // Drop the table
    await queryInterface.dropTable('project_client_associations');
  }
};
