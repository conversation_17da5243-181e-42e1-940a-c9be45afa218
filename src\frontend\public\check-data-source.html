<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Data Source Checker</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
      background-color: #f9f9f9;
    }
    .button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    .button.secondary {
      background-color: #2196F3;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>Data Source Checker</h1>
  
  <div class="card">
    <h2>Current Data Source</h2>
    <p>Click the button below to check the current data source:</p>
    <button id="checkDataSource" class="button">Check Data Source</button>
    <pre id="dataSourceResult">Results will appear here...</pre>
  </div>
  
  <div class="card">
    <h2>Switch Data Source</h2>
    <p>Click the buttons below to switch between API and Mock data:</p>
    <button id="switchToAPI" class="button">Switch to API</button>
    <button id="switchToMock" class="button secondary">Switch to Mock</button>
    <pre id="switchResult">Results will appear here...</pre>
  </div>
  
  <div class="card">
    <h2>Test API Connection</h2>
    <p>Click the button below to test the API connection:</p>
    <button id="testAPI" class="button">Test API Connection</button>
    <pre id="apiResult">Results will appear here...</pre>
  </div>
  
  <script>
    // Function to check the current data source
    document.getElementById('checkDataSource').addEventListener('click', function() {
      const resultElement = document.getElementById('dataSourceResult');
      resultElement.textContent = 'Checking...';
      
      try {
        // Try to access the parent window's localStorage
        const dataSource = window.opener.localStorage.getItem('app_data_source');
        resultElement.textContent = `Current data source from localStorage: ${dataSource || 'Not found (default is used)'}`;
      } catch (e) {
        resultElement.textContent = `Error accessing parent window: ${e.message}\n\nPlease make sure this page is opened from the main application.`;
      }
    });
    
    // Function to switch to API data source
    document.getElementById('switchToAPI').addEventListener('click', function() {
      const resultElement = document.getElementById('switchResult');
      resultElement.textContent = 'Switching...';
      
      try {
        window.opener.localStorage.setItem('app_data_source', 'api');
        resultElement.textContent = 'Switched to API data source. Please refresh the main application.';
      } catch (e) {
        resultElement.textContent = `Error: ${e.message}\n\nPlease make sure this page is opened from the main application.`;
      }
    });
    
    // Function to switch to Mock data source
    document.getElementById('switchToMock').addEventListener('click', function() {
      const resultElement = document.getElementById('switchResult');
      resultElement.textContent = 'Switching...';
      
      try {
        window.opener.localStorage.setItem('app_data_source', 'mock');
        resultElement.textContent = 'Switched to Mock data source. Please refresh the main application.';
      } catch (e) {
        resultElement.textContent = `Error: ${e.message}\n\nPlease make sure this page is opened from the main application.`;
      }
    });
    
    // Function to test API connection
    document.getElementById('testAPI').addEventListener('click', function() {
      const resultElement = document.getElementById('apiResult');
      resultElement.textContent = 'Testing API connection...';
      
      // Get the API URL from the parent window if possible
      let apiUrl;
      try {
        apiUrl = window.opener.axios?.defaults?.baseURL || '';
      } catch (e) {
        apiUrl = '';
      }
      
      // Make a simple request to the API
      fetch(`${apiUrl}/health-check`)
        .then(response => {
          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }
          return response.json();
        })
        .then(data => {
          resultElement.textContent = `API connection successful!\nResponse: ${JSON.stringify(data, null, 2)}`;
        })
        .catch(error => {
          resultElement.textContent = `API connection failed: ${error.message}\n\nThis could mean:\n1. The API server is not running\n2. The API endpoint doesn't exist\n3. There's a CORS issue`;
          
          // Try another endpoint
          resultElement.textContent += '\n\nTrying another endpoint...';
          
          fetch(`${apiUrl}/users`)
            .then(response => {
              if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
              }
              return response.json();
            })
            .then(data => {
              resultElement.textContent += `\n\nSecond attempt successful!\nResponse: ${JSON.stringify(data, null, 2)}`;
            })
            .catch(secondError => {
              resultElement.textContent += `\n\nSecond attempt failed: ${secondError.message}`;
            });
        });
    });
  </script>
</body>
</html>
