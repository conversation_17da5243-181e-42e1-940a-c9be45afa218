<template>
  <div class="bg-white rounded-lg shadow-sm overflow-hidden">
    <div class="p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-6">
        {{ isEditMode ? 'Edit Task' : 'Create New Task' }}
      </h2>

      <form @submit.prevent="handleSubmit" class="flex flex-col h-full">
        <!-- Task Name -->
        <div class="mb-4">
          <label for="name" class="form-label required">Task Name</label>
          <input
            id="name"
            v-model="taskForm.name"
            type="text"
            class="form-input"
            :class="{ 'border-red-500': errors.name }"
            placeholder="Enter task name"
          >
          <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
        </div>

        <!-- Task Type -->
        <div class="mb-4">
          <label for="taskType" class="form-label required">Task Type</label>
          <select
            id="taskType"
            v-model="taskForm.taskType"
            class="form-input"
            :class="{ 'border-red-500': errors.taskType }"
          >
            <option value="">Select task type</option>
            <option value="design">Design</option>
            <option value="development">Development</option>
            <option value="testing">Testing</option>
            <option value="deployment">Deployment</option>
          </select>
          <p v-if="errors.taskType" class="mt-1 text-sm text-red-600">{{ errors.taskType }}</p>
        </div>

        <!-- Task Status -->
        <div class="mb-4">
          <label for="status" class="form-label">Status</label>
          <select
            id="status"
            v-model="taskForm.status"
            class="form-input"
          >
            <option value="not_started">Not Started</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="on_hold">On Hold</option>
            <option value="cancelled">Cancelled</option>
          </select>
        </div>

        <!-- Priority -->
        <div class="mb-4">
          <label for="priority" class="form-label">Priority</label>
          <select
            id="priority"
            v-model="taskForm.priority"
            class="form-input"
          >
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>

        <!-- Assignee -->
        <div class="mb-4">
          <label for="assigneeId" class="form-label required">Assignee</label>
          <select
            id="assigneeId"
            v-model="taskForm.assigneeId"
            class="form-input"
            :class="{ 'border-red-500': errors.assigneeId }"
          >
            <option value="">Select assignee</option>
            <option v-for="user in users" :key="user.id" :value="user.id">
              {{ user.firstName }} {{ user.lastName }}
            </option>
          </select>
          <p v-if="errors.assigneeId" class="mt-1 text-sm text-red-600">{{ errors.assigneeId }}</p>
        </div>

        <!-- Planned Dates -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label for="plannedStartDate" class="form-label required">Planned Start Date</label>
            <input
              id="plannedStartDate"
              v-model="taskForm.plannedStartDate"
              type="date"
                max="3000-12-31"
                class="form-input"
              :class="{ 'border-red-500': errors.plannedStartDate }"
            >
            <p v-if="errors.plannedStartDate" class="mt-1 text-sm text-red-600">{{ errors.plannedStartDate }}</p>
          </div>
          <div>
            <label for="plannedEndDate" class="form-label required">Planned End Date</label>
            <input
              id="plannedEndDate"
              v-model="taskForm.plannedEndDate"
              type="date"
                max="3000-12-31"
                class="form-input"
              :class="{ 'border-red-500': errors.plannedEndDate }"
            >
            <p v-if="errors.plannedEndDate" class="mt-1 text-sm text-red-600">{{ errors.plannedEndDate }}</p>
          </div>
        </div>

        <!-- Actual Dates -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label for="actualStartDate" class="form-label">Actual Start Date</label>
            <input
              id="actualStartDate"
              v-model="taskForm.actualStartDate"
              type="date"
              max="3000-12-31"
              class="form-input"
            >
          </div>
          <div>
            <label for="actualEndDate" class="form-label">Actual End Date</label>
            <input
              id="actualEndDate"
              v-model="taskForm.actualEndDate"
              type="date"
                max="3000-12-31"
                class="form-input"
            >
          </div>
        </div>

        <!-- Remaining Work -->
        <div class="mb-4">
          <label for="remainingWork" class="form-label">Remaining Work (hours)</label>
          <input
            id="remainingWork"
            v-model.number="taskForm.remainingWork"
            type="number"
            min="0"
            step="0.5"
            class="form-input"
          >
        </div>

        <!-- Description -->
        <div class="mb-6">
          <label for="description" class="form-label">Description</label>
          <textarea
            id="description"
            v-model="taskForm.description"
            rows="4"
            class="form-input"
            placeholder="Enter task description"
          ></textarea>
        </div>

        <!-- Form Actions - Moved to bottom right -->
        <div class="flex justify-end space-x-3 mt-auto pt-6">
          <button
            type="button"
            class="btn btn-secondary"
            @click="$emit('cancel')"
          >
            Cancel
          </button>
          <button
            type="submit"
            class="btn btn-primary"
            :disabled="isSaving"
          >
            <span v-if="isSaving" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Saving...
            </span>
            <span v-else>{{ isEditMode ? 'Update Task' : 'Create Task' }}</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import axios from 'axios';

const props = defineProps({
  projectId: {
    type: String,
    required: true
  },
  taskId: {
    type: String,
    default: null
  }
});

const emit = defineEmits(['cancel', 'saved']);

const isEditMode = ref(!!props.taskId);
const isSaving = ref(false);
const users = ref([]);
const errors = reactive({});

const taskForm = reactive({
  name: '',
  taskType: '',
  status: 'not_started',
  priority: 'medium',
  assigneeId: '',
  plannedStartDate: '',
  plannedEndDate: '',
  actualStartDate: '',
  actualEndDate: '',
  remainingWork: 0,
  description: ''
});

// Fetch users for assignee selection
const fetchUsers = async () => {
  try {
    const response = await axios.get('/api/users');
    users.value = response.data;
  } catch (error) {
    console.error('Error fetching users:', error);
  }
};

// Fetch task details if in edit mode
const fetchTaskDetails = async () => {
  if (!props.taskId) return;
  
  try {
    const response = await axios.get(`/api/projects/tasks/${props.taskId}`);
    const task = response.data;
    
    Object.assign(taskForm, {
      name: task.name,
      taskType: task.taskType,
      status: task.status,
      priority: task.priority,
      assigneeId: task.assigneeId,
      plannedStartDate: task.plannedStartDate?.split('T')[0],
      plannedEndDate: task.plannedEndDate?.split('T')[0],
      actualStartDate: task.actualStartDate?.split('T')[0],
      actualEndDate: task.actualEndDate?.split('T')[0],
      remainingWork: task.remainingWork,
      description: task.description
    });
  } catch (error) {
    console.error('Error fetching task details:', error);
  }
};

// Validate form
const validateForm = () => {
  errors.name = !taskForm.name ? 'Task name is required' : '';
  errors.taskType = !taskForm.taskType ? 'Task type is required' : '';
  errors.assigneeId = !taskForm.assigneeId ? 'Assignee is required' : '';
  errors.plannedStartDate = !taskForm.plannedStartDate ? 'Planned start date is required' : '';
  errors.plannedEndDate = !taskForm.plannedEndDate ? 'Planned end date is required' : '';

  if (taskForm.plannedStartDate && taskForm.plannedEndDate) {
    if (new Date(taskForm.plannedStartDate) > new Date(taskForm.plannedEndDate)) {
      errors.plannedEndDate = 'End date must be after start date';
    }
  }

  return Object.values(errors).every(error => !error);
};

// Handle form submission
const handleSubmit = async () => {
  if (!validateForm()) return;

  isSaving.value = true;

  try {
    const taskData = { ...taskForm };
    let response;

    if (isEditMode.value) {
      response = await axios.patch(`/api/projects/tasks/${props.taskId}`, taskData);
    } else {
      response = await axios.post(`/api/projects/${props.projectId}/tasks`, taskData);
    }

    emit('saved', response.data);
  } catch (error) {
    console.error('Error saving task:', error);
  } finally {
    isSaving.value = false;
  }
};

onMounted(() => {
  fetchUsers();
  if (isEditMode.value) {
    fetchTaskDetails();
  }
});
</script>

<style scoped>
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-label.required:after {
  content: " *";
  @apply text-red-500;
}

.form-input {
  @apply block w-full rounded-md border-gray-300 shadow-sm
    focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50
    transition-all duration-200;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent
    rounded-md shadow-sm text-sm font-medium focus:outline-none
    focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700
    focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-white text-gray-700 border-gray-300
    hover:bg-gray-50 focus:ring-blue-500;
}
</style> 