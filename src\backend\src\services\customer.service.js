const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { Customer, User } = require('../models');

/**
 * Create a customer
 * @param {Object} customerBody
 * @returns {Promise<Customer>}
 */
const createCustomer = async (customerBody) => {
  return Customer.create(customerBody);
};

/**
 * Get customer by id
 * @param {string} id
 * @returns {Promise<Customer>}
 */
const getCustomerById = async (id) => {
  const customer = await Customer.findByPk(id, {
    include: [
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!customer) {
    throw new ApiError(404, 'Customer not found');
  }
  return customer;
};

/**
 * Update customer by id
 * @param {string} customerId
 * @param {Object} updateBody
 * @returns {Promise<Customer>}
 */
const updateCustomerById = async (customerId, updateBody) => {
  const customer = await getCustomerById(customerId);
  Object.assign(customer, updateBody);
  await customer.save();
  return customer;
};

/**
 * Delete customer by id
 * @param {string} customerId
 * @returns {Promise<void>}
 */
const deleteCustomerById = async (customerId) => {
  const customer = await getCustomerById(customerId);
  await customer.destroy();
};

/**
 * Query for customers
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Customers and pagination info
 */
const queryCustomers = async (filter, options) => {
  const { page, limit, sortBy, sortOrder } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};

  if (filter.category) {
    whereClause.category = filter.category;
  }

  if (filter.level) {
    whereClause.level = filter.level;
  }

  if (filter.followUpStage) {
    whereClause.followUpStage = filter.followUpStage;
  }

  if (filter.search) {
    whereClause[Op.or] = [
      { name: { [Op.like]: `%${filter.search}%` } },
      { contactName: { [Op.like]: `%${filter.search}%` } },
      { contactPhone: { [Op.like]: `%${filter.search}%` } },
      { contactEmail: { [Op.like]: `%${filter.search}%` } },
      { notes: { [Op.like]: `%${filter.search}%` } }
    ];
  }

  if (filter.tags) {
    // Handle tags filtering - this is a bit complex since tags are stored as JSON
    // This is a simplified approach that checks if any of the requested tags exist in the customer's tags
    const tagsArray = Array.isArray(filter.tags) ? filter.tags : [filter.tags];

    // Create a condition for each tag
    const tagConditions = tagsArray.map(tag => ({
      tags: { [Op.like]: `%${tag}%` }
    }));

    // Add the tag conditions to the where clause
    if (whereClause[Op.or]) {
      whereClause[Op.or] = [...whereClause[Op.or], ...tagConditions];
    } else {
      whereClause[Op.or] = tagConditions;
    }
  }

  // Query with pagination
  const { count, rows } = await Customer.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ],
    order: [[sortBy, sortOrder]],
    limit,
    offset
  });

  return {
    results: rows,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

module.exports = {
  createCustomer,
  getCustomerById,
  updateCustomerById,
  deleteCustomerById,
  queryCustomers
};