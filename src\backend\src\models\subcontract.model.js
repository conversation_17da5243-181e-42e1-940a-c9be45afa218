const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Subcontract = sequelize.define('subcontract', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'project',
      key: 'id'
    }
  },
  projectName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  contractName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  contractCode: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true
  },
  contractType: {
    type: DataTypes.STRING,
    allowNull: false
  },
  contractStatus: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'draft'
  },
  totalAmount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false
  },
  taxRate: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  invoiceDate: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  paymentDate: {
    type: DataTypes.DATEONLY,
    allowNull: true
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  createdBy: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'user',
      key: 'id'
    }
  },
  updatedBy: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'user',
      key: 'id'
    }
  }
}, {
  timestamps: true,
  freezeTableName: true
});

const setupAssociations = (models) => {
  // Define associations here
  if (models.Attachment) {
    Subcontract.hasMany(models.Attachment, {
      foreignKey: 'subcontractId',
      as: 'attachments'
    });
  }

  if (models.Project) {
    Subcontract.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project'
    });
  }

  if (models.User) {
    Subcontract.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator'
    });

    Subcontract.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater'
    });
  }
};

module.exports = { Subcontract, setupAssociations };

