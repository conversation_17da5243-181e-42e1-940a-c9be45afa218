<template>
  <aside v-if="authStore.isInitialized" class="border-r w-64 flex-shrink-0 flex-grow-0 min-h-screen overflow-y-auto" :style="{ backgroundColor: 'var(--bg-secondary)', borderColor: 'var(--secondary-border)' }">
    <div class="h-full flex flex-col">
      <!-- Sidebar Header -->
      <div class="px-4 py-5 border-b" :style="{ borderColor: 'var(--secondary-border)' ,height:'65px'}">
        <h2 class="text-lg font-medium" :style="{ color: 'var(--text-primary)' }">屹和消防</h2>
      </div>

      <!-- Sidebar Navigation -->
      <nav class="flex-1 overflow-y-auto p-4">
        <div class="space-y-1">
          <!-- Dashboard Link -->
          <router-link
            to="/dashboard"
            class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
            :style="{
              backgroundColor: $route.path === '/dashboard' ? 'var(--bg-primary)' : 'transparent',
              color: $route.path === '/dashboard' ? 'var(--primary-color)' : 'var(--text-secondary)'
            }"
          >
            <svg
              class="mr-3 h-6 w-6"
              :style="{ color: $route.path === '/dashboard' ? 'var(--primary-color)' : 'var(--text-secondary)' }"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
            仪表盘
          </router-link>

          <!-- 业务管理 Section -->
          <div class="pt-4">
            <p class="px-2 text-xs font-semibold uppercase tracking-wide" :style="{ color: 'var(--text-secondary)' }">
              业务管理
            </p>
            <div class="mt-2 space-y-1">
              <!-- 客户管理 -->
              <router-link
                to="/clients"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/clients') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/clients') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/clients') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0c.23.574.356 1.201.356 1.857v2a1 1 0 01-1 1h-2"></path>
                </svg>
                客户管理
              </router-link>

              <!-- 项目管理 -->
              <router-link
                to="/projects"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/projects') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/projects') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/projects') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
                项目管理
              </router-link>

              <!-- 合同管理 -->
              <router-link
                to="/contracts"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/contracts') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/contracts') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/contracts') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                合同管理
              </router-link>

              <!-- 分包管理 -->
              <router-link
                to="/subcontracts"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/subcontracts') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/subcontracts') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/subcontracts') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
                分包管理
              </router-link>
            </div>
          </div>

          <!-- 采购与库存 Section -->
          <div class="pt-4">
            <p class="px-2 text-xs font-semibold uppercase tracking-wide" :style="{ color: 'var(--text-secondary)' }">
              采购与库存
            </p>
            <div class="mt-2 space-y-1">
              <!-- 供应商管理 -->
              <router-link
                to="/suppliers"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/suppliers') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/suppliers') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/suppliers') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                供应商管理
              </router-link>

              <!-- 产品管理 -->
              <router-link
                to="/products"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/products') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/products') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/products') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                产品管理
              </router-link>

              <!-- 采购管理 -->
              <router-link
                to="/purchases"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/purchases') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/purchases') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/purchases') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"></path>
                </svg>
                采购管理
              </router-link>

              <!-- 库存管理 -->
              <router-link
                to="/inventory"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/inventory') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/inventory') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/inventory') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                库存管理
              </router-link>
            </div>
          </div>

          <!-- 财务管理 Section -->
          <div class="pt-4">
            <p class="px-2 text-xs font-semibold uppercase tracking-wide" :style="{ color: 'var(--text-secondary)' }">
              财务管理
            </p>
            <div class="mt-2 space-y-1">
              <!-- 财务管理 -->
              <router-link
                to="/finance"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/finance') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/finance') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/finance') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                财务管理
              </router-link>

              <!-- 报销管理 -->
              <router-link
                to="/reimbursements"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/reimbursements') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/reimbursements') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/reimbursements') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                报销管理
              </router-link>
            </div>
          </div>

          <!-- 人力资源 Section -->
          <div class="pt-4">
            <p class="px-2 text-xs font-semibold uppercase tracking-wide" :style="{ color: 'var(--text-secondary)' }">
              人力资源
            </p>
            <div class="mt-2 space-y-1">
              <!-- 合同工管理 -->
              <router-link
                to="/employees/contract"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path === '/employees/contract' ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path === '/employees/contract' ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg class="mr-3 h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                合同工管理
              </router-link>

              <!-- 临时工管理 -->
              <router-link
                to="/employees/temporary"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path === '/employees/temporary' ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path === '/employees/temporary' ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg class="mr-3 h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
                临时工管理
              </router-link>

              <!-- 考勤管理 -->
              <router-link
                to="/attendance"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/attendance') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/attendance') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/attendance') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                考勤管理
              </router-link>

              <!-- 工时管理 -->
              <router-link
                to="/workhours"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/workhours') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/workhours') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/workhours') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                工时管理
              </router-link>
            </div>
          </div>

          <!-- 组织管理 Section -->
          <div class="pt-4">
            <p class="px-2 text-xs font-semibold uppercase tracking-wide" :style="{ color: 'var(--text-secondary)' }">
              组织管理
            </p>
            <div class="mt-2 space-y-1">
              <router-link
                to="/departments"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/departments') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/departments') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/departments') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
                部门列表
              </router-link>
              
              <router-link v-if="0"
                to="/departments/structure"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path === '/departments/structure' ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path === '/departments/structure' ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path === '/departments/structure' ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"></path>
                </svg>
                组织架构
              </router-link>
            </div>
          </div>

          <!-- Admin Section -->
          <div v-if="authStore.isAdmin" class="pt-4">
            <p class="px-2 text-xs font-semibold uppercase tracking-wide" :style="{ color: 'var(--text-secondary)' }">
              系统管理
            </p>
            <div class="mt-2 space-y-1">
              <router-link
                to="/admin/users"
                class="group flex items-center px-2 py-2 text-base font-medium rounded-md"
                :style="{
                  backgroundColor: $route.path.startsWith('/admin/users') ? 'var(--bg-primary)' : 'transparent',
                  color: $route.path.startsWith('/admin/users') ? 'var(--primary-color)' : 'var(--text-secondary)'
                }"
              >
                <svg
                  class="mr-3 h-6 w-6"
                  :style="{ color: $route.path.startsWith('/admin/users') ? 'var(--primary-color)' : 'var(--text-secondary)' }"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                </svg>
                用户管理
              </router-link>
            </div>
          </div>
        </div>
      </nav>
    </div>
  </aside>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useAuthStore } from '../stores/auth';
import axios from 'axios';
import { useThemeStore } from '../stores/theme';
import { useRoute } from 'vue-router';

const authStore = useAuthStore();
const themeStore = useThemeStore();
const $route = useRoute();
const isAdmin = computed(() => authStore.user && authStore.user.role === 'admin');
const categories = ref([]);
const tags = ref([]);
const categoriesLoading = ref(true);
const tagsLoading = ref(true);
onMounted(async () => {
  fetchCategories();
  fetchTags();
});
const fetchCategories = async () => {
  try {
    categoriesLoading.value = true;
    const response = await axios.get('/api/categories', {
      params: {
        limit: 10,
        includeCounts: true
      }
    });
    categories.value = response.data.results;
  } catch (error) {
    console.error('Failed to fetch categories:', error);
  } finally {
    categoriesLoading.value = false;
  }
};
const fetchTags = async () => {
  try {
    tagsLoading.value = true;
    const response = await axios.get('/api/tags', {
      params: {
        limit: 15,
        includeCounts: true,
        sort: '-count' // Sort by popularity
      }
    });
    tags.value = response.data.results;
  } catch (error) {
    console.error('Failed to fetch tags:', error);
  } finally {
    tagsLoading.value = false;
  }
};
</script>