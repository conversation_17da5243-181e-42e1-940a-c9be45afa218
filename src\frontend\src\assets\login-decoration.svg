<svg xmlns="http://www.w3.org/2000/svg" width="400" height="400" viewBox="0 0 400 400">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#4f46e5" stop-opacity="0.2" />
      <stop offset="100%" stop-color="#818cf8" stop-opacity="0.1" />
    </linearGradient>
  </defs>
  <circle cx="200" cy="200" r="150" fill="none" stroke="url(#grad1)" stroke-width="2" />
  <circle cx="200" cy="200" r="120" fill="none" stroke="url(#grad1)" stroke-width="2" />
  <circle cx="200" cy="200" r="90" fill="none" stroke="url(#grad1)" stroke-width="2" />
  <circle cx="200" cy="200" r="60" fill="none" stroke="url(#grad1)" stroke-width="2" />
  <circle cx="200" cy="200" r="30" fill="none" stroke="url(#grad1)" stroke-width="2" />
</svg>
