<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-semibold text-gray-900">Categories</h1>
      <button
        class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        @click="createCategory"
      >
        Add Category
      </button>
    </div>

    <!-- Success message -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- Error message -->
    <div v-if="error" class="mb-4 p-4 bg-red-100 text-red-700 rounded-md flex items-center justify-between">
      <div>{{ error }}</div>
      <button @click="fetchCategories" class="text-red-700 hover:text-red-900 font-medium">
        Retry
      </button>
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="bg-white shadow overflow-hidden sm:rounded-md p-8 text-center">
      <svg class="animate-spin h-10 w-10 text-indigo-600 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p class="text-gray-600">Loading categories...</p>
    </div>

    <!-- Empty state -->
    <div v-else-if="categories.length === 0" class="bg-white shadow overflow-hidden sm:rounded-md p-8 text-center">
      <svg class="h-12 w-12 text-gray-400 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </svg>
      <p class="text-gray-600">No categories found</p>
      <button @click="createCategory" class="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
        Add your first category
      </button>
    </div>

    <!-- Categories list -->
    <div v-else class="bg-white shadow overflow-hidden sm:rounded-md">
      <ul class="divide-y divide-gray-200">
        <li v-for="(category, index) in categories" :key="index" class="px-4 py-4 sm:px-6 hover:bg-gray-50">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <div class="h-8 w-8 rounded-full flex items-center justify-center"
                :class="getCategoryColorClass(category.color)">
                <span class="text-white text-sm font-medium">
                  {{ category.name.substring(0, 1).toUpperCase() }}
                </span>
              </div>
              <div class="ml-4">
                <div class="text-sm font-medium text-gray-900">{{ category.name }}</div>
                <div class="text-sm text-gray-500">{{ category.count }} items</div>
              </div>
            </div>
            <div class="flex space-x-2">              <button
                class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                @click="editCategory(category)"
              >
                <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                </svg>
                <span class="font-medium">Edit</span>
              </button>
              <button
                class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                @click="deleteCategory(category)"
              >
                <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                </svg>
                <span class="font-medium">Delete</span>
              </button>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import axios from 'axios'

export default {
  name: 'CategoryList',
  setup() {
    const categories = ref([])
    const loading = ref(false)
    const error = ref(null)
    const successMessage = ref(null)

    // 显示成功消息并自动隐藏
    const showSuccessMessage = (message, duration = 5000) => {
      successMessage.value = message;
      setTimeout(() => {
        successMessage.value = null;
      }, duration);
    }

    const getCategoryColorClass = (color) => {
      const classes = {
        'blue': 'bg-blue-600',
        'gray': 'bg-gray-600',
        'purple': 'bg-purple-600',
        'green': 'bg-green-600',
        'orange': 'bg-orange-600',
        'red': 'bg-red-600',
        'yellow': 'bg-yellow-600'
      }

      return classes[color] || 'bg-gray-600'
    }

    // Fetch categories from API
    const fetchCategories = async () => {
      loading.value = true;
      error.value = null;

      try {
        const response = await axios.get('/api/categories');
        categories.value = response.data.results || [];
      } catch (err) {
        console.error('Failed to fetch categories:', err);
        error.value = err.response?.data?.message || '很抱歉，获取分类列表时遇到了问题。\n\n可能的原因：\n· 网络连接暂时不稳定\n· 服务器正在维护中\n· 系统临时故障\n\n建议您稍后再次尝试，如果问题持续存在，请联系系统管理员获取帮助。';
      } finally {
        loading.value = false;
      }
    };

    // Create a new category
    const createCategory = async () => {
      const name = prompt('请输入分类名称：');
      if (!name) return;

      const color = prompt('请选择分类颜色（可选：blue蓝色, gray灰色, purple紫色, green绿色, orange橙色, red红色, yellow黄色）：', 'blue');
      if (!color) return;

      try {
        await axios.post('/api/categories', { name, color });
        showSuccessMessage('恭喜！分类创建成功。\n\n您现在可以：\n· 在列表中查看新创建的分类\n· 使用此分类来组织您的项目\n· 随时编辑分类的名称和颜色\n\n感谢您使用分类功能，这将帮助您更好地管理项目。');
        fetchCategories(); // Refresh the list
      } catch (err) {
        console.error('Failed to create category:', err);
        error.value = err.response?.data?.message || '很抱歉，创建分类时遇到了问题。\n\n可能的原因：\n· 分类名称可能已存在\n· 输入的颜色值不在支持范围内\n· 系统暂时出现故障\n\n建议您：\n· 检查您的输入内容\n· 尝试使用不同的分类名称\n· 稍后再次尝试\n\n如果问题持续存在，请联系系统管理员获取帮助。';
      }
    };

    // Edit an existing category
    const editCategory = async (category) => {
      const name = prompt(`请输入新的分类名称（当前：${category.name}）：`, category.name);
      if (!name) return;

      const color = prompt(`请选择新的分类颜色（当前：${category.color}）\n可选：blue蓝色, gray灰色, purple紫色, green绿色, orange橙色, red红色, yellow黄色`, category.color);
      if (!color) return;

      try {
        await axios.patch(`/api/categories/${category.id}`, { name, color });
        showSuccessMessage('恭喜！分类信息已成功更新。\n\n· 您所做的修改已保存\n· 列表中的显示已更新\n· 所有使用此分类的项目将自动应用新的设置\n\n您可以继续使用此分类来组织您的项目。');
        fetchCategories(); // Refresh the list
      } catch (err) {
        console.error('Failed to update category:', err);
        error.value = err.response?.data?.message || '很抱歉，更新分类信息时遇到了问题。\n\n可能的原因：\n· 新的分类名称可能已被使用\n· 输入的颜色值不在支持范围内\n· 网络连接暂时不稳定\n· 系统暂时出现故障\n\n建议您：\n· 检查您的输入内容\n· 尝试使用不同的分类名称\n· 稍后再次尝试\n\n如果问题持续存在，请联系系统管理员获取帮助。';
      }
    };

    // Delete a category
    const deleteCategory = async (category) => {
      if (!confirm(`尊敬的用户，您确定要删除"${category.name}"分类吗？\n\n请注意以下影响：\n· 删除分类将影响所有使用该分类的项目\n· 相关项目可能需要重新分类\n· 此操作执行后将无法撤销\n\n如果您确定要删除此分类，请点击"确定"按钮继续。`)) {
        return;
      }

      try {
        await axios.delete(`/api/categories/${category.id}`);
        showSuccessMessage('分类已成功删除。\n\n重要提示：\n· 所有使用此分类的项目现在将显示为"未分类"\n· 建议您及时为这些项目重新分配适当的分类\n· 您可以随时创建新的分类来替代已删除的分类\n\n感谢您的操作，系统已完成分类删除。');
        fetchCategories(); // Refresh the list
      } catch (err) {
        console.error('Failed to delete category:', err);
        error.value = err.response?.data?.message || '很抱歉，删除分类时遇到了问题。\n\n可能的原因：\n· 该分类正在被多个项目使用中\n· 系统策略限制删除使用中的分类\n· 网络连接暂时不稳定\n· 系统暂时出现故障\n\n建议您：\n· 先将使用此分类的项目重新分类\n· 确认没有项目使用此分类后再尝试删除\n· 稍后再次尝试\n\n如果问题持续存在，请联系系统管理员获取帮助。';
      }
    };

    // Load categories when component mounts
    onMounted(() => {
      fetchCategories();
    });

    return {
      categories,
      loading,
      error,
      successMessage,
      getCategoryColorClass,
      fetchCategories,
      createCategory,
      editCategory,
      deleteCategory,
      showSuccessMessage
    }
  }
}
</script>