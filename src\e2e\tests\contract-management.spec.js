// @ts-check
import { test, expect } from '@playwright/test';

test.describe('合同管理流程', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('合同列表页面', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('合同列表');

    // 验证合同表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加合同按钮存在
    expect(await page.isVisible('a[href="/contracts/create"]')).toBeTruthy();
  });

  test('创建合同流程', async ({ page }) => {
    // 访问创建合同页面
    await page.goto('/contracts/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('创建合同');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 填写表单
    await page.fill('input[name="contractNumber"]', `CT-${Date.now()}`);
    await page.fill('input[name="name"]', 'E2E Test Contract');
    
    // 选择项目
    await page.click('select[name="projectId"]');
    await page.selectOption('select[name="projectId"]', { index: 1 });
    
    // 设置开始日期
    const startDate = new Date();
    await page.fill('input[name="startDate"]', startDate.toISOString().split('T')[0]);
    
    // 设置结束日期
    const endDate = new Date();
    endDate.setFullYear(endDate.getFullYear() + 1);
    await page.fill('input[name="endDate"]', endDate.toISOString().split('T')[0]);
    
    // 设置合同类型
    await page.selectOption('select[name="contractType"]', 'full-time');
    
    // 设置状态
    await page.selectOption('select[name="status"]', 'active');
    
    // 设置金额
    await page.fill('input[name="amount"]', '100000');
    
    // 添加备注
    await page.fill('textarea[name="notes"]', 'This is a test contract created by E2E test');

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到合同列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/contracts');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('查看合同详情', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 点击第一个合同的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/contracts/');

    // 验证详情页面内容
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('合同详情');
    expect(await page.isVisible('.contract-info')).toBeTruthy();
    expect(await page.isVisible('a.edit-button')).toBeTruthy();
  });

  test('编辑合同流程', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 点击第一个编辑按钮
    await page.click('table tbody tr:first-child a.edit-button');

    // 等待页面跳转到编辑页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/contracts/');
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/edit');

    // 验证表单已预填充
    expect(await page.inputValue('input[name="contractNumber"]')).toBeTruthy();

    // 修改合同名称
    const newName = `Updated Contract ${Date.now()}`;
    await page.fill('input[name="name"]', newName);

    // 修改状态
    await page.selectOption('select[name="status"]', 'completed');

    // 修改备注
    await page.fill('textarea[name="notes"]', 'Updated by E2E test');

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到合同列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/contracts');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('删除合同流程', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 获取合同数量
    const initialContractCount = await page.locator('table tbody tr').count();

    // 点击第一个删除按钮
    await page.click('table tbody tr:first-child button.delete-button');

    // 确认删除
    await page.click('button.confirm-delete');

    // 等待页面刷新
    await page.waitForTimeout(1000);

    // 验证合同数量减少
    const newContractCount = await page.locator('table tbody tr').count();
    expect(newContractCount).toBeLessThan(initialContractCount);

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('合同搜索功能', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 在搜索框中输入关键字
    await page.fill('input[placeholder*="搜索"]', 'CT-');

    // 点击搜索按钮
    await page.click('button.search-button');

    // 等待搜索结果加载
    await page.waitForTimeout(1000);

    // 验证搜索结果
    const contractNumbers = await page.locator('table tbody tr td:nth-child(2)').allTextContents();
    for (const number of contractNumbers) {
      expect(number).toContain('CT-');
    }
  });

  test('合同状态筛选功能', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 选择状态
    await page.selectOption('select.status-filter', 'active');

    // 等待筛选结果加载
    await page.waitForTimeout(1000);

    // 验证筛选结果
    const statuses = await page.locator('table tbody tr td:nth-child(5)').allTextContents();
    for (const status of statuses) {
      expect(status.toLowerCase()).toContain('active');
    }
  });

  test('合同日期范围筛选功能', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 设置开始日期
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 1);
    await page.fill('input[name="startDateFrom"]', startDate.toISOString().split('T')[0]);

    // 设置结束日期
    const endDate = new Date();
    endDate.setFullYear(endDate.getFullYear() + 1);
    await page.fill('input[name="startDateTo"]', endDate.toISOString().split('T')[0]);

    // 点击筛选按钮
    await page.click('button.filter-button');

    // 等待筛选结果加载
    await page.waitForTimeout(1000);

    // 验证筛选结果存在
    expect(await page.locator('table tbody tr').count()).toBeGreaterThan(0);
  });
});
