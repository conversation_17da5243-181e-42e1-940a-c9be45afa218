const bcrypt = require('bcryptjs');
const { User } = require('../models');
const logger = require('../config/logger');

/**
 * Create an admin user with specified credentials or defaults
 * @param {Object} options - User creation options
 * @param {string} options.username - Admin username (default: 'admin')
 * @param {string} options.email - Admin email (default: '<EMAIL>')
 * @param {string} options.password - Admin password (default: 'password123')
 * @returns {Promise<void>}
 */
const createAdminUser = async (options = {}) => {
  // Set default options
  const {
    username = 'admin',
    email = '<EMAIL>',
    password = 'password123',
    lastName = 'Admin'
  } = options;

  try {
    // Check if admin user already exists by email
    const existingEmail = await User.findOne({
      where: { email }
    });

    if (existingEmail) {
      logger.info(`User with email ${email} already exists`);
      return;
    }
    
    // Check if username already exists
    const existingUsername = await User.findOne({
      where: { username }
    });
    
    if (existingUsername) {
      logger.info(`User with username ${username} already exists`);
      return;
    }

    // Generate a unique ID number based on username and timestamp
    const timestamp = new Date().getTime();
    const idNumber = `${username.toUpperCase()}-ID-${timestamp}`;

    // Create admin user with all required fields
    const adminUser = await User.create({
      username,
      email,
      password,
      lastName,
      role: 'admin',
      gender: 'other',
      idNumber,
      phoneNumber: '************',
      position: 'System Administrator',
      address: 'System Admin Office',
      isAdmin: true,
      isActive: true
    });

    logger.info(`Admin user created with ID: ${adminUser.id}`);
    return adminUser;
  } catch (error) {
    logger.error('Error creating admin user:', error);
    if (error.name === 'SequelizeValidationError') {
      logger.error('Validation errors:');
      error.errors.forEach(err => {
        logger.error(`- Field '${err.path}': ${err.message}`);
      });
    } else if (error.name === 'SequelizeUniqueConstraintError') {
      logger.error('Unique constraint violation:');
      error.errors.forEach(err => {
        logger.error(`- Field '${err.path}': ${err.message}`);
      });
    }
    throw error;
  }
};

// Run the function if this script is executed directly
if (require.main === module) {
  // Parse command line arguments
  const args = process.argv.slice(2);
  const options = {};
  
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace(/^--/, '');
    const value = args[i + 1];
    if (key && value) {
      options[key] = value;
    }
  }

  // If no options provided, display help
  if (Object.keys(options).length === 0) {
    console.log(`
Usage: node createAdminUser.js [options]

Options:
  --username USERNAME    Admin username (default: 'admin')
  --email EMAIL          Admin email (default: '<EMAIL>')
  --password PASSWORD    Admin password (default: 'password123')
  --lastName LASTNAME    Admin last name (default: 'Admin')

Example:
  node createAdminUser.js --username admin8 --email <EMAIL>
`);
  }

  createAdminUser(options)
    .then(() => {
      logger.info('Script completed');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('Script failed:', error);
      process.exit(1);
    });
} else {
  // Export for use in other files
  module.exports = createAdminUser;
}
