const Joi = require('joi');

const createCategorySchema = {
  body: Joi.object().keys({
    name: Joi.string().required().min(2).max(100).messages({
      'string.empty': '分类名称不能为空',
      'string.min': '分类名称至少需要2个字符',
      'string.max': '分类名称不能超过100个字符',
      'any.required': '分类名称是必填项'
    }),
    description: Joi.string().allow('', null),
    parentId: Joi.string().uuid().allow(null).messages({
      'string.guid': '父级分类ID必须是有效的UUID格式'
    }),
    iconUrl: Joi.string().uri().allow('', null).messages({
      'string.uri': '图标URL必须是有效的URI格式'
    }),
    sortOrder: Joi.number().integer().min(0).default(0).messages({
      'number.base': '排序序号必须是数字',
      'number.integer': '排序序号必须是整数',
      'number.min': '排序序号不能小于0'
    }),
    isActive: Joi.boolean().default(true).messages({
      'boolean.base': '激活状态必须是布尔值'
    })
  })
};

const updateCategorySchema = {
  params: Joi.object().keys({
    categoryId: Joi.string().uuid().required().messages({
      'string.guid': '分类ID必须是有效的UUID格式',
      'string.empty': '分类ID不能为空',
      'any.required': '分类ID是必填项'
    })
  }),
  body: Joi.object().keys({
    name: Joi.string().min(2).max(100).messages({
      'string.min': '分类名称至少需要2个字符',
      'string.max': '分类名称不能超过100个字符'
    }),
    description: Joi.string().allow('', null),
    parentId: Joi.string().uuid().allow(null).messages({
      'string.guid': '父级分类ID必须是有效的UUID格式'
    }),
    iconUrl: Joi.string().uri().allow('', null).messages({
      'string.uri': '图标URL必须是有效的URI格式'
    }),
    sortOrder: Joi.number().integer().min(0).messages({
      'number.base': '排序序号必须是数字',
      'number.integer': '排序序号必须是整数',
      'number.min': '排序序号不能小于0'
    }),
    isActive: Joi.boolean().messages({
      'boolean.base': '激活状态必须是布尔值'
    })
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  }) // At least one field must be provided for update
};

const getCategorySchema = {
  params: Joi.object().keys({
    categoryId: Joi.string().uuid().required().messages({
      'string.guid': '分类ID必须是有效的UUID格式',
      'string.empty': '分类ID不能为空',
      'any.required': '分类ID是必填项'
    })
  })
};

const deleteCategorySchema = {
  params: Joi.object().keys({
    categoryId: Joi.string().uuid().required().messages({
      'string.guid': '分类ID必须是有效的UUID格式',
      'string.empty': '分类ID不能为空',
      'any.required': '分类ID是必填项'
    })
  })
};

const getCategoriesSchema = {
  query: Joi.object().keys({
    name: Joi.string().allow(null),
    parentId: Joi.string().uuid().allow(null, '').messages({
      'string.guid': '父级分类ID必须是有效的UUID格式'
    }),
    isActive: Joi.boolean().messages({
      'boolean.base': '激活状态必须是布尔值'
    }),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于1'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于1',
      'number.max': '每页数量不能超过100000'
    }),
    sortBy: Joi.string().valid('name', 'createdAt', 'level', 'sortOrder').default('sortOrder').messages({
      'any.only': '排序字段必须是有效的类型'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').default('asc').messages({
      'any.only': '排序方式必须是有效的类型'
    }),
    includeCounts: Joi.boolean().default(false).messages({
      'boolean.base': '包含计数必须是布尔值'
    })
  })
};

module.exports = {
  createCategorySchema,
  updateCategorySchema,
  getCategorySchema,
  deleteCategorySchema,
  getCategoriesSchema
};