const Joi = require('joi');
const { objectId } = require('./custom.validation');

const createProcurement = {
  body: Joi.object().keys({
    projectId: Joi.string().custom(objectId).required().messages({
      'string.empty': '项目ID不能为空',
      'any.required': '项目ID是必填项'
    }),
    content: Joi.string().required().messages({
      'string.empty': '采购内容不能为空',
      'any.required': '采购内容是必填项'
    }),
    supplierId: Joi.string().custom(objectId).required().messages({
      'string.empty': '供应商ID不能为空',
      'any.required': '供应商ID是必填项'
    }),
    quantity: Joi.number().integer().min(1).required().messages({
      'number.base': '数量必须是数字',
      'number.integer': '数量必须是整数',
      'number.min': '数量不能小于{#limit}',
      'any.required': '数量是必填项'
    }),
    amount: Joi.number().required().messages({
      'number.base': '金额必须是数字',
      'any.required': '金额是必填项'
    }),
    invoiceType: Joi.string().allow(null, ''),
    procurementDate: Joi.date().required().messages({
      'date.base': '采购日期必须是有效的日期格式',
      'any.required': '采购日期是必填项'
    }),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'completed').messages({
      'any.only': '状态必须是待处理(pending)、已批准(approved)、已拒绝(rejected)或已完成(completed)'
    }),
    returnInfo: Joi.string().allow(null, ''),
    returnBatch: Joi.string().allow(null, ''),
    returnInvoiceInfo: Joi.string().allow(null, ''),
    paymentSchedule: Joi.string().allow(null, ''),
    description: Joi.string().allow(null, '')
  })
};

const getProcurement = {
  params: Joi.object().keys({
    procurementId: Joi.string().custom(objectId).required().messages({
      'string.empty': '采购ID不能为空',
      'any.required': '采购ID是必填项'
    })
  })
};

const updateProcurement = {
  params: Joi.object().keys({
    procurementId: Joi.string().custom(objectId).required().messages({
      'string.empty': '采购ID不能为空',
      'any.required': '采购ID是必填项'
    })
  }),
  body: Joi.object().keys({
    content: Joi.string().messages({
      'string.empty': '采购内容不能为空'
    }),
    supplierId: Joi.string().custom(objectId).messages({
      'string.empty': '供应商ID不能为空'
    }),
    quantity: Joi.number().integer().min(1).messages({
      'number.base': '数量必须是数字',
      'number.integer': '数量必须是整数',
      'number.min': '数量不能小于{#limit}'
    }),
    amount: Joi.number().messages({
      'number.base': '金额必须是数字'
    }),
    invoiceType: Joi.string().allow(null, ''),
    procurementDate: Joi.date().messages({
      'date.base': '采购日期必须是有效的日期格式'
    }),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'completed').messages({
      'any.only': '状态必须是待处理(pending)、已批准(approved)、已拒绝(rejected)或已完成(completed)'
    }),
    returnInfo: Joi.string().allow(null, ''),
    returnBatch: Joi.string().allow(null, ''),
    returnInvoiceInfo: Joi.string().allow(null, ''),
    paymentSchedule: Joi.string().allow(null, ''),
    description: Joi.string().allow(null, '')
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deleteProcurement = {
  params: Joi.object().keys({
    procurementId: Joi.string().custom(objectId).required().messages({
      'string.empty': '采购ID不能为空',
      'any.required': '采购ID是必填项'
    })
  })
};

const getProcurements = {
  query: Joi.object().keys({
    projectId: Joi.string().custom(objectId).messages({
      'string.empty': '项目ID不能为空'
    }),
    supplierId: Joi.string().custom(objectId).messages({
      'string.empty': '供应商ID不能为空'
    }),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'completed').messages({
      'any.only': '状态必须是待处理(pending)、已批准(approved)、已拒绝(rejected)或已完成(completed)'
    }),
    search: Joi.string().allow(null),
    procurementDateFrom: Joi.date().messages({
      'date.base': '开始日期必须是有效的日期格式'
    }),
    procurementDateTo: Joi.date().messages({
      'date.base': '结束日期必须是有效的日期格式'
    }),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    }),
    sortBy: Joi.string().messages({
      'string.empty': '排序字段不能为空'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    })
  })
};

const getProjectProcurements = {
  params: Joi.object().keys({
    projectId: Joi.string().custom(objectId).required().messages({
      'string.empty': '项目ID不能为空',
      'any.required': '项目ID是必填项'
    })
  }),
  query: Joi.object().keys({
    supplierId: Joi.string().custom(objectId).messages({
      'string.empty': '供应商ID不能为空'
    }),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'completed').messages({
      'any.only': '状态必须是待处理(pending)、已批准(approved)、已拒绝(rejected)或已完成(completed)'
    }),
    search: Joi.string().allow(null),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    }),
    sortBy: Joi.string().messages({
      'string.empty': '排序字段不能为空'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    })
  })
};

module.exports = {
  createProcurement,
  getProcurement,
  updateProcurement,
  deleteProcurement,
  getProcurements,
  getProjectProcurements
};