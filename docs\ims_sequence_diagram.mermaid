sequenceDiagram
    participant Client as 浏览器客户端
    participant Authentication as 认证服务
    participant UserAPI as 用户管理API
    participant ProjectAPI as 项目管理API
    participant CustomerAPI as 客户管理API
    participant SupplierAPI as 供应商管理API
    participant PurchaseAPI as 采购管理API
    participant InventoryAPI as 库存管理API
    participant FinanceAPI as 财务管理API
    participant ContractAPI as 合同管理API
    participant AttendanceAPI as 考勤管理API
    participant DB as 数据库
    participant FileStorage as 文件存储
    
    %% 用户认证流程
    Client->>Authentication: 登录请求(username, password)
    Authentication->>DB: 查询用户信息
    DB-->>Authentication: 返回用户数据
    Authentication->>Authentication: 验证密码
    Authentication-->>Client: 返回JWT令牌
    
    %% 项目管理流程
    Client->>ProjectAPI: 获取项目列表(JWT token, 筛选条件)
    ProjectAPI->>Authentication: 验证Token和权限
    Authentication-->>ProjectAPI: 验证结果
    ProjectAPI->>DB: 查询项目数据
    DB-->>ProjectAPI: 返回项目列表
    ProjectAPI-->>Client: 返回处理后的项目数据
    
    %% 创建新项目
    Client->>ProjectAPI: 创建项目(项目信息)
    ProjectAPI->>Authentication: 验证Token和权限
    Authentication-->>ProjectAPI: 验证结果
    ProjectAPI->>DB: 保存项目数据
    DB-->>ProjectAPI: 返回创建结果
    ProjectAPI-->>Client: 返回项目创建结果
    
    %% 上传项目附件
    Client->>ProjectAPI: 上传项目附件(文件数据)
    ProjectAPI->>FileStorage: 保存文件
    FileStorage-->>ProjectAPI: 返回文件路径
    ProjectAPI->>DB: 保存附件记录
    DB-->>ProjectAPI: 返回保存结果
    ProjectAPI-->>Client: 返回上传结果
    
    %% 项目任务管理
    Client->>ProjectAPI: 创建任务(任务信息)
    ProjectAPI->>DB: 保存任务数据
    DB-->>ProjectAPI: 返回创建结果
    ProjectAPI-->>Client: 返回任务创建结果
    
    %% 项目进度更新
    Client->>ProjectAPI: 更新项目进度(进度信息)
    ProjectAPI->>DB: 更新进度数据
    DB-->>ProjectAPI: 返回更新结果
    ProjectAPI-->>Client: 返回进度更新结果
    
    %% 客户管理流程
    Client->>CustomerAPI: 获取客户列表(筛选条件)
    CustomerAPI->>Authentication: 验证Token和权限
    Authentication-->>CustomerAPI: 验证结果
    CustomerAPI->>DB: 查询客户数据
    DB-->>CustomerAPI: 返回客户列表
    CustomerAPI-->>Client: 返回处理后的客户数据
    
    %% 添加客户跟进记录
    Client->>CustomerAPI: 添加客户跟进(跟进信息)
    CustomerAPI->>DB: 保存跟进数据
    DB-->>CustomerAPI: 返回保存结果
    CustomerAPI-->>Client: 返回跟进添加结果
    
    %% 供应商管理流程
    Client->>SupplierAPI: 获取供应商列表(筛选条件)
    SupplierAPI->>Authentication: 验证Token和权限
    Authentication-->>SupplierAPI: 验证结果
    SupplierAPI->>DB: 查询供应商数据
    DB-->>SupplierAPI: 返回供应商列表
    SupplierAPI-->>Client: 返回处理后的供应商数据
    
    %% 采购管理流程
    Client->>PurchaseAPI: 创建采购申请(采购信息)
    PurchaseAPI->>Authentication: 验证Token和权限
    Authentication-->>PurchaseAPI: 验证结果
    PurchaseAPI->>DB: 保存采购申请
    DB-->>PurchaseAPI: 返回保存结果
    PurchaseAPI-->>Client: 返回采购创建结果
    
    %% 采购审批流程
    Client->>PurchaseAPI: 审批采购申请(审批结果)
    PurchaseAPI->>DB: 更新采购状态
    PurchaseAPI->>InventoryAPI: 采购审批通过通知
    DB-->>PurchaseAPI: 返回更新结果
    PurchaseAPI-->>Client: 返回审批结果
    
    %% 库存管理流程
    InventoryAPI->>DB: 创建库存入库记录
    DB-->>InventoryAPI: 返回入库结果
    
    %% 库存出库流程
    Client->>InventoryAPI: 申请库存出库(出库信息)
    InventoryAPI->>Authentication: 验证Token和权限
    Authentication-->>InventoryAPI: 验证结果
    InventoryAPI->>DB: 查询库存状态
    DB-->>InventoryAPI: 返回库存数据
    InventoryAPI->>InventoryAPI: 验证库存是否充足
    InventoryAPI->>DB: 创建出库记录并更新库存
    DB-->>InventoryAPI: 返回操作结果
    InventoryAPI-->>Client: 返回出库结果
    
    %% 合同管理流程
    Client->>ContractAPI: 创建合同(合同信息)
    ContractAPI->>Authentication: 验证Token和权限
    Authentication-->>ContractAPI: 验证结果
    ContractAPI->>DB: 保存合同数据
    DB-->>ContractAPI: 返回保存结果
    ContractAPI-->>Client: 返回合同创建结果
    
    %% 合同付款记录
    Client->>ContractAPI: 添加付款记录(付款信息)
    ContractAPI->>DB: 保存付款数据
    DB-->>ContractAPI: 返回保存结果
    ContractAPI->>FinanceAPI: 通知财务系统
    ContractAPI-->>Client: 返回付款记录添加结果
    
    %% 财务记录流程
    FinanceAPI->>DB: 创建财务记录
    DB-->>FinanceAPI: 返回创建结果
    
    %% 考勤记录流程
    Client->>AttendanceAPI: 提交考勤记录(考勤信息)
    AttendanceAPI->>Authentication: 验证Token和权限
    Authentication-->>AttendanceAPI: 验证结果
    AttendanceAPI->>DB: 保存考勤数据
    DB-->>AttendanceAPI: 返回保存结果
    AttendanceAPI-->>Client: 返回考勤提交结果
    
    %% 系统初始化流程
    Client->>Authentication: 系统初始化请求
    Authentication->>DB: 检查系统配置
    DB-->>Authentication: 返回配置状态
    Authentication->>DB: 创建默认管理员账号
    Authentication->>DB: 初始化基础配置
    Authentication->>DB: 初始化权限数据
    DB-->>Authentication: 返回初始化结果
    Authentication-->>Client: 返回初始化完成信息