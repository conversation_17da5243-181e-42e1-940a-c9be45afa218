const { v4: uuidv4 } = require('uuid');
const { Document } = require('../models/document.model');
const { User } = require('../models/user.model');
const { Category } = require('../models/category.model');
const { Tag } = require('../models/tag.model');
const { Project } = require('../models/project.model');

async function getRandomItem(model) {
  const count = await model.count();
  const offset = Math.floor(Math.random() * count);
  const items = await model.findAll({ limit: 1, offset });
  return items[0];
}

async function getRandomItems(model, count) {
  const totalCount = await model.count();
  const limit = Math.min(count, totalCount);
  const offset = Math.floor(Math.random() * (totalCount - limit + 1));
  return await model.findAll({ limit, offset });
}

async function seedDocuments() {
  try {
    // Check if documents already exist
    const documentCount = await Document.count();
    if (documentCount > 0) {
      console.log('Documents already exist, skipping document seeding');
      return;
    }

    // Get admin user for author
    const adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      console.error('No admin user found for document seeding');
      return;
    }

    // Sample document titles and content
    const documentTemplates = [
      {
        title: '项目计划书',
        content: '# 项目计划书\n\n## 项目概述\n\n本项目旨在开发一个综合性的企业管理系统，包括人力资源、财务、项目和文档管理等模块。\n\n## 项目目标\n\n1. 提高企业管理效率\n2. 降低运营成本\n3. 提升数据安全性\n\n## 项目时间线\n\n- 需求分析：2周\n- 设计阶段：3周\n- 开发阶段：8周\n- 测试阶段：3周\n- 部署上线：1周',
        documentType: 'article'
      },
      {
        title: '技术方案设计',
        content: '# 技术方案设计\n\n## 架构设计\n\n本系统采用前后端分离架构，前端使用Vue.js，后端使用Node.js和Express框架，数据库使用PostgreSQL。\n\n## 技术栈\n\n- 前端：Vue.js, Vuex, Vue Router, Element UI\n- 后端：Node.js, Express, Sequelize ORM\n- 数据库：PostgreSQL\n- 部署：Docker, Kubernetes\n\n## 安全考虑\n\n- 使用JWT进行身份验证\n- 数据加密传输\n- 定期备份数据',
        documentType: 'procedure'
      },
      {
        title: '用户手册',
        content: '# 用户手册\n\n## 系统登录\n\n1. 打开浏览器，访问系统地址\n2. 输入用户名和密码\n3. 点击登录按钮\n\n## 基本操作\n\n### 文档管理\n\n- 创建文档：点击"新建文档"按钮\n- 编辑文档：点击文档列表中的"编辑"按钮\n- 删除文档：点击文档列表中的"删除"按钮\n\n### 项目管理\n\n- 创建项目：点击"新建项目"按钮\n- 查看项目详情：点击项目名称\n- 编辑项目：点击项目列表中的"编辑"按钮',
        documentType: 'guide'
      },
      {
        title: '会议纪要',
        content: '# 会议纪要\n\n## 会议信息\n\n- 日期：2024年7月1日\n- 时间：14:00-16:00\n- 地点：公司会议室\n- 参会人员：张三、李四、王五、赵六\n\n## 会议议题\n\n1. 项目进度回顾\n2. 问题讨论\n3. 下一阶段计划\n\n## 会议决议\n\n1. 加快开发进度，确保本月底完成核心功能\n2. 解决用户反馈的界面问题\n3. 下周开始进行内部测试',
        documentType: 'article'
      },
      {
        title: '系统操作规范',
        content: '# 系统操作规范\n\n## 账号管理\n\n1. 严禁共享账号\n2. 密码至少包含大小写字母、数字和特殊字符\n3. 每90天必须修改一次密码\n\n## 数据管理\n\n1. 重要数据必须定期备份\n2. 敏感信息必须加密存储\n3. 禁止将公司数据存储在个人设备上\n\n## 系统使用\n\n1. 非工作时间禁止登录系统\n2. 禁止安装未经授权的软件\n3. 发现系统异常立即报告IT部门',
        documentType: 'policy'
      }
    ];

    // Create 20 documents
    const documents = [];
    for (let i = 0; i < 20; i++) {
      // Get random template
      const templateIndex = Math.floor(Math.random() * documentTemplates.length);
      const template = documentTemplates[templateIndex];

      // Get random category, project, and tags
      const category = await getRandomItem(Category);
      const project = await getRandomItem(Project);
      const tags = await getRandomItems(Tag, Math.floor(Math.random() * 3) + 1);

      // Create document
      const document = {
        id: uuidv4(),
        title: `${template.title} - ${i + 1}`,
        content: template.content,
        summary: template.content.substring(0, 200) + '...',
        categoryId: category.id,
        authorId: adminUser.id,
        status: ['draft', 'published', 'archived'][Math.floor(Math.random() * 3)],
        viewCount: Math.floor(Math.random() * 100),
        isPublic: Math.random() > 0.5,
        publishDate: Math.random() > 0.3 ? new Date() : null,
        lastModifierId: adminUser.id,
        documentType: template.documentType,
        priority: ['low', 'medium', 'high', 'urgent'][Math.floor(Math.random() * 4)],
        version: '1.0',
        metaData: {
          createdAt: new Date().toISOString(),
          keywords: ['文档', '管理', '系统']
        }
      };

      // Add document to array
      documents.push(document);
    }

    // Bulk create documents
    const createdDocuments = await Document.bulkCreate(documents);
    console.log('Successfully seeded documents');

    // Associate documents with tags
    try {
      for (const document of createdDocuments) {
        const tags = await getRandomItems(Tag, Math.floor(Math.random() * 3) + 1);

        // Check if setTags method exists (requires Document-Tag association to be set up)
        if (typeof document.setTags === 'function') {
          await document.setTags(tags);
        } else {
          // Fallback: manually create entries in the junction table
          const { DocumentTag } = require('../models');
          for (const tag of tags) {
            await DocumentTag.create({
              documentId: document.id,
              tagId: tag.id
            });
          }
        }
      }
      console.log('Successfully associated documents with tags');
    } catch (error) {
      console.error('Error associating documents with tags:', error);
      // Continue execution even if tag association fails
    }

  } catch (error) {
    console.error('Error seeding documents:', error);
    throw error;
  }
}

module.exports = seedDocuments;
