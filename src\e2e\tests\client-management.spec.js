// @ts-check
import { test, expect } from '@playwright/test';

test.describe('客户管理流程', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('客户列表页面', async ({ page }) => {
    // 访问客户列表页面
    await page.goto('/clients');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('客户列表');
    
   
    const abc = await page.isVisible('a[href="/clients/create"]')
    debugger
    // 验证添加客户按钮存在
    expect(await page.isVisible('a[href="/clients/create"]')).toBeTruthy();
  });

  test('创建客户流程', async ({ page }) => {
    // 访问创建客户页面
    await page.goto('/clients/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('创建客户');

    // 验证表单存在

    // 填写表单
    const clientName = `测试客户 ${Date.now()}`;
    await page.fill('input[name="name"]', clientName);
    
    // 填写客户代码
    await page.fill('input[name="code"]', `CL-${Date.now()}`);
    
    // 填写联系人
    await page.fill('input[name="contactName"]', '测试联系人');
    
    // 填写电话
    await page.fill('input[name="phone"]', '13900139000');
    
    // 填写邮箱
    await page.fill('input[name="email"]', `test${Date.now()}@example.com`);
    
    // 填写地址
    if (await page.isVisible('input[name="address"]')) {
      await page.fill('input[name="address"]', '测试地址');
    }
    
    // 选择客户类型
    if (await page.isVisible('select[name="type"]')) {
      await page.selectOption('select[name="type"]', { index: 1 });
    }
    
    // 添加备注
    if (await page.isVisible('textarea[name="notes"]')) {
      await page.fill('textarea[name="notes"]', '这是一个由E2E测试创建的测试客户');
    }

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到客户列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/clients');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
    
    // 验证新客户已添加到列表
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain(clientName);
  });

  test('查看客户详情', async ({ page }) => {
    // 访问客户列表页面
    await page.goto('/clients');

    // 点击第一个客户的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/clients/');

    // 验证详情页面内容
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('客户详情');
    expect(await page.isVisible('.client-info')).toBeTruthy();
    expect(await page.isVisible('a.edit-button')).toBeTruthy();
  });

  test('编辑客户流程', async ({ page }) => {
    // 访问客户列表页面
    await page.goto('/clients');

    // 点击第一个编辑按钮
    await page.click('table tbody tr:first-child a.edit-button');

    // 等待页面跳转到编辑页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/clients/');
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/edit');

    // 验证表单已预填充
    expect(await page.inputValue('input[name="name"]')).toBeTruthy();

    // 修改客户名称
    const updatedName = `更新的客户 ${Date.now()}`;
    await page.fill('input[name="name"]', updatedName);

    // 修改联系人
    await page.fill('input[name="contactName"]', '更新的联系人');

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到客户列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/clients');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
    
    // 验证客户已更新
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain(updatedName);
  });

  test('客户搜索功能', async ({ page }) => {
    // 访问客户列表页面
    await page.goto('/clients');

    // 在搜索框中输入关键字
    await page.fill('input[placeholder*="搜索"]', 'CL-');

    // 点击搜索按钮
    await page.click('button.search-button');

    // 等待搜索结果加载
    await page.waitForTimeout(1000);

    // 验证搜索结果
    const clientCodes = await page.locator('table tbody tr td:nth-child(2)').allTextContents();
    for (const code of clientCodes) {
      expect(code).toContain('CL-');
    }
  });

  test('客户类型筛选功能', async ({ page }) => {
    // 访问客户列表页面
    await page.goto('/clients');

    // 如果存在类型筛选器
    if (await page.isVisible('select.type-filter')) {
      // 选择类型
      await page.selectOption('select.type-filter', { index: 1 });

      // 等待筛选结果加载
      await page.waitForTimeout(1000);

      // 验证筛选结果存在
      expect(await page.locator('table tbody tr').count()).toBeGreaterThan(0);
    }
  });

  test('删除客户流程', async ({ page }) => {
    // 访问客户列表页面
    await page.goto('/clients');

    // 获取客户数量
    const initialClientCount = await page.locator('table tbody tr').count();

    // 点击第一个删除按钮
    await page.click('table tbody tr:first-child button.delete-button');

    // 确认删除
    await page.click('button.confirm-delete');

    // 等待页面刷新
    await page.waitForTimeout(1000);

    // 验证客户数量减少
    const newClientCount = await page.locator('table tbody tr').count();
    expect(newClientCount).toBeLessThan(initialClientCount);

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('查看客户关联项目', async ({ page }) => {
    // 访问客户列表页面
    await page.goto('/clients');

    // 点击第一个客户的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();

    // 点击关联项目选项卡(如果存在)
    if (await page.isVisible('button:has-text("关联项目")')) {
      await page.click('button:has-text("关联项目")');
      
      // 验证项目列表存在
      expect(await page.isVisible('.projects-list')).toBeTruthy();
    }
  });
});
