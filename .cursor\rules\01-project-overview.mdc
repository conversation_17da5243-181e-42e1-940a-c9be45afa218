---
description: 
globs: 
alwaysApply: true
---
# Project Structure Overview

This project appears to be a web application with the following structure:

## Key Directories
- `src/` - Main source code directory
- `docs/` - Project documentation
- `deploy/` - Deployment configuration and scripts
- `docker/` - Docker-related configuration files
- `uploads/` - Directory for uploaded files
- `.vscode/` - VS Code editor configuration

## Important Files
- [todolist.md](mdc:todolist.md) - Project todo list and task tracking
- [check-data-source.js](mdc:check-data-source.js) - Data source verification script
- [start.bat](mdc:start.bat) and [start-pnpm.bat](mdc:start-pnpm.bat) - Project startup scripts

## Development Scripts
The project includes several batch scripts for development:
- [start.bat](mdc:start.bat) - Main startup script
- [start-pnpm.bat](mdc:start-pnpm.bat) - PNPM-based startup script
