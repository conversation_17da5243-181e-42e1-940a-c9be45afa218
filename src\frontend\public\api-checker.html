<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Checker</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
      background-color: #f9f9f9;
    }
    .button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    .button.secondary {
      background-color: #2196F3;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
    .error {
      color: #d32f2f;
    }
    .success {
      color: #388e3c;
    }
    .endpoint-list {
      list-style-type: none;
      padding: 0;
    }
    .endpoint-item {
      margin-bottom: 10px;
    }
    .endpoint-button {
      background-color: #2196F3;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 3px;
      cursor: pointer;
    }
    .endpoint-button:hover {
      background-color: #0b7dda;
    }
  </style>
</head>
<body>
  <h1>API Checker</h1>
  
  <div class="card">
    <h2>API Configuration</h2>
    <pre id="apiConfig">Checking...</pre>
  </div>
  
  <div class="card">
    <h2>Test API Endpoints</h2>
    <p>Click on an endpoint to test it:</p>
    <ul class="endpoint-list">
      <li class="endpoint-item">
        <button class="endpoint-button" data-endpoint="/api/users">GET /api/users</button>
      </li>
      <li class="endpoint-item">
        <button class="endpoint-button" data-endpoint="/api/projects">GET /api/projects</button>
      </li>
      <li class="endpoint-item">
        <button class="endpoint-button" data-endpoint="/api/products">GET /api/products</button>
      </li>
      <li class="endpoint-item">
        <button class="endpoint-button" data-endpoint="/api/orders">GET /api/orders</button>
      </li>
      <li class="endpoint-item">
        <button class="endpoint-button" data-endpoint="/api/health">GET /api/health</button>
      </li>
    </ul>
    <div>
      <label for="custom-endpoint">Custom Endpoint:</label>
      <input type="text" id="custom-endpoint" placeholder="/api/..." style="padding: 5px; width: 200px;">
      <button id="test-custom-endpoint" class="button secondary">Test</button>
    </div>
    <pre id="apiResult">Results will appear here...</pre>
  </div>
  
  <script>
    // Function to check API configuration
    function checkApiConfig() {
      const apiConfigElement = document.getElementById('apiConfig');
      
      // Get data source from localStorage
      const dataSource = localStorage.getItem('app_data_source') || 'default (API)';
      
      // Get API URL
      let apiBaseUrl = '';
      try {
        apiBaseUrl = window.location.origin;
      } catch (e) {
        apiBaseUrl = 'Could not determine API URL';
      }
      
      apiConfigElement.textContent = `Data Source: ${dataSource}\nAPI Base URL: ${apiBaseUrl}`;
    }
    
    // Function to test an API endpoint
    async function testEndpoint(endpoint) {
      const resultElement = document.getElementById('apiResult');
      resultElement.textContent = `Testing ${endpoint}...`;
      resultElement.className = '';
      
      try {
        const startTime = Date.now();
        const response = await fetch(endpoint);
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = await response.text();
        }
        
        let result = `Status: ${response.status} ${response.statusText}\n`;
        result += `Response Time: ${responseTime}ms\n\n`;
        
        if (typeof responseData === 'object') {
          result += JSON.stringify(responseData, null, 2);
        } else {
          result += responseData;
        }
        
        resultElement.textContent = result;
        resultElement.className = response.ok ? 'success' : 'error';
      } catch (error) {
        resultElement.textContent = `Error: ${error.message}`;
        resultElement.className = 'error';
      }
    }
    
    // Add event listeners to endpoint buttons
    document.querySelectorAll('.endpoint-button').forEach(button => {
      button.addEventListener('click', function() {
        const endpoint = this.getAttribute('data-endpoint');
        testEndpoint(endpoint);
      });
    });
    
    // Add event listener to custom endpoint button
    document.getElementById('test-custom-endpoint').addEventListener('click', function() {
      const endpoint = document.getElementById('custom-endpoint').value;
      if (endpoint) {
        testEndpoint(endpoint);
      } else {
        document.getElementById('apiResult').textContent = 'Please enter an endpoint';
        document.getElementById('apiResult').className = 'error';
      }
    });
    
    // Check API configuration on page load
    window.addEventListener('DOMContentLoaded', checkApiConfig);
  </script>
</body>
</html>
