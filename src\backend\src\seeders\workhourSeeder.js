const { v4: uuidv4 } = require('uuid');
const { WorkHour } = require('../models/workhour.model');
const { User } = require('../models/user.model');
const { Project } = require('../models/project.model');
const { Employee } = require('../models/employee.model');
const { faker } = require('@faker-js/faker/locale/zh_CN');

const employmentTypes = ['合同工', '临时工', '兼职'];
const performanceLevels = ['优秀', '良好', '一般', '需改进'];
const departments = ['工程部', '设计部', '财务部', '行政部', '销售部'];

async function seedWorkhours() {
  try {
    // Check if workhours already exist
    const workhourCount = await WorkHour.count();
    if (workhourCount > 0) {
      console.log('Workhours already exist, skipping workhour seeding');
      return;
    }

    // Use a default admin ID for createdBy field
    // Since we're using integer IDs for createdBy but User model uses UUID
    const adminId = 1; // Using a default admin ID

    // Get projects for reference
    const projects = await Project.findAll({ limit: 10 });
    if (projects.length === 0) {
      console.log('No projects found for workhour seeding, using generic project names');
    }

    // Get employees or create mock employees
    let employees = await Employee.findAll({ limit: 20 });
    if (employees.length === 0) {
      console.log('No employees found for workhour seeding, using mock employee data');
      // Create mock employee data
      employees = Array.from({ length: 20 }, (_, i) => ({
        id: `EMP${String(i + 1).padStart(4, '0')}`,
        name: faker.person.fullName(),
        department: faker.helpers.arrayElement(departments)
      }));
    }

    // Create 100 workhour records
    const workhours = [];

    // Generate records for the past 30 days
    const today = new Date();
    for (let day = 0; day < 30; day++) {
      const date = new Date(today);
      date.setDate(date.getDate() - day);

      // Skip weekends (Saturday = 6, Sunday = 0)
      if (date.getDay() === 0 || date.getDay() === 6) {
        continue;
      }

      // Create 3-5 records per day
      const recordsPerDay = faker.number.int({ min: 3, max: 5 });

      for (let i = 0; i < recordsPerDay; i++) {
        // Select random employee and project
        const employee = faker.helpers.arrayElement(employees);
        const project = projects.length > 0
          ? faker.helpers.arrayElement(projects)
          : { name: `${faker.location.city()}${faker.helpers.arrayElement(['商业中心', '住宅小区', '办公楼', '工业园区', '学校'])}项目` };

        // Generate random work hours (7-10 hours)
        const startHour = faker.number.int({ min: 8, max: 9 });
        const startMinute = faker.number.int({ min: 0, max: 59 });
        const startTime = `${String(startHour).padStart(2, '0')}:${String(startMinute).padStart(2, '0')}:00`;

        const workHours = faker.number.float({ min: 7, max: 10, precision: 1 });

        // Calculate end time properly to avoid invalid time values
        let endHour = startHour + Math.floor(workHours);
        let endMinute = startMinute + Math.round((workHours % 1) * 60);

        // Adjust if minutes exceed 59
        if (endMinute >= 60) {
          endHour += 1;
          endMinute -= 60;
        }

        // Ensure hours don't exceed 23
        if (endHour > 23) {
          endHour = 23;
          endMinute = 59;
        }

        const endTime = `${String(endHour).padStart(2, '0')}:${String(endMinute).padStart(2, '0')}:00`;

        // Determine if overtime
        const isOvertime = workHours > 8;

        // Calculate hourly rate and payable amount
        const hourlyRate = faker.number.float({ min: 30, max: 100, precision: 2 });
        const payableAmount = hourlyRate * workHours;

        // Calculate daily wage
        const dailyWage = faker.number.float({ min: 200, max: 800, precision: 2 });

        // Add occasional absence records
        const isAbsent = faker.datatype.boolean(0.05); // 5% chance of absence

        const workhour = {
          employeeId: employee.id,
          employeeName: employee.name,
          department: employee.department || faker.helpers.arrayElement(departments),
          date: date.toISOString().split('T')[0],
          startTime: isAbsent ? '09:00:00' : startTime,
          endTime: isAbsent ? '18:00:00' : endTime,
          workHours: isAbsent ? 0 : workHours,
          projectName: project.name,
          remarks: isAbsent ? '请假' : `${project.name}项目日常工作`,
          employmentType: faker.helpers.arrayElement(employmentTypes),
          hourlyRate,
          payableAmount: isAbsent ? 0 : payableAmount,
          overtime: isAbsent ? false : isOvertime,
          absence: isAbsent,
          employeeSign: faker.datatype.boolean(0.9), // 90% chance of being signed
          supervisorSign: faker.datatype.boolean(0.8), // 80% chance of being signed
          performanceLevel: isAbsent ? null : faker.helpers.arrayElement(performanceLevels),
          dailyWage,
          createdBy: adminId,
          updatedBy: adminId,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        workhours.push(workhour);
      }
    }

    // Bulk create workhours
    await WorkHour.bulkCreate(workhours);
    console.log('Successfully seeded workhours');
  } catch (error) {
    console.error('Error seeding workhours:', error);
    throw error;
  }
}

module.exports = seedWorkhours;
