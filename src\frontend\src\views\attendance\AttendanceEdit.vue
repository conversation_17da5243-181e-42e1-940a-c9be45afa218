<template>
  <div class="max-w-4xl mx-auto px-4 py-6">
    <!-- 顶部导航卡片 -->
    <div class="bg-white rounded-lg p-6 flex justify-between items-center mb-6 shadow-md">
      <div>
        <h1 class="page-title">考勤记录</h1>
        <p class="text-gray-600 mt-1">{{ isEditMode ? '编辑工时记录' : '创建新的工时记录' }}</p>
      </div>
      <router-link
        to="/attendance"
        class="btn btn-secondary flex items-center"
      >
        <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        返回列表
      </router-link>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- 表单卡片 -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="p-6">
        <form @submit.prevent="submitForm" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 项目选择 -->
            <div class="form-group">
              <label for="project" class="form-label required">项目</label>
              <select
                id="project"
                v-model="form.projectId"
                class="form-input"
                :class="{ 'border-red-500': errors.projectId }"
                required
              >
                <option value="" disabled>请选择项目</option>
                <option v-for="project in projects" :key="project.id" :value="project.id">
                  {{ project.name }}
                </option>
              </select>
              <p v-if="errors.projectId" class="text-red-500 text-sm mt-1">{{ errors.projectId }}</p>
            </div>

            <!-- 员工姓名 -->
            <div class="form-group">
              <label for="employeeName" class="form-label required">员工姓名</label>
              <div class="relative">
                <input
                  type="text"
                  id="employeeNameFilter"
                  v-model="employeeFilter"
                  class="form-input mb-1"
                  placeholder="输入员工姓名筛选..."
                  @focus="showEmployeeDropdown = true"
                  @input="filterEmployees"
                />
                <div v-if="showEmployeeDropdown" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                  <div v-if="filteredEmployees.length === 0" class="px-4 py-2 text-gray-500">
                    未找到匹配的员工
                  </div>
                  <div
                    v-for="employee in filteredEmployees"
                    :key="employee.id"
                    class="px-4 py-2 hover:bg-blue-50 cursor-pointer"
                    @click="selectEmployee(employee)"
                  >
                    {{ employee.name }} ({{ employee.department || '无部门' }})
                  </div>
                </div>
              </div>
              <input
                type="text"
                id="employeeName"
                v-model="form.employeeName"
                class="form-input"
                :class="{ 'border-red-500': errors.employeeName }"
                readonly
                placeholder="已选择的员工"
              />
              <p v-if="errors.employeeName" class="text-red-500 text-sm mt-1">{{ errors.employeeName }}</p>
            </div>

            <!-- 员工编号，隐藏不显示，但保留数据绑定 -->
            <input type="hidden" v-model="form.employeeId">

            <!-- 员工部门和用工类型显示 -->
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">部门</label>
                <div class="form-text-display" id="department-display">—</div>
              </div>
              <div class="form-group">
                <label class="form-label">用工类型</label>
                <div class="form-text-display" id="employment-type-display">—</div>
              </div>
            </div>

            <!-- 日期 -->
            <div class="form-group">
              <label for="date" class="form-label required">日期</label>
              <input
                type="date"
                id="date"
                v-model="form.date"
                class="form-input"
                :class="{ 'border-red-500': errors.date }"
                required
                max="3000-12-31"
              />
              <p v-if="errors.date" class="text-red-500 text-sm mt-1">{{ errors.date }}</p>
            </div>

            <!-- 上班时间 -->
            <div class="form-group">
              <label for="timeIn" class="form-label required">上班时间</label>
              <input
                type="time"
                id="timeIn"
                v-model="form.timeIn"
                class="form-input"
                :class="{ 'border-red-500': errors.timeIn }"
                required
              />
              <p v-if="errors.timeIn" class="text-red-500 text-sm mt-1">{{ errors.timeIn }}</p>
            </div>

            <!-- 下班时间 -->
            <div class="form-group">
              <label for="timeOut" class="form-label">下班时间</label>
              <input
                type="time"
                id="timeOut"
                v-model="form.timeOut"
                class="form-input"
                :class="{ 'border-red-500': errors.timeOut }"
              />
              <p v-if="errors.timeOut" class="text-red-500 text-sm mt-1">{{ errors.timeOut }}</p>
            </div>

            <!-- 工作时长 -->
            <div class="form-group">
              <label for="hoursWorked" class="form-label">工作时长（小时）</label>
              <input
                type="number"
                id="hoursWorked"
                v-model="form.hoursWorked"
                min="0"
                step="0.5"
                class="form-input"
                :class="{ 'border-red-500': errors.hoursWorked }"
                placeholder="自动计算或手动输入"
              />
              <p v-if="errors.hoursWorked" class="text-red-500 text-sm mt-1">{{ errors.hoursWorked }}</p>
            </div>

            <!-- 状态 -->
            <div class="form-group">
              <label for="status" class="form-label required">状态</label>
              <select
                id="status"
                v-model="form.status"
                class="form-input"
                :class="{ 'border-red-500': errors.status }"
                required
              >
                <option value="present">出勤</option>
                <option value="absent">缺勤</option>
                <option value="late">迟到</option>
                <option value="half-day">半天</option>
              </select>
              <p v-if="errors.status" class="text-red-500 text-sm mt-1">{{ errors.status }}</p>
            </div>
          </div>

          <!-- 备注 -->
          <div class="form-group">
            <label for="notes" class="form-label">备注</label>
            <textarea
              id="notes"
              v-model="form.notes"
              class="form-input"
              :class="{ 'border-red-500': errors.notes }"
              rows="3"
              placeholder="请输入备注信息"
            ></textarea>
            <p v-if="errors.notes" class="text-red-500 text-sm mt-1">{{ errors.notes }}</p>
          </div>

          <!-- 提交按钮 -->
          <div class="flex justify-end space-x-3 pt-4 border-t">
            <router-link
              to="/attendance"
              class="btn btn-secondary">
              取消
            </router-link>
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="isSubmitting">
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isEditMode ? '保存' : '提交' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import { useDepartmentStore } from '@/stores/department';
import attendanceService from '@/services/attendance.service';
import apiService from '@/services/apiService';

const route = useRoute();
const router = useRouter();

// 判断是编辑模式还是新增模式
const isEditMode = computed(() => !!route.params.id);

// 表单数据
const form = reactive({
  projectId: '',
  employeeName: '',
  employeeId: '',
  date: new Date().toISOString().split('T')[0], // 默认为今天
  timeIn: '',
  timeOut: '',
  hoursWorked: null,
  status: 'present',
  notes: ''
});

// 验证错误
const errors = reactive({});

// 状态
const isSubmitting = ref(false);
const isLoadingProjects = ref(false);
const isLoadingEmployees = ref(false);
const projects = ref([]);
const employees = ref([]);
const error = ref(null);
const successMessage = ref(null);

// 员工筛选相关
const employeeFilter = ref('');
const showEmployeeDropdown = ref(false);
const filteredEmployees = ref([]);

// 使用部门存储
const departmentStore = useDepartmentStore();

// 点击页面其他地方关闭下拉框
const closeDropdownOnOutsideClick = (event) => {
  const dropdown = document.getElementById('employeeNameFilter');
  if (dropdown && !dropdown.contains(event.target)) {
    showEmployeeDropdown.value = false;
  }
};

// 添加和移除全局点击事件监听器
onMounted(() => {
  document.addEventListener('click', closeDropdownOnOutsideClick);
});

// 组件卸载时移除事件监听器
const onBeforeUnmount = () => {
  document.removeEventListener('click', closeDropdownOnOutsideClick);
};

// 显示成功消息并自动隐藏
const showSuccessMessage = (message, duration = 5000) => {
  successMessage.value = message;
  setTimeout(() => {
    successMessage.value = null;
  }, duration);
};

// 如果是编辑模式，获取待编辑的记录
onMounted(async () => {
  // 获取项目列表和员工列表
  await Promise.all([
    fetchProjects(),
    fetchEmployees(),
    departmentStore.fetchDepartments()
  ]);

  // 如果是编辑模式，获取考勤记录数据
  if (isEditMode.value) {
    await fetchAttendanceRecord();
  }
});

// 获取考勤记录数据
const fetchAttendanceRecord = async () => {
  isSubmitting.value = true; // 使用加载状态
  try {
    // 使用考勤服务
    const response = await attendanceService.getAttendance(route.params.id);
    const data = response.data;

    // 填充表单数据
    Object.keys(form).forEach(key => {
      if (key in data) {
        form[key] = data[key];
      }
    });
    
    // 显示部门和用工类型
    if (data.department) {
      document.getElementById('department-display').textContent = data.department;
    }
    
    if (data.employmentType) {
      document.getElementById('employment-type-display').textContent = data.employmentType;
    }
    
  } catch (error) {
    console.error('获取考勤记录失败:', error);
    const errorMessage = error.response?.data?.details || error.response?.data?.message || '很抱歉，系统无法获取您请求的考勤记录信息，请返回列表页重新尝试。';
    error.value = errorMessage;
    // 如果获取失败，3秒后返回列表页
    setTimeout(() => {
      router.push('/attendance');
    }, 3000);
  } finally {
    isSubmitting.value = false;
  }
};

// 获取项目列表
const fetchProjects = async () => {
  isLoadingProjects.value = true;
  try {
    const response = await apiService.getProjects({
      status: 'in_progress',
      limit: 100
    });
    projects.value = response.results || [];
  } catch (error) {
    console.error('获取项目列表失败:', error);
    error.value = '很抱歉，系统暂时无法获取项目列表信息。请您刷新页面重试，如果问题持续存在，请联系系统管理员获取帮助。';
    projects.value = [];
  } finally {
    isLoadingProjects.value = false;
  }
};

// 获取员工列表
const fetchEmployees = async () => {
  isLoadingEmployees.value = true;
  try {
    const response = await apiService.getAllEmployees({ limit: 100 });
    employees.value = response.results || [];
    filteredEmployees.value = employees.value; // 初始化筛选列表
  } catch (error) {
    console.error('获取员工列表失败:', error);
    error.value = '很抱歉，系统暂时无法获取员工列表信息。请您刷新页面重试，如果问题持续存在，请联系系统管理员获取帮助。';
    employees.value = [];
  } finally {
    isLoadingEmployees.value = false;
  }
};

// 筛选员工
const filterEmployees = () => {
  if (!employeeFilter.value) {
    filteredEmployees.value = employees.value;
    return;
  }

  const searchTerm = employeeFilter.value.toLowerCase();
  filteredEmployees.value = employees.value.filter(emp =>
    emp.name.toLowerCase().includes(searchTerm) ||
    (emp.department && emp.department.toLowerCase().includes(searchTerm))
  );
};

// 将员工类型代码转为中文显示名称
const getEmploymentTypeLabel = (type) => {
  const typeMap = {
    'contract': '合同工',
    'temporary': '临时工'
  };
  return typeMap[type] || '未知类型';
};

// 选择员工
const selectEmployee = (employee) => {
  form.employeeId = employee.id;
  form.employeeName = employee.name;
  // 只显示部门和用工类型，但不提交这些字段
  const departmentValue = employee.department || '';
  const employmentValue = employee.employeeType ? getEmploymentTypeLabel(employee.employeeType) : '';
  
  // 在页面上显示这些信息，但不绑定到form
  document.getElementById('department-display').textContent = departmentValue;
  document.getElementById('employment-type-display').textContent = employmentValue;
  
  employeeFilter.value = '';
  showEmployeeDropdown.value = false;
};

// 处理员工选择 (保留原有函数以兼容其他地方的调用)
const handleEmployeeChange = () => {
  const selectedEmployee = employees.value.find(emp => emp.id === form.employeeId);
  if (selectedEmployee) {
    form.employeeName = selectedEmployee.name;
    // 只显示部门和用工类型，但不提交这些字段
    const departmentValue = selectedEmployee.department || '';
    const employmentValue = selectedEmployee.employeeType ? getEmploymentTypeLabel(selectedEmployee.employeeType) : '';
    
    // 在页面上显示这些信息，但不绑定到form
    document.getElementById('department-display').textContent = departmentValue;
    document.getElementById('employment-type-display').textContent = employmentValue;
  }
};

// 自动计算工作时长
watch([() => form.timeIn, () => form.timeOut], ([newTimeIn, newTimeOut]) => {
  if (newTimeIn && newTimeOut) {
    const [inHours, inMinutes] = newTimeIn.split(':').map(Number);
    const [outHours, outMinutes] = newTimeOut.split(':').map(Number);

    const inTotalMinutes = inHours * 60 + inMinutes;
    const outTotalMinutes = outHours * 60 + outMinutes;

    if (outTotalMinutes > inTotalMinutes) {
      const diffMinutes = outTotalMinutes - inTotalMinutes;
      form.hoursWorked = Math.round((diffMinutes / 60) * 2) / 2; // 四舍五入到最近的0.5
    }
  }
});

// 表单验证
const validateForm = () => {
  const newErrors = {};

  if (!form.projectId) {
    newErrors.projectId = '请选择项目';
  }

  if (!form.employeeName) {
    newErrors.employeeName = '请输入员工姓名';
  }

  if (!form.date) {
    newErrors.date = '请选择日期';
  }

  if (!form.timeIn) {
    newErrors.timeIn = '请输入上班时间';
  }

  if (form.hoursWorked !== null && form.hoursWorked < 0) {
    newErrors.hoursWorked = '工作时长必须大于或等于0';
  }

  Object.assign(errors, newErrors);
  return Object.keys(newErrors).length === 0;
};

// 提交表单
const submitForm = async () => {
  // 清除之前的错误
  Object.keys(errors).forEach(key => delete errors[key]);

  // 验证表单
  if (!validateForm()) {
    return;
  }

  isSubmitting.value = true;

  try {
    // 创建要提交的数据对象，排除department和employmentType字段
    const submitData = {
      projectId: form.projectId,
      employeeName: form.employeeName,
      employeeId: form.employeeId,
      date: form.date,
      timeIn: form.timeIn,
      timeOut: form.timeOut,
      hoursWorked: form.hoursWorked,
      status: form.status,
      notes: form.notes
    };

    if (isEditMode.value) {
      // 更新已有记录
      await attendanceService.updateAttendance(route.params.id, submitData);
    } else {
      // 创建新记录
      await attendanceService.createAttendance(submitData);
    }

    // 显示成功消息并返回列表页
    showSuccessMessage(isEditMode.value ? '恭喜！您的考勤记录已成功更新。系统已保存所有修改内容，您可以在列表中查看最新信息。' : '恭喜！新的考勤记录已成功添加。系统已保存您提交的所有信息，您可以在列表中查看该记录。');
    // 2秒后跳转到列表页
    setTimeout(() => {
      router.push('/attendance');
    }, 2000);
  } catch (error) {
    console.error('提交考勤记录失败:', error);

    // 处理服务器返回的验证错误
    if (error.response && error.response.data && error.response.data.errors) {
      const serverErrors = error.response.data.errors;
      Object.keys(serverErrors).forEach(key => {
        errors[key] = serverErrors[key];
      });
    } else {
      error.value = isEditMode.value ? '很抱歉，更新考勤记录时遇到了问题。这可能是由于网络连接不稳定或系统临时故障导致的。请您稍后再次尝试，如果问题持续存在，请联系技术支持团队获取帮助。' : '很抱歉，添加考勤记录时遇到了问题。这可能是由于网络连接不稳定或系统临时故障导致的。请您检查输入信息并再次尝试，如果问题持续存在，请联系技术支持团队获取帮助。';
    }
  } finally {
    isSubmitting.value = false;
  }
};
</script>

<style scoped>
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-label.required:after {
  content: " *";
  @apply text-red-500;
}

.form-input {
  @apply block w-full rounded-md border-gray-300 shadow-sm
    focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50
    transition-all duration-200;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent
    rounded-md shadow-sm text-sm font-medium focus:outline-none
    focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700
    focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-white text-gray-700 border-gray-300
    hover:bg-gray-50 focus:ring-blue-500;
}

.form-grid {
  @apply grid grid-cols-1 md:grid-cols-2 gap-6;
}

.form-text-display {
  @apply block w-full py-2 px-3 bg-gray-50 border border-gray-300 rounded-md text-gray-700;
  min-height: 38px;
  line-height: 24px;
}
</style>