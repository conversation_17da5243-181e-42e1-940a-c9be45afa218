const Joi = require('joi');

// 中文错误消息
const chineseMessages = {
  'any.required': '{{#label}}是必填项',
  'any.empty': '{{#label}}不能为空',
  'any.only': '{{#label}}必须是以下值之一: {{#valids}}',
  
  'string.base': '{{#label}}必须是文本',
  'string.empty': '{{#label}}不能为空',
  'string.min': '{{#label}}至少需要{{#limit}}个字符',
  'string.max': '{{#label}}不能超过{{#limit}}个字符',
  'string.pattern.base': '{{#label}}格式不正确',
  'string.email': '{{#label}}必须是有效的电子邮箱',
  'string.uri': '{{#label}}必须是有效的URL',
  'string.length': '{{#label}}长度必须为{{#limit}}个字符',
  'string.alphanum': '{{#label}}只能包含字母和数字',
  
  'number.base': '{{#label}}必须是数字',
  'number.min': '{{#label}}必须大于或等于{{#limit}}',
  'number.max': '{{#label}}必须小于或等于{{#limit}}',
  'number.integer': '{{#label}}必须是整数',
  'number.positive': '{{#label}}必须是正数',
  'number.negative': '{{#label}}必须是负数',
  
  'date.base': '{{#label}}必须是有效的日期',
  'date.min': '{{#label}}必须在{{#limit}}之后',
  'date.max': '{{#label}}必须在{{#limit}}之前',
  'date.format': '{{#label}}必须符合格式 {{#format}}',
  
  'boolean.base': '{{#label}}必须是布尔值',
  
  'array.base': '{{#label}}必须是数组',
  'array.min': '{{#label}}至少需要{{#limit}}个项目',
  'array.max': '{{#label}}不能超过{{#limit}}个项目',
  'array.unique': '{{#label}}不能包含重复项',
  
  'object.base': '{{#label}}必须是对象',
  'object.min': '{{#label}}至少需要{{#limit}}个属性',
  'object.max': '{{#label}}不能超过{{#limit}}个属性',
  'object.missing': '{{#label}}至少需要一个属性',
  'object.unknown': '{{#label}}包含未允许的属性',
};

// 创建一个带有中文错误消息的Joi实例
const JoiZh = Joi.defaults((schema) => {
  return schema.messages(chineseMessages);
});

module.exports = {
  Joi: JoiZh,
  originalJoi: Joi,
}; 