<template>
  <div class="max-w-7xl mx-auto px-4 py-6">
    <!-- 顶部操作栏 -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center space-x-4">
        <h1 class="text-2xl font-bold text-gray-900">产品管理</h1>
        <div class="flex items-center space-x-2">
          <button
            @click="viewMode = 'card'"
            class="p-2 rounded-lg hover:bg-gray-100"
            :class="{ 'bg-gray-100': viewMode === 'card' }"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
            </svg>
          </button>
          <button
            @click="viewMode = 'table'"
            class="p-2 rounded-lg hover:bg-gray-100"
            :class="{ 'bg-gray-100': viewMode === 'table' }"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </button>
          <router-link to="/products/stats"
            class="px-4 py-2 rounded-lg text-sm font-medium bg-purple-100 text-purple-700 hover:bg-purple-200 transition-colors duration-200 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            产品统计
          </router-link>
        </div>
      </div>
      <router-link to="/products/create" class="btn btn-primary">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        新建产品
      </router-link>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-card mb-6">
      <div class="flex justify-between gap-4">
        <div class="flex-1">
          <label for="product-search" class="form-label font-medium">搜索产品</label>
          <div class="relative">
            <input
              id="product-search"
              v-model="searchQuery"
              type="text"
              placeholder="搜索产品名称或编码..."
              class="form-input pl-10 py-2 mt-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
              @input="debouncedFetchProducts"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1 flex gap-4">
          <div class="flex-1">
            <label for="category-filter" class="form-label font-medium">产品分类</label>
            <select id="category-filter" v-model="categoryFilter" class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200">
              <option value="">所有分类</option>
              <option v-for="category in categories" :key="category" :value="category">{{ category }}</option>
            </select>
          </div>
          <div class="flex-1">
            <label for="status-filter" class="form-label font-medium">产品状态</label>
            <select id="status-filter" v-model="statusFilter" class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200">
              <option value="">所有状态</option>
              <option value="active">在售</option>
              <option value="inactive">停售</option>
              <option value="discontinued">已下架</option>
            </select>
          </div>
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button @click="resetFilters" class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200" :disabled="loading">重置</button>
        <button @click="fetchProducts" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200" :disabled="loading">
          <span v-if="loading" class="inline-block animate-spin mr-1">↻</span>
          {{ loading ? '加载中...' : '查询' }}
        </button>
        <ExportButton
        v-if="0"
          :data="filteredProducts"
          :headers="exportHeaders"
          entity-name="products"
          button-text="导出数据"
          variant="secondary"
          :disabled="loading || filteredProducts.length === 0"
          class="ml-3"
        />
      </div>
    </div>

    <!-- 数据统计信息 -->
    <div class="mb-4 flex justify-between items-center">
      <div class="text-sm" :style="{ color: 'var(--text-secondary)' }">
        <span v-if="loading">正在加载数据...</span>
        <span v-else>已加载 {{ filteredProducts.length }} 条记录 {{ totalCount > 0 ? `(共 ${totalCount} 条)` : '' }}</span>
      </div>
      <div class="text-sm" :style="{ color: 'var(--text-secondary)' }">
        最后更新时间: {{ lastUpdated }}
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-4 p-4 bg-red-50 text-red-800 rounded-md">
      <div class="flex items-center">
        <span class="mr-2">⚠️</span>
        <span>{{ error }}</span>
      </div>
      <div class="mt-2 text-sm">
        <button
          @click="fetchProducts"
          class="text-red-800 underline"
        >
          点击重试
        </button>
      </div>
    </div>

    <!-- 产品列表 -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>

    <div v-else>
      <!-- 卡片视图 -->
      <div v-if="viewMode === 'card'" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-for="product in filteredProducts" :key="product.id" class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
          <div class="p-6">
            <div class="flex justify-between items-start mb-4">
              <h3 class="text-lg font-semibold text-gray-900">{{ product.name }}</h3>
              <span :class="['badge', getStatusClass(product.status)]">{{ getStatusText(product.status) }}</span>
            </div>
            <div class="space-y-2 text-sm text-gray-600">
              <p>产品编码：{{ product.code }}</p>
              <p>分类：{{ product.category }}</p>
              <p>单价：¥{{ formatPrice(product.price) }}</p>
              <p>库存：{{ product.stock }} {{ product.unit }}</p>
            </div>
            <div class="mt-4" v-if="product.minStock > 0">
              <div class="flex justify-between items-center mb-2">
                <span class="text-sm text-gray-600">库存状态</span>
                <span class="text-sm font-medium" :class="getStockStatusTextColor(product)">
                  {{ getStockStatusText(product) }}
                </span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-2">
                <div
                  class="h-2 rounded-full"
                  :class="getStockStatusBarColor(product)"
                  :style="{ width: `${Math.min(100, (product.stock / product.minStock) * 100)}%` }"
                ></div>
              </div>
            </div>            <div class="mt-6 flex justify-end space-x-3">
              <router-link
                :to="`/products/edit/${product.id}`"
                class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
              >
                <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                </svg>
                <span class="font-medium">编辑</span>
              </router-link>
              <button
                @click="handleDelete(product.id)"
                class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
              >
                <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                </svg>
                <span class="font-medium">删除</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredProducts.length === 0" class="col-span-3 py-12 text-center">
          <div v-if="searchQuery || categoryFilter || statusFilter" class="py-8">
            <div class="text-lg mb-2">没有找到符合条件的产品</div>
            <div class="text-sm text-gray-500">请尝试修改搜索条件或清除过滤器</div>
          </div>
          <div v-else class="py-8">
            <div class="text-lg mb-2">暂无产品数据</div>
            <div class="text-sm text-gray-500">
              <router-link to="/products/create" class="text-blue-600 hover:underline">点击创建第一个产品</router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- 表格视图 -->
      <div v-else class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品编码</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品名称</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单价</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">库存</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">最低库存</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="product in filteredProducts" :key="product.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">{{ product.code }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ product.name }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ product.category }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥{{ formatPrice(product.price) }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ product.unit }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span :class="getStockStatusTextColor(product)">{{ product.stock }}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ product.minStock || '-' }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="['badge', getStatusClass(product.status)]">{{ getStatusText(product.status) }}</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">                  <router-link
                    :to="`/products/edit/${product.id}`"
                    class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                    </svg>
                    <span class="font-medium">编辑</span>
                  </router-link>
                  <button
                    @click="handleDelete(product.id)"
                    class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                    <span class="font-medium">删除</span>
                  </button>
                </td>
              </tr>

              <!-- 空状态 -->
              <tr v-if="filteredProducts.length === 0">
                <td colspan="9" class="px-6 py-12 text-center text-sm text-gray-500">
                  <div v-if="searchQuery || categoryFilter || statusFilter">
                    <div class="text-lg mb-2">没有找到符合条件的产品</div>
                    <div class="text-sm">请尝试修改搜索条件或清除过滤器</div>
                  </div>
                  <div v-else>
                    <div class="text-lg mb-2">暂无产品数据</div>
                    <div class="text-sm">
                      <router-link to="/products/create" class="text-blue-600 hover:underline">点击创建第一个产品</router-link>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 分页控件 -->
      <div v-if="totalPages > 1" class="mt-4 flex justify-center">
        <div class="flex items-center space-x-2">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="px-3 py-1 rounded border"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
            :style="{ borderColor: 'var(--border-color)' }"
          >
            上一页
          </button>

          <template v-for="page in displayedPages" :key="page">
            <span
              v-if="page === '...'"
              class="px-3 py-1"
            >
              ...
            </span>
            <button
              v-else
              @click="changePage(page)"
              class="px-3 py-1 rounded border"
              :class="{ 'font-bold': currentPage === page }"
              :style="{
                backgroundColor: currentPage === page ? 'var(--primary-color)' : 'transparent',
                color: currentPage === page ? 'white' : 'var(--text-primary)',
                borderColor: 'var(--border-color)'
              }"
            >
              {{ page }}
            </button>
          </template>

          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="px-3 py-1 rounded border"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
            :style="{ borderColor: 'var(--border-color)' }"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, reactive, onUnmounted } from 'vue';
import apiService from '@/services/apiService';
import notificationService from '@/services/notification.service';
import ExportButton from '@/components/common/ExportButton.vue';
import { IS_PROD } from '@/utils/env';

// 基本状态
const products = ref([]);
const loading = ref(false);
const error = ref(null);
const lastUpdated = ref(new Date().toLocaleString());
let refreshInterval = null;

// 导出相关
const exportHeaders = [
  { title: '产品编码', key: 'code' },
  { title: '产品名称', key: 'name' },
  { title: '分类', key: 'category' },
  { title: '单价', key: 'price' },
  { title: '单位', key: 'unit' },
  { title: '库存', key: 'stock' },
  { title: '最低库存', key: 'minStock' },
  { title: '状态', key: 'status' },
  { title: '描述', key: 'description' }
];

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const totalCount = ref(0);
const totalPages = ref(1);

// 搜索和过滤
const searchQuery = ref('');
const categoryFilter = ref('');
const statusFilter = ref('');
const viewMode = ref(localStorage.getItem('productViewMode') || 'card');
const categories = ref([]);

// 当前数据源类型
const isMockData = computed(() => {
  // 检查全局数据源变量
  if (window.__currentDataSource) {
    return window.__currentDataSource === 'mock';
  }

  // 从 localStorage 获取
  const storedDataSource = localStorage.getItem('app_data_source');
  if (storedDataSource) {
    return storedDataSource === 'mock';
  }

  // 默认使用真实API
  return false;
});

// 监听视图模式变化并保存到 localStorage
watch(viewMode, (newMode) => {
  localStorage.setItem('productViewMode', newMode);
});

// 过滤后的产品列表
const filteredProducts = computed(() => {
  if (!products.value.length) return [];

  return products.value.filter(product => {
    // 搜索条件
    const matchesSearch = !searchQuery.value ||
      (product.name && product.name.toLowerCase().includes(searchQuery.value.toLowerCase())) ||
      (product.code && product.code.toLowerCase().includes(searchQuery.value.toLowerCase()));

    // 分类过滤
    const matchesCategory = !categoryFilter.value || product.category === categoryFilter.value;

    // 状态过滤
    const matchesStatus = !statusFilter.value || product.status === statusFilter.value;

    return matchesSearch && matchesCategory && matchesStatus;
  });
});

// 计算要显示的页码
const displayedPages = computed(() => {
  if (totalPages.value <= 7) {
    // 如果总页数小于等于7，显示所有页码
    return Array.from({ length: totalPages.value }, (_, i) => i + 1);
  }

  // 否则显示带省略号的页码
  const pages = [];

  // 始终显示第一页
  pages.push(1);

  // 当前页靠近开始
  if (currentPage.value <= 3) {
    pages.push(2, 3, 4, '...', totalPages.value - 1, totalPages.value);
  }
  // 当前页靠近结尾
  else if (currentPage.value >= totalPages.value - 2) {
    pages.push('...', totalPages.value - 3, totalPages.value - 2, totalPages.value - 1, totalPages.value);
  }
  // 当前页在中间
  else {
    pages.push(
      '...',
      currentPage.value - 1,
      currentPage.value,
      currentPage.value + 1,
      '...',
      totalPages.value
    );
  }

  return pages;
});

// 状态相关函数
const statusMap = reactive({
  'active': '在售',
  'inactive': '停售',
  'discontinued': '已下架'
});

const getStatusText = (status) => statusMap[status] || status;

const getStatusClass = (status) => {
  const statusClasses = {
    'active': 'badge-green',
    'inactive': 'badge-yellow',
    'discontinued': 'badge-red'
  };
  return statusClasses[status] || 'badge-gray';
};

// 库存状态相关函数
const getStockStatusText = (product) => {
  if (!product.minStock) return '正常';

  const ratio = product.stock / product.minStock;
  if (ratio <= 0.2) return '库存紧急';
  if (ratio <= 0.5) return '库存不足';
  return '库存充足';
};

const getStockStatusBarColor = (product) => {
  if (!product.minStock) return 'bg-green-500';

  const ratio = product.stock / product.minStock;
  if (ratio <= 0.2) return 'bg-red-500';
  if (ratio <= 0.5) return 'bg-yellow-500';
  return 'bg-green-500';
};

const getStockStatusTextColor = (product) => {
  if (!product.minStock) return 'text-green-600';

  const ratio = product.stock / product.minStock;
  if (ratio <= 0.2) return 'text-red-600';
  if (ratio <= 0.5) return 'text-yellow-600';
  return 'text-green-600';
};

// 格式化价格
const formatPrice = (price) => {
  return Number(price).toFixed(2);
};

// 获取产品列表
const fetchProducts = async () => {
  loading.value = true;
  error.value = null;

  try {
    // 准备查询参数
    const params = {
      page: currentPage.value,
      limit: pageSize.value
    };

    // 添加搜索和过滤条件
    if (searchQuery.value) {
      params.search = searchQuery.value;
    }

    if (categoryFilter.value) {
      params.category = categoryFilter.value;
    }

    if (statusFilter.value) {
      params.status = statusFilter.value;
    }

    // 获取产品数据
    console.log(`使用${isMockData.value ? '模拟数据' : '真实API'}获取产品数据`);
    const result = await apiService.getProducts(params);

    // 处理响应数据
    if (Array.isArray(result)) {
      // 非分页响应
      products.value = result;
      totalCount.value = result.length;
      totalPages.value = 1;
    } else if (result && Array.isArray(result.results)) {
      // 分页响应
      products.value = result.results;
      totalCount.value = result.totalResults || result.results.length;
      totalPages.value = result.totalPages || Math.ceil(totalCount.value / pageSize.value);
      currentPage.value = result.page || currentPage.value;

      // 更新分类列表
      const uniqueCategories = [...new Set(result.results.map(product => product.category).filter(Boolean))];
      categories.value = uniqueCategories;
    } else {
      console.warn('意外的数据格式:', result);
      products.value = [];
      totalCount.value = 0;
      totalPages.value = 1;
      categories.value = [];
      error.value = '返回的数据格式不正确';
    }

    // 更新最后加载时间
    lastUpdated.value = new Date().toLocaleString();

    // 产品页面加载成功不需要提示
    // 已移除成功通知
  } catch (err) {
    console.error('加载产品数据失败:', err);
    const errorMessage = err.message || '加载产品数据失败';

    // 显示错误消息
    error.value = errorMessage;

    // 显示错误通知
    notificationService.error('加载失败', errorMessage);

    // 重置数据
    products.value = [];
    totalCount.value = 0;
    totalPages.value = 1;
    categories.value = [];
  } finally {
    loading.value = false;
  }
};

// 切换页面
const changePage = (page) => {
  if (page < 1 || page > totalPages.value || page === currentPage.value) {
    return;
  }

  currentPage.value = page;
  fetchProducts();
};

// 处理删除
const handleDelete = async (productId) => {
  if (!confirm('确定要删除这个产品吗？此操作无法撤销。')) {
    return;
  }

  try {
    loading.value = true;

    // 获取要删除的产品信息（用于通知）
    const productToDelete = products.value.find(p => p.id === productId);
    const productName = productToDelete ? productToDelete.name : '产品';

    // 删除产品
    await apiService.deleteProduct(productId);

    // 显示成功通知
    notificationService.success('删除成功', `产品 "${productName}" 已成功删除`);

    // 重新加载产品列表
    await fetchProducts();
  } catch (err) {
    const errorMessage = '删除产品失败: ' + (err.message || '未知错误');

    // 显示错误消息
    error.value = errorMessage;
    console.error('删除产品失败:', err);

    // 显示错误通知
    notificationService.error('删除产品失败', err.message || '未知错误');
  } finally {
    loading.value = false;
  }
};

// 设置自动刷新
const setupRefresh = () => {
  // 清除已有的定时器
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }

  // 生产环境且使用API时，每60秒自动刷新一次
  if (IS_PROD && !isMockData.value) {
    refreshInterval = setInterval(() => {
      fetchProducts();
    }, 60000); // 60秒
  }
};

// 监听数据源变化
watch(isMockData, () => {
  // 重置到第一页
  currentPage.value = 1;
  // 重新加载数据
  fetchProducts();
  // 重新设置自动刷新
  setupRefresh();
});

// 页面加载时获取产品数据并设置自动刷新
onMounted(() => {
  fetchProducts();
  setupRefresh();

  // 添加数据源变化事件监听
  window.addEventListener('datasource-changed', () => {
    fetchProducts();
    setupRefresh();
  });
});

// 页面卸载时清除定时器和事件监听器
onUnmounted(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }

  window.removeEventListener('datasource-changed', () => {});
});

// 重置筛选条件
const resetFilters = () => {
  searchQuery.value = '';
  categoryFilter.value = '';
  statusFilter.value = '';
  fetchProducts();
};
</script>

<style scoped>
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-green {
  @apply bg-green-100 text-green-800;
}

.badge-yellow {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-red {
  @apply bg-red-100 text-red-800;
}

.badge-gray {
  @apply bg-gray-100 text-gray-800;
}

.btn {
  @apply px-4 py-2 rounded-md flex items-center justify-center transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300 transition-colors duration-200;
}

.page-header {
  @apply flex justify-between items-center mb-6;
}

.page-title {
  @apply text-2xl font-bold text-gray-900;
}

.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.filter-card {
  @apply p-4 bg-white rounded-lg shadow;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50;
}
</style>
