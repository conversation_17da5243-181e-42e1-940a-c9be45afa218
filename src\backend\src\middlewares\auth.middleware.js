const jwt = require('jsonwebtoken');
const logger = require('../config/logger');
const { User } = require('../models');
const config = require('../config/config');

/**
 * Authentication middleware
 * Validates JWT token and attaches user to request
 */
const authenticate = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: {
          message: 'Authorization denied. No token provided.',
          requestId: req.id
        }
      });
    }

    const token = authHeader.split(' ')[1];

    if (!token || token.trim() === '') {
      return res.status(401).json({
        error: {
          message: 'Authorization denied. Empty token provided.',
          requestId: req.id
        }
      });
    }

    // Ensure JWT_SECRET is properly configured
    if (!config.jwt || !config.jwt.secret) {
      logger.error('JWT_SECRET not configured properly', { requestId: req.id });
      return res.status(500).json({
        error: {
          message: 'Server authentication configuration error',
          requestId: req.id
        }
      });
    }

    // Verify token
    logger.info(`Verifying token with secret: ${config.jwt.secret.substring(0, 5)}...`);

    // Log token details for debugging
    try {
      const decodedHeader = jwt.decode(token, { complete: true }).header;
      logger.info(`Token header: ${JSON.stringify(decodedHeader)}`);
      logger.info(`Token: ${token.substring(0, 20)}...`);
    } catch (e) {
      logger.error(`Error decoding token header: ${e.message}`);
    }

    try {
      // Verify the token signature and expiration
      const decoded = jwt.verify(token, config.jwt.secret);
      logger.info(`Token verified successfully for user: ${decoded.sub}`);

      // Get user from database (excluding password)
      const user = await User.findByPk(decoded.sub, {
        attributes: { exclude: ['password'] }
      });

      if (!user) {
        return res.status(401).json({
          error: {
            message: 'User not found',
            requestId: req.id
          }
        });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(403).json({
          error: {
            message: 'Account is disabled. Please contact an administrator.',
            requestId: req.id
          }
        });
      }

      // Attach user to request
      req.user = user;
      next();
    } catch (error) {
      logger.error(`JWT verification error: ${error.message}`, {
        stack: error.stack,
        name: error.name
      });

      return res.status(401).json({
        error: {
          message: 'Invalid token',
          details: error.message,
          code: 'INVALID_TOKEN'
        }
      });
    }
  } catch (error) {
    logger.error(`Authentication error: ${error.message}`, {
      requestId: req.id,
      stack: error.stack,
      tokenInfo: req.headers.authorization ? 'Token present' : 'No token',
      errorName: error.name,
      errorCode: error.code
    });

    // Return a generic error message to avoid leaking sensitive information
    return res.status(500).json({
      error: {
        message: 'Server error during authentication',
        requestId: req.id,
        code: 'AUTH_SERVER_ERROR'
      }
    });
  }
};

/**
 * Authorization middleware
 * Checks if user has the required permissions
 */
const authorize = (requiredPermissions = []) => {
  return async (req, res, next) => {
    try {
      // If no permissions required, proceed
      if (!requiredPermissions.length) {
        return next();
      }

      const user = req.user;

      // Check if user is admin (has all permissions)
      if (user.isAdmin) {
        return next();
      }

      // Get user's permissions through their roles
      // This requires proper setup of Role and Permission models with associations
      const userWithPermissions = await User.findByPk(user.id, {
        include: {
          model: 'Role',
          include: 'Permission'
        }
      });

      // Extract user permissions
      const userPermissions = new Set();
      if (userWithPermissions.Roles) {
        userWithPermissions.Roles.forEach(role => {
          if (role.Permissions) {
            role.Permissions.forEach(permission => {
              userPermissions.add(permission.name);
            });
          }
        });
      }

      // Check if user has all required permissions
      const hasPermission = requiredPermissions.every(permission =>
        userPermissions.has(permission)
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: {
            message: 'Insufficient permissions to access this resource',
            requestId: req.id
          }
        });
      }

      next();
    } catch (error) {
      logger.error(`Authorization error: ${error.message}`, {
        requestId: req.id,
        stack: error.stack
      });

      res.status(500).json({
        error: {
          message: 'Server error during authorization',
          requestId: req.id
        }
      });
    }
  };
};

module.exports = {
  authenticate,
  authorize
};