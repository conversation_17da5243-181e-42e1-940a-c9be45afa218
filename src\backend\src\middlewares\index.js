const auth = require('./auth');
const { authenticate, authorize } = require('./auth.middleware');
const validate = require('./validate');
const errorMiddleware = require('./error.middleware');
const { simpleAuthenticate } = require('./simple-auth.middleware');

module.exports = {
  auth,
  authenticate,
  authorize,
  validate,
  errorMiddleware,
  simpleAuthenticate,
  // Role middleware for convenient use
  roleMiddleware: (roles) => auth(...roles)
}; 