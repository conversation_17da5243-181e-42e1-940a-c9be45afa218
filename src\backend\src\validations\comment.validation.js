const Joi = require('joi');

const createCommentSchema = {
  params: Joi.object().keys({
    documentId: Joi.string().uuid().required().messages({
      'string.empty': '文档编号不能为空',
      'string.guid': '文档编号必须是有效的UUID格式',
      'any.required': '文档编号是必填项'
    })
  }),
  body: Joi.object().keys({
    content: Joi.string().required().min(1).max(1000).messages({
      'string.empty': '评论内容不能为空',
      'string.min': '评论内容至少需要{#limit}个字符',
      'string.max': '评论内容不能超过{#limit}个字符',
      'any.required': '评论内容是必填项'
    }),
    parentId: Joi.string().uuid().allow(null).messages({
      'string.guid': '父评论编号必须是有效的UUID格式'
    }),
    isPrivate: Joi.boolean().default(false).messages({
      'boolean.base': '私密状态必须是布尔值'
    })
  })
};

const updateCommentSchema = {
  params: Joi.object().keys({
    id: Joi.string().uuid().required().messages({
      'string.empty': '评论编号不能为空',
      'string.guid': '评论编号必须是有效的UUID格式',
      'any.required': '评论编号是必填项'
    })
  }),
  body: Joi.object().keys({
    content: Joi.string().min(1).max(1000).messages({
      'string.empty': '评论内容不能为空',
      'string.min': '评论内容至少需要{#limit}个字符',
      'string.max': '评论内容不能超过{#limit}个字符'
    }),
    isPrivate: Joi.boolean().messages({
      'boolean.base': '私密状态必须是布尔值'
    })
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const getCommentSchema = {
  params: Joi.object().keys({
    id: Joi.string().uuid().required().messages({
      'string.empty': '评论编号不能为空',
      'string.guid': '评论编号必须是有效的UUID格式',
      'any.required': '评论编号是必填项'
    })
  })
};

const deleteCommentSchema = {
  params: Joi.object().keys({
    id: Joi.string().uuid().required().messages({
      'string.empty': '评论编号不能为空',
      'string.guid': '评论编号必须是有效的UUID格式',
      'any.required': '评论编号是必填项'
    })
  })
};

const getCommentsSchema = {
  params: Joi.object().keys({
    documentId: Joi.string().uuid().required().messages({
      'string.empty': '文档编号不能为空',
      'string.guid': '文档编号必须是有效的UUID格式',
      'any.required': '文档编号是必填项'
    })
  }),
  query: Joi.object().keys({
    parentId: Joi.string().uuid().allow(null, '').messages({
      'string.guid': '父评论编号必须是有效的UUID格式'
    }),
    userId: Joi.string().uuid().allow(null, '').messages({
      'string.guid': '用户编号必须是有效的UUID格式'
    }),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于{#limit}'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    }),
    sortBy: Joi.string().valid('createdAt').default('createdAt').messages({
      'any.only': '排序字段必须是创建时间(createdAt)'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    }),
    includePrivate: Joi.boolean().default(false).messages({
      'boolean.base': '包含私密评论必须是布尔值'
    })
  })
};

module.exports = {
  createCommentSchema,
  updateCommentSchema,
  getCommentSchema,
  deleteCommentSchema,
  getCommentsSchema
};