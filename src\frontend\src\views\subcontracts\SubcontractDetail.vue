<template>
  <div class="page-container mx-auto px-4 py-8">
    <!-- 消息提示 -->
    <div v-if="showMessage" 
         :class="['fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300', 
                 messageType === 'success' ? 'bg-green-500' : 'bg-red-500']"
         class="text-white">
      {{ messageText }}
    </div>

    <!-- 页面标题 -->
    <div class="page-header flex justify-between items-center mb-6">
      <div>
        <h1 class="page-title">{{ isEdit ? '编辑分包' : '新建分包' }}</h1>
        <p class="page-subtitle">{{ isEdit ? '修改现有分包记录' : '创建新的分包记录' }}</p>
      </div>
      <button 
        @click="goBack" 
        class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200"
      >
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        返回列表
      </button>
    </div>

    <form class="space-y-8 bg-white p-8 rounded-lg shadow">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 基本信息 -->
        <div class="form-section">
          <h2 class="form-section-title">基本信息</h2>
          <div class="space-y-4">
            <div>
              <label for="project-name" class="form-label">项目名称 <span class="form-required text-red-500">*</span></label>
              <div class="relative">
                <select
                  id="projectId"
                  v-model="subcontractForm.projectId"
                  class="form-input"
                  @change="handleProjectChange"
                >
                  <option value="" disabled>请选择项目</option>
                  <option v-for="project in projects" :key="project.id" :value="project.id">
                    {{ project.name }} ({{ project.code }})
                  </option>
                </select>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
              </div>
              <span class="text-xs text-gray-500 mt-1 block" v-if="projectSearchLoading">加载中...</span>
              <span class="text-xs text-gray-500 mt-1 block" v-else-if="projects.length === 0">没有找到项目，请先创建项目</span>
            </div>
            <div>
              <label for="contract-name" class="form-label">合同名称 <span class="form-required text-red-500">*</span></label>
              <input
                id="contractName"
                v-model="subcontractForm.contractName"
                type="text"
                class="form-input"
                placeholder="请输入合同名称"
              >
              <span class="text-xs text-gray-500 mt-1 block">请输入完整的合同名称，将用于合同文档和报表</span>
            </div>
            <div>
              <label for="contract-code" class="form-label">合同编号 <span class="form-required text-red-500">*</span></label>
              <input
                id="contractCode"
                v-model="subcontractForm.contractCode"
                type="text"
                class="form-input"
                placeholder="请输入合同编号"
              >
              <span class="text-xs text-gray-500 mt-1 block">请确保合同编号唯一且符合编号规则</span>
            </div>
            <div>
              <label for="contract-type" class="form-label">合同类型 <span class="form-required text-red-500">*</span></label>
              <select
                id="contractType"
                v-model="subcontractForm.contractType"
                class="form-input"
              >
                <option value="">请选择合同类型</option>
                <option value="construction">施工分包</option>
                <option value="design">设计分包</option>
                <option value="material">材料分包</option>
              </select>
              <span class="text-xs text-gray-500 mt-1 block">选择合同的业务类型</span>
            </div>
            <div>
              <label for="contract-status" class="form-label">合同状态 <span class="form-required text-red-500">*</span></label>
              <select
                id="contractStatus"
                v-model="subcontractForm.contractStatus"
                class="form-input"
              >
                <option value="">请选择合同状态</option>
                <option value="draft">草稿</option>
                <option value="pending">待审核</option>
                <option value="approved">已审核</option>
                <option value="executing">执行中</option>
                <option value="completed">已完成</option>
                <option value="terminated">已终止</option>
              </select>
              <span class="text-xs text-gray-500 mt-1 block">表示合同当前的执行状态</span>
            </div>
          </div>
        </div>

        <!-- 财务信息 -->
        <div class="form-section">
          <h2 class="form-section-title">财务信息</h2>
          <div class="space-y-4">
            <div>
              <label for="total-amount" class="form-label">合同总价 <span class="form-required text-red-500">*</span></label>
              <input
                id="totalAmount"
                v-model="subcontractForm.totalAmount"
                type="number"
                class="form-input"
                placeholder="请输入合同总价"
              >
              <span class="text-xs text-gray-500 mt-1 block">合同约定的总金额，不含税</span>
            </div>
            <div>
              <label for="tax-rate" class="form-label">税率</label>
              <input
                id="tax-rate"
                v-model="subcontractForm.taxRate"
                type="number"
                class="form-input"
                placeholder="请输入税率"
              >
              <span class="text-xs text-gray-500 mt-1 block">合同适用的税率百分比</span>
            </div>
            <div>
              <label for="invoice-date" class="form-label">发票时间</label>
              <input
                id="invoice-date"
                v-model="subcontractForm.invoiceDate"
                type="date"
                max="3000-12-31"
                class="form-input"
              >
              <span class="text-xs text-gray-500 mt-1 block">预计开具发票的日期</span>
            </div>
            <div>
              <label for="payment-date" class="form-label">付款时间</label>
              <input
                id="payment-date"
                v-model="subcontractForm.paymentDate"
                type="date"
                max="3000-12-31"
                class="form-input"
              >
              <span class="text-xs text-gray-500 mt-1 block">预计付款的日期</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 附件信息 -->
      <div class="form-section border-t border-gray-200 pt-6">
        <h2 class="form-section-title">附件信息</h2>
        <div class="space-y-4">
          <div class="flex items-center space-x-4">
            <input
              type="file"
              @change="handleFileUpload"
              class="hidden"
              ref="fileInput"
              multiple
            >
            <button
              type="button"
              @click="$refs.fileInput.click()"
              class="btn btn-secondary"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              上传附件
            </button>
          </div>
          <div v-if="subcontractForm.attachments.length > 0" class="space-y-2">
            <div v-for="(file, index) in subcontractForm.attachments" :key="index" class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div class="flex items-center space-x-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                <span class="text-sm text-gray-600">{{ file.name }}</span>
              </div>
              <button
                type="button"
                @click="removeFile(index)"
                class="text-gray-400 hover:text-gray-600"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
          <span class="text-xs text-gray-500 block">上传合同相关的附件文件，如合同扫描件、补充协议等</span>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="form-section border-t border-gray-200 pt-6">
        <h2 class="form-section-title">备注信息</h2>
        <div>
          <textarea
            v-model="subcontractForm.notes"
            rows="4"
            class="form-input"
            placeholder="请输入备注信息"
          ></textarea>
          <span class="text-xs text-gray-500 mt-1 block">可记录合同的特殊条款、执行注意事项等补充信息</span>
        </div>
      </div>

      <!-- 表单底部按钮区域 -->
      <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200">
        <button
          type="button"
          @click="saveSubcontract"
          :disabled="loading"
          class="btn btn-primary"
        >
          <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          {{ loading ? '保存中...' : '保存' }}
        </button>
      </div>
    </form>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteConfirm" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">确认删除</h3>
        <p class="text-gray-600 mb-6">确定要删除这个分包记录吗？此操作不可撤销。</p>
        <div class="flex justify-end space-x-3">
          <button
            @click="showDeleteConfirm = false"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            取消
          </button>
          <button
            @click="deleteSubcontract"
            class="px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors"
          >
            删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { subcontractService } from '@/services/api.service';
import projectService from '@/services/project.service';

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const showDeleteConfirm = ref(false)
const showMessage = ref(false)
const messageText = ref('')
const messageType = ref('success')

const isEdit = computed(() => !!route.params.id)
const isCreate = computed(() => route.name === 'SubcontractCreate')

// 项目下拉列表数据
const projects = ref([])
const projectSearchLoading = ref(false)

// 分包表单数据
const subcontractForm = ref({
  projectId: '',
  projectName: '',
  contractName: '',
  contractCode: '',
  contractType: '',
  contractStatus: '',
  totalAmount: '',
  taxRate: '',
  invoiceDate: '',
  paymentDate: '',
  attachments: [],
  notes: ''
})

// 已移除模拟数据

// 加载分包数据
const loadSubcontractData = async () => {
  if (isEdit.value) {
    loading.value = true
    try {
      const id = route.params.id
      const subcontractData = await subcontractService.getSubcontract(id)
      const attachmentsInfo = subcontractData.attachments || []
      subcontractData.attachments = []
      Object.assign(subcontractForm.value, subcontractData)
      if (attachmentsInfo.length > 0) {
        subcontractForm.value.attachments = attachmentsInfo.map(info => ({
          name: info.fileName || info.name,
          id: info.id,
          url: info.url,
          isExisting: true
        }))
      }
    } catch (error) {
      console.error('加载分包数据失败:', error)
      showMessageFn('加载分包数据失败', 'error')
    } finally {
      loading.value = false
    }
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 显示消息
const showMessageFn = (text, type = 'success') => {
  messageText.value = text
  messageType.value = type
  showMessage.value = true
  setTimeout(() => {
    showMessage.value = false
  }, 3005)
}

// 验证表单
const validateForm = () => {
  const requiredFields = [
    { key: 'projectId', label: '项目名称', message: '请选择项目' },
    { key: 'contractName', label: '合同名称', message: '请输入合同名称' },
    { key: 'contractCode', label: '合同编号', message: '请输入合同编号' },
    { key: 'contractType', label: '合同类型', message: '请选择合同类型' },
    { key: 'contractStatus', label: '合同状态', message: '请选择合同状态' },
    { key: 'totalAmount', label: '合同总价', message: '请输入合同总价' }
  ]

  // 检查是否有空字段
  for (const field of requiredFields) {
    const value = subcontractForm.value[field.key]
    if (value === '' || value === null || value === undefined) {
      // 找到对应的DOM元素并添加错误样式
      const elementId = field.key.replace(/([A-Z])/g, '-$1').toLowerCase()
      const element = document.getElementById(elementId) || document.getElementById(field.key)
      if (element) {
        element.classList.add('border-red-500')
        element.focus()
        // 3秒后移除错误样式
        setTimeout(() => {
          element?.classList.remove('border-red-500')
        }, 3000)
      }
      
      // 显示具体的错误信息
      showMessageFn(field.message, 'error')
      return false
    }
  }

  return true
}

// 保存分包
const saveSubcontract = async () => {
// 验证表单
if (!validateForm()) {
return
}

// 处理税率，确保保留两位小数
if (subcontractForm.value.taxRate) {
subcontractForm.value.taxRate = Number(subcontractForm.value.taxRate).toFixed(2)
}

loading.value = true
try {
// 准备表单数据
const formData = new FormData()

// 添加基本字段
Object.keys(subcontractForm.value).forEach(key => {
if (key !== 'attachments') {
// 处理日期字段，确保空日期不会被提交为"Invalid date"
if ((key === 'invoiceDate' || key === 'paymentDate') && 
      (!subcontractForm.value[key] || subcontractForm.value[key] === '')) {
      // 如果日期为空，不添加该字段
          return
        }
        formData.append(key, subcontractForm.value[key])
      }
    })
    
    // 添加附件
    subcontractForm.value.attachments.forEach((file, index) => {
      if (!file.isExisting) {
        formData.append(`attachments[${index}]`, file)
      } else {
        formData.append(`existingAttachments[${index}]`, file.id)
      }
    })
    
    let response
    if (isEdit.value) {
      const id = route.params.id
      response = await subcontractService.updateSubcontract(id, formData)
    } else {
      response = await subcontractService.createSubcontract(formData)
    }
    
    showMessageFn(isEdit.value ? '分包更新成功' : '分包创建成功')
    router.push('/subcontracts')
  } catch (error) {
    console.error('保存失败:', error)
    
    // 处理特定错误
    if (error.response?.data?.field === 'contractCode') {
      // 合同编号已存在
      showMessageFn('合同编号已存在，请使用不同的编号', 'error')
      // 给合同编号输入框添加错误样式
      document.getElementById('contractCode').classList.add('border-red-500')
      document.getElementById('contractCode').focus()
      // 3秒后移除错误样式
      setTimeout(() => {
        document.getElementById('contractCode')?.classList.remove('border-red-500')
      }, 3000)
      
    } else if (error.response?.data?.errors) {
      // 处理验证错误
      const errorFields = error.response.data.errors.map(err => err.field).join('、')
      showMessageFn(`表单验证失败，请检查以下字段: ${errorFields}`, 'error')
    } else {
      // 其他错误
      showMessageFn('保存失败：' + (error.response?.data?.message || error.message), 'error')
    }
  } finally {
    loading.value = false
  }
}

// 删除分包
const deleteSubcontract = async () => {
  loading.value = true
  try {
    const id = route.params.id
    await subcontractService.deleteSubcontract(id)
    showMessageFn('分包删除成功')
    router.push('/subcontracts')
  } catch (error) {
    console.error('删除失败:', error)
    showMessageFn('删除失败：' + (error.response?.data?.message || error.message), 'error')
  } finally {
    loading.value = false
    showDeleteConfirm.value = false
  }
}

const handleFileUpload = (event) => {
  const files = event.target.files
  if (files.length > 0) {
    subcontractForm.value.attachments.push(...Array.from(files))
  }
  event.target.value = ''
}

const removeFile = (index) => {
  subcontractForm.value.attachments.splice(index, 1)
}

// 加载项目列表
const loadProjects = async () => {
  projectSearchLoading.value = true
  try {
    const response = await projectService.getProjectsForDropdown()
    projects.value = response.data || []
  } catch (error) {
    console.error('加载项目列表失败:', error)
    showMessageFn('加载项目列表失败', 'error')
  } finally {
    projectSearchLoading.value = false
  }
}

// 处理项目选择变更
const handleProjectChange = () => {
  if (subcontractForm.value.projectId) {
    // 找到选中的项目
    const selectedProject = projects.value.find(p => p.id === subcontractForm.value.projectId)
    if (selectedProject) {
      // 更新项目名称
      subcontractForm.value.projectName = selectedProject.name
    }
  }
}

onMounted(() => {
  loadProjects()
  loadSubcontractData()
})
</script>

<style scoped>
.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.page-header {
  animation: fadeSlideIn 0.5s ease-out;
}

.form-section {
  @apply mb-8;
}

.form-section-title {
  @apply text-lg font-semibold text-gray-700 mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply mt-1 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm border-gray-300 rounded-md;
}

.btn {
  @apply inline-flex justify-center py-2 px-4 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 border-transparent focus:ring-blue-500;
}

.btn-secondary {
  @apply text-gray-700 bg-white hover:bg-gray-50 border-gray-300 focus:ring-blue-500;
}

/* 添加过渡动画 */
.fixed {
  transition: all 0.3s ease-in-out;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
