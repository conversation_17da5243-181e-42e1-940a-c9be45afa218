{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:windsurf": "vite --mode windsurf", "dev:cursor": "vite --mode cursor", "dev:vscode": "vite --mode vscode", "dev:insider": "vite --mode insider", "dev:trae": "vite --mode trae", "build": "vite build", "preview": "vite preview", "lint": "echo 'No linting configured yet'", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:simple": "node run-tests.js"}, "dependencies": {"@vueuse/core": "^13.1.0", "axios": "^1.8.2", "chartjs-plugin-datalabels": "^2.2.0", "date-fns": "^4.1.0", "echarts": "^5.6.0", "file-saver": "^2.0.5", "pinia": "^2.3.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-toastification": "2.0.0-rc.5", "xlsx": "^0.18.5"}, "devDependencies": {"@playwright/test": "^1.52.0", "@tailwindcss/forms": "^0.5.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/vue": "^8.1.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.14", "jsdom": "^26.1.0", "module-alias": "^2.2.3", "postcss": "^8.4.31", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "tailwindcss": "^3.3.0", "vite": "^6.3.5", "vitest": "^3.1.1"}, "packageManager": "pnpm@9.9.0+sha512.60c18acd138bff695d339be6ad13f7e936eea6745660d4cc4a776d5247c540d0edee1a563695c183a66eb917ef88f2b4feb1fc25f32a7adcadc7aaf3438e99c1"}