<template>
  <div class="page-container">
    <!-- 消息提示 -->
    <div v-if="showMessage"
         :class="['fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300',
                 messageType === 'success' ? 'bg-green-500' : 'bg-red-500']"
         class="text-white">
      {{ messageText }}
    </div>

    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">{{ isEdit ? '编辑报销' : (isDetail ? '报销详情' : '新建报销') }}</h1>
        <p class="page-subtitle">{{ isEdit ? '修改现有报销信息' : (isDetail ? '查看报销详情' : '添加新的报销信息') }}</p>
      </div>
      <button @click="goBack" class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        返回列表
      </button>
    </div>

    <!-- 表单内容 -->
    <div class="space-y-8 bg-white p-8 rounded-lg shadow">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 基本信息 -->
        <div class="space-y-6">
          <h2 class="text-lg font-medium text-gray-900">基本信息</h2>
          <div class="space-y-4">
            <div class="space-y-2">
              <label for="project-name" class="block text-sm font-medium text-gray-700">项目名称<span class="form-required">*</span></label>
              <div class="relative">
                <select
                  v-if="!isReadOnly"
                  id="project-name"
                  v-model="reimbursementForm.projectId"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  @change="onProjectChange"
                >
                  <option value="">请选择项目</option>
                  <option v-for="project in projectOptions" :key="project.value" :value="project.value">
                    {{ project.label || project.name || project.projectName }}
                  </option>
                  <option v-if="!projectOptions || projectOptions.length === 0" disabled>加载中或暂无数据</option>
                </select>
                <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
                  {{ reimbursementForm.projectName || reimbursementForm.project?.name || '无' }}
                </div>
                <div v-if="isLoadingProjects" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              </div>
            </div>
            <div class="space-y-2">
              <label for="reimbursement-date" class="block text-sm font-medium text-gray-700">报销时间<span class="form-required">*</span></label>
              <input
                v-if="!isReadOnly"
                id="reimbursement-date"
                v-model="reimbursementForm.reimbursementDate"
                type="date"
                max="3000-12-31"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
                {{ reimbursementForm.reimbursementDate || '无' }}
              </div>
            </div>
            <div class="space-y-2">
              <label for="reimbursement-type" class="block text-sm font-medium text-gray-700">报销类型<span class="form-required">*</span></label>
              <select
                v-if="!isReadOnly"
                id="reimbursement-type"
                v-model="reimbursementForm.reimbursementType"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">请选择报销类型</option>
                <option value="travel">差旅费</option>
                <option value="office">办公费</option>
                <option value="entertainment">招待费</option>
                <option value="other">其他</option>
              </select>
              <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
                {{ reimbursementForm.reimbursementType === 'travel' ? '差旅费' :
                   reimbursementForm.reimbursementType === 'office' ? '办公费' :
                   reimbursementForm.reimbursementType === 'entertainment' ? '招待费' :
                   reimbursementForm.reimbursementType === 'other' ? '其他' : '无' }}
              </div>
            </div>
            <div class="space-y-2">
              <label for="supplier" class="block text-sm font-medium text-gray-700">供应商</label>
              <div class="relative">
                <select
                  v-if="!isReadOnly"
                  id="supplier"
                  v-model="reimbursementForm.supplierId"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  @change="onSupplierChange"
                >
                  <option value="">请选择供应商</option>
                  <option v-for="supplier in supplierOptions" :key="supplier.value" :value="supplier.value">
                    {{ supplier.label || supplier.name || supplier.supplierName }}
                  </option>
                  <option v-if="!supplierOptions || supplierOptions.length === 0" disabled>加载中或暂无数据</option>
                </select>
                <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
                  {{ reimbursementForm.supplier || reimbursementForm.supplierInfo?.name || '无' }}
                </div>
                <div v-if="isLoadingSuppliers" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                </div>
              </div>
            </div>
            <div class="space-y-2">
              <label for="reason" class="block text-sm font-medium text-gray-700">事由</label>
              <input
                v-if="!isReadOnly"
                id="reason"
                v-model="reimbursementForm.reason"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入事由"
              >
              <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
                {{ reimbursementForm.reason || '无' }}
              </div>
            </div>
            <div class="space-y-2">
              <label for="reimburser" class="block text-sm font-medium text-gray-700">报销人<span class="form-required">*</span></label>
              <select
                v-if="!isReadOnly"
                id="reimburser"
                v-model="reimbursementForm.reimburserId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">请选择报销人</option>
                <option v-for="user in userOptions" :key="user.value" :value="user.value">
                  {{ user.label }}
                </option>
              </select>
              <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
                {{ reimbursementForm.reimburser?.username || '无' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 财务信息 -->
        <div class="space-y-6">
          <h2 class="text-lg font-medium text-gray-900">财务信息</h2>
          <div class="space-y-4">
            <div class="space-y-2">
              <label for="tax-rate" class="block text-sm font-medium text-gray-700">税率<span class="form-required">*</span></label>
              <input
                v-if="!isReadOnly"
                id="tax-rate"
                v-model="reimbursementForm.taxRate"
                type="number"
                step="0.01"
                min="0"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入税率"
              >
              <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
                {{ reimbursementForm.taxRate ? Number(reimbursementForm.taxRate).toFixed(2) : '无' }}
              </div>
            </div>
            <div class="space-y-2">
              <label for="invoice-type" class="block text-sm font-medium text-gray-700">发票类型<span class="form-required">*</span></label>
              <select
                v-if="!isReadOnly"
                id="invoice-type"
                v-model="reimbursementForm.invoiceType"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">请选择发票类型</option>
                <option value="增值税专用发票">增值税专用发票</option>
                <option value="增值税普通发票">增值税普通发票</option>
                <option value="其他发票">其他发票</option>
              </select>
              <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
                {{ reimbursementForm.invoiceType || '无' }}
              </div>
            </div>
            <div class="space-y-2">
              <label for="total-amount" class="block text-sm font-medium text-gray-700">价税合计<span class="form-required">*</span></label>
              <input
                v-if="!isReadOnly"
                id="total-amount"
                v-model="reimbursementForm.totalAmount"
                type="number"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入价税合计"
              >
              <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
                {{ reimbursementForm.totalAmount ? `¥${reimbursementForm.totalAmount}` : '无' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 附件信息 -->
      <div class="mt-8 space-y-6">
        <h2 class="text-lg font-medium text-gray-900">附件信息</h2>
        <div class="space-y-4">
          <div class="flex items-center space-x-4">
            <input
              type="file"
              @change="handleFileUpload"
              class="hidden"
              ref="fileInput"
              multiple
            >
            <button
              v-if="!isReadOnly"
              @click="$refs.fileInput.click()"
              class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              上传附件
            </button>
          </div>
          <div v-if="reimbursementForm.attachments.length > 0" class="space-y-2">
            <div v-for="(file, index) in reimbursementForm.attachments" :key="index" class="flex items-center justify-between p-3 bg-gray-50 rounded-md">
              <div class="flex items-center space-x-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
                <span class="text-sm text-gray-600">{{ file.name }}</span>
              </div>
              <button
                v-if="!isReadOnly"
                @click="removeFile(index)"
                class="text-gray-400 hover:text-gray-600"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 付款信息 -->
      <div class="mt-8 space-y-6">
        <h2 class="text-lg font-medium text-gray-900">付款信息</h2>
        <div class="space-y-4">
          <div class="space-y-2">
            <label for="bank-name" class="block text-sm font-medium text-gray-700">开户银行</label>
            <input
              v-if="!isReadOnly"
              id="bank-name"
              v-model="reimbursementForm.paymentInfo.bankName"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入开户银行"
            >
            <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
              {{ reimbursementForm.paymentInfo.bankName || '无' }}
            </div>
          </div>
          <div class="space-y-2">
            <label for="account-number" class="block text-sm font-medium text-gray-700">银行账号</label>
            <input
              v-if="!isReadOnly"
              id="account-number"
              v-model="reimbursementForm.paymentInfo.accountNumber"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入银行账号"
            >
            <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
              {{ reimbursementForm.paymentInfo.accountNumber || '无' }}
            </div>
          </div>
          <div class="space-y-2">
            <label for="account-name" class="block text-sm font-medium text-gray-700">开户人</label>
            <input
              v-if="!isReadOnly"
              id="account-name"
              v-model="reimbursementForm.paymentInfo.accountName"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入开户人"
            >
            <div v-else class="w-full px-3 py-2 border border-gray-200 bg-gray-50 rounded-md text-gray-700">
              {{ reimbursementForm.paymentInfo.accountName || '无' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="flex justify-end space-x-4 border-t pt-6 mt-8">
      <button
        v-if="!isDetail || isEdit"
        @click="saveReimbursement"
        :disabled="loading"
        class="btn btn-primary flex items-center"
      >
        <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        {{ loading ? '保存中...' : '保存' }}
      </button>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteConfirm" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">确认删除</h3>
        <p class="text-gray-600 mb-6">确定要删除这条报销记录吗？此操作不可撤销。</p>
        <div class="flex justify-end space-x-3">
          <button
            @click="showDeleteConfirm = false"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            取消
          </button>
          <button
            @click="deleteReimbursement"
            class="px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors"
          >
            删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { reimbursementService } from '@/services/api.service'

const route = useRoute()
const router = useRouter()
const loading = ref(false)
const showMessage = ref(false)
const messageText = ref('')
const messageType = ref('success')
const showDeleteConfirm = ref(false)
const isEdit = ref(false)
const isDetail = ref(false) // 是否是查看详情模式
const isReadOnly = computed(() => isDetail.value && !isEdit.value) // 是否只读模式

// 项目和供应商下拉列表相关
const projectOptions = ref([])
const supplierOptions = ref([])
const isLoadingProjects = ref(false)
const isLoadingSuppliers = ref(false)

const reimbursementForm = ref({
  projectName: '',
  projectId: '',
  reimbursementDate: '',
  reimbursementType: '',
  supplier: '',
  supplierId: '',
  reason: '',
  taxRate: '',
  invoiceType: '',
  totalAmount: '',
  reimburserId: '',
  attachments: [],
  paymentInfo: {
    bankName: '',
    accountNumber: '',
    accountName: ''
  },
  // 添加报销人对象，用于显示报销人信息
  reimburser: {
    username: '',
    id: ''
  },
  // 添加项目对象，用于显示项目信息
  project: {
    id: '',
    name: '',
    code: ''
  },
  // 添加供应商对象，用于显示供应商信息
  supplierInfo: {
    id: '',
    name: '',
    category: ''
  }
})

// 用户下拉选项
const userOptions = ref([])

const goBack = () => {
  router.back()
}

const saveReimbursement = async () => {
  // 表单验证
  if (!reimbursementForm.value.projectName || !reimbursementForm.value.projectId) {
    showMessage.value = true
    messageText.value = '请选择项目'
    messageType.value = 'error'
    setTimeout(() => { showMessage.value = false }, 2000)
    return
  }

  if (!reimbursementForm.value.reimbursementDate) {
    showMessage.value = true
    messageText.value = '请选择报销时间'
    messageType.value = 'error'
    setTimeout(() => { showMessage.value = false }, 2000)
    return
  }

  if (!reimbursementForm.value.reimbursementType) {
    showMessage.value = true
    messageText.value = '请选择报销类型'
    messageType.value = 'error'
    setTimeout(() => { showMessage.value = false }, 2000)
    return
  }

  if (!reimbursementForm.value.reimburserId) {
    showMessage.value = true
    messageText.value = '请选择报销人'
    messageType.value = 'error'
    setTimeout(() => { showMessage.value = false }, 2000)
    return
  }

  if (!reimbursementForm.value.taxRate) {
    showMessage.value = true
    messageText.value = '请输入税率'
    messageType.value = 'error'
    setTimeout(() => { showMessage.value = false }, 2000)
    return
  }

  loading.value = true
  try {
    // 格式化税率为两位小数
    if (reimbursementForm.value.taxRate) {
      reimbursementForm.value.taxRate = Number(reimbursementForm.value.taxRate).toFixed(2)
    }

    // 准备表单数据
    const formData = new FormData()

    // 添加基本字段 - 只添加后端需要的字段
    const basicFields = [
      'projectName', 'projectId', 'reimbursementDate', 'reimbursementType',
      'supplier', 'supplierId', 'reason', 'taxRate', 'invoiceType',
      'totalAmount', 'reimburserId', 'status'
    ]

    basicFields.forEach(key => {
      if (reimbursementForm.value[key] !== undefined && reimbursementForm.value[key] !== null) {
        formData.append(key, reimbursementForm.value[key])
      }
    })


    // 添加付款信息
    Object.keys(reimbursementForm.value.paymentInfo).forEach(key => {
      formData.append(`paymentInfo[${key}]`, reimbursementForm.value.paymentInfo[key])
    })

    // 收集已存在的附件ID
    const existingAttachmentIds = reimbursementForm.value.attachments
      .filter(file => file.isExisting)
      .map(file => file.id)

    // 如果有已存在的附件，添加到表单
    if (existingAttachmentIds.length > 0) {
      // 在后端，existingAttachments 应该是一个数组
      // 在 FormData 中，我们需要将数组转换为 JSON 字符串
      formData.append('existingAttachments', JSON.stringify(existingAttachmentIds))
    }

    // 添加新上传的附件
    reimbursementForm.value.attachments
      .filter(file => !file.isExisting)
      .forEach(file => {
        // 对于新上传的文件，使用正确的字段名 'attachments'，与后端 multer 配置匹配
        formData.append('attachments', file)
      })

    // 发送请求
    let response
    if (isEdit.value) {
      const id = route.params.id
      response = await reimbursementService.updateReimbursement(id, formData)
    } else {
      response = await reimbursementService.createReimbursement(formData)
    }

    console.log('保存成功:', response.data)

    showMessage.value = true
    messageText.value = '保存成功'
    messageType.value = 'success'
    setTimeout(() => {
      showMessage.value = false
      router.push('/reimbursements')
    }, 2000)
  } catch (error) {
    console.error('保存失败:', error)
    showMessage.value = true
    messageText.value = error.response?.data?.details || error.response?.data?.message || '保存失败'
    messageType.value = 'error'
    setTimeout(() => {
      showMessage.value = false
    }, 2000)
  } finally {
    loading.value = false
  }
}

const deleteReimbursement = async () => {
  loading.value = true
  try {
    const id = route.params.id
    await reimbursementService.deleteReimbursement(id)

    showMessage.value = true
    messageText.value = '删除成功'
    messageType.value = 'success'
    setTimeout(() => {
      showMessage.value = false
      router.push('/reimbursements')
    }, 2000)
  } catch (error) {
    console.error('删除失败:', error)
    showMessage.value = true
    messageText.value = error.response?.data?.details || error.response?.data?.message || '删除失败'
    messageType.value = 'error'
    setTimeout(() => {
      showMessage.value = false
    }, 2000)
  } finally {
    loading.value = false
    showDeleteConfirm.value = false
  }
}

const handleFileUpload = (event) => {
  const files = event.target.files
  for (let i = 0; i < files.length; i++) {
    reimbursementForm.value.attachments.push(files[i])
  }
}

const removeFile = (index) => {
  reimbursementForm.value.attachments.splice(index, 1)
}

const loadReimbursementData = async () => {
  loading.value = true
  try {
    const id = route.params.id
    const response = await reimbursementService.getReimbursement(id)

    // 处理附件信息
    const reimbursementData = response || {}
    console.log('获取到的报销数据:', JSON.stringify(reimbursementData))



    // 附件需要特殊处理，因为我们不能直接从 API 获取文件对象
    // 这里只保存文件信息，实际文件对象需要用户重新上传
    const attachmentsInfo = reimbursementData.attachments || []

    // 确保paymentInfo对象存在
    if (!reimbursementData.paymentInfo) {
      reimbursementData.paymentInfo = {
        bankName: '',
        accountNumber: '',
        accountName: ''
      }
    }

    // 清空附件数组，稍后会重新填充
    const attachmentsCopy = [...attachmentsInfo]
    reimbursementData.attachments = []

    // 将数据应用到表单
    Object.assign(reimbursementForm.value, reimbursementData)

    // 格式化税率为两位小数
    if (reimbursementForm.value.taxRate) {
      reimbursementForm.value.taxRate = Number(reimbursementForm.value.taxRate).toFixed(2)
    }

    console.log('应用数据后的表单:', JSON.stringify(reimbursementForm.value))

    // 如果有附件信息，显示已上传的文件名称
    if (attachmentsCopy.length > 0) {
      // 将附件信息转换为类似文件对象的格式
      reimbursementForm.value.attachments = attachmentsCopy.map(info => ({
        name: info.fileName || info.name,
        id: info.id,
        url: info.url,
        // 添加一个标记来识别这是已存在的文件
        isExisting: true
      }))
    }

    // 确保下拉框选项已加载
    await Promise.all([
      loadUserOptions(),
      loadProjectOptions(),
      loadSupplierOptions()
    ])

    // 设置项目下拉框的值
    if (reimbursementForm.value.projectId) {
      // 查找对应的项目选项
      const selectedProject = projectOptions.value.find(
        option => option.value === reimbursementForm.value.projectId
      )
      if (selectedProject) {
        reimbursementForm.value.projectName = selectedProject.label || selectedProject.projectName
        console.log('设置项目名称:', reimbursementForm.value.projectName)
      }
    }

    // 设置供应商下拉框的值
    if (reimbursementForm.value.supplierId) {
      // 查找对应的供应商选项
      const selectedSupplier = supplierOptions.value.find(
        option => option.value === reimbursementForm.value.supplierId
      )
      if (selectedSupplier) {
        reimbursementForm.value.supplier = selectedSupplier.label || selectedSupplier.supplierName
        console.log('设置供应商名称:', reimbursementForm.value.supplier)
      }
    }

    // 设置报销人下拉框的值
    if (reimbursementForm.value.reimburserId) {
      // 查找对应的用户选项
      const selectedUser = userOptions.value.find(
        option => option.value === reimbursementForm.value.reimburserId
      )
      if (selectedUser) {
        reimbursementForm.value.reimburser = {
          username: selectedUser.label,
          id: selectedUser.value
        }
        console.log('设置报销人:', reimbursementForm.value.reimburser.username)
      }
    }

    console.log('数据加载完成，最终表单数据:', JSON.stringify(reimbursementForm.value))

  } catch (error) {
    console.error('加载报销数据失败:', error)
    showMessage.value = true
    messageText.value = error.response?.data?.details || error.response?.data?.message || '加载报销数据失败'
    messageType.value = 'error'
    setTimeout(() => {
      showMessage.value = false
    }, 2000)
  } finally {
    loading.value = false
  }
}

// 获取用户下拉选项
const loadUserOptions = async () => {
  try {
    const response = await reimbursementService.getUserOptions()
    console.log('用户选项响应:', response)
    console.log('用户选项原始数据:', JSON.stringify(response))

    if (response) {
      // 直接使用API返回的数据
      userOptions.value = response
      console.log('用户选项已设置:', userOptions.value)
    } else {
      userOptions.value = []
    }
  } catch (error) {
    console.error('获取用户下拉选项失败:', error)
    showMessage.value = true
    messageText.value = '获取用户列表失败，请刷新页面重试'
    messageType.value = 'error'
    setTimeout(() => { showMessage.value = false }, 2000)
    userOptions.value = []
  }
}

// 获取项目下拉选项
const loadProjectOptions = async () => {
  isLoadingProjects.value = true
  try {
    const response = await reimbursementService.getProjectOptions()
    console.log('项目选项响应:', response)
    console.log('项目选项原始数据:', JSON.stringify(response))

    if (response) {
      // 直接使用API返回的数据
      projectOptions.value = response
      console.log('项目选项已设置:', projectOptions.value)
    } else {
      projectOptions.value = []
    }
  } catch (error) {
    console.error('获取项目下拉选项失败:', error)
    showMessage.value = true
    messageText.value = '获取项目列表失败，请刷新页面重试'
    messageType.value = 'error'
    setTimeout(() => { showMessage.value = false }, 2000)
    projectOptions.value = []
  } finally {
    isLoadingProjects.value = false
  }
}

// 获取供应商下拉选项
const loadSupplierOptions = async () => {
  isLoadingSuppliers.value = true
  try {
    const response = await reimbursementService.getSupplierOptions()
    console.log('供应商选项响应:', response)
    console.log('供应商选项原始数据:', JSON.stringify(response))

    if (response) {
      // 直接使用API返回的数据
      supplierOptions.value = response
      console.log('供应商选项已设置:', supplierOptions.value)
    } else {
      supplierOptions.value = []
    }
  } catch (error) {
    console.error('获取供应商下拉选项失败:', error)
    showMessage.value = true
    messageText.value = '获取供应商列表失败，请刷新页面重试'
    messageType.value = 'error'
    setTimeout(() => { showMessage.value = false }, 2000)
    supplierOptions.value = []
  } finally {
    isLoadingSuppliers.value = false
  }
}

// 项目选择处理
const onProjectChange = () => {
  console.log('项目选择变更:', reimbursementForm.value.projectId)
  console.log('当前项目选项:', projectOptions.value)

  const selectedProject = projectOptions.value.find(project => project.value === reimbursementForm.value.projectId)
  console.log('选中的项目:', selectedProject)

  if (selectedProject) {
    reimbursementForm.value.projectName = selectedProject.label || selectedProject.name || selectedProject.projectName

    // 更新项目对象
    if (reimbursementForm.value.project) {
      reimbursementForm.value.project.id = selectedProject.value
      reimbursementForm.value.project.name = selectedProject.projectName || selectedProject.name
      reimbursementForm.value.project.code = selectedProject.code
    }

    console.log('设置项目名称:', reimbursementForm.value.projectName)
  } else {
    reimbursementForm.value.projectName = ''
  }
}

// 供应商选择处理
const onSupplierChange = () => {
  console.log('供应商选择变更:', reimbursementForm.value.supplierId)
  console.log('当前供应商选项:', supplierOptions.value)

  const selectedSupplier = supplierOptions.value.find(supplier => supplier.value === reimbursementForm.value.supplierId)
  console.log('选中的供应商:', selectedSupplier)

  if (selectedSupplier) {
    reimbursementForm.value.supplier = selectedSupplier.label || selectedSupplier.name || selectedSupplier.supplierName

    // 更新供应商对象
    if (reimbursementForm.value.supplierInfo) {
      reimbursementForm.value.supplierInfo.id = selectedSupplier.value
      reimbursementForm.value.supplierInfo.name = selectedSupplier.supplierName || selectedSupplier.name
      reimbursementForm.value.supplierInfo.category = selectedSupplier.category
    }

    console.log('设置供应商名称:', reimbursementForm.value.supplier)
  } else {
    reimbursementForm.value.supplier = ''
  }
}

// 监听项目ID变化
watch(() => reimbursementForm.value.projectId, (newVal) => {
  if (newVal) {
    onProjectChange()
  }
})

// 监听供应商ID变化
watch(() => reimbursementForm.value.supplierId, (newVal) => {
  if (newVal) {
    onSupplierChange()
  }
})

// 监听路由变化
watch(
  () => route.path,
  async (newPath) => {
    // 判断是否是编辑模式或查看详情模式
    isEdit.value = newPath.includes('/edit')
    isDetail.value = route.params.id && !newPath.includes('/create') && !newPath.includes('/edit')

    console.log('路由路径变化:', newPath)
    console.log('路由参数:', route.params)
    console.log('是否编辑模式:', isEdit.value)
    console.log('是否详情模式:', isDetail.value)

    // 如果是编辑模式或查看详情模式，加载报销数据
    if (isEdit.value || isDetail.value) {
      await loadReimbursementData()
    }
  }
)

onMounted(async () => {
  // 判断是否是编辑模式或查看详情模式
  isEdit.value = route.path.includes('/edit')
  isDetail.value = route.params.id && !route.path.includes('/create') && !route.path.includes('/edit')

  console.log('路由路径:', route.path)
  console.log('路由参数:', route.params)
  console.log('是否编辑模式:', isEdit.value)
  console.log('是否详情模式:', isDetail.value)

  // 获取用户下拉选项
  await loadUserOptions()

  // 获取项目和供应商下拉选项
  await Promise.all([
    loadProjectOptions(),
    loadSupplierOptions()
  ])

  // 如果是编辑模式或查看详情模式，加载报销数据
  if (isEdit.value || isDetail.value) {
    await loadReimbursementData()
  }
})
</script>

<style scoped>
/* 添加过渡动画 */
.fixed {
  transition: all 0.3s ease-in-out;
}
.page-title {
  @apply text-2xl font-bold text-gray-800;
}
.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}
.page-header {
  @apply flex justify-between items-center mb-6;
  animation: fadeSlideIn 0.5s ease-out;
}
.btn {
  @apply inline-flex justify-center py-2 px-4 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2;
}
.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 border-transparent focus:ring-blue-500;
}
.btn-secondary {
  @apply text-gray-700 bg-white hover:bg-gray-50 border-gray-300 focus:ring-blue-500;
}
</style>