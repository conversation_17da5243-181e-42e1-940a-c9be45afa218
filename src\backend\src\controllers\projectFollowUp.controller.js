const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { projectFollowUpService } = require('../services');

/**
 * Create a new project follow-up
 * @route POST /api/projects/:projectId/follow-ups
 */
const createProjectFollowUp = catchAsync(async (req, res) => {
  const followUp = await projectFollowUpService.createProjectFollowUp({
    ...req.body,
    projectId: req.params.projectId,
    createdBy: req.user.id
  });
  res.status(201).send(followUp);
});

/**
 * Get a project follow-up by ID
 * @route GET /api/projects/follow-ups/:followUpId
 */
const getProjectFollowUp = catchAsync(async (req, res) => {
  const followUp = await projectFollowUpService.getProjectFollowUpById(req.params.followUpId);
  res.send(followUp);
});

/**
 * Update a project follow-up
 * @route PATCH /api/projects/follow-ups/:followUpId
 */
const updateProjectFollowUp = catchAsync(async (req, res) => {
  const followUp = await projectFollowUpService.updateProjectFollowUpById(req.params.followUpId, req.body);
  res.send(followUp);
});

/**
 * Delete a project follow-up
 * @route DELETE /api/projects/follow-ups/:followUpId
 */
const deleteProjectFollowUp = catchAsync(async (req, res) => {
  await projectFollowUpService.deleteProjectFollowUpById(req.params.followUpId);
  // 使用明确的数字状态码，避免可能的undefined问题
  res.status(204).send();
});

/**
 * Get all follow-ups for a project
 * @route GET /api/projects/:projectId/follow-ups
 */
const getProjectFollowUps = catchAsync(async (req, res) => {
  const filter = {
    projectId: req.params.projectId,
    startDate: req.query.startDate,
    endDate: req.query.endDate
  };
  const options = {
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10,
    sortBy: req.query.sortBy || 'followUpDate',
    sortOrder: req.query.sortOrder || 'desc'
  };
  const result = await projectFollowUpService.queryProjectFollowUps(filter, options);
  res.send(result);
});

module.exports = {
  createProjectFollowUp,
  getProjectFollowUp,
  updateProjectFollowUp,
  deleteProjectFollowUp,
  getProjectFollowUps
};