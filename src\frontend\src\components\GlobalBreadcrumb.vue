<template>
  <nav class="global-breadcrumb-container">
    <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
      <ol class="global-breadcrumb-list">
        <li class="breadcrumb-item">
          <router-link to="/dashboard" class="breadcrumb-link home">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h2a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1h3"></path>
            </svg>
          </router-link>
        </li>
        <template v-for="(item, index) in breadcrumbItems" :key="index">
          <li class="breadcrumb-separator" aria-hidden="true">
            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </li>
          <li class="breadcrumb-item">
            <router-link v-if="item.path && index !== breadcrumbItems.length - 1" :to="item.path" class="breadcrumb-link">
              {{ item.name }}
            </router-link>
            <span v-else class="breadcrumb-current">{{ item.name }}</span>
          </li>
        </template>
        <li style="float:right;"></li>
      </ol>
    </div>
  </nav>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import clientStore from '@/stores/clientStore';

const route = useRoute();

// 用于强制重新计算面包屑
const updateCounter = ref(0);

// 监听客户名称变化
watch(() => clientStore.clientName.value, (newName) => {
  if (newName) {
    updateCounter.value++;
    console.log('客户名称已更新，面包屑将重新计算:', newName);
  }
});

// 根据当前路由自动生成面包屑导航
// 使用updateCounter作为依赖项，强制重新计算
const breadcrumbItems = computed(() => {
  // 读取计数器值，使其成为依赖项
  const _ = updateCounter.value;
  // 读取客户名称，使其成为依赖项
  const clientName = clientStore.clientName.value;
  const items = [];
  const pathSegments = route.path.split('/').filter(Boolean);

  // 路由路径映射表，定义各级路径对应的名称
  const breadcrumbConfig = {
    // 项目相关
    projects: { name: '项目管理', path: '/projects' },
    'projects/create': { name: '新建项目', path: null },
    'projects/:id': { name: '编辑项目', path: null },
    'projects/:id/edit': { name: '编辑项目', path: null },
    'projects/stats': { name: '项目统计', path: null },

    // 客户相关
    clients: { name: '客户管理', path: '/clients' },
    'clients/create': { name: '新建客户', path: null },
    'clients/:id': {
      name: () => {
        // 使用客户存储中的客户名称
        return 'asdf'
        const name = clientStore.clientName.value;
        if (name) return name;
        // 如果没有客户名称，则返回默认标题
        return route.name === 'ClientEdit' ? '编辑客户' : '客户详情';
      },
      path: null
    },
    'clients/:id/edit': {
      name: () => {
        return 'bbbb'
        // 使用客户存储中的客户名称
        const name = clientStore.clientName.value;
        if (name) return `编辑：${name}`;
        return '编辑客户';
      },
      path: null
    },

    // 供应商相关
    suppliers: { name: '供应商管理', path: '/suppliers' },
    'suppliers/create': { name: '新建供应商', path: null },
    'suppliers/:id': { name: '编辑供应商', path: null },
    'suppliers/:id/edit': { name: '编辑供应商', path: null },

    // 文档相关
    documents: { name: '文档管理', path: '/documents' },
    'documents/create': { name: '新建文档', path: null },
    'documents/:id': { name: '编辑文档', path: null },
    'documents/:id/edit': { name: '编辑文档', path: null },

    // 财务相关
    finance: { name: '财务管理', path: '/finance' },
    'finance/create': { name: '新建财务记录', path: null },
    'finance/:id': { name: '编辑财务记录', path: null },
    'finance/:id/edit': { name: '编辑财务记录', path: null },
    'finance/stats': { name: '财务统计', path: null },

    // 合同相关
    contracts: { name: '合同管理', path: '/contracts' },
    'contracts/create': { name: '新建合同', path: null },
    'contracts/:id': { name: '编辑合同', path: null },
    'contracts/:id/edit': { name: '编辑合同', path: null },

    // 分包相关
    subcontracts: { name: '分包管理', path: '/subcontracts' },
    'subcontracts/create': { name: '新建分包', path: null },
    'subcontracts/:id': { name: '编辑分包', path: null },
    'subcontracts/:id/edit': { name: '编辑分包', path: null },

    // 采购相关
    purchases: { name: '采购管理', path: '/purchases' },
    'purchases/create': { name: '新建采购', path: null },
    'purchases/:id': { name: '编辑采购', path: null },
    'purchases/:id/edit': { name: '编辑采购', path: null },

    // 产品相关
    products: { name: '产品管理', path: '/products' },
    'products/create': { name: '新建产品', path: null },
    'products/:id': { name: '编辑产品', path: null },
    'products/:id/edit': { name: '编辑产品', path: null },
    'products/stats': { name: '产品统计', path: null },

    // 库存相关
    inventory: { name: '库存管理', path: '/inventory' },
    'inventory/create': { name: '新建库存', path: null },
    'inventory/:id': { name: '编辑库存', path: null },
    'inventory/:id/edit': { name: '编辑库存', path: null },

    // 考勤相关
    attendance: { name: '考勤管理', path: '/attendance' },
    'attendance/create': { name: '新建考勤', path: null },
    'attendance/:id': { name: '编辑考勤', path: null },
    'attendance/:id/edit': { name: '编辑考勤', path: null },

    // 员工相关
    employees: { name: '员工管理', path: '/employees' },
    'employees/contract': { name: '合同工管理', path: '/employees/contract' },
    'employees/temporary': { name: '临时工管理', path: '/employees/temporary' },

    // 分类相关
    categories: { name: '分类管理', path: '/categories' },

    // 这里移除了未使用的页面配置

    // 用户相关
    users: { name: '用户管理', path: '/users' },
    'users/:id': { name: '编辑用户', path: null },
    'users/:id/edit': { name: '编辑用户', path: null },

    // 管理员相关
    'admin/users': { name: '用户管理', path: '/admin/users' },
    'admin/users/:id': { name: '编辑用户', path: null },
    'admin/roles': { name: '角色管理', path: '/admin/roles' },

    // 设置相关
    settings: { name: '系统设置', path: '/settings' },

    // 仪表盘
    dashboard: { name: '仪表盘', path: '/dashboard' },

    // 报销相关
    reimbursements: { name: '报销管理', path: '/reimbursements' },
    'reimbursements/create': { name: '新建报销', path: null },
    'reimbursements/:id': { name: '编辑报销', path: null },
    'reimbursements/:id/edit': { name: '编辑报销', path: null },

    // 工时相关
    workhours: { name: '工时管理', path: '/workhours' },
    'workhours/calendar': { name: '工时日历', path: '/workhours/calendar' },
    'workhours/statistics': { name: '工时统计', path: '/workhours/statistics' },
  };

  // 构建面包屑路径
  let currentPath = '';
  for (let i = 0; i < pathSegments.length; i++) {
    const segment = pathSegments[i];
    currentPath += `/${segment}`;

    // 处理新建页面
    if (segment === 'create' && i > 0) {
      const parentSegment = pathSegments[i-1];
      const parentNameSingular = getEntityNameSingular(parentSegment);
      items.push({
        name: `新建${parentNameSingular}`,
        path: null
      });
      continue;
    }

    // 处理编辑页面 - 跳过编辑路径段
    if (segment === 'edit' && i > 0) {
      continue;
    }

    // 如果是ID参数（数字或带前缀的ID）
    if (/^\d+$/.test(segment) || /^[0-9a-f-]{24,}$/i.test(segment)) {
      const parentSegment = pathSegments[i-1];
      if (parentSegment === 'clients') {
        // 客户详情页面特殊处理
        let name = route.meta?.title;
        if (!name) {
          // 根据路由名称设置默认标题

        switch (route.name) {
            case 'ClientEdit':
              name = '编辑客户';
              break;
            case 'ClientDetail':
              name = '编辑客户';
              break;
            default:
              name = '编辑客户';
          }
        }
        items.push({
          name: name,
          path: null
        });
      } else if (parentSegment) {
        const parentNameSingular = getEntityNameSingular(parentSegment);
        items.push({
          name: `编辑${parentNameSingular}`,
          path: null
        });
      }
      continue;
    }

    // 如果路由映射表中存在当前段，则添加到面包屑中
    if (breadcrumbConfig[segment]) {
      const config = breadcrumbConfig[segment];
      const name = typeof config.name === 'function' ? config.name(route) : config.name;
      items.push({
        name: name,
        path: i === pathSegments.length - 1 ? null : config.path
      });
    } else {
      // 未在映射表中找到，使用路径段的首字母大写形式
      items.push({
        name: segment.charAt(0).toUpperCase() + segment.slice(1),
        path: i === pathSegments.length - 1 ? null : currentPath
      });
    }
  }

  return items;
});

// 获取实体名称的单数形式，用于详情页面标题
function getEntityNameSingular(entityName) {
  const entityMap = {
    inventory: '产品',
    documents: '文档',
    categories: '分类',
    clients: '客户',
    projects: '项目',
    suppliers: '供应商',
    products: '产品',
    purchases: '采购单',
    finance: '财务记录',
    contracts: '合同',
    attendance: '考勤记录',
    users: '用户'
  };

  return entityMap[entityName] || entityName;
}
</script>

<style scoped>
.global-breadcrumb-container {
  @apply bg-gray-50 border-b border-gray-200;
}

.global-breadcrumb-list {
  @apply flex items-center h-10 text-sm font-medium;
}

.breadcrumb-item {
  @apply flex items-center;
}

.breadcrumb-separator {
  @apply mx-1;
}

.breadcrumb-link {
  @apply text-blue-600 hover:text-blue-800 transition-colors duration-200;
}

.breadcrumb-link.home {
  @apply text-gray-500 hover:text-gray-700;
}

.breadcrumb-current {
  @apply text-gray-600 font-semibold;
}
</style>