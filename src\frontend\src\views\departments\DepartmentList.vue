<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-800">部门管理</h1>
      <router-link
        to="/departments/create"
        class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md"
      >
        新建部门
      </router-link>
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center items-center py-10">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error message -->
    <div v-else-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
      <button @click="retryFetchDepartments" class="mt-3 text-blue-600 underline">重试</button>
    </div>

    <!-- Delete error message -->
    <div v-if="deleteError" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ deleteError }}</p>
    </div>

    <!-- Department hierarchy view -->
    <div v-else-if="viewMode === 'hierarchy'" class="mb-6">
      <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="p-4 border-b">
          <div class="flex justify-between items-center">
            <h2 class="text-lg font-semibold">部门架构</h2>
            <button
              @click="viewMode = 'list'"
              class="text-blue-600 hover:text-blue-800 flex items-center"
            >
              <span class="mr-1">列表视图</span>
              <i class="fas fa-list"></i>
            </button>
          </div>
        </div>
        <div class="p-4">
          <div v-if="departmentHierarchy.length === 0" class="text-center py-6 text-gray-500">
            没有部门数据
          </div>
          <div v-else class="department-tree">
            <div v-for="dept in departmentHierarchy" :key="dept.id" class="mb-4">
              <DepartmentTreeNode :department="dept" @view="viewDepartment" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Department list view -->
    <div v-else class="mb-6">
      <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="p-4 border-b">
 
          <div class="mt-3">
            <div class="flex justify-between gap-4">
              <div class="flex-1">
                <div class="relative">
                  <input
                    v-model="searchQuery"
                    type="text"
                    placeholder="搜索部门名称、代码..."
                    class="form-input pl-10 py-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                  />
                  <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                </div>
              </div>
              <div class="flex items-end">
                <button @click="searchDepartments" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200">查询</button>
                <button @click="resetSearch" class="btn btn-secondary ml-2 shadow-sm hover:shadow transition-all duration-200">重置</button>
              </div>
            </div>
          </div>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  名称
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  代码
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  上级部门
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  员工数量
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-if="filteredDepartments.length === 0">
                <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                  没有找到部门数据
                </td>
              </tr>
              <tr v-for="department in filteredDepartments" :key="department.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900">{{ department.name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">{{ department.code }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">
                    {{ department.parentDepartment ? department.parentDepartment.name : '无' }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-500">
                    {{ department.employees ? department.employees.length : 0 }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    :class="{
                      'px-2 py-1 text-xs rounded-full': true,
                      'bg-green-100 text-green-800': department.status === 'active',
                      'bg-red-100 text-red-800': department.status === 'inactive'
                    }"
                  >
                    {{ department.status === 'active' ? '活跃' : '禁用' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div class="flex space-x-2">

                    <router-link
                      :to="`/departments/${department.id}/edit`"
                      class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                    >
                      <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                      </svg>
                      <span class="font-medium">编辑</span>
                    </router-link>
                    <button
                      @click="confirmDelete(department)"
                      class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                    >
                      <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                      </svg>
                      <span class="font-medium">删除</span>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Delete confirmation modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-8 max-w-md w-full">
        <h3 class="text-xl font-medium mb-4">确认删除部门</h3>
        <p class="mb-6">
          尊敬的用户，您确定要删除部门 <span class="font-bold">{{ departmentToDelete?.name }}</span> 吗？
          <br><br>
          <span class="text-red-600 font-medium">请您注意以下重要事项：</span>
          <br>
          · 此操作执行后将<span class="font-medium">无法恢复</span><br>
          · 相关的部门数据将被<span class="font-medium">永久删除</span><br>
          · 删除部门可能会影响组织架构和员工归属关系<br>
          · 建议在删除前确保部门内没有员工和子部门<br>
          <br>
          请确认此部门确实不再需要保留后再进行删除操作。
        </p>
        <div class="flex justify-end space-x-4">
          <button
            @click="showDeleteModal = false"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
          >
            取消
          </button>
          <button
            @click="deleteDepartment"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useDepartmentStore } from '@/stores/department';
import DepartmentTreeNode from '@/components/departments/DepartmentTreeNode.vue';
import { useToast } from 'vue-toastification';

const departmentStore = useDepartmentStore();
const router = useRouter();
const toast = useToast();

// State
const viewMode = ref('list'); // 'list' or 'hierarchy'
const searchQuery = ref('');
const showDeleteModal = ref(false);
const departmentToDelete = ref(null);
const deleteError = ref('');

// Computed properties
const departments = computed(() => departmentStore.getDepartments);
const departmentHierarchy = computed(() => departmentStore.getDepartmentHierarchy);
const loading = computed(() => departmentStore.isLoading);
const error = computed(() => departmentStore.getError);

const filteredDepartments = computed(() => {
  if (!searchQuery.value) return departments.value;

  const query = searchQuery.value.toLowerCase();
  return departments.value.filter(dept =>
    dept.name.toLowerCase().includes(query) ||
    dept.code.toLowerCase().includes(query) ||
    (dept.parentDepartment && dept.parentDepartment.name.toLowerCase().includes(query))
  );
});

// Methods
async function fetchData() {
  await departmentStore.fetchDepartments();
  if (viewMode.value === 'hierarchy') {
    await departmentStore.fetchDepartmentHierarchy();
  }
}

function retryFetchDepartments() {
  departmentStore.resetError();
  fetchData();
}

async function switchToHierarchyView() {
  viewMode.value = 'hierarchy';
  if (departmentHierarchy.value.length === 0) {
    await departmentStore.fetchDepartmentHierarchy();
  }
}

function viewDepartment(id) {
  router.push(`/departments/${id}`);
}

function confirmDelete(department) {
  departmentToDelete.value = department;
  showDeleteModal.value = true;
}

async function deleteDepartment() {
  try {
    const departmentName = departmentToDelete.value.name;
    await departmentStore.deleteDepartment(departmentToDelete.value.id);
    showDeleteModal.value = false;
    
    // 显示删除成功的提示信息
    toast.success(`部门 ${departmentName} 删除成功`);
    
    departmentToDelete.value = null;

    // Refresh hierarchy if in hierarchy view
    if (viewMode.value === 'hierarchy') {
      await departmentStore.fetchDepartmentHierarchy();
    }
  } catch (error) {
    console.error('Failed to delete department:', error);
    deleteError.value = '很抱歉，删除部门时遇到了问题。\n\n可能的原因：\n· 该部门下仍有员工，需要先将员工调离此部门\n· 该部门下存在子部门，需要先删除所有子部门\n· 该部门与其他业务数据有关联\n· 系统临时故障\n\n建议操作：\n1. 确保部门内没有员工和子部门\n2. 检查是否有关联的业务数据\n3. 稍后再次尝试删除\n\n如果问题持续存在，请联系系统管理员获取帮助。我们将尽快为您解决此问题。';
    
    // 显示删除失败的提示信息
    toast.error(error.response?.data?.message || '删除部门失败');

    // 5秒后自动清除错误信息
    setTimeout(() => {
      deleteError.value = '';
    }, 5000);
  }
}

// Lifecycle hooks
onMounted(() => {
  fetchData();
});
</script>

<style scoped>
.btn {
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  border: 1px solid #d1d5db;
  color: #374151;
}

.btn-secondary:hover {
  background-color: #f3f4f6;
}

.form-input {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.form-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}
</style>

<style scoped>
.department-tree {
  margin-left: 0.5rem;
}
</style>