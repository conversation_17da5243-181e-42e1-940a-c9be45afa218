const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { ProjectProgress, Project, User } = require('../models');

/**
 * Create a project progress entry
 * @param {Object} progressBody
 * @returns {Promise<ProjectProgress>}
 */
const createProjectProgress = async (progressBody) => {
  // Check if project exists
  const project = await Project.findByPk(progressBody.projectId);
  if (!project) {
    throw new ApiError(400, 'Project not found');
  }

  return ProjectProgress.create(progressBody);
};

/**
 * Get project progress by id
 * @param {string} id
 * @returns {Promise<ProjectProgress>}
 */
const getProjectProgressById = async (id) => {
  const progress = await ProjectProgress.findByPk(id, {
    include: [
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: User,
        as: 'UpdatedBy',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!progress) {
    throw new ApiError(404, 'Project progress not found');
  }
  return progress;
};

/**
 * Update project progress by id
 * @param {string} progressId
 * @param {Object} updateBody
 * @returns {Promise<ProjectProgress>}
 */
const updateProjectProgressById = async (progressId, updateBody) => {
  const progress = await getProjectProgressById(progressId);
  
  Object.assign(progress, updateBody);
  await progress.save();
  return progress;
};

/**
 * Delete project progress by id
 * @param {string} progressId
 * @returns {Promise<void>}
 */
const deleteProjectProgressById = async (progressId) => {
  const progress = await getProjectProgressById(progressId);
  await progress.destroy();
};

/**
 * Query for project progress entries
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Progress entries and pagination info
 */
const queryProjectProgress = async (filter, options) => {
  const { page, limit, sortBy, sortOrder } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};
  
  if (filter.projectId) {
    whereClause.projectId = filter.projectId;
  }
  
  if (filter.stage) {
    whereClause.stage = filter.stage;
  }
  
  if (filter.delayStatus !== undefined) {
    whereClause.delayStatus = filter.delayStatus;
  }

  // Query with pagination
  const { count, rows } = await ProjectProgress.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'UpdatedBy',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ],
    order: [[sortBy, sortOrder]],
    limit,
    offset
  });

  return {
    results: rows,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

module.exports = {
  createProjectProgress,
  getProjectProgressById,
  updateProjectProgressById,
  deleteProjectProgressById,
  queryProjectProgress
}; 