const { sequelize } = require('./src/models');
const bcrypt = require('bcryptjs');

async function updateTestUser() {
  try {
    // 生成密码哈希
    const hash = await bcrypt.hash('Test@123', 8);
    
    // 更新测试用户的密码
    const result = await sequelize.query(
      'UPDATE "user" SET password = :password WHERE email = :email',
      {
        replacements: { 
          password: hash,
          email: '<EMAIL>'
        },
        type: sequelize.QueryTypes.UPDATE
      }
    );
    
    console.log('Password updated successfully');
    console.log('Rows affected:', result[1]);
    
    // 关闭数据库连接
    await sequelize.close();
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

updateTestUser();
