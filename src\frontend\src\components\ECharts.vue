<template>
  <div ref="chartRef" :style="{ height: height, width: width }"></div>
</template>

<script setup>
import { ref, onMounted, watch, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

const props = defineProps({
  option: {
    type: Object,
    required: true
  },
  height: {
    type: String,
    default: '300px'
  },
  width: {
    type: String,
    default: '100%'
  }
})

const chartRef = ref(null)
let chart = null

const initChart = () => {
  if (chartRef.value) {
    // 如果图表已经存在，先销毁
    if (chart) {
      chart.dispose()
    }
    
    // 创建新图表
    chart = echarts.init(chartRef.value)
    
    // 设置图表选项
    console.log('设置图表选项:', props.option)
    chart.setOption(props.option)
  }
}

const resizeChart = () => {
  if (chart) {
    chart.resize()
  }
}

onMounted(() => {
  // 等待 DOM 更新后再初始化图表
  nextTick(() => {
    initChart()
    window.addEventListener('resize', resizeChart)
  })
})

watch(() => props.option, (newOption) => {
  console.log('选项变化，更新图表:', newOption)
  if (chart) {
    chart.setOption(newOption)
  }
}, { deep: true })

onUnmounted(() => {
  if (chart) {
    chart.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})
</script> 