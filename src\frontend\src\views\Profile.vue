<template>
  <div class="max-w-5xl mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-8">个人信息设置</h1>

    <div class="grid gap-8 md:grid-cols-3">
      <!-- Sidebar Navigation -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="p-6 border-b border-gray-100">
          <div class="flex items-center">
            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 text-white rounded-full flex items-center justify-center text-2xl font-bold shadow-inner">
              {{ userInitials }}
            </div>
            <div class="ml-4">
              <div class="font-semibold text-lg text-gray-900">{{ authStore.user?.name }}</div>
              <div class="text-sm text-gray-500">{{ authStore.user?.email }}</div>
            </div>
          </div>
        </div>
        <nav class="p-2">
          <button
            @click="activeTab = 'profile'"
            :class="['w-full text-left px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200',
              activeTab === 'profile' ? 'bg-blue-50 text-blue-700 shadow-sm' : 'text-gray-700 hover:bg-gray-50']"
          >
            基本信息
          </button>
          <button
            @click="activeTab = 'password'"
            :class="['w-full text-left px-4 py-3 rounded-lg text-sm font-medium transition-colors duration-200',
              activeTab === 'password' ? 'bg-blue-50 text-blue-700 shadow-sm' : 'text-gray-700 hover:bg-gray-50']"
          >
            修改密码
          </button>

        </nav>
      </div>

      <!-- Main Content Area -->
      <div class="md:col-span-2">
        <!-- Personal Information Form -->
        <div v-show="activeTab === 'profile'" class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">基本信息</h2>

          <div v-if="profileStatus" :class="['mb-6 p-4 rounded-lg',
            profileStatus.type === 'error' ? 'bg-red-50 text-red-700 border border-red-100' : 'bg-green-50 text-green-700 border border-green-100']">
            {{ profileStatus.message }}
          </div>

          <form @submit.prevent="updateProfileInfo" class="space-y-6">
            <div class="space-y-4">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">姓名 <span class="text-red-500">*</span></label>
                <input
                  id="name"
                  v-model="profileForm.name"
                  type="text"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  :disabled="isUpdating"
                />
                <div v-if="profileErrors.name" class="mt-1 text-sm text-red-600">{{ profileErrors.name }}</div>
              </div>

              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">邮箱地址</label>
                <input
                  id="email"
                  v-model="profileForm.email"
                  type="email"
                  class="w-full px-4 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-500"
                  disabled
                />
                <div class="mt-1 text-xs text-gray-500">邮箱地址无法修改</div>
              </div>

              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">电话 (选填)</label>
                <input
                  id="phone"
                  v-model="profileForm.phone"
                  type="tel"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  :disabled="isUpdating"
                />
              </div>

              <div>
                <label for="jobTitle" class="block text-sm font-medium text-gray-700 mb-1">职位 (选填)</label>
                <input
                  id="jobTitle"
                  v-model="profileForm.jobTitle"
                  type="text"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  :disabled="isUpdating"
                />
              </div>

              <div>
                <label for="department" class="block text-sm font-medium text-gray-700 mb-1">部门 (选填)</label>
                <input
                  id="department"
                  v-model="profileForm.department"
                  type="text"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  :disabled="isUpdating"
                />
              </div>
            </div>

            <div class="pt-4">
              <button
                type="submit"
                class="inline-flex items-center px-6 py-2.5 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="isUpdating"
              >
                <span v-if="isUpdating" class="flex items-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  更新中...
                </span>
                <span v-else>保存更改</span>
              </button>
            </div>
          </form>
        </div>

        <!-- Change Password Form -->
        <div v-show="activeTab === 'password'" class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">修改密码</h2>

          <div v-if="passwordStatus" :class="['mb-6 p-4 rounded-lg',
            passwordStatus.type === 'error' ? 'bg-red-50 text-red-700 border border-red-100' : 'bg-green-50 text-green-700 border border-green-100']">
            {{ passwordStatus.message }}
          </div>

          <form @submit.prevent="updatePassword" class="space-y-6">
            <div class="space-y-4">
              <div>
                <label for="currentPassword" class="block text-sm font-medium text-gray-700 mb-1">当前密码 <span class="text-red-500">*</span></label>
                <input
                  id="currentPassword"
                  v-model="passwordForm.currentPassword"
                  type="password"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  :disabled="isUpdating"
                  required
                />
                <div v-if="passwordErrors.currentPassword" class="mt-1 text-sm text-red-600">
                  {{ passwordErrors.currentPassword }}
                </div>
              </div>

              <div>
                <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-1">新密码 <span class="text-red-500">*</span></label>
                <input
                  id="newPassword"
                  v-model="passwordForm.newPassword"
                  type="password"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  :disabled="isUpdating"
                  required
                />
                <div v-if="passwordErrors.newPassword" class="mt-1 text-sm text-red-600">
                  {{ passwordErrors.newPassword }}
                </div>
                <div class="mt-1 text-xs text-gray-500">
                  密码必须至少8个字符，并包含大写字母、小写字母和数字
                </div>
              </div>

              <div>
                <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">确认新密码 <span class="text-red-500">*</span></label>
                <input
                  id="confirmPassword"
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                  :disabled="isUpdating"
                  required
                />
                <div v-if="passwordErrors.confirmPassword" class="mt-1 text-sm text-red-600">
                  {{ passwordErrors.confirmPassword }}
                </div>
              </div>
            </div>

            <div class="pt-4">
              <button
                type="submit"
                class="inline-flex items-center px-6 py-2.5 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="isUpdating"
              >
                <span v-if="isUpdating" class="flex items-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  更新中...
                </span>
                <span v-else>修改密码</span>
              </button>
            </div>
          </form>
        </div>

        <!-- Preferences Form -->
        <div v-show="activeTab === 'preferences'" class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-6">系统偏好设置</h2>

          <div v-if="prefStatus" :class="['mb-6 p-4 rounded-lg',
            prefStatus.type === 'error' ? 'bg-red-50 text-red-700 border border-red-100' : 'bg-green-50 text-green-700 border border-green-100']">
            {{ prefStatus.message }}
          </div>

          <form @submit.prevent="updatePreferences" class="space-y-6">
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-3">邮件通知</label>
                <div class="space-y-3">
                  <div class="flex items-center">
                    <input
                      id="notifyDocuments"
                      v-model="preferencesForm.notifyDocuments"
                      type="checkbox"
                      class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors duration-200"
                      :disabled="isUpdating"
                    />
                    <label for="notifyDocuments" class="ml-3 block text-sm text-gray-700">
                      文档添加或更新时通知我
                    </label>
                  </div>

                  <div class="flex items-center">
                    <input
                      id="notifyComments"
                      v-model="preferencesForm.notifyComments"
                      type="checkbox"
                      class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors duration-200"
                      :disabled="isUpdating"
                    />
                    <label for="notifyComments" class="ml-3 block text-sm text-gray-700">
                      有新评论时通知我
                    </label>
                  </div>
                </div>
              </div>

              <div>
                <label for="defaultCategory" class="block text-sm font-medium text-gray-700 mb-1">默认分类</label>
                <select
                  id="defaultCategory"
                  v-model="preferencesForm.defaultCategory"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white"
                  :disabled="isUpdating"
                >
                  <option value="">无默认分类</option>
                  <option v-for="category in categories" :key="category.id" :value="category.id">
                    {{ category.name }}
                  </option>
                </select>
                <div class="mt-1 text-xs text-gray-500">设置默认分类，方便快速筛选内容</div>
              </div>

              <div>
                <label for="itemsPerPage" class="block text-sm font-medium text-gray-700 mb-1">每页显示数量</label>
                <select
                  id="itemsPerPage"
                  v-model="preferencesForm.itemsPerPage"
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 bg-white"
                  :disabled="isUpdating"
                >
                  <option value="10">10 条/页</option>
                  <option value="20">20 条/页</option>
                  <option value="50">50 条/页</option>
                  <option value="100">100 条/页</option>
                </select>
                <div class="mt-1 text-xs text-gray-500">设置列表页面每页显示的数据条数</div>
              </div>
            </div>

            <div class="pt-4">
              <button
                type="submit"
                class="inline-flex items-center px-6 py-2.5 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="isUpdating"
              >
                <span v-if="isUpdating" class="flex items-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  更新中...
                </span>
                <span v-else>保存偏好设置</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useAuthStore } from '../stores/auth';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';

const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const activeTab = ref('profile');
const isUpdating = ref(false);
const categories = ref([]);

// Status messages for each form
const profileStatus = ref(null);
const passwordStatus = ref(null);
const prefStatus = ref(null);

// Error objects for form validation
const profileErrors = reactive({});
const passwordErrors = reactive({});

// Profile Form
const profileForm = reactive({
  name: authStore.user?.name || '',
  email: authStore.user?.email || '',
  phone: authStore.user?.phone || '',
  jobTitle: authStore.user?.jobTitle || '',
  department: authStore.user?.department || ''
});

// Password Form
const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// Preferences Form
const preferencesForm = reactive({
  notifyDocuments: true,
  notifyComments: true,
  defaultCategory: '',
  itemsPerPage: '20'
});

// Computed properties
const userInitials = computed(() => {
  const user = authStore.user;
  if (!user || !user.name) return '?';

  // Get first character of first name and last name (if available)
  const names = user.name.split(' ');
  if (names.length === 1) return names[0].charAt(0).toUpperCase();
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
});

// Load user data and preferences
onMounted(async () => {
  // Check for tab parameter and set active tab
  if (route.query.tab && ['profile', 'password', 'preferences'].includes(route.query.tab)) {
    activeTab.value = route.query.tab;
  }

  if (authStore.user) {
    profileForm.name = authStore.user.name || '';
    profileForm.email = authStore.user.email || '';
    profileForm.phone = authStore.user.phone || '';
    profileForm.jobTitle = authStore.user.jobTitle || '';
    profileForm.department = authStore.user.department || '';
  }

  try {
    // Load preferences
    const prefsResponse = await axios.get('/api/users/me/preferences');
    const prefs = prefsResponse.data;

    preferencesForm.notifyDocuments = prefs.notifyDocuments ?? true;
    preferencesForm.notifyComments = prefs.notifyComments ?? true;
    preferencesForm.defaultCategory = prefs.defaultCategory || '';
    preferencesForm.itemsPerPage = prefs.itemsPerPage || '20';

    // Load categories for dropdown
    const categoriesResponse = await axios.get('/api/categories');
    categories.value = categoriesResponse.data.results;
  } catch (error) {
    console.error('无法加载用户偏好设置:', error);
  }
});

// Validate profile form
const validateProfileForm = () => {
  profileErrors.name = '';
  let isValid = true;

  if (!profileForm.name || profileForm.name.trim().length < 2) {
    profileErrors.name = '姓名必须至少包含2个字符';
    isValid = false;
  }

  return isValid;
};

// Validate password form
const validatePasswordForm = () => {
  passwordErrors.currentPassword = '';
  passwordErrors.newPassword = '';
  passwordErrors.confirmPassword = '';

  let isValid = true;

  if (!passwordForm.currentPassword) {
    passwordErrors.currentPassword = '请输入当前密码';
    isValid = false;
  }

  if (!passwordForm.newPassword || passwordForm.newPassword.length < 8 ||
      !/[A-Z]/.test(passwordForm.newPassword) ||
      !/[a-z]/.test(passwordForm.newPassword) ||
      !/[0-9]/.test(passwordForm.newPassword)) {
    passwordErrors.newPassword = '密码必须至少8个字符，并包含大写字母、小写字母和数字';
    isValid = false;
  }

  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    passwordErrors.confirmPassword = '两次输入的密码不匹配';
    isValid = false;
  }

  return isValid;
};

// Update profile information
const updateProfileInfo = async () => {
  if (!validateProfileForm()) {
    return;
  }

  isUpdating.value = true;
  profileStatus.value = null;

  try {
    // 只包含后端允许的字段
    const payload = {
      name: profileForm.name,
      email: authStore.user?.email  // 保持原有邮箱不变，后端需要
    };

    const response = await authStore.updateProfile(payload);

    profileStatus.value = {
      type: 'success',
      message: '恭喜！您的个人信息已成功更新。系统已保存您的最新资料，这些信息将用于系统中的个人识别和通知。'
    };

    // Update the form with the returned data
    if (response) {
      profileForm.name = response.name || '';
      profileForm.email = response.email || '';
      profileForm.phone = response.phone || '';
      profileForm.jobTitle = response.jobTitle || '';
      profileForm.department = response.department || '';
    }
  } catch (error) {
    profileStatus.value = {
      type: 'error',
      message: error.response?.data?.message || '很抱歉，更新个人信息时遇到了问题。这可能是由于网络连接问题或系统临时故障导致的。请检查您的输入信息并稍后再试，如果问题持续存在，请联系系统管理员获取帮助。'
    };
  } finally {
    isUpdating.value = false;
  }
};

// Update password
const updatePassword = async () => {
  if (!validatePasswordForm()) {
    return;
  }

  isUpdating.value = true;
  passwordStatus.value = null;

  try {
    await authStore.changePassword(
      passwordForm.currentPassword,
      passwordForm.newPassword
    );

    passwordStatus.value = {
      type: 'success',
      message: '恭喜！您的密码已成功更新。为了确保安全，建议您重新登录以使用新密码。'
    };

    // Reset form
    passwordForm.currentPassword = '';
    passwordForm.newPassword = '';
    passwordForm.confirmPassword = '';

    // 可选：提示用户重新登录
    setTimeout(() => {
      if (confirm('密码已更新成功。是否现在重新登录以确保安全？')) {
        authStore.logout();
        router.push('/login');
      }
    }, 2000);

  } catch (error) {
    console.error('Password change error:', error);
    
    // 更详细的错误处理
    let errorMessage = '很抱歉，更新密码时遇到了问题。';
    
    if (error.response?.status === 401) {
      errorMessage = '当前密码不正确，请检查后重试。';
    } else if (error.response?.status === 400) {
      errorMessage = error.response?.data?.message || '密码格式不符合要求，请检查密码强度。';
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else {
      errorMessage += '请检查网络连接并稍后再试，如果问题持续存在，请联系系统管理员。';
    }
    
    passwordStatus.value = {
      type: 'error',
      message: errorMessage
    };
  } finally {
    isUpdating.value = false;
  }
};

// Update preferences
const updatePreferences = async () => {
  isUpdating.value = true;
  prefStatus.value = null;

  try {
    await axios.put('/api/users/me/preferences', {
      notifyDocuments: preferencesForm.notifyDocuments,
      notifyComments: preferencesForm.notifyComments,
      defaultCategory: preferencesForm.defaultCategory,
      itemsPerPage: parseInt(preferencesForm.itemsPerPage)
    });

    prefStatus.value = {
      type: 'success',
      message: '恭喜！您的偏好设置已成功更新。系统将根据您的新设置调整显示内容和通知方式，为您提供更个性化的使用体验。'
    };
  } catch (error) {
    prefStatus.value = {
      type: 'error',
      message: error.response?.data?.message || '很抱歉，更新偏好设置时遇到了问题。这可能是由于网络连接问题或系统临时故障导致的。请稍后再次尝试，如果问题持续存在，请联系系统管理员获取帮助。'
    };
  } finally {
    isUpdating.value = false;
  }
};
</script>