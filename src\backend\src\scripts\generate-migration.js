/**
 * Database Schema Migration Generator
 *
 * This script compares the current database schema with the models defined in src/models
 * and generates migration files to align the database with the models.
 *
 * Usage:
 *   node src/scripts/generate-migration.js
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { Sequelize, DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const logger = require('../config/logger');
const { mapPostgresTypeToSequelize } = require('../utils/type-mapping-utils');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Helper function to convert camelCase to snake_case
function camelToSnakeCase(str) {
  return str;
  // return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

// Migrations directory
const MIGRATIONS_DIR = path.join(__dirname, '../migrations/postgres');

// Ensure migrations directory exists
if (!fs.existsSync(MIGRATIONS_DIR)) {
  fs.mkdirSync(MIGRATIONS_DIR, { recursive: true });
  console.log(`${colors.green}Created migrations directory at: ${MIGRATIONS_DIR}${colors.reset}`);
}

// Get all model files
const MODELS_DIR = path.join(__dirname, '../models');
const modelFiles = fs.readdirSync(MODELS_DIR)
  .filter(file => file.endsWith('.model.js'))
  .map(file => path.join(MODELS_DIR, file));

console.log(`${colors.blue}Found ${modelFiles.length} model files${colors.reset}`);

// Function to get database schema
async function getDatabaseSchema() {
  try {
    console.log(`${colors.blue}Retrieving current database schema...${colors.reset}`);

    // Test database connection
    try {
      await sequelize.authenticate();
      console.log(`${colors.green}Database connection established successfully${colors.reset}`);
    } catch (error) {
      console.error(`${colors.red}Unable to connect to the database:${colors.reset}`, error);
      return undefined;
    }

    // Get all tables in the database
    const tables = {};

    // For PostgreSQL
    if (process.env.DB_DIALECT === 'postgres') {
      const schema = process.env.DB_SCHEMA || 'public';
      const { getTableInfo, getAllTables } = require('../utils/postgres-utils');

      // Get all tables
      const tableNames = await getAllTables(sequelize, schema);

      // For each table, get its information
      for (const tableName of tableNames) {
        // Skip sequelize_meta table
        if (tableName === 'sequelize_meta') continue;

        const tableInfo = await getTableInfo(sequelize, schema, tableName);

        // Process column information
        const columns = {};
        for (const column of tableInfo.columns) {
          // Determine if column is primary key
          const isPrimaryKey = tableInfo.primaryKeys.some(pk => pk.column_name === column.column_name);

          // Determine if column has unique constraint
          const isUnique = tableInfo.uniqueConstraints.some(unique => unique.column_name === column.column_name);

          // Find foreign key information if any
          const foreignKey = tableInfo.foreignKeys.find(fk => fk.column_name === column.column_name);

          columns[column.column_name] = {
            type: column.data_type,
            udtName: column.udt_name,
            length: column.character_maximum_length,
            allowNull: column.is_nullable === 'YES',
            defaultValue: column.column_default,
            primaryKey: isPrimaryKey,
            unique: isUnique,
            references: foreignKey ? {
              model: foreignKey.foreign_table_name,
              key: foreignKey.foreign_column_name
            } : undefined
          };
        }

        tables[tableName] = { columns };
      }
    }

    return tables;
  } catch (error) {
    console.error(`${colors.red}Error retrieving database schema:${colors.reset}`, error);
    return undefined;
  }
}

// Function to parse model files and extract schema
function parseModels() {
  try {
    console.log(`${colors.blue}Parsing model files...${colors.reset}`);

    const models = {};

    // For each model file
    for (const modelFile of modelFiles) {
      // Get model name from filename
      const modelName = path.basename(modelFile, '.model.js');

      // Require the model
      const modelModule = require(modelFile);


      for (const key in modelModule) {
        
        if (Object.prototype.hasOwnProperty.call(modelModule, key)) {
          const modelDefinition = modelModule[key];

          if (!modelDefinition) {
            console.error(`${colors.red}Warning: Could not extract model definition from ${modelFile}${colors.reset}`);
            continue;
          }

          // Get table name from model
          parseModelsItem(modelDefinition, modelName, models);
        }
      }

    }

    return models;
  } catch (error) {
    console.error(`${colors.red}Error parsing model files:${colors.reset}`, error);
    throw error;
  }
}

function parseModelsItem(modelDefinition, modelName, models) {
  let tableName = modelDefinition.tableName || modelName.toLowerCase();
  tableName = tableName.toLowerCase();

  // Get model attributes
  const attributes = modelDefinition.rawAttributes;
  if (!attributes) {
    console.info(`${colors.green}Warning: Could not extract attributes from ${modelName}${colors.reset}`);
    return;
  }

  // Process attributes
  const columns = {};
  for (const [attrName, attrDef] of Object.entries(attributes)) {
    // Skip Sequelize-specific attributes
    if (['createdAt', 'updatedAt'].includes(attrName) && !attrDef.field) continue;

    // Get field name (column name in database)
    const fieldName = attrDef.field || attrName;

    // Get data type
    let dataType;
    let dataLength;

    if (attrDef.type) {
      const { mapDataType } = require('../utils/type-mapping-utils');
      const { type, length } = mapDataType(attrDef);
      dataType = type;
      dataLength = length;
    }

    // Get references
    let references;
    if (attrDef.references) {
      references = {
        model: typeof attrDef.references.model === 'string'
          ? attrDef.references.model.toLowerCase()
          : attrDef.references.model.tableName.toLowerCase() || attrDef.references.model.name.toLowerCase(),
        key: attrDef.references.key || 'id'
      };
    }

    columns[fieldName] = {
      type: dataType,
      length: dataLength,
      allowNull: attrDef.allowNull !== false,
      defaultValue: attrDef.defaultValue,
      primaryKey: attrDef.primaryKey === true,
      unique: attrDef.unique === true,
      autoIncrement: attrDef.autoIncrement === true,
      references
    };
  }

  models[tableName] = { columns };
}

// Function to compare database schema with models
function compareSchemas(dbSchema, modelSchema) {
  console.log(`${colors.blue}Comparing database schema with models...${colors.reset}`);

  const differences = {
    missingTables: [],
    missingColumns: [],
    differentColumns: [],
    extraColumns: []
  };

  // Ensure dbSchema is not undefined
  if (!dbSchema) {
    console.log(`${colors.yellow}Warning: Database schema is undefined. All tables will be considered missing.${colors.reset}`);
    differences.missingTables = Object.keys(modelSchema);
    return differences;
  }

  // Check for missing tables
  for (const tableName in modelSchema) {
    if (!dbSchema[tableName]) {
      differences.missingTables.push(tableName);
      continue;
    }

    // Check for missing columns
    for (const columnName in modelSchema[tableName].columns) {
      if (!dbSchema[tableName].columns[columnName]) {
        differences.missingColumns.push({
          table: tableName,
          column: columnName,
          definition: modelSchema[tableName].columns[columnName]
        });
        continue;
      }

      if (['id', 'createdAt', 'updatedAt'].includes(columnName)) {
        continue;
      }

      // Check for different column definitions
      const dbColumn = dbSchema[tableName].columns[columnName];
      const modelColumn = modelSchema[tableName].columns[columnName];

      // Compare relevant properties with detailed logging
      const columnDifferences = [];

      // 检查类型是否不同
      const { areTypesEquivalent, areUdtNameEquivalent } = require('../utils/type-utils');
      if (!(areTypesEquivalent(dbColumn.type, modelColumn.type) || areUdtNameEquivalent(dbColumn.udtName, modelColumn.type))) {
        const columnDifference = `type: DB='${dbColumn.type}','${dbColumn.udtName}', Model='${modelColumn.type}','${columnName}'`;
        console.log('Pushing to columnDifferences 1:', columnDifference);
        columnDifferences.push(columnDifference);
      }

      if (dbColumn.allowNull !== modelColumn.allowNull) {
        // const columnDifference = `allowNull: DB=${dbColumn.allowNull}, Model=${modelColumn.allowNull}`;
        // console.log('Pushing to columnDifferences:', columnDifference);
        // columnDifferences.push(columnDifference);
      }

      if (dbColumn.primaryKey !== modelColumn.primaryKey) {
        // const columnDifference = `primaryKey: DB=${dbColumn.primaryKey}, Model=${modelColumn.primaryKey}`;
        // console.log('Pushing to columnDifferences:', columnDifference);
        // columnDifferences.push(columnDifference);
      }

      if (dbColumn.unique !== modelColumn.unique) {
        // const columnDifference = `unique: DB=${dbColumn.unique}, Model=${modelColumn.unique}`;
        // console.log('Pushing to columnDifferences:', columnDifference);
        // columnDifferences.push(columnDifference);
      }


      // Helper function to normalize default values for comparison
      function areDefaultValuesEqual(dbDefault, modelDefault, dbType) {
        // Handle null/undefined cases
        if (dbDefault === null && modelDefault === null) return true;
        if (dbDefault === null && modelDefault === undefined) return true;
        if (dbDefault === undefined && modelDefault === null) return true;
        if (dbDefault === undefined && modelDefault === undefined) return true;

        // Special handling for JSON type
        if (dbType) {
          if (/ARRAY/i.test(dbType)) {
            return true;
          }
          // Handle empty object/array cases for JSON type
          if (dbDefault === null && (
            modelDefault === '{}' ||
            modelDefault === '[]' ||
            (typeof modelDefault === 'object' && Object.keys(modelDefault).length === 0) ||
            (Array.isArray(modelDefault) && modelDefault.length === 0)
          )) {
            return true;
          }
        }

        if (dbDefault === null || modelDefault === null) return false;

        // Convert both values to strings for comparison
        const dbStr = dbDefault.toString().toLowerCase();
        const modelStr = modelDefault.toString().toLowerCase();

        // Handle special PostgreSQL default value cases
        if (dbStr.startsWith('nextval(')) return modelDefault === null || modelDefault === undefined;
        if (dbStr.includes('::')) {
          // Remove PostgreSQL type casting
          const dbValue = dbStr.split('::')[0].replace(/^'|'$/g, '').replace(/^\[]$/g, '');
          return dbValue === modelStr;
        }

        // Handle boolean defaults
        if (dbType instanceof DataTypes.BOOLEAN) {
          const dbBool = dbStr === 'true' || dbStr === 't' || dbStr === '1';
          const modelBool = modelStr === 'true' || modelStr === 't' || modelStr === '1';
          return dbBool === modelBool;
        }

        // Handle numeric defaults
        if (dbType instanceof DataTypes.INTEGER || dbType instanceof DataTypes.FLOAT) {
          return parseFloat(dbStr) === parseFloat(modelStr);
        }

        // Default string comparison
        return dbStr === modelStr;
      }

      if (columnName === 'products') {
        console.log('dbColumn.defaultValue:', dbColumn.defaultValue);
        console.log('modelColumn.defaultValue:', modelColumn.defaultValue);
      }
      if (!areDefaultValuesEqual(dbColumn.defaultValue, modelColumn.defaultValue, dbColumn.type)) {
        const columnDifference = `defaultValue: DB=${JSON.stringify(dbColumn.defaultValue)}, Model=${JSON.stringify(modelColumn.defaultValue)}, dbtype=${dbColumn.type}, mtype=${modelColumn.type}, tableName=${tableName}, columnName=${columnName}`;
        console.log('Pushing to columnDifferences:', columnDifference);
        columnDifferences.push(columnDifference);
      }

      if (columnDifferences.length > 0) {
        console.log(`${colors.yellow}Column differences for ${tableName}.${columnName}:${colors.reset}`);
        columnDifferences.forEach(diff => console.log(`  - ${diff}`));

        // Add to the list of columns that need to be changed
        differences.differentColumns.push({
          table: tableName,
          column: columnName,
          dbDefinition: dbColumn,
          modelDefinition: modelColumn,
          differences: columnDifferences // Store the specific differences for reference
        });
      }
    }

    // Check for extra columns in database
    for (const columnName in dbSchema[tableName].columns) {
      if (!modelSchema[tableName].columns[columnName] &&
        !['createdAt', 'updatedAt', 'id'].includes(columnName)) {
        differences.extraColumns.push({
          table: tableName,
          column: columnName,
          definition: dbSchema[tableName].columns[columnName]
        });
      }
    }
  }

  return differences;
}

// Function to generate migration file
function generateMigration(differences, modelSchema) {
  // For storing sorted tables based on predefined order
  let sortedTables = [];
  console.log(`${colors.blue}Generating migration file...${colors.reset}`);

  // Create migration name
  const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, 14);
  const filename = `${timestamp}_schema_sync.js`;
  const filePath = path.join(MIGRATIONS_DIR, filename);

  // Helper function to handle default values
  function getDefaultValueString(columnDef) {
    if (columnDef.defaultValue === undefined) return '';
    if (columnDef.defaultValue === null) {
      return `        defaultValue: null,\n`;
    }
    if (/^uuid$/i.test(columnDef.type) && columnDef.defaultValue === 'UUIDV4') {
      return `        defaultValue: Sequelize.DataTypes.UUIDV4,\n`;
    }

    // Handle empty objects and arrays
    if (typeof columnDef.defaultValue === 'object') {
      if (Array.isArray(columnDef.defaultValue)) {
        if (columnDef.defaultValue.length === 0) {
          if (columnDef.type.toLowerCase().includes('array')) {
            return `        defaultValue: [],\n`;
          }
          return `        defaultValue: null,\n`;
        }
      } else if (Object.keys(columnDef.defaultValue).length === 0) {
        // Convert empty objects to null
        return `        defaultValue: null,\n`;
      }
    }

    return `        defaultValue: ${JSON.stringify(columnDef.defaultValue)},\n`;
  }

  // Extract table dependencies automatically from model schemas
  const tableDependencies = {};

  // Initialize dependency map with empty arrays for all tables
  for (const tableName in modelSchema) {
    tableDependencies[tableName.toLowerCase()] = [];
  }

  // Analyze each table's columns to find foreign key references
  for (const tableName in modelSchema) {
    const tableColumns = modelSchema[tableName].columns;

    // Check each column for foreign key references
    for (const columnName in tableColumns) {
      const column = tableColumns[columnName];

      // If this column has a foreign key reference
      if (column.references && column.references.model) {
        const referencedTable = column.references.model.toLowerCase();

        // Add the dependency if it's not already in the list
        if (!tableDependencies[tableName.toLowerCase()].includes(referencedTable)) {
          tableDependencies[tableName.toLowerCase()].push(referencedTable);
        }
        if (!modelSchema[referencedTable]) {
          console.log(`${colors.red}Error: Referenced table ${referencedTable} on ${tableName} not found in model schema${colors.reset}`);
          throw new Error(`Referenced table ${referencedTable} on ${tableName} not found in model schema`);
        }
      }
    }
  }

  // Log the extracted dependencies
  console.log(`${colors.blue}Extracted table dependencies:${colors.reset}`);
  for (const tableName in tableDependencies) {
    if (tableDependencies[tableName].length > 0) {
      console.log(`  ${tableName} depends on: ${tableDependencies[tableName].join(', ')}`);
    }
  }

  // Start building migration content
  let migrationContent = `/**
 * Migration: Schema Synchronization
 * Created at: ${new Date().toISOString()}
 */
'use strict';

/** @type {import('sequelize').QueryInterface} */
module.exports = {
  // Apply the migration
  async up(queryInterface, Sequelize) {
`;

  // Add missing tables
  if (differences.missingTables.length > 0) {
    migrationContent += `    // Create missing tables\n`;

    // Sort tables based on dependencies using topological sort
    sortedTables = [];
    const visited = new Set();
    const temp = new Set();

    // Helper function to find a table name in missingTables with case-insensitive comparison
    function findTableCaseInsensitive(targetName) {
      return differences.missingTables.find(tableName =>
        tableName.toLowerCase() === targetName.toLowerCase()
      );
    }

    // Topological sort function to visit tables in dependency order
    function visit(tableName) {
      // Skip if the table is not in missingTables
      const actualTableName = findTableCaseInsensitive(tableName);
      if (!actualTableName) return;

      // Check for circular dependencies
      if (temp.has(actualTableName.toLowerCase())) {
        console.warn(`${colors.yellow}Warning: Circular dependency detected for table ${actualTableName}${colors.reset}`);
        return;
      }

      // Skip if already visited
      if (visited.has(actualTableName.toLowerCase())) {
        return;
      }

      // Mark as being visited
      temp.add(actualTableName.toLowerCase());

      // Visit dependencies first
      // Get dependencies from the map, using case-insensitive lookup
      // If the table isn't in the dependency map, assume no dependencies
      const dependencies = tableDependencies[tableName.toLowerCase()] || [];

      for (const dep of dependencies) {
        const depTableName = findTableCaseInsensitive(dep);
        if (depTableName) {
          visit(depTableName);
        }
      }

      // Mark as visited and add to sorted list
      temp.delete(actualTableName.toLowerCase());
      visited.add(actualTableName.toLowerCase());
      sortedTables.push(actualTableName);
    }


    // Then visit any remaining tables
    for (const tableName of differences.missingTables) {
      if (!visited.has(tableName.toLowerCase())) {
        visit(tableName);
      }
    }

    // Log the sorted order
    console.log(`${colors.blue}Tables will be created in this order: ${sortedTables.join(', ')}${colors.reset}`);

    // Create tables in dependency order
    for (const tableName of sortedTables) {
      // Get model definition for this table
      const modelDef = modelSchema[tableName];
      if (!modelDef || !modelDef.columns) {
        migrationContent += `    // TODO: Create table '${tableName}'\n`;
        migrationContent += `    await queryInterface.createTable('${tableName}', { ... });\n\n`;
        continue;
      }

      // Start table definition
      migrationContent += `    await queryInterface.createTable('${tableName}', {\n`;

      // Add columns
      for (const [columnName, columnDef] of Object.entries(modelDef.columns)) {
        // Skip if this is a virtual column
        if (!columnDef.type) continue;

        migrationContent += `      ${columnName}: {\n`;

        // Map type to Sequelize type
        const { mapPostgresTypeToSequelize, mapDataType, normalizeDefaultValue, areDefaultValuesEqual } = require('../utils/type-mapping-utils');

        // Handle ENUM types specially
        if (typeof columnDef.type === 'object' && columnDef.type.values) {
          const enumValues = columnDef.type.values;
          migrationContent += `        type: new Sequelize.DataTypes.ENUM(${enumValues.map(v => `'${v}'`).join(', ')}),\n`;
        } else {
          typeDefinition = mapPostgresTypeToSequelize(columnDef.type, columnDef.length);
          migrationContent += `        type: ${typeDefinition},\n`;
        }

        // For UUID fields, always add UUIDV4 as default value if not specified
        if (columnDef.type === 'uuid' && columnDef.defaultValue === undefined && columnName === 'id') {
          columnDef.defaultValue = 'UUIDV4';
        }

        // Add defaultValue if specified
        migrationContent += getDefaultValueString(columnDef);

        // Add allowNull
        if (columnDef.allowNull === false) {
          migrationContent += `        allowNull: false,\n`;
        }

        // Add primaryKey
        if (columnDef.primaryKey) {
          migrationContent += `        primaryKey: true,\n`;
        }

        // Add autoIncrement
        if (columnDef.autoIncrement) {
          migrationContent += `        autoIncrement: true,\n`;
        }

        // Add unique constraint
        if (columnDef.unique) {
          migrationContent += `        unique: true,\n`;
        }

        // Add references (foreign keys)
        if (columnDef.references) {
          migrationContent += `        references: {\n`;
          migrationContent += `          model: '${columnDef.references.model}',\n`;
          migrationContent += `          key: '${columnDef.references.key}'\n`;
          migrationContent += `        },\n`;
        }

        // Add field mapping if the column name is different from the field name
        const dbColumnName = camelToSnakeCase(columnName);
        if (dbColumnName !== columnName) {
          migrationContent += `        field: '${dbColumnName}',\n`;
        }

        // Close column definition
        migrationContent += `      },\n`;
      }

      // Add timestamp columns if not already defined
      if (!modelDef.columns.createdAt) {
        migrationContent += `      createdAt: {\n`;
        migrationContent += `        type: Sequelize.DataTypes.DATE,\n`;
        migrationContent += `        allowNull: false,\n`;
        migrationContent += `        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),\n`;
        migrationContent += `        field: 'createdAt'\n`;
        migrationContent += `      },\n`;
      }

      if (!modelDef.columns.updatedAt) {
        migrationContent += `      updatedAt: {\n`;
        migrationContent += `        type: Sequelize.DataTypes.DATE,\n`;
        migrationContent += `        allowNull: false,\n`;
        migrationContent += `        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),\n`;
        migrationContent += `        field: 'updatedAt'\n`;
        migrationContent += `      }\n`;
      } else {
        // Remove trailing comma from last column
        migrationContent = migrationContent.replace(/,\n$/, '\n');
      }

      // Close table definition
      migrationContent += `    });\n\n`;
    }
  }

  // Add missing columns
  if (differences.missingColumns.length > 0) {
    migrationContent += `    // Add missing columns\n`;

    for (const { table, column, definition } of differences.missingColumns) {

      // Map type to Sequelize type

      const { mapPostgresTypeToSequelize } = require('../utils/type-mapping-utils');
      let typeDefinition = mapPostgresTypeToSequelize(definition.type, definition.length);


      migrationContent += `    await queryInterface.addColumn('${table}', '${column}', {\n`;
      migrationContent += `      type: ${typeDefinition},\n`;

      if (definition.allowNull === false) {
        migrationContent += `      allowNull: false,\n`;
      }

      if (definition.defaultValue !== undefined) {
        migrationContent += `      defaultValue: ${JSON.stringify(definition.defaultValue)},\n`;
      }

      if (definition.primaryKey) {
        migrationContent += `      primaryKey: true,\n`;
      }

      if (definition.autoIncrement) {
        migrationContent += `      autoIncrement: true,\n`;
      }

      if (definition.unique) {
        migrationContent += `      unique: true,\n`;
      }

      if (definition.references) {
        migrationContent += `      references: {\n`;
        migrationContent += `        model: '${definition.references.model}',\n`;
        migrationContent += `        key: '${definition.references.key}'\n`;
        migrationContent += `      },\n`;
      }

      migrationContent += `    });\n\n`;
    }
  }



  // Modify different columns
  if (differences.differentColumns.length > 0) {
    migrationContent += `    // Modify columns with different definitions\n`;

    for (const { table, column, modelDefinition } of differences.differentColumns) {

      let typeDefinition = mapPostgresTypeToSequelize(modelDefinition.type, modelDefinition.length);

      // Create a variable for the column definition
      const varName = `${table}${column.charAt(0).toUpperCase() + column.slice(1)}Def`;

      migrationContent += `    const ${varName} = {\n`;
      migrationContent += `      type: ${typeDefinition},\n`;

      if (modelDefinition.allowNull === false) {
        migrationContent += `      allowNull: false,\n`;
      }

      migrationContent += getDefaultValueString(modelDefinition);

      if (modelDefinition.primaryKey) {
        migrationContent += `      primaryKey: true,\n`;
      }

      if (modelDefinition.autoIncrement) {
        migrationContent += `      autoIncrement: true,\n`;
      }

      if (modelDefinition.unique) {
        migrationContent += `      unique: true,\n`;
      }

      if (modelDefinition.references) {
        migrationContent += `      references: {\n`;
        migrationContent += `        model: '${modelDefinition.references.model}',\n`;
        migrationContent += `        key: '${modelDefinition.references.key}'\n`;
        migrationContent += `      },\n`;
      }

      migrationContent += `    };\n\n`;

      // Add conditional check before executing the change
      migrationContent += `      await queryInterface.changeColumn('${table}', '${column}', ${varName});\n`;
    }
  }

  // Handle extra columns (commented out by default for safety)
  if (differences.extraColumns.length > 0) {
    migrationContent += `    // Remove extra columns (commented out for safety)\n`;

    for (const { table, column } of differences.extraColumns) {
      migrationContent += `    // await queryInterface.removeColumn('${table}', '${column}');\n`;
    }

    migrationContent += '\n';
  }

  // Close up function
  migrationContent += `  },

  // Revert the migration
  async down(queryInterface, Sequelize) {
`;

  // Revert in opposite order

  // Restore extra columns (commented out)
  if (differences.extraColumns.length > 0) {
    migrationContent += `    // Restore removed columns (commented out for safety)\n`;

    for (const { table, column } of differences.extraColumns) {
      migrationContent += `    // await queryInterface.addColumn('${table}', '${column}', { /* original definition */ });\n`;
    }

    migrationContent += '\n';
  }

  // Revert modified columns
  if (differences.differentColumns.length > 0) {
    migrationContent += `    // Revert modified columns\n`;

    for (const { table, column, dbDefinition } of differences.differentColumns) {

      // Map type to Sequelize type (same as above)
      let typeDefinition = mapPostgresTypeToSequelize(dbDefinition.type, dbDefinition.length);

      // Create a variable for the column definition
      const varName = `${table}${column.charAt(0).toUpperCase() + column.slice(1)}OrigDef`;

      migrationContent += `    const ${varName} = {\n`;
      migrationContent += `      type: ${typeDefinition},\n`;

      if (dbDefinition.allowNull === false) {
        migrationContent += `      allowNull: false,\n`;
      }

      migrationContent += getDefaultValueString(dbDefinition);

      if (dbDefinition.primaryKey) {
        migrationContent += `      primaryKey: true,\n`;
      }

      if (dbDefinition.autoIncrement) {
        migrationContent += `      autoIncrement: true,\n`;
      }

      if (dbDefinition.unique) {
        migrationContent += `      unique: true,\n`;
      }

      if (dbDefinition.references) {
        migrationContent += `      references: {\n`;
        migrationContent += `        model: '${dbDefinition.references.model}',\n`;
        migrationContent += `        key: '${dbDefinition.references.key}'\n`;
        migrationContent += `      },\n`;
      }

      migrationContent += `    };\n\n`;

      // Add conditional check before executing the change
      migrationContent += `      await queryInterface.changeColumn('${table}', '${column}', ${varName});\n`;
    }
  }

  // Remove added columns
  if (differences.missingColumns.length > 0) {
    migrationContent += `    // Remove added columns\n`;

    for (const { table, column } of differences.missingColumns) {
      migrationContent += `    await queryInterface.removeColumn('${table}', '${column}');\n`;
    }

    migrationContent += '\n';
  }

  // Drop created tables
  if (differences.missingTables.length > 0) {
    migrationContent += `    // Drop created tables\n`;

    // Drop tables in reverse order of creation to respect dependencies
    const reversedTables = sortedTables.slice().reverse();
    for (const tableName of reversedTables) {
      migrationContent += `    await queryInterface.dropTable('${tableName}');\n`;
    }
  }

  // Close the migration
  migrationContent += `  }
};
`;

  // Write the migration file
  fs.writeFileSync(filePath, migrationContent);
  console.log(`${colors.green}Created migration file:${colors.reset} ${filename}`);

  return filename;
}

// Main function
async function main() {
  try {
    // Get database schema
    const dbSchema = await getDatabaseSchema();

    // Parse model files
    const modelSchema = parseModels();

    // Compare schemas
    const differences = compareSchemas(dbSchema, modelSchema);

    // Print differences
    console.log(`${colors.magenta}Schema differences:${colors.reset}`);
    console.log(`${colors.yellow}Missing tables:${colors.reset} ${differences.missingTables.length}`);
    console.log(`${colors.yellow}Missing columns:${colors.reset} ${differences.missingColumns.length}`);
    console.log(`${colors.yellow}Different columns:${colors.reset} ${differences.differentColumns.length}`);
    console.log(`${colors.yellow}Extra columns:${colors.reset} ${differences.extraColumns.length}`);

    // Generate migration if there are differences
    if (differences.missingTables.length > 0 ||
      differences.missingColumns.length > 0 ||
      differences.differentColumns.length > 0 ||
      differences.extraColumns.length > 0) {
      const migrationFile = generateMigration(differences, modelSchema);
      console.log(`${colors.green}Migration file generated successfully: ${migrationFile}${colors.reset}`);
      console.log(`${colors.cyan}Run the migration with: npm run postgres:migration:run${colors.reset}`);
    } else {
      console.log(`${colors.green}No schema differences found. Database is in sync with models.${colors.reset}`);
    }

    // Close database connection
    await sequelize.close();

  } catch (error) {
    console.error(`${colors.red}Error:${colors.reset}`, error);
    process.exit(1);
  }
}

// Run the script
main();
