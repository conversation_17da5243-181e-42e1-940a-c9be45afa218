const { Permission } = require('../models');
const { v4: uuidv4 } = require('uuid');

async function seedPermissions() {
  try {
    // Clear existing data using force: true to bypass foreign key constraints
    await Permission.destroy({ 
      where: {},
      force: true // Use force: true for hard delete
    });

    // Wait a bit to ensure cleanup is complete
    await new Promise(resolve => setTimeout(resolve, 1000));

    const permissions = [
      // 用户管理权限
      {
        id: uuidv4(),
        name: 'manageUsers',
        description: '用户管理权限',
        category: 'user',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: uuidv4(),
        name: 'viewUsers',
        description: '查看用户信息',
        category: 'user',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // 项目管理权限
      {
        id: uuidv4(),
        name: 'manageProjects',
        description: '项目管理权限',
        category: 'project',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: uuidv4(),
        name: 'viewProjects',
        description: '查看项目信息',
        category: 'project',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // 文档管理权限
      {
        id: uuidv4(),
        name: 'manageDocuments',
        description: '文档管理权限',
        category: 'document',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: uuidv4(),
        name: 'viewDocuments',
        description: '查看文档',
        category: 'document',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // 员工管理权限
      {
        id: uuidv4(),
        name: 'manageEmployees',
        description: '员工管理权限',
        category: 'employee',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: uuidv4(),
        name: 'viewEmployees',
        description: '查看员工信息',
        category: 'employee',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // 财务管理权限
      {
        id: uuidv4(),
        name: 'manageFinance',
        description: '财务管理权限',
        category: 'finance',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: uuidv4(),
        name: 'viewFinance',
        description: '查看财务信息',
        category: 'finance',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      // 系统管理权限
      {
        id: uuidv4(),
        name: 'manageSystem',
        description: '系统管理权限',
        category: 'system',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: uuidv4(),
        name: 'viewSystemLogs',
        description: '查看系统日志',
        category: 'system',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Use transaction to ensure data consistency
    await Permission.sequelize.transaction(async (t) => {
      await Permission.bulkCreate(permissions, {
        transaction: t,
        ignoreDuplicates: true // Ignore duplicate entries instead of failing
      });
    });

    console.log('Permissions seeder executed successfully');
  } catch (error) {
    console.error('Error seeding permissions:', error);
    throw error; // Re-throw to see full error stack
  }
}

module.exports = seedPermissions; 