const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { dashboardService } = require('../services');

/**
 * Get dashboard statistics
 * @route GET /api/dashboard/stats
 */
const getDashboardStats = catchAsync(async (req, res) => {
  const stats = await dashboardService.getDashboardStats();
  res.send(stats);
});

/**
 * Get project statistics for dashboard
 * @route GET /api/dashboard/projects
 */
const getProjectStats = catchAsync(async (req, res) => {
  const stats = await dashboardService.getProjectStats();
  res.send(stats);
});

/**
 * Get supplier statistics for dashboard
 * @route GET /api/dashboard/suppliers
 */
const getSupplierStats = catchAsync(async (req, res) => {
  const stats = await dashboardService.getSupplierStats();
  res.send(stats);
});

/**
 * Get purchase statistics for dashboard
 * @route GET /api/dashboard/purchases
 */
const getPurchaseStats = catchAsync(async (req, res) => {
  const stats = await dashboardService.getPurchaseStats();
  res.send(stats);
});

/**
 * Get inventory statistics for dashboard
 * @route GET /api/dashboard/inventory
 */
const getInventoryStats = catchAsync(async (req, res) => {
  const stats = await dashboardService.getInventoryStats();
  res.send(stats);
});

/**
 * Get recent projects for dashboard
 * @route GET /api/dashboard/recent-projects
 */
const getRecentProjects = catchAsync(async (req, res) => {
  const limit = parseInt(req.query.limit, 10) || 5;
  const projects = await dashboardService.getRecentProjects(limit);
  res.send(projects);
});

/**
 * Get recent documents for dashboard
 * @route GET /api/dashboard/recent-documents
 */
const getRecentDocuments = catchAsync(async (req, res) => {
  const limit = parseInt(req.query.limit, 10) || 5;
  const documents = await dashboardService.getRecentDocuments(limit);
  res.send(documents);
});

module.exports = {
  getDashboardStats,
  getProjectStats,
  getSupplierStats,
  getPurchaseStats,
  getInventoryStats,
  getRecentProjects,
  getRecentDocuments
}; 