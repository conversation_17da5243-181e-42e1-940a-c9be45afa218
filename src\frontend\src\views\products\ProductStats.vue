<template>
  <div class="max-w-7xl mx-auto px-4 py-6">
    <div class="flex items-center mb-6">
      <router-link to="/products" class="text-blue-600 hover:text-blue-800 mr-3">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
      </router-link>
      <h1 class="text-2xl font-bold text-gray-900">产品统计</h1>
    </div>

    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>

    <div v-else-if="error" class="bg-red-50 p-4 rounded-md text-red-800">
      {{ error }}
      <button @click="fetchProductStats" class="ml-2 text-red-800 underline">重试</button>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 产品总数统计卡片 -->
      <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500">
        <div class="flex items-center">
          <div class="flex-shrink-0 bg-blue-100 p-3 rounded-full">
            <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <div class="ml-4">
            <h2 class="text-lg font-semibold text-gray-900">产品总数</h2>
            <p class="text-3xl font-bold text-blue-600">{{ productStats.totalProducts }}</p>
          </div>
        </div>
      </div>

      <!-- 库存总量统计卡片 -->
      <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500">
        <div class="flex items-center">
          <div class="flex-shrink-0 bg-green-100 p-3 rounded-full">
            <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
            </svg>
          </div>
          <div class="ml-4">
            <h2 class="text-lg font-semibold text-gray-900">库存总量</h2>
            <p class="text-3xl font-bold text-green-600">{{ productStats.totalStock }}</p>
          </div>
        </div>
      </div>

      <!-- 库存价值统计卡片 -->
      <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-purple-500">
        <div class="flex items-center">
          <div class="flex-shrink-0 bg-purple-100 p-3 rounded-full">
            <svg class="w-8 h-8 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <h2 class="text-lg font-semibold text-gray-900">库存总价值</h2>
            <p class="text-3xl font-bold text-purple-600">¥{{ formatNumber(productStats.totalValue) }}</p>
          </div>
        </div>
      </div>

      <!-- 分类统计卡片 -->
      <div class="bg-white rounded-lg shadow-md p-6 col-span-1 md:col-span-2 lg:col-span-3">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">产品分类统计</h2>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类名称</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品数量</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">库存总量</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价值占比</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="(category, index) in productStats.categories" :key="index">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ category.name || '未分类' }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ category.count }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ category.stock }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div class="flex items-center">
                    <span class="mr-2">{{ category.percentage }}%</span>
                    <div class="w-24 bg-gray-200 rounded-full h-2.5">
                      <div class="h-2.5 rounded-full bg-blue-600" :style="{ width: category.percentage + '%' }"></div>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 状态统计卡片 -->
      <div class="bg-white rounded-lg shadow-md p-6 col-span-1 md:col-span-2 lg:col-span-3">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">产品状态统计</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-green-50 rounded-lg p-4 border border-green-200">
            <div class="flex justify-between items-center">
              <div>
                <p class="text-sm text-green-700">上架中</p>
                <p class="text-2xl font-bold text-green-700">{{ productStats.statusCounts.active || 0 }}</p>
              </div>
              <div class="bg-green-200 rounded-full p-2">
                <svg class="w-6 h-6 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>
          <div class="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
            <div class="flex justify-between items-center">
              <div>
                <p class="text-sm text-yellow-700">草稿</p>
                <p class="text-2xl font-bold text-yellow-700">{{ productStats.statusCounts.draft || 0 }}</p>
              </div>
              <div class="bg-yellow-200 rounded-full p-2">
                <svg class="w-6 h-6 text-yellow-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
            </div>
          </div>
          <div class="bg-red-50 rounded-lg p-4 border border-red-200">
            <div class="flex justify-between items-center">
              <div>
                <p class="text-sm text-red-700">已下架</p>
                <p class="text-2xl font-bold text-red-700">{{ productStats.statusCounts.discontinued || 0 }}</p>
              </div>
              <div class="bg-red-200 rounded-full p-2">
                <svg class="w-6 h-6 text-red-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';

const loading = ref(true);
const error = ref(null);
const productStats = ref({
  totalProducts: 0,
  totalStock: 0,
  totalValue: 0,
  categories: [],
  statusCounts: {
    active: 0,
    draft: 0,
    discontinued: 0
  }
});

const fetchProductStats = async () => {
  loading.value = true;
  error.value = null;

  try {
    // 由于后端没有实现/api/products/stats接口，我们使用/api/products接口获取所有产品，然后在前端计算统计数据
    const response = await axios.get('/api/products');
    const products = Array.isArray(response.data) ? response.data : 
                    (response.data.results || []);
    
    // 计算总数
    productStats.value.totalProducts = products.length;
    
    // 计算库存总量和总价值
    let totalStock = 0;
    let totalValue = 0;
    const categoriesMap = new Map();
    const statusCounts = {
      active: 0,
      draft: 0,
      discontinued: 0
    };
    
    products.forEach(product => {
      // 库存和价值
      const stock = product.stock || 0;
      const price = product.price || 0;
      const value = stock * price;
      
      totalStock += stock;
      totalValue += value;
      
      // 分类统计
      const category = product.category || '未分类';
      if (!categoriesMap.has(category)) {
        categoriesMap.set(category, { name: category, count: 0, stock: 0, value: 0 });
      }
      const categoryData = categoriesMap.get(category);
      categoryData.count += 1;
      categoryData.stock += stock;
      categoryData.value += value;
      
      // 状态统计
      if (product.status === 'active') statusCounts.active += 1;
      else if (product.status === 'draft') statusCounts.draft += 1;
      else if (product.status === 'discontinued') statusCounts.discontinued += 1;
    });
    
    // 更新统计数据
    productStats.value.totalStock = totalStock;
    productStats.value.totalValue = totalValue;
    productStats.value.statusCounts = statusCounts;
    
    // 处理分类数据，计算百分比
    const categories = Array.from(categoriesMap.values());
    categories.forEach(category => {
      category.percentage = totalValue > 0 ? Math.round((category.value / totalValue) * 100) : 0;
    });
    productStats.value.categories = categories;
    
  } catch (err) {
    console.error('获取产品统计数据失败:', err);
    error.value = '获取产品统计数据失败: ' + (err.message || '未知错误');
  } finally {
    loading.value = false;
  }
};

const formatNumber = (num) => {
  return num.toLocaleString('zh-CN');
};

onMounted(() => {
  fetchProductStats();
});
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md flex items-center justify-center transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
}
</style>