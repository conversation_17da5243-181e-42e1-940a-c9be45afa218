# AGENT.md - Guidelines for Coding Agents

## Build & Run Commands
- **Backend**: `cd src/backend && pnpm run dev` (or `dev:windsurf` for windsurf env)
- **Frontend**: `cd src/frontend && pnpm run dev` (or `dev:windsurf` for windsurf env)
- **Lint**: `cd src/backend && pnpm run lint` or `pnpm run lint:fix`
- **Tests**:
  - Backend: `cd src/backend && pnpm run test` (or `test:unit`, `test:integration`)
  - Single test: `cd src/backend && pnpm run test -- -g "test description"` 
  - Frontend: `cd src/frontend && pnpm run test` 
  - E2E: `cd src/e2e && pnpm run test`

## Code Style Guidelines
- **File naming**: Use kebab-case for files and directories
- **JavaScript/TypeScript**:
  - PascalCase for classes and components
  - camelCase for variables, functions, and methods
  - UPPER_SNAKE_CASE for constants
  - Follow Airbnb style guide (max line length: 120)
- **CSS**: Use kebab-case for class names and BEM naming convention
- **Imports**: Group imports by type (core modules, third-party, local)
- **Error handling**: Use try/catch patterns with proper error propagation
- **Documentation**: Use descriptive variable names and JSDoc where appropriate

## Port Rules
- Do not modify existing port configurations
- Document port conflicts but don't change without approval