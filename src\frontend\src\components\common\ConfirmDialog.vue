<template>
  <div v-if="state.visible" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 animate-fadeIn">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6 transform transition-all duration-300 ease-in-out" :class="{'scale-100 opacity-100': state.visible, 'scale-95 opacity-0': !state.visible}">
      <div class="text-center">
        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full mb-4" 
          :class="{
            'bg-red-100': state.type === 'danger',
            'bg-yellow-100': state.type === 'warning',
            'bg-blue-100': state.type === 'info'
          }">
          <svg v-if="state.type === 'danger'" class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
          </svg>
          <svg v-else-if="state.type === 'warning'" class="h-6 w-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
          </svg>
          <svg v-else class="h-6 w-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-1">{{ state.title }}</h3>
        <p class="text-gray-500">{{ state.message }}</p>
        
        <div class="mt-6 flex justify-center space-x-3">
          <button
            type="button"
            @click="handleCancel"
            class="btn btn-secondary"
            :disabled="state.loading"
          >
            {{ state.cancelText }}
          </button>
          <button
            type="button"
            @click="handleConfirm"
            class="btn"
            :class="{
              'btn-danger': state.type === 'danger',
              'btn-warning': state.type === 'warning',
              'btn-primary': state.type === 'info'
            }"
            :disabled="state.loading"
          >
            <span v-if="state.loading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ state.loadingText }}
            </span>
            <span v-else>{{ state.confirmText }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import confirmService from '../../services/confirm.service';

// 从服务中获取状态和方法
const { state, handleConfirm, handleCancel } = confirmService;
</script>

<style scoped>
.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style> 