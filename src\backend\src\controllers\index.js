module.exports.authController = require('./auth.controller');
module.exports.userController = require('./user.controller');
module.exports.categoryController = require('./category.controller');
module.exports.documentController = require('./document.controller');
module.exports.tagController = require('./tag.controller');
module.exports.attachmentController = require('./attachment.controller');
module.exports.commentController = require('./comment.controller');
module.exports.projectController = require('./project.controller');
module.exports.contractController = require('./contract.controller');
module.exports.procurementController = require('./procurement.controller');
module.exports.customerController = require('./customer.controller');
module.exports.clientController = require('./client.controller');
module.exports.supplierController = require('./supplier.controller');
module.exports.productController = require('./product.controller');
module.exports.financeController = require('./finance.controller');
module.exports.attendanceController = require('./attendance.controller');
module.exports.projectProgressController = require('./projectProgress.controller');
module.exports.projectTaskController = require('./projectTask.controller');
module.exports.projectFollowUpController = require('./projectFollowUp.controller');
module.exports.reimbursementController = require('./reimbursement.controller');
module.exports.subcontractController = require('./subcontract.controller');
module.exports.workhourController = require('./workhour.controller');
module.exports.userMessageController = require('./userMessage.controller');
