/**
 * 数据源配置服务
 * 允许在真实API和Mock数据之间切换
 */

// 导入数据源
import apiService from './apiService';
import mockService from './mockService';

// 数据源类型枚举
export const DataSourceType = {
  API: 'api',
  MOCK: 'mock'
};

// 存储当前数据源类型的键
const DATA_SOURCE_KEY = 'app_data_source';

// 默认数据源 - 默认使用API
const DEFAULT_DATA_SOURCE = DataSourceType.API;

/**
 * 获取当前数据源类型
 * @returns {string} 数据源类型 (api 或 mock)
 */
export const getCurrentDataSource = () => {
  // 生产环境强制使用API
  if (import.meta.env.PROD) {
    return DataSourceType.API;
  }

  const savedDataSource = localStorage.getItem(DATA_SOURCE_KEY) || DEFAULT_DATA_SOURCE;
  console.log(`当前数据源: ${savedDataSource}`);
  return savedDataSource;
};

/**
 * 设置当前数据源类型
 * @param {string} type - 数据源类型 (api 或 mock)
 */
export const setDataSource = (type) => {
  // 生产环境不允许设置为MOCK
  if (import.meta.env.PROD && type === DataSourceType.MOCK) {
    console.warn('生产环境不允许使用模拟数据');
    return;
  }

  if (type === DataSourceType.API || type === DataSourceType.MOCK) {
    localStorage.setItem(DATA_SOURCE_KEY, type);
    console.log(`数据源已切换为: ${type}`);
  } else {
    console.error('无效的数据源类型:', type);
  }
};

/**
 * 切换数据源类型
 * @returns {string} 切换后的数据源类型
 */
export const toggleDataSource = () => {
  // 生产环境不允许切换到MOCK
  if (import.meta.env.PROD) {
    console.warn('生产环境不允许使用模拟数据');
    return DataSourceType.API;
  }

  const currentType = getCurrentDataSource();
  const newType = currentType === DataSourceType.API
    ? DataSourceType.MOCK
    : DataSourceType.API;

  setDataSource(newType);
  return newType;
};

/**
 * 获取当前活跃的数据服务
 * @returns {Object} 数据服务对象
 */
export const getActiveService = () => {
  const currentType = getCurrentDataSource();
  // Set a global variable for debugging
  window.__currentDataSource = currentType;
  console.log(`💾 Active data source: ${currentType}`);
  return currentType === DataSourceType.MOCK ? mockService : apiService;
};

/**
 * 重置为默认数据源
 */
export const resetToDefaultDataSource = () => {
  setDataSource(DEFAULT_DATA_SOURCE);
  return DEFAULT_DATA_SOURCE;
};

// 默认导出当前活跃的数据服务
export default getActiveService();