---
description: 
globs: 
alwaysApply: false
---
# Development Environment Setup

## Getting Started
1. The project uses PNPM as the package manager
2. Use [start-pnpm.bat](mdc:start-pnpm.bat) for PNPM-based development
3. Use [start.bat](mdc:start.bat) for standard development setup

## Development Tools
- [check-data-source.js](mdc:check-data-source.js) can be used to verify your data source configuration
- Docker configurations are available in the `docker/` directory for containerized development

## Development Tasks
Refer to [todolist.md](mdc:todolist.md) for current development tasks and project status.

## Configuration
- VS Code settings are maintained in the `.vscode/` directory
- Docker configurations are in the `docker/` directory
- Deployment configurations can be found in the `deploy/` directory

##