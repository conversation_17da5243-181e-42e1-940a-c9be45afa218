const { test, expect } = require('@playwright/test');

test.describe('Users View Page', () => {
  test('should load users view page', async ({ page }) => {
    await page.goto('/users');
    await expect(page).toHaveURL(/.*users/);
    await expect(page.locator('.users-list')).toBeVisible();
  });

  test('should search users', async ({ page }) => {
    await page.goto('/users');
    await page.fill('input[type="search"]', 'test');
    await page.click('button[type="submit"]');
    await expect(page.locator('.users-list')).toContainText('test');
  });
}); 