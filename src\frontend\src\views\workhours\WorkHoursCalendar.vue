<template>
  <div class="max-w-7xl mx-auto px-4 py-6">
    <div class="bg-white rounded-lg shadow">
      <!-- 标题和返回按钮 -->
      <div class="p-4 border-b flex justify-between items-center">
        <div class="flex items-center">
          <button
            @click="router.back()"
            class="mr-4 text-gray-600 hover:text-gray-900"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          </button>
          <h2 class="text-xl font-semibold text-gray-800">
            {{ employeeName ? `${employeeName} - ` : '' }}月记工日历
          </h2>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <button
              @click="previousMonth"
              class="p-2 hover:bg-gray-100 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
            <span class="text-lg font-medium">{{ currentYearMonth }}</span>
            <button
              @click="nextMonth"
              class="p-2 hover:bg-gray-100 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
          <button
            @click="today"
            class="px-3 py-1 text-sm border rounded-lg hover:bg-gray-50"
          >
            今天
          </button>
        </div>
      </div>

      <!-- 日历主体 -->
      <div class="p-4">
        <!-- 星期标题 -->
        <div class="grid grid-cols-7 gap-px bg-gray-200 border border-gray-200 rounded-t-lg overflow-hidden">
          <div
            v-for="day in ['日', '一', '二', '三', '四', '五', '六']"
            :key="day"
            class="bg-gray-50 p-2 text-center text-sm font-medium text-gray-700"
          >
            {{ day }}
          </div>
        </div>

        <!-- 日期格子 -->
        <div class="grid grid-cols-7 gap-px bg-gray-200 border-l border-r border-b border-gray-200 rounded-b-lg overflow-hidden">
          <div
            v-for="(day, index) in calendarDays"
            :key="index"
            :class="[
              'bg-white min-h-[120px] p-2',
              day.isCurrentMonth ? 'bg-white' : 'bg-gray-50',
              day.isToday ? 'bg-blue-50' : '',
              day.hasWorkHours ? 'cursor-pointer hover:bg-blue-50' : ''
            ]"
            @click="day.hasWorkHours && viewDayDetail(day)"
          >
            <div class="flex justify-between items-start">
              <span
                :class="[
                  'inline-flex items-center justify-center w-6 h-6 text-sm rounded-full',
                  day.isToday ? 'bg-blue-500 text-white' : 'text-gray-700'
                ]"
              >
                {{ day.date.getDate() }}
              </span>
              <div v-if="day.hasWorkHours" class="flex flex-col items-end space-y-1">
                <span class="text-xs px-1.5 py-0.5 rounded bg-blue-100 text-blue-800">
                  {{ day.workHours }}h
                </span>
                <span v-if="day.overtime" class="text-xs px-1.5 py-0.5 rounded bg-yellow-100 text-yellow-800">
                  加班{{ day.overtimeHours }}h
                </span>
              </div>
            </div>
            <div v-if="day.hasWorkHours" class="mt-2">
              <div class="text-xs text-gray-500">{{ day.projectName }}</div>
              <div class="text-xs text-gray-500 truncate">{{ day.taskDescription }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 月度统计 -->
      <div class="p-4 border-t">
        <h3 class="text-lg font-medium mb-3">月度统计</h3>
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div class="bg-blue-50 p-4 rounded-lg">
            <div class="text-sm text-blue-800">总工时</div>
            <div class="text-2xl font-bold text-blue-600">{{ monthlyStats.totalHours }}h</div>
          </div>
          <div class="bg-green-50 p-4 rounded-lg">
            <div class="text-sm text-green-800">研发工时</div>
            <div class="text-2xl font-bold text-green-600">{{ monthlyStats.rdHours }}h</div>
          </div>
          <div class="bg-yellow-50 p-4 rounded-lg">
            <div class="text-sm text-yellow-800">加班工时</div>
            <div class="text-2xl font-bold text-yellow-600">{{ monthlyStats.overtimeHours }}h</div>
          </div>
          <div class="bg-purple-50 p-4 rounded-lg">
            <div class="text-sm text-purple-800">工时效率</div>
            <div class="text-2xl font-bold text-purple-600">{{ monthlyStats.efficiency }}%</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 日期详情弹窗 -->
    <div v-if="showDayDetail" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div class="bg-white rounded-lg p-6 w-full max-w-lg">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">{{ formatDate(selectedDay?.date) }} 记工详情</h3>
          <button @click="showDayDetail = false" class="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <div class="text-sm text-gray-500">工作时间</div>
              <div class="text-sm font-medium">{{ selectedDay?.startTime }} - {{ selectedDay?.endTime }}</div>
            </div>
            <div>
              <div class="text-sm text-gray-500">总工时</div>
              <div class="text-sm font-medium">{{ selectedDay?.workHours }}h</div>
            </div>
            <div>
              <div class="text-sm text-gray-500">项目名称</div>
              <div class="text-sm font-medium">{{ selectedDay?.projectName }}</div>
            </div>
            <div>
              <div class="text-sm text-gray-500">工作地点</div>
              <div class="text-sm font-medium">{{ selectedDay?.workLocation }}</div>
            </div>
          </div>
          <div>
            <div class="text-sm text-gray-500">工作内容</div>
            <div class="text-sm">{{ selectedDay?.taskDescription }}</div>
          </div>
          <div v-if="selectedDay?.overtime" class="bg-yellow-50 p-3 rounded">
            <div class="text-sm font-medium text-yellow-800">加班信息</div>
            <div class="text-sm text-yellow-700">加班时长：{{ selectedDay?.overtimeHours }}h</div>
            <div class="text-sm text-yellow-700">加班事由：{{ selectedDay?.overtimeReason }}</div>
          </div>
        </div>
        <div class="mt-6 flex justify-end">
          <button
            @click="showDayDetail = false"
            class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { workhoursService } from '@/services/api.service';

const router = useRouter();
const route = useRoute();

// 从路由参数获取员工信息
const employeeId = ref(route.query.employeeId || null);
const employeeName = ref(route.query.employeeName || null);

// 日历状态
const currentDate = ref(route.query.month ? new Date(route.query.month) : new Date());
const showDayDetail = ref(false);
const selectedDay = ref(null);

// 日历数据
const calendarDays = ref([]);

// 初始化日历数据
const initializeCalendarDays = () => {
  const year = currentDate.value.getFullYear();
  const month = currentDate.value.getMonth();
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);

  const days = [];
  const startOffset = firstDay.getDay();

  // 上个月的天数
  for (let i = startOffset - 1; i >= 0; i--) {
    const date = new Date(year, month, -i);
    days.push({
      date,
      isCurrentMonth: false,
      isToday: isSameDay(date, new Date()),
      hasWorkHours: false
    });
  }

  // 当前月的天数
  for (let i = 1; i <= lastDay.getDate(); i++) {
    const date = new Date(year, month, i);
    days.push({
      date,
      isCurrentMonth: true,
      isToday: isSameDay(date, new Date()),
      hasWorkHours: false,
      workHours: 0,
      overtimeHours: 0,
      overtime: false,
      projectName: '',
      taskDescription: '',
      startTime: '',
      endTime: '',
      workLocation: '',
      overtimeReason: ''
    });
  }

  // 下个月的天数
  const remainingDays = 42 - days.length;
  for (let i = 1; i <= remainingDays; i++) {
    const date = new Date(year, month + 1, i);
    days.push({
      date,
      isCurrentMonth: false,
      isToday: isSameDay(date, new Date()),
      hasWorkHours: false
    });
  }

  calendarDays.value = days;
};

// 计算当前年月显示
const currentYearMonth = computed(() => {
  return currentDate.value.getFullYear() + '年' + (currentDate.value.getMonth() + 1) + '月';
});

// 月度统计
const monthlyStats = ref({
  totalHours: '0.0',
  rdHours: '0.0',
  overtimeHours: '0.0',
  efficiency: '0.0'
});

// 日期操作函数
const previousMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1);
  updateRouteQuery();
  initializeCalendarDays();
};

const nextMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1);
  updateRouteQuery();
  initializeCalendarDays();
};

const today = () => {
  currentDate.value = new Date();
  updateRouteQuery();
  initializeCalendarDays();
};

// 更新路由查询参数
const updateRouteQuery = () => {
  router.push({
    query: {
      ...route.query,
      month: currentDate.value.toISOString().slice(0, 7)
    }
  });
};

// 查看日期详情
const viewDayDetail = (day) => {
  // 如果没有工时记录，不显示详情
  if (!day.hasWorkHours) return;
  
  // 根据 API 返回的日期数据格式化选中日的记录
  selectedDay.value = {
    ...day,
    // 确保日期正确显示
    date: new Date(day.date),
    // 确保工时正确显示
    workHours: parseFloat(day.workHours).toFixed(1),
    // 确保加班工时正确显示
    overtimeHours: day.overtime ? parseFloat(day.overtimeHours).toFixed(1) : 0,
    // 如果缺少项目名，提供默认值
    projectName: day.projectName || '未指定项目',
    // 如果缺少任务描述，提供默认值
    taskDescription: day.taskDescription || '无工作内容描述',
    // 如果缺少开始/结束时间，提供默认值
    startTime: day.startTime || '09:00',
    endTime: day.endTime || '18:00',
    // 如果缺少工作地点，提供默认值
    workLocation: day.workLocation || '办公室',
    // 如果缺少加班原因，提供默认值
    overtimeReason: day.overtime ? (day.overtimeReason || '项目需要') : ''
  };
  
  showDayDetail.value = true;
};

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  return `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`;
};

// 判断是否是同一天
const isSameDay = (date1, date2) => {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
};

// 监听路由参数变化
watch(() => route.query, (newQuery) => {
  if (newQuery.employeeId) {
    employeeId.value = newQuery.employeeId;
    employeeName.value = newQuery.employeeName;
  }
  if (newQuery.month) {
    currentDate.value = new Date(newQuery.month);
  }
  initializeCalendarDays();
}, { immediate: true });

// 加载员工记工数据
const loadEmployeeWorkHours = async () => {
  try {
    if (!employeeId.value) return;

    const params = {
      employeeId: employeeId.value,
      month: currentDate.value.toISOString().slice(0, 7)
    };

    const response = await workhoursService.getWorkHoursCalendar(params);
    console.log('加载员工记工数据成功:', response);
    
    // 处理返回的数据，更新日历显示
    if (response) {
      const { calendarData, summary } = response;
      
      if (calendarData && Array.isArray(calendarData)) {
        // 更新日历数据
        const updatedDays = [...calendarDays.value];
        
        calendarData.forEach(dayData => {
          // 使用date字段进行匹配，而不是依赖day字段
          const apiDate = new Date(dayData.date);
          
          // 找到对应的日期
          const dayIndex = updatedDays.findIndex(day => 
            day.isCurrentMonth && 
            day.date.getDate() === apiDate.getDate() &&
            day.date.getMonth() === apiDate.getMonth() &&
            day.date.getFullYear() === apiDate.getFullYear()
          );
          
          if (dayIndex !== -1 && dayData.hasRecords) {
            // 获取该天的第一条记录用于显示
            const firstRecord = dayData.records[0] || {};
            
            updatedDays[dayIndex] = {
              ...updatedDays[dayIndex],
              hasWorkHours: true,
              workHours: dayData.totalHours,
              projectName: firstRecord.projectName || '',
              taskDescription: firstRecord.remarks || '',
              startTime: firstRecord.startTime || '',
              endTime: firstRecord.endTime || '',
              workLocation: firstRecord.workLocation || '',
              overtime: Boolean(firstRecord.overtime),
              overtimeHours: firstRecord.overtime ? firstRecord.workHours : 0,
              overtimeReason: firstRecord.overtime ? (firstRecord.remarks || '加班') : ''
            };
          }
        });
        
        // 替换日历数据
        calendarDays.value = updatedDays;
      }
      
      // 更新月度统计
      if (summary) {
        monthlyStats.value = {
          totalHours: summary.totalHours.toFixed(1),
          rdHours: (summary.totalHours * 0.8).toFixed(1), // 假设研发工时占总工时的80%
          overtimeHours: (summary.totalHours * 0.2).toFixed(1), // 假设加班工时占总工时的20%
          efficiency: '95.0' // 假设固定效率
        };
      }
    }
  } catch (error) {
    console.error('加载员工记工数据失败:', error);
  }
};

// 初始化
onMounted(() => {
  initializeCalendarDays();
  loadEmployeeWorkHours();
});

// 监听日期变化
watch(currentDate, () => {
  loadEmployeeWorkHours();
});
</script>

<style scoped>
.aspect-square {
  aspect-ratio: 1;
}
</style>