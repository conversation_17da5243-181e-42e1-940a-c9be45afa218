const express = require('express');
const { authenticate } = require('../middlewares/auth.middleware');
const validate = require('../middlewares/validate');
const attachmentValidation = require('../validations/attachment.validation');
const attachmentController = require('../controllers/attachment.controller');
const multer = require('multer');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueFilename = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    // Add file type restrictions if needed
    cb(null, true);
  }
});

const router = express.Router();

router
  .route('/')
  .post(
    authenticate,
    upload.single('file'),
    validate(attachmentValidation.uploadAttachment),
    attachmentController.uploadAttachment
  );

router
  .route('/:attachmentId')
  .get(
    authenticate,
    validate(attachmentValidation.getAttachment),
    attachmentController.getAttachment
  )
  .delete(
    authenticate,
    validate(attachmentValidation.deleteAttachment),
    attachmentController.deleteAttachment
  );

router
  .route('/:attachmentId/download')
  .get(
    authenticate,
    validate(attachmentValidation.downloadAttachment),
    attachmentController.downloadAttachment
  );

module.exports = router;