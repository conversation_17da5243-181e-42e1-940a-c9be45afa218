const Joi = require('joi');

const createDepartmentSchema = {
  body: Joi.object().keys({
    name: Joi.string().required().min(2).max(50).messages({
      'string.empty': '部门名称不能为空',
      'string.min': '部门名称至少需要2个字符',
      'string.max': '部门名称不能超过50个字符',
      'any.required': '部门名称为必填项'
    }),
    code: Joi.string().required().pattern(/^[A-Za-z0-9_-]+$/).max(20).messages({
      'string.empty': '部门代码不能为空',
      'string.pattern.base': '部门代码格式不正确，只能包含字母、数字、下划线和连字符',
      'string.max': '部门代码不能超过20个字符',
      'any.required': '部门代码为必填项'
    }),
    description: Joi.string().allow('', null),
    parentId: Joi.number().integer().allow(null).messages({
      'number.base': '上级部门ID必须是数字',
      'number.integer': '上级部门ID必须是整数'
    }),
    status: Joi.string().valid('active', 'inactive').default('active').messages({
      'any.only': '状态必须是有效的类型（active或inactive）'
    })
  })
};

const updateDepartmentSchema = {
  params: Joi.object().keys({
    id: Joi.number().integer().required().messages({
      'number.base': '部门ID必须是数字',
      'number.integer': '部门ID必须是整数',
      'any.required': '部门ID为必填项'
    })
  }),
  body: Joi.object().keys({
    name: Joi.string().min(2).max(50).messages({
      'string.min': '部门名称至少需要2个字符',
      'string.max': '部门名称不能超过50个字符'
    }),
    code: Joi.string().pattern(/^[A-Za-z0-9_-]+$/).max(20).messages({
      'string.pattern.base': '部门代码格式不正确，只能包含字母、数字、下划线和连字符',
      'string.max': '部门代码不能超过20个字符'
    }),
    description: Joi.string().allow('', null),
    parentId: Joi.number().integer().allow(null).messages({
      'number.base': '上级部门ID必须是数字',
      'number.integer': '上级部门ID必须是整数'
    }),
    status: Joi.string().valid('active', 'inactive').messages({
      'any.only': '状态必须是有效的类型（active或inactive）'
    })
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const getDepartmentSchema = {
  params: Joi.object().keys({
    id: Joi.number().integer().required().messages({
      'number.base': '部门ID必须是数字',
      'number.integer': '部门ID必须是整数',
      'any.required': '部门ID为必填项'
    })
  })
};

const deleteDepartmentSchema = {
  params: Joi.object().keys({
    id: Joi.number().integer().required().messages({
      'number.base': '部门ID必须是数字',
      'number.integer': '部门ID必须是整数',
      'any.required': '部门ID为必填项'
    })
  })
};

const getDepartmentsSchema = {
  query: Joi.object().keys({
    name: Joi.string().allow('', null),
    code: Joi.string().allow('', null),
    status: Joi.string().valid('active', 'inactive').allow('', null).messages({
      'any.only': '状态必须是有效的类型（active或inactive）'
    }),
    parentId: Joi.number().integer().allow(null).messages({
      'number.base': '上级部门ID必须是数字',
      'number.integer': '上级部门ID必须是整数'
    }),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于1'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于1',
      'number.max': '每页数量不能超过100000'
    }),
    sortBy: Joi.string().valid('name', 'code', 'createdAt', 'status').default('name').messages({
      'any.only': '排序字段必须是有效的类型'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').default('asc').messages({
      'any.only': '排序方式必须是有效的类型'
    })
  })
};

module.exports = {
  createDepartmentSchema,
  updateDepartmentSchema,
  getDepartmentSchema,
  deleteDepartmentSchema,
  getDepartmentsSchema
};
