const bcrypt = require('bcryptjs');

async function testPassword() {
  const storedHash = '$2b$10$qYjpaA3DwUCt5LafOTBwWe582Lia3KfPtiJTvCOzfrCc4wFFbIfvS'; // 从数据库中获取的密码哈希
  const testPassword = 'password123'; // 用户输入的密码
  
  try {
    const isMatch = await bcrypt.compare(testPassword, storedHash);
    console.log(`Password match: ${isMatch}`);
  } catch (error) {
    console.error('Error comparing passwords:', error);
  }
}

testPassword();
