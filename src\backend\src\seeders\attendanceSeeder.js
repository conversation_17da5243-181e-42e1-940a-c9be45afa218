const { Attendance, User, Project } = require('../models');
const { faker } = require('@faker-js/faker/locale/zh_CN');

const attendanceTypes = ['正常', '迟到', '早退', '加班', '请假', '外勤'];
const leaveTypes = ['年假', '病假', '事假', '调休', '婚假', '产假', '陪产假'];
const overtimeTypes = ['工作日加班', '周末加班', '节假日加班'];

async function seedAttendance() {
  try {
    // Clear existing data
    await Attendance.destroy({ where: {} });

    // Get all users and projects for reference
    const users = await User.findAll();
    const projects = await Project.findAll();

    if (!users.length) {
      console.log('Please seed users first');
      return;
    }

    if (!projects.length) {
      console.log('Please seed projects first');
      return;
    }

    const attendanceRecords = [];

    // Create attendance records for the past 30 days for each user
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    for (const user of users) {
      // Assign a random project to this user
      const userProject = faker.helpers.arrayElement(projects);
      
      let currentDate = new Date(thirtyDaysAgo);
      
      while (currentDate <= today) {
        // Skip weekends
        if (currentDate.getDay() !== 0 && currentDate.getDay() !== 6) {
          const type = faker.helpers.arrayElement(attendanceTypes);
          
          // 设置基本的上下班时间
          let timeIn = faker.date.between({
            from: `${currentDate.toISOString().split('T')[0]} 08:30:00`,
            to: `${currentDate.toISOString().split('T')[0]} 09:30:00`
          });
          
          let timeOut = faker.date.between({
            from: `${currentDate.toISOString().split('T')[0]} 17:30:00`,
            to: `${currentDate.toISOString().split('T')[0]} 18:30:00`
          });

          // 根据考勤类型调整时间
          if (type === '迟到') {
            timeIn = faker.date.between({
              from: `${currentDate.toISOString().split('T')[0]} 09:31:00`,
              to: `${currentDate.toISOString().split('T')[0]} 10:30:00`
            });
          } else if (type === '早退') {
            timeOut = faker.date.between({
              from: `${currentDate.toISOString().split('T')[0]} 16:30:00`,
              to: `${currentDate.toISOString().split('T')[0]} 17:29:00`
            });
          } else if (type === '请假') {
            // 请假时设置标准的上下班时间
            timeIn = new Date(`${currentDate.toISOString().split('T')[0]} 09:00:00`);
            timeOut = new Date(`${currentDate.toISOString().split('T')[0]} 18:00:00`);
          }

          const record = {
            employeeId: user.id,
            employeeName: user.username,
            projectId: userProject.id,
            date: new Date(currentDate),
            type: type,
            timeIn: timeIn,
            timeOut: timeOut,
            status: type === '请假' ? 'absent' : type === '迟到' ? 'late' : type === '早退' ? 'half-day' : 'present',
            location: faker.location.streetAddress(),
            notes: type === '请假' ? `${faker.helpers.arrayElement(leaveTypes)}请假` : faker.lorem.sentence(),
            createdBy: user.id,
            createdAt: new Date(),
            updatedAt: new Date()
          };

          attendanceRecords.push(record);
        }
        
        // Move to next day
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }

    await Attendance.bulkCreate(attendanceRecords);
    console.log('Attendance seeder executed successfully');
  } catch (error) {
    console.error('Error seeding attendance:', error);
  }
}

module.exports = seedAttendance; 