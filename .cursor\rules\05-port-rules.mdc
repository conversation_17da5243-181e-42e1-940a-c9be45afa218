---
description: 
globs: 
alwaysApply: false
---
# Port Usage Rules

## General Port Rules
- Do not modify existing port configurations
- All services must use their pre-defined ports
- Port changes require explicit approval from project maintainers

## Port Assignments
- Keep all existing port configurations as is
- Document any port conflicts but do not modify them without approval
- New services should avoid conflicts with existing port assignments

## Configuration Files
- Port configurations should be treated as constants
- Port numbers should be documented but not changed
- Any port-related issues should be reported rather than modified

## Development Guidelines
- Use the default ports provided in configuration files
- Do not override port settings in local development
- If port conflicts occur, report the issue rather than changing ports

## Security Considerations
- Maintain consistent port usage for security scanning
- Keep firewall rules aligned with established port configurations
- Document all port-related security requirements 