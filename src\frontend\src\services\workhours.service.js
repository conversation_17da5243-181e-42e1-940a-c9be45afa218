import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || '';

const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor for adding auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;

    // Handle token expiration
    if (response && response.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

const workhoursService = {
  getWorkHours: (params) => {
    return apiClient.get('/api/workhours', { params });
  },
  
  getWorkHour: (id) => {
    return apiClient.get(`/api/workhours/${id}`);
  },
  
  createWorkHour: (data) => {
    return apiClient.post('/api/workhours', data);
  },
  
  updateWorkHour: (id, data) => {
    return apiClient.put(`/api/workhours/${id}`, data);
  },
  
  deleteWorkHour: (id) => {
    return apiClient.delete(`/api/workhours/${id}`);
  },
  
  getWorkHoursStatistics: (params) => {
    return apiClient.get('/api/workhours/statistics', { params });
  },
  
  getWorkHoursCalendar: (params) => {
    return apiClient.get('/api/workhours/calendar', { params });
  }
};

export default workhoursService;
