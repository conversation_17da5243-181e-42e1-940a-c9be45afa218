<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和按钮 -->
    <div class="flex justify-between items-center mb-8">
      <div class="flex items-center">
        <router-link v-if="!isPublicMode" to="/finance" class="text-blue-600 hover:text-blue-800 mr-3 transition-colors duration-200">
      
        </router-link>
        <h1 class="text-2xl font-bold text-gray-900">{{ isPublicMode ? '财务数据展示' : '财务信息统计' }}</h1>
      </div>      <div class="flex items-center space-x-3">
        <router-link v-if="!isPublicMode" to="/finance" class="btn  flex items-center shadow-md hover:shadow-lg transition-all duration-200">
               <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          返回列表
        </router-link>
        <router-link v-if="isPublicMode" to="/login" class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
          </svg>
          登录系统
        </router-link>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2">{{ error }}</p>
      <button @click="fetchFinanceStats()" class="mt-2 text-red-700 hover:text-red-900 font-medium underline">
        重试
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>

    <!-- 统计内容 -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 总收入卡片 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500">总收入</p>
            <p class="text-2xl font-bold text-gray-900 mt-1">¥{{ formatNumber(stats.totalIncome) }}</p>
          </div>
          <div class="bg-green-100 rounded-full p-3">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- 总支出卡片 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500">总支出</p>
            <p class="text-2xl font-bold text-gray-900 mt-1">¥{{ formatNumber(stats.totalExpense) }}</p>
          </div>
          <div class="bg-red-100 rounded-full p-3">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- 净利润卡片 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500">净利润</p>
            <p class="text-2xl font-bold text-gray-900 mt-1">¥{{ formatNumber(stats.netProfit) }}</p>
          </div>
          <div class="bg-blue-100 rounded-full p-3">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- 收入类型分布 -->
      <div class="bg-white rounded-xl shadow-sm p-6 md:col-span-2">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">收入类型分布</h3>
          <div class="flex space-x-2">
            <button
              @click="incomeChartType = 'amount'"
              :class="[
                'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                incomeChartType === 'amount'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              金额
            </button>
            <button
              @click="incomeChartType = 'count'"
              :class="[
                'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                incomeChartType === 'count'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              数量
            </button>
          </div>
        </div>
        <ECharts :option="incomeChartOption" height="300px" />
      </div>

      <!-- 支出类型分布 -->
      <div class="bg-white rounded-xl shadow-sm p-6 md:col-span-2">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">支出类型分布</h3>
          <div class="flex space-x-2">
            <button
              @click="expenseChartType = 'amount'"
              :class="[
                'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                expenseChartType === 'amount'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              金额
            </button>
            <button
              @click="expenseChartType = 'count'"
              :class="[
                'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                expenseChartType === 'count'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              数量
            </button>
          </div>
        </div>
        <ECharts :option="expenseChartOption" height="300px" />
      </div>

      <!-- 月度收支趋势 -->
      <div class="bg-white rounded-xl shadow-sm p-6 md:col-span-3">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">月度收支趋势</h3>
        <ECharts :option="trendChartOption" height="400px" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import apiService from '@/services/apiService'

const route = useRoute()
const isPublicMode = computed(() => route.path.includes('/public'))

const isLoading = ref(true)
const error = ref(null)
const incomeChartType = ref('amount')
const expenseChartType = ref('amount')
const stats = ref({
  totalIncome: 0,
  totalExpense: 0,
  netProfit: 0,
  incomeTypes: {},
  expenseTypes: {},
  monthlyTrend: []
})

// 格式化数字
function formatNumber(num) {
  return new Intl.NumberFormat('zh-CN').format(num)
}

// 收入类型分布图表选项
const incomeChartOption = computed(() => {
  const data = incomeChartType.value === 'amount'
    ? Object.entries(stats.value.incomeTypes).map(([name, value]) => ({
        name,
        value: value.amount
      }))
    : Object.entries(stats.value.incomeTypes).map(([name, value]) => ({
        name,
        value: value.count
      }))

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: data.map(item => item.name)
    },
    series: [
      {
        name: incomeChartType.value === 'amount' ? '收入金额' : '收入笔数',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: data
      }
    ]
  }
})

// 支出类型分布图表选项
const expenseChartOption = computed(() => {
  const data = expenseChartType.value === 'amount'
    ? Object.entries(stats.value.expenseTypes).map(([name, value]) => ({
        name,
        value: value.amount
      }))
    : Object.entries(stats.value.expenseTypes).map(([name, value]) => ({
        name,
        value: value.count
      }))

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: data.map(item => item.name)
    },
    series: [
      {
        name: expenseChartType.value === 'amount' ? '支出金额' : '支出笔数',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: data
      }
    ]
  }
})

// 月度收支趋势图表选项
const trendChartOption = computed(() => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['收入', '支出', '净利润']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: stats.value.monthlyTrend.map(item => item.month)
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '收入',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: stats.value.monthlyTrend.map(item => item.income)
      },
      {
        name: '支出',
        type: 'line',
        stack: 'Total',
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        data: stats.value.monthlyTrend.map(item => item.expense)
      },
      {
        name: '净利润',
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        data: stats.value.monthlyTrend.map(item => item.netProfit)
      }
    ]
  }
})

// 获取财务统计数据
const fetchFinanceStats = async () => {
  try {
    isLoading.value = true
    error.value = null

    // 根据模式选择不同的API端点
    if (isPublicMode.value) {
      const response = await apiService.getPublicFinanceStats()
      stats.value = response
    } else {
      // 调用需要授权的API获取财务统计数据
      const response = await apiService.getFinanceStats()
      stats.value = response
    }

    console.log('财务统计数据:', stats.value)
  } catch (err) {
    console.error('获取财务统计数据失败:', err)
    error.value = err.response?.data?.message || err.message || '获取财务统计数据失败，请重试'

    // 如果API调用失败，初始化空数据结构
    stats.value = {
      totalIncome: 0,
      totalExpense: 0,
      netProfit: 0,
      incomeTypes: {},
      expenseTypes: {},
      monthlyTrend: []
    }
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchFinanceStats()
})
</script>