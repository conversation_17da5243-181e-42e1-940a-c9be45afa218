const express = require('express');
const router = express.Router();
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const employeeController = require('../controllers/employee.controller');
const employeeValidation = require('../validations/employee.validation');

/**
 * @route GET /api/employees/contract-workers
 * @desc Get all contract workers
 * @access Private
 */
router.get('/', auth('getEmployees'), validate(employeeValidation.getContractWorkers), employeeController.getContractWorkers);

/**
 * @route POST /api/employees/contract-workers
 * @desc Create a new contract worker
 * @access Private
 */
router.post('/', auth('manageEmployees'), validate(employeeValidation.createContractWorker), employeeController.createContractWorker);

/**
 * @route GET /api/employees/contract-workers/:workerId
 * @desc Get a contract worker by ID
 * @access Private
 */
router.get('/:workerId', auth('getEmployees'), validate(employeeValidation.getContractWorker), employeeController.getContractWorker);

/**
 * @route PUT /api/employees/contract-workers/:workerId
 * @desc Update a contract worker
 * @access Private
 */
router.put('/:workerId', auth('manageEmployees'), validate(employeeValidation.updateContractWorker), employeeController.updateContractWorker);

/**
 * @route DELETE /api/employees/contract-workers/:workerId
 * @desc Delete a contract worker
 * @access Private
 */
router.delete('/:workerId', auth('manageEmployees'), validate(employeeValidation.deleteContractWorker), employeeController.deleteContractWorker);

module.exports = router;
