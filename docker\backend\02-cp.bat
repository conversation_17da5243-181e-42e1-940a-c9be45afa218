@echo off
setlocal
cd %~dp0

REM Set environment variables
set DEPLOY_ENV=production
set SERVER_HOST=*************
set SERVER_USER=%user35%
set SERVER_PASSWORD=%pwd35%
set REMOTE_DIR=/root/images/
set IMAGE_TAG=yihe-backend


REM Deploy to server using pscp
echo Deploying to server...
echo Checking today's backup on remote server...
set TODAY_BACKUP_DIR=%REMOTE_DIR%\%date:~0,4%%date:~5,2%%date:~8,2%

pscp -batch -pw %SERVER_PASSWORD% -r %cd%\*.tar %SERVER_USER%@%SERVER_HOST%:%REMOTE_DIR%

plink -pw %SERVER_PASSWORD% %SERVER_USER%@%SERVER_HOST% "cd %REMOTE_DIR%;docker inspect --format '{{range $p, $conf := .NetworkSettings.Ports}}-p {{(index $conf 0).HostPort}}:{{$p}} {{end}} {{range .Config.Env}}-e {{.}} {{end}} {{range .Mounts}}-v {{.Source}}:{{.Destination}}{{end}}' yihe-backend > env.txt; docker stop yihe-backend; docker rm yihe-backend; docker load < yihe-backend.tar; docker run -d --name yihe-backend $(cat env.txt) %IMAGE_TAG%"



echo Deployment completed!
endlocal