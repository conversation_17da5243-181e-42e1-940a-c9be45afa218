const { sequelize } = require('./src/config/database');
const { Product } = require('./src/models/product.model');
const { User } = require('./src/models/user.model');

async function createSampleProducts() {
  try {
    await sequelize.authenticate();
    console.log('数据库连接成功');

    // Find an admin user to use as creator
    const adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      console.error('未找到管理员用户，请先创建管理员用户');
      return;
    }

    const sampleProducts = [
      {
        name: '钢筋',
        code: 'STL001',
        category: '建材',
        description: '高强度钢筋，用于建筑结构',
        price: 5000.00,
        unit: '吨',
        stock: 5,
        minStock: 20,
        status: 'active',
        createdBy: adminUser.id
      },
      {
        name: '水泥',
        code: 'CMT001',
        category: '建材',
        description: '优质水泥，适用于各种建筑工程',
        price: 400.00,
        unit: '吨',
        stock: 8,
        minStock: 15,
        status: 'active',
        createdBy: adminUser.id
      },
      {
        name: '电缆',
        code: 'CBL001',
        category: '电气',
        description: '高压电缆，用于电力传输',
        price: 15.00,
        unit: '米',
        stock: 200,
        minStock: 500,
        status: 'active',
        createdBy: adminUser.id
      },
      {
        name: '螺栓',
        code: 'BLT001',
        category: '五金',
        description: '不锈钢螺栓，M12规格',
        price: 4.00,
        unit: '个',
        stock: 50,
        minStock: 100,
        status: 'active',
        createdBy: adminUser.id
      },
      {
        name: '砖块',
        code: 'BRK001',
        category: '建材',
        description: '红砖，标准规格',
        price: 0.50,
        unit: '块',
        stock: 1000,
        minStock: 5000,
        status: 'active',
        createdBy: adminUser.id
      },
      {
        name: '油漆',
        code: 'PNT001',
        category: '装饰',
        description: '环保内墙漆',
        price: 120.00,
        unit: '桶',
        stock: 3,
        minStock: 10,
        status: 'active',
        createdBy: adminUser.id
      }
    ];

    // Check if products already exist and create only new ones
    for (const productData of sampleProducts) {
      const existingProduct = await Product.findOne({ where: { code: productData.code } });
      if (!existingProduct) {
        await Product.create(productData);
        console.log(`创建产品: ${productData.name} (${productData.code})`);
      } else {
        console.log(`产品已存在: ${productData.name} (${productData.code})`);
      }
    }

    console.log('样本产品创建完成');

    // Show low stock items
    const lowStockItems = await Product.findAll({
      where: sequelize.literal('"product"."stock" <= "product"."minStock"'),
      attributes: ['name', 'code', 'stock', 'minStock', 'category']
    });

    console.log('\n库存不足物品:');
    lowStockItems.forEach(item => {
      console.log(`- ${item.name} (${item.code}): 当前库存 ${item.stock}, 最低库存 ${item.minStock}`);
    });

  } catch (error) {
    console.error('创建样本产品失败:', error);
  } finally {
    await sequelize.close();
  }
}

createSampleProducts(); 