const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ProjectClientAssociation = sequelize.define('projectClientAssociation', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },  projectId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'project',
      key: 'id'
    }
  },
  clientId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'client',
      key: 'id'
    }
  },
  role: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Role of the client in the project (e.g., main_client, partner, consultant)'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'project_client_associations',
  timestamps: true,
  indexes: [
    {
      unique: true,
      fields: ['projectId', 'clientId'],
      name: 'unique_project_client_association'
    },
    {
      fields: ['projectId']
    },
    {
      fields: ['clientId']
    }
  ]
});

module.exports = { ProjectClientAssociation };
