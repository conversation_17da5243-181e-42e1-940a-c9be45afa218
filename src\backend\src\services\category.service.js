const httpStatus = require('http-status');
const { Category } = require('../models');
const ApiError = require('../utils/ApiError');
const { Op } = require('sequelize');

/**
 * Create a category
 * @param {Object} categoryBody
 * @returns {Promise<Category>}
 */
const createCategory = async (categoryBody) => {
  return Category.create(categoryBody);
};

/**
 * Query for categories
 * @param {Object} filter - Sequelize filter
 * @param {Object} options - Query options
 * @param {string} [options.sortBy] - Sort option in the format: sortField:(asc|desc)
 * @param {number} [options.limit] - Maximum number of results per page
 * @param {number} [options.page] - Current page
 * @returns {Promise<Object>}
 */
const queryCategories = async (filter, options) => {
  const { limit, offset } = options;

  // Handle sorting
  let order = [];
  if (options.sortBy && options.sortOrder) {
    order.push([options.sortBy, options.sortOrder]);
  } else {
    order.push(['sortOrder', 'ASC']); // Default sorting
  }

  // Clean up filter
  const whereClause = {};
  if (filter.name) {
    whereClause.name = { [Op.iLike]: `%${filter.name}%` };
  }
  if (filter.isActive !== undefined) {
    whereClause.isActive = filter.isActive;
  }
  if (filter.parentId !== undefined) {
    whereClause.parentId = filter.parentId === '' ? null : filter.parentId;
  }

  const categories = await Category.findAndCountAll({
    where: whereClause,
    limit,
    offset,
    order,
    include: [
      {
        model: Category,
        as: 'children',
        attributes: ['id'],
        required: false
      }
    ],
    distinct: true
  });

  // Transform results to include counts if requested
  const results = categories.rows.map(category => {
    const result = category.toJSON();
    if (options.includeCounts) {
      result.childCount = category.children ? category.children.length : 0;
    }
    delete result.children;
    return result;
  });

  return {
    results,
    page: options.page,
    limit: options.limit,
    totalPages: Math.ceil(categories.count / options.limit),
    totalResults: categories.count,
  };
};

/**
 * Get category by id
 * @param {string} id
 * @returns {Promise<Category>}
 */
const getCategoryById = async (id) => {
  return Category.findByPk(id, {
    include: [
      {
        model: Category,
        as: 'children',
        attributes: ['id', 'name', 'isActive', 'sortOrder'],
        required: false
      },
      {
        model: Category,
        as: 'parent',
        attributes: ['id', 'name'],
        required: false
      }
    ]
  });
};

/**
 * Update category by id
 * @param {string} categoryId
 * @param {Object} updateBody
 * @returns {Promise<Category>}
 */
const updateCategoryById = async (categoryId, updateBody) => {
  const category = await getCategoryById(categoryId);
  if (!category) {
    throw new ApiError(404, 'Category not found');
  }

  // Prevent circular parent reference
  if (updateBody.parentId === categoryId) {
    throw new ApiError(400, 'Category cannot be its own parent');
  }

  Object.assign(category, updateBody);
  await category.save();
  return category;
};

/**
 * Delete category by id
 * @param {string} categoryId
 * @returns {Promise<void>}
 */
const deleteCategoryById = async (categoryId) => {
  const category = await getCategoryById(categoryId);
  if (!category) {
    throw new ApiError(404, 'Category not found');
  }

  // Check for child categories
  const childCount = await Category.count({ where: { parentId: categoryId } });
  if (childCount > 0) {
    throw new ApiError(400, 'Cannot delete category with child categories');
  }

  await category.destroy();
};

module.exports = {
  createCategory,
  queryCategories,
  getCategoryById,
  updateCategoryById,
  deleteCategoryById,
};