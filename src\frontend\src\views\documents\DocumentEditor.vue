<template>
  <div class="max-w-6xl mx-auto">
    <!-- Editor Header -->
    <div class="mb-6 flex items-center justify-between">
      <h1 class="text-2xl font-bold text-gray-900">
        {{ isEditMode ? 'Edit Document' : 'Create New Document' }}
      </h1>
      <div class="flex space-x-3">
        <button
          @click="$router.back()"
          class="btn btn-secondary"
        >
          Cancel
        </button>
        <button
          @click="saveDocument"
          class="btn btn-primary"
          :disabled="isSaving"
        >
          <span v-if="isSaving" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
          </span>
          <span v-else>Save Document</span>
        </button>
      </div>
    </div>

    <!-- Error messages -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
    </div>

    <!-- Success messages -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- Document Form -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden border">
      <!-- Title Section -->
      <div class="p-6 border-b">
        <input
          v-model="documentForm.title"
          type="text"
          placeholder="Document Title"
          class="w-full text-2xl font-bold border-none focus:outline-none focus:ring-0"
        >
      </div>

      <!-- Document Metadata -->
      <div class="p-6 bg-gray-50 grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label for="category" class="form-label">Category</label>
          <select
            id="category"
            v-model="documentForm.categoryId"
            class="form-input"
          >
            <option value="">No Category</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>
        </div>

        <div>
          <label for="tags" class="form-label">Tags</label>
          <div class="relative">
            <input
              id="tag-input"
              v-model="tagInput"
              @keydown.enter.prevent="addTag"
              @keydown.tab.prevent="addTag"
              type="text"
              placeholder="Add tags separated by comma or enter"
              class="form-input pr-10"
            >
            <button
              @click="addTag"
              class="absolute right-2 top-2 text-gray-400 hover:text-gray-700"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </button>
          </div>

          <div class="flex flex-wrap gap-2 mt-2">
            <div
              v-for="(tag, index) in documentForm.tags"
              :key="index"
              class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full flex items-center"
            >
              {{ tag.name }}
              <button
                @click="removeTag(index)"
                class="ml-1 text-blue-700 hover:text-blue-900"
              >
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div class="md:col-span-2">
          <label for="description" class="form-label">Description (Optional)</label>
          <textarea
            id="description"
            v-model="documentForm.description"
            placeholder="Brief description of the document"
            rows="2"
            class="form-input"
          ></textarea>
        </div>
      </div>

      <!-- Rich Text Editor -->
      <div class="p-6">
        <label for="content" class="form-label">Document Content</label>
        <div class="border rounded-md">
          <!-- Editor Toolbar -->
          <div class="flex flex-wrap gap-1 p-2 border-b bg-gray-50">
            <button
              v-for="format in editorFormats"
              :key="format.command"
              @click="executeFormat(format.command, format.value)"
              :class="[
                'p-1.5 rounded hover:bg-gray-200 text-gray-700',
                isFormatActive(format.command, format.value) ? 'bg-gray-200' : ''
              ]"
              :title="format.title"
            >
              <span v-html="format.icon"></span>
            </button>

            <div class="border-l h-6 mx-1 my-auto"></div>

            <select
              @change="executeFormat('formatBlock', $event.target.value)"
              class="p-1 text-sm border rounded bg-white"
            >
              <option value="">Paragraph</option>
              <option value="h1">Heading 1</option>
              <option value="h2">Heading 2</option>
              <option value="h3">Heading 3</option>
              <option value="blockquote">Blockquote</option>
              <option value="pre">Code Block</option>
            </select>
          </div>

          <!-- Content Editable Div -->
          <div
            id="editor"
            ref="editorRef"
            contenteditable="true"
            @paste="handlePaste"
            class="min-h-[400px] p-4 focus:outline-none overflow-auto"
          ></div>
        </div>
      </div>

      <!-- Attachments Section -->
      <div class="p-6 border-t">
        <label class="form-label">Attachments</label>
        <div class="mt-2">
          <div class="flex items-center justify-center w-full">
            <label
              for="file-upload"
              class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
            >
              <div class="flex flex-col items-center justify-center pt-5 pb-6">
                <svg class="w-8 h-8 mb-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <p class="mb-2 text-sm text-gray-500">
                  <span class="font-semibold">Click to upload</span> or drag and drop
                </p>
                <p class="text-xs text-gray-500">PDF, DOCX, XLSX, JPG, PNG (MAX. 10MB)</p>
              </div>
              <input
                id="file-upload"
                type="file"
                multiple
                class="hidden"
                @change="handleFileUpload"
              />
            </label>
          </div>
        </div>

        <!-- Attachment List -->
        <div v-if="documentForm.attachments.length > 0" class="mt-4">
          <h3 class="text-sm font-medium text-gray-700 mb-2">Uploaded Files</h3>
          <ul class="divide-y divide-gray-200">
            <li v-for="(file, index) in documentForm.attachments" :key="index" class="py-3 flex justify-between items-center">
              <div class="flex items-center">
                <div class="bg-blue-50 p-2 rounded-md mr-3">
                  <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                  </svg>
                </div>
                <div class="truncate">
                  <p class="text-sm font-medium text-gray-900">{{ file.originalName || file.fileName }}</p>
                  <p class="text-xs text-gray-500">
                    {{ formatSize(file.fileSize || file.size) }}
                  </p>
                </div>
              </div>
              <button @click="removeAttachment(index)" class="text-red-500 hover:text-red-700">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';

const route = useRoute();
const router = useRouter();
const editorRef = ref(null);

// State
const documentForm = ref({
  title: '',
  description: '',
  content: '',
  categoryId: '',
  tags: [],
  attachments: []
});

const isEditMode = ref(false);
const documentId = ref(null);
const isSaving = ref(false);
const error = ref('');
const successMessage = ref('');
const categories = ref([]);
const tagInput = ref('');

// Rich text editor formatting options
const editorFormats = [
  {
    command: 'bold',
    icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 4h8a4 4 0 014 4 4 4 0 01-4 4H6"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 12h9a4 4 0 014 4 4 4 0 01-4 4H6"></path></svg>',
    title: 'Bold'
  },
  {
    command: 'italic',
    icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9H8M10 9V5H8M10 9L8 15M14 9H16M14 9V5H16M14 9L16 15M10 15H8M10 15V19H8M10 15L8 9M14 15H16M14 15V19H16M14 15L16 9"></path></svg>',
    title: 'Italic'
  },
  {
    command: 'underline',
    icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>',
    title: 'Underline'
  },
  {
    command: 'insertUnorderedList',
    icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>',
    title: 'Bullet List'
  },
  {
    command: 'insertOrderedList',
    icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"></path></svg>',
    title: 'Numbered List'
  },
  {
    command: 'createLink',
    icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path></svg>',
    title: 'Insert Link'
  },
  {
    command: 'unlink',
    icon: '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"></path></svg>',
    title: 'Remove Link'
  }
];

onMounted(async () => {
  try {
    // Fetch categories
    const categoryResponse = await axios.get('/api/categories');
    categories.value = categoryResponse.data.results;

    // Check if we're in edit mode
    documentId.value = route.params.id;
    isEditMode.value = !!documentId.value;

    if (isEditMode.value) {
      await fetchDocument(documentId.value);
    }
  } catch (err) {
    console.error('Error initializing editor:', err);
    error.value = 'Failed to initialize editor. Please try again.';
  }
});

// Watch for changes in document content and update the form
watch(() => editorRef.value?.innerHTML, (newContent) => {
  if (newContent !== undefined) {
    documentForm.value.content = newContent;
  }
}, { immediate: true });

// Fetch existing document for editing
const fetchDocument = async (id) => {
  try {
    const response = await axios.get(`/api/documents/${id}`);
    const doc = response.data;

    // Populate form fields
    documentForm.value.title = doc.title || '';
    documentForm.value.description = doc.description || '';
    documentForm.value.categoryId = doc.categoryId || '';
    documentForm.value.tags = doc.tags || [];
    documentForm.value.attachments = doc.attachments || [];

    // Set content to editor
    nextTick(() => {
      if (editorRef.value && doc.content) {
        editorRef.value.innerHTML = doc.content;
      }
    });
  } catch (err) {
    console.error('Error fetching document:', err);
    error.value = 'Failed to load document. Please try again.';
  }
};

// Execute formatting commands
const executeFormat = (command, value) => {
  if (command === 'createLink') {
    const url = prompt('Enter URL:');
    if (url) {
      document.execCommand(command, false, url);
    }
  } else {
    document.execCommand(command, false, value);
  }
  editorRef.value.focus();
};

// Check if format is active
const isFormatActive = (command, value) => {
  return document.queryCommandState(command);
};

// Handle file uploads
const handleFileUpload = async (event) => {
  const files = event.target.files;
  if (!files || files.length === 0) return;

  for (const file of files) {
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      error.value = `文件 ${file.name} 超过了10MB的大小限制。`;
      setTimeout(() => {
        error.value = '';
      }, 5000);
      continue;
    }

    try {
      if (isEditMode.value) {
        // If editing an existing document, upload the file immediately
        const formData = new FormData();
        formData.append('file', file);

        const response = await axios.post(`/api/documents/${documentId.value}/attachments`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });

        documentForm.value.attachments.push(response.data);
      } else {
        // If creating a new document, store the file temporarily
        documentForm.value.attachments.push({
          file,
          originalName: file.name,
          size: file.size
        });
      }
    } catch (err) {
      console.error('Error uploading file:', err);
      error.value = `上传文件 ${file.name} 失败，请稍后重试。`;
      setTimeout(() => {
        error.value = '';
      }, 5000);
    }
  }

  // Clear the input
  event.target.value = '';
};

// Remove attachment
const removeAttachment = async (index) => {
  const attachment = documentForm.value.attachments[index];

  if (isEditMode.value && attachment.id) {
    // If editing and the attachment exists on the server
    try {
      await axios.delete(`/api/attachments/${attachment.id}`);
    } catch (err) {
      console.error('Error deleting attachment:', err);
      error.value = '删除附件失败，请稍后重试。';
      setTimeout(() => {
        error.value = '';
      }, 5000);
      return;
    }
  }

  // Remove from the list
  documentForm.value.attachments.splice(index, 1);
};

// Format file size for display
const formatSize = (bytes) => {
  if (!bytes) return '0 Bytes';

  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${parseFloat((bytes / Math.pow(1024, i)).toFixed(2))} ${sizes[i]}`;
};

// Add tag to document
const addTag = () => {
  if (!tagInput.value.trim()) return;

  // Handle multiple tags separated by comma
  const tagNames = tagInput.value.split(',').map(t => t.trim()).filter(t => t);

  for (const tagName of tagNames) {
    // Check if tag already exists
    if (!documentForm.value.tags.some(t => t.name?.toLowerCase() === tagName.toLowerCase())) {
      documentForm.value.tags.push({ name: tagName });
    }
  }

  tagInput.value = '';
};

// Remove tag from document
const removeTag = (index) => {
  documentForm.value.tags.splice(index, 1);
};

// Handle paste events to clean up pasted content
const handlePaste = (e) => {
  e.preventDefault();

  // Get text representation of clipboard
  const text = e.clipboardData.getData('text/plain');

  // Insert text at cursor position
  document.execCommand('insertText', false, text);
};

// Save document
const saveDocument = async () => {
  // Basic validation
  if (!documentForm.value.title.trim()) {
    error.value = 'Document title is required';
    return;
  }

  if (!documentForm.value.content.trim() && editorRef.value?.innerHTML.trim() === '') {
    error.value = 'Document content is required';
    return;
  }

  // Get content from editor if not already set
  if (editorRef.value && !documentForm.value.content.trim()) {
    documentForm.value.content = editorRef.value.innerHTML;
  }

  isSaving.value = true;
  error.value = '';

  try {
    let response;
    const docData = {
      title: documentForm.value.title,
      content: documentForm.value.content,
      description: documentForm.value.description,
      categoryId: documentForm.value.categoryId || null,
      tags: documentForm.value.tags.map(tag => tag.name || tag)
    };

    if (isEditMode.value) {
      // Update existing document
      response = await axios.put(`/api/documents/${documentId.value}`, docData);
    } else {
      // Create new document
      response = await axios.post('/api/documents', docData);

      // Upload attachments if any
      const newDocId = response.data.id;

      for (const attachment of documentForm.value.attachments) {
        if (attachment.file) {
          const formData = new FormData();
          formData.append('file', attachment.file);

          await axios.post(`/api/documents/${newDocId}/attachments`, formData, {
            headers: { 'Content-Type': 'multipart/form-data' }
          });
        }
      }
    }

    // Redirect to the document view page
    router.push(`/documents/${response.data.id}`);
  } catch (err) {
    console.error('Error saving document:', err);
    error.value = err.response?.data?.message || 'Failed to save document. Please try again.';
  } finally {
    isSaving.value = false;
  }
};
</script>

<style>
/* Additional styles for the rich text editor */
#editor a {
  color: #2563eb;
  text-decoration: underline;
}

#editor h1 {
  font-size: 1.5rem;
  font-weight: bold;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

#editor h2 {
  font-size: 1.25rem;
  font-weight: bold;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

#editor h3 {
  font-size: 1.125rem;
  font-weight: bold;
  margin-top: 0.75rem;
  margin-bottom: 0.5rem;
}

#editor ul {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

#editor ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

#editor blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  font-style: italic;
  margin: 0.75rem 0;
}

#editor pre {
  background-color: #f3f4f6;
  padding: 0.75rem;
  border-radius: 0.25rem;
  font-family: monospace;
  white-space: pre-wrap;
  margin: 0.75rem 0;
}
</style>