const { v4: uuidv4 } = require('uuid');
const { User, Project, Supplier } = require('../models');
const { Procurement } = require('../models/procurement.model');

/**
 * Helper function to get the text representation of procurement type
 * @param {string} procurementType - The procurement type
 * @returns {string} - The text representation
 */
function getProcurementTypeText(procurementType) {
  if (procurementType === 'material') {
    return '材料';
  } else if (procurementType === 'equipment') {
    return '设备';
  }
  return '服务';
}

/**
 * Helper function to map status values
 * @param {string} status - The original status
 * @returns {string} - The mapped status
 */
function getStatusMapping(status) {
  if (status === 'ordered' || status === 'received') {
    return 'approved';
  } else if (status === 'completed') {
    return 'completed';
  } else if (status === 'cancelled') {
    return 'rejected';
  }
  return 'pending';
}

async function seedProcurements() {
  try {
    // Check if procurements already exist
    const procurementCount = await Procurement.count();
    if (procurementCount > 0) {
      console.log('Procurements already exist, skipping procurement seeding');
      return;
    }

    // Get or create a default admin user for createdBy field
    let adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      adminUser = await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin'
      });
    }

    // Get existing projects
    const projects = await Project.findAll({ limit: 10 });
    if (projects.length === 0) {
      console.log('No projects found for procurement seeding');
      return;
    }

    // Get existing suppliers
    const suppliers = await Supplier.findAll({ limit: 10 });
    if (suppliers.length === 0) {
      console.log('No suppliers found for procurement seeding');
      return;
    }

    // Procurement types
    const procurementTypes = ['material', 'equipment', 'service'];

    // Status options
    const statusOptions = ['draft', 'pending_approval', 'approved', 'ordered', 'received', 'completed', 'cancelled'];

    // Priority options - commented out as not currently used
    // const priorityOptions = ['low', 'medium', 'high', 'urgent'];

    // Generate random procurements
    const procurements = [];

    for (let i = 0; i < 50; i++) {
      const procurementType = procurementTypes[Math.floor(Math.random() * procurementTypes.length)];
      const project = projects[Math.floor(Math.random() * projects.length)];
      const supplier = suppliers[Math.floor(Math.random() * suppliers.length)];

      // Generate random date within the last year
      const requestDate = new Date();
      requestDate.setDate(requestDate.getDate() - Math.floor(Math.random() * 365));

      // Generate expected delivery date (between 7 and 60 days after request date)
      const expectedDeliveryDate = new Date(requestDate);
      expectedDeliveryDate.setDate(expectedDeliveryDate.getDate() + Math.floor(Math.random() * 53) + 7);

      // Generate random amount between 1000 and 100000
      const totalAmount = Math.floor(Math.random() * 99000) + 1000;

      // Select status
      const status = statusOptions[Math.floor(Math.random() * statusOptions.length)];

      // We'll use these variables later if needed
      // const priority = priorityOptions[Math.floor(Math.random() * priorityOptions.length)];
      // const poNumber = `PO-${String(i + 1).padStart(6, '0')}`;

      procurements.push({
        id: uuidv4(),
        content: `采购申请 ${i + 1}: ${getProcurementTypeText(procurementType)}采购`,
        description: `${getProcurementTypeText(procurementType)}采购 - ${i + 1}`,
        projectId: project.id,
        supplierId: supplier.id,
        quantity: Math.floor(Math.random() * 10) + 1,
        amount: totalAmount,
        invoiceType: ['增值税专用发票', '增值税普通发票', '电子发票'][Math.floor(Math.random() * 3)],
        procurementDate: requestDate,
        status: getStatusMapping(status),
        paymentSchedule: '30天付款',
        returnInfo: null,
        returnBatch: null,
        returnInvoiceInfo: null,
        createdBy: adminUser.id
      });
    }

    // Bulk create procurements
    await Procurement.bulkCreate(procurements);
    console.log('Successfully seeded procurements');
  } catch (error) {
    console.error('Error seeding procurements:', error);
    throw error;
  }
}

module.exports = seedProcurements;
