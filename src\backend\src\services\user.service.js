const bcrypt = require('bcryptjs');
const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { User } = require('../models');
const logger = require('../config/logger');
const { roles } = require('../config/roles');

/**
 * Create a user
 * @param {Object} userBody
 * @param {Object} options - Optional parameters like transaction
 * @returns {Promise<User>}
 */
const createUser = async (userBody, options = {}) => {
  if (await User.findOne({ where: { email: userBody.email } })) {
    throw new ApiError(400, 'Email already taken');
  }
  
  if (await User.findOne({ where: { username: userBody.username } })) {
    throw new ApiError(400, 'Username already taken');
  }

  // 移除手动密码哈希，依赖模型的beforeCreate钩子
  // 密码哈希将由User模型的beforeCreate钩子自动处理
  const userData = {
    ...userBody,
    role: userBody.role || 'user' // Default role if not specified
  };

  // Create the user with optional transaction
  const user = await User.create(userData, options);
  return user;
};

/**
 * Get user by id
 * @param {string} id
 * @returns {Promise<User>}
 */
const getUserById = async (id) => {
  const user = await User.findByPk(id);
  if (!user) {
    throw new ApiError(404, 'User not found');
  }
  return user;
};

/**
 * Get user by email
 * @param {string} email
 * @returns {Promise<User>}
 */
const getUserByEmail = async (email) => {
  return User.findOne({ where: { email } });
};

/**
 * Update user by id
 * @param {string} userId
 * @param {Object} updateBody
 * @returns {Promise<User>}
 */
const updateUserById = async (userId, updateBody) => {
  const user = await getUserById(userId);

  // Check if email is being updated and if it's already taken
  if (updateBody.email && (await User.findOne({ where: { email: updateBody.email, id: { [Op.ne]: userId } } }))) {
    throw new ApiError(400, 'Email already taken');
  }

  // Check if username is being updated and if it's already taken
  if (updateBody.username && (await User.findOne({ where: { username: updateBody.username, id: { [Op.ne]: userId } } }))) {
    throw new ApiError(400, 'Username already taken');
  }

  // 移除密码哈希处理，依赖模型的beforeUpdate钩子
  // 密码哈希将由User模型的beforeUpdate钩子自动处理

  // Update user basic information
  Object.assign(user, updateBody);
  await user.save();

  return getUserById(userId);
};

/**
 * Delete user by id
 * @param {string} userId
 * @returns {Promise<void>}
 */
const deleteUserById = async (userId) => {
  const user = await getUserById(userId);
  await user.destroy();
};

/**
 * Query users with pagination
 * @param {Object} filter - Filter criteria
 * @param {Object} options - Query options
 * @returns {Promise<Object>}
 */
const queryUsers = async (filter, options) => {
  const { role, isActive, search, department } = filter;
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;

  // Build where clause
  const whereClause = {};
  if (role) whereClause.role = role;
  if (isActive !== undefined) whereClause.isActive = isActive;
  if (department) whereClause.department = department;

  // Add search capability
  if (search) {
    whereClause[Op.or] = [
      { username: { [Op.iLike]: `%${search}%` } },
      { lastName: { [Op.iLike]: `%${search}%` } },
      { email: { [Op.iLike]: `%${search}%` } },
      { role: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Calculate offset
  const offset = (page - 1) * limit;

  // Execute query
  const { rows: users, count } = await User.findAndCountAll({
    where: whereClause,
    offset,
    limit,
    order: [[sortBy, sortOrder]]
  });

  // Map users to match frontend expectations
  const mappedUsers = users.map(user => ({
    id: user.id,
    name: user.username || '',
    email: user.email,
    role: user.role,
    status: user.isActive ? 'active' : 'inactive',
    createdAt: user.createdAt,
    departmentId: user.departmentId
  }));

  return {
    results: mappedUsers,
    totalPages: Math.ceil(count / limit),
    totalResults: count,
    currentPage: page
  };
};

/**
 * Get user preferences by id
 * @param {string} userId
 * @returns {Promise<Object>}
 */
const getUserPreferencesById = async (userId) => {
  // TODO: Implement logic to retrieve user preferences
  // This is a placeholder implementation
  const user = await getUserById(userId);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
  }
  // Assuming preferences are stored in a 'preferences' field on the user model
  // or in a related model. Adjust as necessary.
  return user.preferences || {};
};

/**
 * Get users for dropdown selection
 * @param {string} search - Optional search term
 * @returns {Promise<Array>} - Array of users for dropdown
 */
const getUsersForDropdown = async (search = '') => {
  try {
    const whereClause = {};
    
    // Add search capability if search term provided
    if (search) {
      whereClause[Op.or] = [
        { username: { [Op.iLike]: `%${search}%` } },
        { lastName: { [Op.iLike]: `%${search}%` } },
        { email: { [Op.iLike]: `%${search}%` } }
      ];
    }
    
    // Only include active users
    whereClause.isActive = true;
    
    // Execute query
    const users = await User.findAll({
      where: whereClause,
      attributes: ['id', 'username', 'lastName', 'email'],
      order: [['username', 'ASC']],
      limit: 50 // Limit results for performance
    });
    
    // Format users for dropdown
    return users.map(user => ({
      id: user.id,
      name: user.username || '',
      fullName: user.lastName ? `${user.username} ${user.lastName}` : user.username,
      email: user.email
    }));
  } catch (error) {
    logger.error('Error fetching users for dropdown:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, '获取用户列表失败');
  }
};

module.exports = {
  createUser,
  getUserById,
  getUserByEmail,
  updateUserById,
  deleteUserById,
  queryUsers,
  getUserPreferencesById,
  getUsersForDropdown,
};