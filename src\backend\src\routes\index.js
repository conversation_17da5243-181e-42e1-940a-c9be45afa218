const express = require('express');
const authRoutes = require('./auth.routes');
const userRoutes = require('./user.routes');
const categoryRoutes = require('./category.routes');
const documentRoutes = require('./document.routes');
const tagRoutes = require('./tag.routes');
const attachmentRoutes = require('./attachment.routes');
const commentRoutes = require('./comment.routes');
const projectRoutes = require('./project.routes');
const contractRoutes = require('./contract.routes');
const procurementRoutes = require('./procurement.routes');
const customerRoutes = require('./customer.routes');
const clientRoutes = require('./client.routes');
const supplierRoutes = require('./supplier.routes');
const productRoutes = require('./product.routes');
const financeRoutes = require('./finance.routes');
const attendanceRoutes = require('./attendance.routes');
const reimbursementRoutes = require('./reimbursement.routes');
const subcontractRoutes = require('./subcontract.routes');
const workhourRoutes = require('./workhour.routes');
const employeeRoutes = require('./employee.routes');
const contractWorkerRoutes = require('./contractWorker.routes');
const temporaryWorkerRoutes = require('./temporaryWorker.routes');
const taskRoutes = require('./task.routes');
const purchaseRoutes = require('./purchase.routes');
const inventoryRoutes = require('./inventory.routes');
const departmentRoutes = require('./department.routes');
const userMessageRoutes = require('./userMessage.routes');
const dashboardRoutes = require('./dashboard.routes');
const testRoutes = require('./test.routes');

const router = express.Router();

// Define routes
const routes = [
  {
    path: '/test',
    route: testRoutes,
  },
  {
    path: '/auth',
    route: authRoutes,
  },
  {
    path: '/users',
    route: userRoutes,
  },
  {
    path: '/categories',
    route: categoryRoutes,
  },
  {
    path: '/documents',
    route: documentRoutes,
  },
  {
    path: '/tags',
    route: tagRoutes,
  },
  {
    path: '/attachments',
    route: attachmentRoutes,
  },
  {
    path: '/comments',
    route: commentRoutes,
  },
  {
    path: '/projects',
    route: projectRoutes,
  },
  {
    path: '/contracts',
    route: contractRoutes,
  },
  {
    path: '/procurements',
    route: procurementRoutes,
  },
  {
    path: '/customers',
    route: customerRoutes,
  },
  {
    path: '/suppliers',
    route: supplierRoutes,
  },
  {
    path: '/products',
    route: productRoutes,
  },
  {
    path: '/finance',
    route: financeRoutes,
  },
  {
    path: '/attendances',
    route: attendanceRoutes,
  },
  {
    path: '/reimbursements',
    route: reimbursementRoutes,
  },
  {
    path: '/subcontracts',
    route: subcontractRoutes,
  },
  {
    path: '/workhours',
    route: workhourRoutes,
  },
  {
    path: '/clients',
    route: clientRoutes,
  },
  {
    path: '/employees',
    route: employeeRoutes,
  },
  {
    path: '/employees/contract-workers',
    route: contractWorkerRoutes,
  },
  {
    path: '/employees/temporary-workers',
    route: temporaryWorkerRoutes,
  },
  {
    path: '/tasks',
    route: taskRoutes,
  },
  {
    path: '/purchases',
    route: purchaseRoutes,
  },
  {
    path: '/inventory',
    route: inventoryRoutes,
  },
  {
    path: '/departments',
    route: departmentRoutes,
  },
  {
    path: '/messages',
    route: userMessageRoutes,
  },
  {
    path: '/dashboard',
    route: dashboardRoutes,
  }
];

// Mount routes
routes.forEach((route) => {
  router.use(route.path, route.route);
});

module.exports = router;