<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">{{ isEdit ? '编辑财务记录' : '新增财务记录' }}</h1>
      </div>
        <router-link v-if="!isPublicMode" to="/finance" class="btn  flex items-center shadow-md hover:shadow-lg transition-all duration-200">
               <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          返回列表
        </router-link>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="card shadow-md p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">加载中...</p>
    </div>

    <!-- 表单区域 -->
    <div v-else class="card shadow-md">
      <form @submit.prevent="submitForm" class="space-y-6">
        <!-- 项目信息 -->
        <div class="form-section">
          <h2 class="form-section-title">项目信息</h2>
          <div>
            <label for="project" class="form-label">项目 <span class="form-required">*</span></label>
            <select id="project" v-model="financeForm.projectId" class="form-input" required>
              <option value="">-- 选择项目 --</option>
              <option v-for="project in projects" :key="project.id" :value="project.id">
                {{ project.name }}
              </option>
            </select>
          </div>
        </div>
        <!-- 描述和参考号 -->
        <div class="form-section">
          <h2 class="form-section-title">其他信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="reference" class="form-label">参考编号</label>
              <input type="text" id="reference" v-model="financeForm.reference" class="form-input" placeholder="发票号/合同号等" />
            </div>
            <div>
              <label for="description" class="form-label">详细说明</label>
              <textarea id="description" v-model="financeForm.description" class="form-input" rows="3" placeholder="添加交易详细说明"></textarea>
            </div>
          </div>
        </div>
        <!-- 基本信息 -->
        <div class="form-section">
          <h2 class="form-section-title">基本信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="type" class="form-label">财务类型 <span class="form-required">*</span></label>
              <select id="type" v-model="financeForm.type" class="form-input" required>
                <option value="">-- 请选择财务类型 --</option>
                <option value="income">收入</option>
                <option value="expense">支出</option>
              </select>
            </div>
            <div>
              <label for="category" class="form-label">类别 <span class="form-required">*</span></label>
              <input type="text" id="category" v-model="financeForm.category" class="form-input" placeholder="请输入类别" required />
            </div>
            <div>
              <label for="amount" class="form-label">金额 <span class="form-required">*</span></label>
              <input type="number" id="amount" v-model="financeForm.amount" class="form-input" step="0.01" placeholder="请输入金额" required />
            </div>
            <div>
              <label for="currency" class="form-label">货币</label>
              <select id="currency" v-model="financeForm.currency" class="form-input">
                <option value="CNY">人民币 (CNY)</option>
                <option value="USD">美元 (USD)</option>
                <option value="EUR">欧元 (EUR)</option>
              </select>
            </div>
          </div>
        </div>
        <!-- 销项信息 -->
        <div class="form-section">
          <h2 class="form-section-title">销项信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="form-label">购方信息</label>
              <input type="text" v-model="financeForm.buyerInfo" class="form-input" placeholder="请输入购方单位名称" />
            </div>
            <div>
              <label class="form-label">开票时间</label>
              <input type="date" v-model="financeForm.invoiceDate" class="form-input" max="3000-12-31" />
            </div>
            <div>
              <label class="form-label">税率</label>
              <input type="number" v-model="financeForm.invoiceTaxRate" class="form-input" step="0.01" placeholder="请输入税率" />
            </div>
            <div>
              <label class="form-label">发票类型</label>
              <select v-model="financeForm.invoiceType" class="form-input">
                <option value="增值税专用发票">增值税专用发票</option>
                <option value="增值税普通发票">增值税普通发票</option>
                <option value="其他">其他</option>
              </select>
            </div>
          </div>
        </div>
        <!-- 回款信息（动态次数） -->
        <div class="form-section">
          <h2 class="form-section-title">回款信息</h2>
          <div v-for="(item, index) in financeForm.incomePayments" :key="index" class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-2">
            <div>
              <label class="form-label">回款时间</label>
              <input type="date" v-model="item.date" class="form-input" max="3000-12-31" />
            </div>
            <div>
              <label class="form-label">回款金额</label>
              <input type="number" v-model="item.amount" class="form-input" step="0.01" placeholder="请输入回款金额" />
            </div>
            <div>
              <label class="form-label">备注</label>
              <input type="text" v-model="item.remarks" class="form-input" placeholder="请输入备注" />
            </div>
            <button v-if="financeForm.incomePayments.length > 1" type="button" @click="financeForm.incomePayments.splice(index, 1)" class="text-red-600 hover:text-red-800">删除</button>
          </div>
          <button type="button" @click="financeForm.incomePayments.push({date:'',amount:'',remarks:''})" class="btn btn-secondary mt-2">添加回款</button>
        </div>
        <!-- 进项信息 -->
        <div class="form-section">
          <h2 class="form-section-title">进项信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label class="form-label">售方信息</label>
              <input type="text" v-model="financeForm.sellerInfo" class="form-input" placeholder="请输入售方单位名称" />
            </div>
            <div>
              <label class="form-label">收票时间</label>
              <input type="date" v-model="financeForm.inputInvoiceDate" class="form-input" max="3000-12-31" />
            </div>
            <div>
              <label class="form-label">税率</label>
              <input type="number" v-model="financeForm.inputInvoiceTaxRate" class="form-input" step="0.01" placeholder="请输入税率" />
            </div>
            <div>
              <label class="form-label">发票类型</label>
              <select v-model="financeForm.inputInvoiceType" class="form-input">
                <option value="增值税专用发票">增值税专用发票</option>
                <option value="增值税普通发票">增值税普通发票</option>
                <option value="其他">其他</option>
              </select>
            </div>
          </div>
        </div>
        <!-- 付款信息 -->
        <div class="form-section">
          <h2 class="form-section-title">付款信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label class="form-label">分包付款</label>
              <input type="number" v-model="financeForm.subcontractPayment" class="form-input" step="0.01" placeholder="请输入分包付款金额" />
            </div>
            <div>
              <label class="form-label">采购付款</label>
              <input type="number" v-model="financeForm.purchasePayment" class="form-input" step="0.01" placeholder="请输入采购付款金额" />
            </div>
            <div>
              <label class="form-label">报销付款</label>
              <input type="number" v-model="financeForm.reimbursementPayment" class="form-input" step="0.01" placeholder="请输入报销付款金额" />
            </div>
          </div>
        </div>
        <!-- 纯收入 -->
        <div class="form-section">
          <h2 class="form-section-title">纯收入</h2>
          <input type="number" v-model="financeForm.netIncome" class="form-input" step="0.01" placeholder="请输入纯收入金额" />
        </div>
        <!-- 附件 -->
        <div class="form-section">
          <h2 class="form-section-title">附件</h2>
          <input type="file" multiple @change="handleFileUpload" class="form-input" />
        </div>
        <!-- 表单操作按钮 -->
        <div class="flex justify-end space-x-4 border-t pt-6">
          <button type="button" @click="resetForm" class="btn btn-secondary flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            重置
          </button>
          <button @click="submitForm" class="btn btn-primary flex items-center" :disabled="loading">
            <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            {{ isEdit ? '保存修改' : '保存记录' }}
          </button>
        </div>

        <!-- 必填项提示 -->
        <div class="mt-4 text-sm text-gray-500">
          <span class="form-required">*</span> 表示必填项
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';

const router = useRouter();
const route = useRoute();

// 加载和错误状态
const loading = ref(false);
const error = ref(null);
const successMessage = ref(null);

// 显示成功消息并自动隐藏
const showSuccessMessage = (message, duration = 5000) => {
  successMessage.value = message;
  setTimeout(() => {
    successMessage.value = null;
  }, duration);
};

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id);
const financeId = computed(() => route.params.id);

// 项目选项
const projects = ref([]);
const loadingProjects = ref(false);

// 获取项目列表
const fetchProjects = async () => {
  loadingProjects.value = true;
  try {
    const response = await axios.get('/api/projects', {
      params: { }// status: 'active'
    });
    projects.value = response.data.results || [];
  } catch (err) {
    console.error('获取项目列表失败:', err);
  } finally {
    loadingProjects.value = false;
  }
};

// 表单数据
const financeForm = ref({
  number: '系统生成',
  date: new Date().toISOString().substr(0, 10),
  type: '',
  category: '',
  status: 'pending',
  amount: null,
  currency: 'CNY',
  paymentMethod: '',
  projectId: '',
  supplierId: '',
  reference: '',
  description: '',
  buyerInfo: '',
  invoiceDate: '',
  invoiceTaxRate: '',
  invoiceType: '',
  incomePayments: [],
  sellerInfo: '',
  inputInvoiceDate: '',
  inputInvoiceTaxRate: '',
  inputInvoiceType: '',
  subcontractPayment: '',
  purchasePayment: '',
  reimbursementPayment: '',
  netIncome: '',
});

// 获取财务记录详情（用于编辑模式）
const fetchFinanceDetails = async (id) => {
  loading.value = true;
  error.value = null;

  try {
    const response = await axios.get(`/api/finance/${id}`);
    const data = response.data;
    console.log('获取到的财务记录详情:', data); // 添加日志

    // 填充表单数据
    Object.keys(financeForm.value).forEach(key => {
      if (key in data) {
        if (key === 'date' && data[key]) {
          // 转换日期格式为 YYYY-MM-DD
          financeForm.value[key] = new Date(data[key]).toISOString().split('T')[0];
        } else {
          financeForm.value[key] = data[key];
        }
      }
    });

    // 打印表单数据，用于调试
    console.log('填充后的表单数据:', financeForm.value);
  } catch (err) {
    console.error('获取财务记录详情失败:', err);
    error.value = err.response?.data?.message || '获取财务记录详情失败，请稍后重试。';
  } finally {
    loading.value = false;
  }
};

// 重置表单
function resetForm() {
  financeForm.value = {
    number: '系统生成',
    date: new Date().toISOString().substr(0, 10),
    type: '',
    category: '',
    status: 'pending',
    amount: null,
    currency: 'CNY',
    paymentMethod: '',
    projectId: '',
    supplierId: '',
    reference: '',
    description: '',
    buyerInfo: '',
    invoiceDate: '',
    invoiceTaxRate: '',
    invoiceType: '',
    incomePayments: [],
    sellerInfo: '',
    inputInvoiceDate: '',
    inputInvoiceTaxRate: '',
    inputInvoiceType: '',
    subcontractPayment: '',
    purchasePayment: '',
    reimbursementPayment: '',
    netIncome: '',
  };
}

// 处理API错误
function handleApiError(err, action) {
  console.error(`${action}财务记录失败:`, err);

  if (!err.response || !err.response.data) {
    return err.message || `${action}失败，请重试`;
  }

  const { data } = err.response;

  if (data.message) {
    return data.message;
  }

  if (data.errors) {
    const errorMessages = Object.values(data.errors).flat();
    return errorMessages.join('\n');
  }

  return `${action}失败，请重试`;
}

// 提交表单
async function submitForm() {
  loading.value = true;
  error.value = null;

  const action = isEdit.value ? '更新' : '创建';

  try {
    // 确保税率保留两位小数
    if (financeForm.value.invoiceTaxRate) {
      financeForm.value.invoiceTaxRate = Number(financeForm.value.invoiceTaxRate).toFixed(2);
    }
    if (financeForm.value.inputInvoiceTaxRate) {
      financeForm.value.inputInvoiceTaxRate = Number(financeForm.value.inputInvoiceTaxRate).toFixed(2);
    }
    
    const subData = JSON.parse(JSON.stringify(financeForm.value))
    // 删除number字段，因为它是系统自动生成的
    delete subData.number;

    if (isEdit.value) {
      // 调用更新API
      await axios.patch(`/api/finance/${financeId.value}`, subData);
    } else {
      // 调用创建API
      await axios.post('/api/finance', subData);
    }

    showSuccessMessage(`财务记录${action}成功！\n\n类型：${financeForm.value.type}\n金额：${financeForm.value.amount} ${financeForm.value.currency}\n日期：${financeForm.value.date}`);

    // 2秒后跳转到列表页
    setTimeout(() => {
      router.push('/finance');
    }, 2000);
  } catch (err) {
    error.value = handleApiError(err, action);
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  // 获取项目列表
  await fetchProjects();

  // 如果是编辑模式，获取财务记录详情
  if (isEdit.value) {
    await fetchFinanceDetails(financeId.value);
  }
});
</script>

<style scoped>
.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.page-header {
  @apply flex justify-between items-center mb-6;
  animation: fadeSlideIn 0.5s ease-out;
}

.card {
  @apply bg-white rounded-xl p-6 mb-6;
  animation: fadeSlideIn 0.6s ease-out;
}

.form-section {
  @apply border-b border-gray-200 pb-6 mb-6 last:border-0 last:pb-0 last:mb-0;
}

.form-section-title {
  @apply text-lg font-semibold text-gray-700 mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50;
}

.btn {
  @apply py-2 px-4 rounded-lg focus:outline-none focus:ring transition-all duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-200;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-gray-200;
}
</style>