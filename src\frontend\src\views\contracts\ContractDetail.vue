<template>
  <div class="container mx-auto px-4 py-8">
    <div class="mb-6">
      <router-link to="/contracts" class="text-blue-600 hover:text-blue-800 flex items-center">
        <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        返回合同列表
      </router-link>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2">{{ error }}</p>
      <button @click="fetchContractDetail(id)" class="mt-2 text-red-700 hover:text-red-900 font-medium underline">
        重试
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="bg-white rounded-lg shadow p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">正在加载合同详情...</p>
    </div>

    <!-- 合同详情 -->
    <div v-else-if="!error" class="bg-white rounded-lg shadow overflow-hidden">
      <!-- 合同头部信息 -->
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h1 class="page-title">合同详情</h1>
          <div class="flex space-x-2">
            <button @click="printContract" class="btn btn-secondary">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
              </svg>
              打印
            </button>
            <router-link :to="`/contracts/edit/${id}`" class="btn btn-primary">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              编辑
            </router-link>
          </div>
        </div>
      </div>

      <!-- 合同基本信息 -->
      <div class="px-6 py-4 bg-gray-50">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-500">合同编号</p>
            <p class="mt-1 text-lg font-semibold">{{ contract.id }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">合同名称</p>
            <p class="mt-1 text-lg font-semibold">{{ contract.name }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">合同状态</p>
            <p class="mt-1">
              <span
                :class="{
                  'px-2 py-1 rounded text-sm font-medium': true,
                  'bg-green-100 text-green-800': contract.status === '已生效',
                  'bg-yellow-100 text-yellow-800': contract.status === '待签署',
                  'bg-red-100 text-red-800': contract.status === '已终止',
                  'bg-gray-100 text-gray-800': contract.status === '已到期'
                }"
              >
                {{ contract.status }}
              </span>
            </p>
          </div>
        </div>
      </div>

      <!-- 合同双方信息 -->
      <div class="px-6 py-5 border-t border-gray-200">
        <h2 class="text-xl font-semibold mb-4">合同双方</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-gray-50 p-4 rounded">
            <h3 class="text-lg font-medium mb-3">甲方信息</h3>
            <div class="space-y-2">
              <div>
                <span class="text-gray-500">公司名称：</span>
                <span class="font-medium">{{ contract.partyA.name }}</span>
              </div>
              <div>
                <span class="text-gray-500">联系人：</span>
                <span class="font-medium">{{ contract.partyA.contact }}</span>
              </div>
              <div>
                <span class="text-gray-500">联系电话：</span>
                <span class="font-medium">{{ contract.partyA.phone }}</span>
              </div>
              <div>
                <span class="text-gray-500">地址：</span>
                <span class="font-medium">{{ contract.partyA.address }}</span>
              </div>
            </div>
          </div>

          <div class="bg-gray-50 p-4 rounded">
            <h3 class="text-lg font-medium mb-3">乙方信息</h3>
            <div class="space-y-2">
              <div>
                <span class="text-gray-500">公司名称：</span>
                <span class="font-medium">{{ contract.partyB.name }}</span>
              </div>
              <div>
                <span class="text-gray-500">联系人：</span>
                <span class="font-medium">{{ contract.partyB.contact }}</span>
              </div>
              <div>
                <span class="text-gray-500">联系电话：</span>
                <span class="font-medium">{{ contract.partyB.phone }}</span>
              </div>
              <div>
                <span class="text-gray-500">地址：</span>
                <span class="font-medium">{{ contract.partyB.address }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 合同金额信息 -->
      <div class="px-6 py-5 border-t border-gray-200">
        <h2 class="text-xl font-semibold mb-4">金额信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-500">合同总金额</p>
            <p class="mt-1 text-xl font-bold text-blue-600">￥{{ formatCurrency(contract.totalAmount) }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">已付款金额</p>
            <p class="mt-1 text-xl font-bold text-green-600">￥{{ formatCurrency(contract.paidAmount) }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">未付款金额</p>
            <p class="mt-1 text-xl font-bold text-red-600">￥{{ formatCurrency(contract.unpaidAmount) }}</p>
          </div>
        </div>
      </div>

      <!-- 合同时间信息 -->
      <div class="px-6 py-5 border-t border-gray-200">
        <h2 class="text-xl font-semibold mb-4">时间信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-500">签署日期</p>
            <p class="mt-1 text-lg font-semibold">{{ formatDate(contract.signDate) }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">生效日期</p>
            <p class="mt-1 text-lg font-semibold">{{ formatDate(contract.effectiveDate) }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">到期日期</p>
            <p class="mt-1 text-lg font-semibold">{{ formatDate(contract.expiryDate) }}</p>
          </div>
        </div>
      </div>

      <!-- 合同条款 -->
      <div class="px-6 py-5 border-t border-gray-200">
        <h2 class="text-xl font-semibold mb-4">合同条款</h2>
        <div class="prose max-w-none">
          <div v-html="contract.terms"></div>
        </div>
      </div>

      <!-- 附件列表 -->
      <div class="px-6 py-5 border-t border-gray-200">
        <h2 class="text-xl font-semibold mb-4">附件列表</h2>
        <div v-if="contract.attachments && contract.attachments.length > 0">
          <ul class="divide-y divide-gray-200">
            <li v-for="(attachment, index) in contract.attachments" :key="index" class="py-3 flex justify-between items-center">
              <div class="flex items-center">
                <svg class="w-5 h-5 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                </svg>
                <span>{{ attachment.name }}</span>
              </div>
              <button class="text-blue-600 hover:text-blue-800">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
                </svg>
              </button>
            </li>
          </ul>
        </div>
        <div v-else class="py-4 text-center text-gray-500">
          无附件
        </div>
      </div>

      <!-- 操作记录 -->
      <div class="px-6 py-5 border-t border-gray-200">
        <h2 class="text-xl font-semibold mb-4">操作记录</h2>
        <div v-if="contract.logs && contract.logs.length > 0">
          <ul class="divide-y divide-gray-200">
            <li v-for="(log, index) in contract.logs" :key="index" class="py-3">
              <div class="flex items-start">
                <div class="flex-shrink-0 h-10 w-10">
                  <img class="h-10 w-10 rounded-full" :src="log.userAvatar || '/images/default-avatar.png'" alt="" />
                </div>
                <div class="ml-4">
                  <div class="text-sm font-medium text-gray-900">{{ log.userName }}</div>
                  <div class="text-sm text-gray-500">{{ log.action }}</div>
                  <div class="text-xs text-gray-400">{{ formatDateTime(log.timestamp) }}</div>
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div v-else class="py-4 text-center text-gray-500">
          无操作记录
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'ContractDetail',
  data() {
    return {
      id: this.$route.params.id,
      contract: {},
      loading: true,
      error: null
    }
  },
  methods: {
    formatCurrency(value) {
      if (typeof value !== 'number') return '0';
      return value.toLocaleString('zh-CN');
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN');
    },
    formatDateTime(dateTimeString) {
      if (!dateTimeString) return '';
      const date = new Date(dateTimeString);
      return date.toLocaleString('zh-CN');
    },
    // 获取合同详情
    async fetchContractDetail(id) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.get(`/api/contracts/${id}`);
        this.contract = response.data;

        // 计算未付款金额（如果API没有返回）
        if (this.contract.totalAmount && this.contract.paidAmount && !this.contract.unpaidAmount) {
          this.contract.unpaidAmount = this.contract.totalAmount - this.contract.paidAmount;
        }
      } catch (err) {
        console.error('获取合同详情失败:', err);
        this.error = err.response?.data?.message || '获取合同详情失败，请重试';
      } finally {
        this.loading = false;
      }
    },
    // 打印合同
    printContract() {
      window.print();
    }
  },
  created() {
    // 获取合同详情
    this.fetchContractDetail(this.id);
  }
}
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded flex items-center justify-center transition duration-150 ease-in-out;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500;
}
</style>