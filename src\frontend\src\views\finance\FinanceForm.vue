<template>
  <div class="max-w-4xl mx-auto px-4 py-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <div class="flex items-center">
        <router-link to="/finance" class="text-blue-600 hover:text-blue-800 mr-3 transition-colors duration-200">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
        </router-link>
        <h1 class="text-2xl font-bold text-gray-900">{{ isEdit ? '编辑财务记录' : '新增财务记录' }}</h1>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading || projectsLoading || detailsLoading" class="bg-white rounded-xl shadow-sm p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">正在加载数据...</p>
    </div>

    <!-- 表单内容 -->
    <div v-else class="bg-white rounded-xl shadow-sm p-6">
      <form @submit.prevent="handleSubmit" class="flex flex-col min-h-[calc(100vh-200px)] space-y-8">
        <!-- 项目信息 -->
        <div class="bg-white p-6 rounded-lg shadow mb-6">
          <h2 class="text-lg font-semibold mb-4">项目信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">项目名称 <span class="text-red-500">*</span></label>
              <template v-if="isEdit">
                <input
                  v-model="formData.projectName"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                  readonly
                />
              </template>
              <template v-else>
                <select
                  v-model="formData.projectId"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  @change="handleProjectChange"
                  required
                >
                  <option value="">请选择项目</option>
                  <option v-for="project in projectList" :key="project.id" :value="project.id">
                    {{ project.name }}
                  </option>
                </select>
              </template>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">项目编号</label>
              <input
                v-model="formData.projectCode"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
                readonly
              />
            </div>
          </div>
        </div>

        <!--分割线-->
        <Divider/>

        <!-- 基本财务信息 -->
        <div class="form-section">
          <h2 class="section-title">基本财务信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <label class="form-label mb-1">财务类型 <span class="text-red-500">*</span></label>
              <select v-model="formData.type" class="form-input w-full" required>
                <option value="">请选择财务类型</option>
                <option value="income">收入</option>
                <option value="expense">支出</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label mb-1">类别 <span class="text-red-500">*</span></label>
              <input type="text" v-model="formData.category" class="form-input w-full" placeholder="请输入类别" required />
            </div>
            <div class="form-group">
              <label class="form-label mb-1">金额 <span class="text-red-500">*</span></label>
              <input type="number" v-model="formData.amount" class="form-input w-full" step="0.01" placeholder="请输入金额" required />
            </div>
            <div class="form-group">
              <label class="form-label mb-1">货币</label>
              <select v-model="formData.currency" class="form-input w-full">
                <option value="CNY">人民币 (CNY)</option>
                <option value="USD">美元 (USD)</option>
                <option value="EUR">欧元 (EUR)</option>
              </select>
            </div>
          </div>
        </div>

        <!--分割线-->
        <Divider/>

        <!-- 销项信息 -->
        <div class="form-section">
          <h2 class="section-title">销项信息</h2>
          <!-- 购方信息 -->
          <div class="mb-4">
            <div class="form-group">
              <label class="form-label">购方信息</label>
              <input type="text" v-model="formData.buyerInfo" class="form-input w-full" placeholder="请输入购方单位名称" />
            </div>
          </div>

          <!-- 开票信息 -->
          <div class="bg-gray-50 p-4 rounded-lg mb-4">
            <div class="flex justify-between items-center mb-3">
              <h3 class="font-medium text-gray-800">开票信息</h3>
              <button
                type="button"
                @click="addPaymentItem('invoice')"
                class="text-blue-600 hover:text-blue-800 text-sm flex items-center"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                添加
              </button>
            </div>
            <div v-for="(item, index) in formData.invoiceDetails" :key="`invoice-${index}`" class="border border-gray-200 rounded-lg p-3 mb-3">
              <div class="grid grid-cols-12 gap-4">
                <div class="form-group mb-2 col-span-3">
                  <label class="form-label mb-1">开票时间</label>
                  <input type="date" v-model="item.date" class="form-input w-full" max="3000-12-31" placeholder="请选择开票时间" />
                </div>
                <div class="form-group mb-2 col-span-3">
                  <label class="form-label mb-1">税率</label>
                  <input type="number" v-model="item.taxRate" class="form-input w-full" step="0.01" min="0" placeholder="请输入税率 (保留两位小数)" />
                </div>
                <div class="form-group mb-2 col-span-5">
                  <label class="form-label mb-1">发票类型</label>
                  <select v-model="item.invoiceType" class="form-input w-full">
                    <option value="增值税专用发票">增值税专用发票</option>
                    <option value="增值税普通发票">增值税普通发票</option>
                    <option value="其他">其他</option>
                  </select>
                </div>
                <div class="form-group flex items-end justify-end col-span-1">
                  <button
                    v-if="formData.invoiceDetails.length > 1"
                    type="button"
                    @click="removePaymentItem('invoice', index)"
                    class="text-red-600 hover:text-red-800"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!--分割线-->
        <Divider/>

        <!-- 回款信息 -->
        <div class="form-section">
          <h2 class="section-title">回款信息</h2>
          <div class="bg-gray-50 p-4 rounded-lg mb-4">
            <div class="flex justify-between items-center mb-3">
              <h3 class="section-title">回款信息</h3>
              <button
                type="button"
                @click="addPaymentItem('income')"
                class="text-blue-600 hover:text-blue-800 text-sm flex items-center"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                添加
              </button>
            </div>
            <div v-for="(item, index) in formData.incomePayments" :key="`income-${index}`" class="border border-gray-200 rounded-lg p-3 mb-3">
              <div class="grid grid-cols-12 gap-4">
                <div class="form-group mb-2 col-span-3">
                  <label class="form-label mb-1">回款时间</label>
                  <input type="date" v-model="item.date" class="form-input w-full" max="3000-12-31" placeholder="请选择回款时间" />
                </div>
                <div class="form-group mb-2 col-span-3">
                  <label class="form-label mb-1">回款金额 <span class="text-red-500">*</span></label>
                  <input type="number" v-model="item.amount" class="form-input w-full" step="0.01" placeholder="请输入回款金额" required />
                </div>
                <div class="form-group mb-2 col-span-5">
                  <label class="form-label mb-1">备注</label>
                  <input type="text" v-model="item.remarks" class="form-input w-full" placeholder="请输入备注信息" />
                </div>
                <div class="form-group flex items-end justify-end col-span-1">
                  <button
                    v-if="formData.incomePayments.length > 1"
                    type="button"
                    @click="removePaymentItem('income', index)"
                    class="text-red-600 hover:text-red-800"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 进项信息 -->
        <div class="space-y-6">
          <div class="bg-white shadow px-4 py-5 sm:rounded-lg sm:p-6">
            <div class="md:grid md:grid-cols-3 md:gap-6">
              <div class="md:col-span-1">
                <h3 class="text-lg font-medium leading-6 text-gray-900">进项信息</h3>
                <p class="mt-1 text-sm text-gray-500">请填写进项发票相关信息</p>
              </div>
              <div class="mt-5 md:mt-0 md:col-span-2">
                <div class="space-y-4">
                  <!-- 售方信息 -->
                  <div>
                    <label for="sellerInfo" class="block text-sm font-medium text-gray-700">售方信息</label>
                    <div class="mt-1">
                      <input
                        type="text"
                        id="sellerInfo"
                        v-model="formData.sellerInfo"
                        class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                        placeholder="请输入售方单位名称"
                      />
                    </div>
                  </div>

                  <!-- 收票信息 -->
                  <div v-for="(detail, index) in formData.inputInvoiceDetails" :key="index" class="space-y-4">
                    <div class="flex items-center justify-between">
                      <h4 class="text-sm font-medium text-gray-900">收票信息 #{{ index + 1 }}</h4>
                      <button
                        v-if="index > 0"
                        type="button"
                        @click="removePaymentItem('inputInvoice', index)"
                        class="text-red-600 hover:text-red-900"
                      >
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                      </button>
                    </div>

                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                      <!-- 收票时间 -->
                      <div>
                        <label class="block text-sm font-medium text-gray-700">收票时间</label>
                        <div class="mt-1">
                          <input
                            type="date"
                            v-model="detail.date"
                            class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                            max="3000-12-31"
                            placeholder="请选择收票时间"
                          />
                        </div>
                      </div>

                      <!-- 税率 -->
                      <div>
                        <label class="block text-sm font-medium text-gray-700">税率</label>
                        <div class="mt-1">
                          <select
                            v-model="detail.taxRate"
                            class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                          >
                            <option value="">请选择</option>
                            <option value="0.13">13%</option>
                            <option value="0.09">9%</option>
                            <option value="0.06">6%</option>
                            <option value="0.03">3%</option>
                            <option value="0">0%</option>
                          </select>
                        </div>
                      </div>

                      <!-- 发票类型 -->
                      <div>
                        <label class="block text-sm font-medium text-gray-700">发票类型</label>
                        <div class="mt-1">
                          <select
                            v-model="detail.invoiceType"
                            class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                          >
                            <option value="增值税专用发票">增值税专用发票</option>
                            <option value="增值税普通发票">增值税普通发票</option>
                            <option value="其他">其他</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 添加收票信息按钮 -->
                  <div class="pt-4">
                    <button
                      type="button"
                      @click="addPaymentItem('inputInvoice')"
                      class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                      </svg>
                      添加收票信息
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 付款信息 -->
        <div class="form-section">
          <h2 class="section-title">付款信息</h2>
          <!-- 分包付款信息 -->
          <div class="bg-gray-50 p-4 rounded-lg mb-4">
            <div class="flex justify-between items-center mb-3">
              <h3 class="font-medium text-gray-800">分包付款</h3>
              <button
                type="button"
                @click="addPaymentItem('subcontract')"
                class="text-blue-600 hover:text-blue-800 text-sm flex items-center"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                添加
              </button>
            </div>
            <div v-for="(item, index) in formData.subcontractPayments" :key="`subcontract-${index}`" class="border border-gray-200 rounded-lg p-3 mb-3">
              <div class="grid grid-cols-12 gap-4">
                <div class="form-group mb-2 col-span-3">
                  <label class="form-label mb-1">付款时间</label>
                  <input type="date" v-model="item.date" class="form-input w-full" max="3000-12-31" placeholder="请选择付款时间" />
                </div>
                <div class="form-group mb-2 col-span-3">
                  <label class="form-label mb-1">付款金额</label>
                  <input type="number" v-model="item.amount" class="form-input w-full" step="0.01" placeholder="请输入付款金额" />
                </div>
                <div class="form-group mb-2 col-span-5">
                  <label class="form-label mb-1">备注</label>
                  <input type="text" v-model="item.remarks" class="form-input w-full" placeholder="请输入备注信息" />
                </div>
                <div class="form-group flex items-end justify-end col-span-1">
                  <button
                    v-if="formData.subcontractPayments.length > 1"
                    type="button"
                    @click="removePaymentItem('subcontract', index)"
                    class="text-red-600 hover:text-red-800"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 采购付款信息 -->
          <div class="bg-gray-50 p-4 rounded-lg mb-4">
            <div class="flex justify-between items-center mb-3">
              <h3 class="font-medium text-gray-800">采购付款</h3>
              <button
                type="button"
                @click="addPaymentItem('purchase')"
                class="text-blue-600 hover:text-blue-800 text-sm flex items-center"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                添加
              </button>
            </div>
            <div v-for="(item, index) in formData.purchasePayments" :key="`purchase-${index}`" class="border border-gray-200 rounded-lg p-3 mb-3">
              <div class="grid grid-cols-12 gap-4">
                <div class="form-group mb-2 col-span-3">
                  <label class="form-label mb-1">付款时间</label>
                  <input type="date" v-model="item.date" class="form-input w-full" max="3000-12-31" />
                </div>
                <div class="form-group mb-2 col-span-3">
                  <label class="form-label mb-1">付款金额</label>
                  <input type="number" v-model="item.amount" class="form-input w-full" step="0.01" />
                </div>
                <div class="form-group mb-2 col-span-5">
                  <label class="form-label mb-1">备注</label>
                  <input type="text" v-model="item.remarks" class="form-input w-full" />
                </div>
                <div class="form-group flex items-end justify-end col-span-1">
                  <button
                    v-if="formData.purchasePayments.length > 1"
                    type="button"
                    @click="removePaymentItem('purchase', index)"
                    class="text-red-600 hover:text-red-800"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 报销付款信息 -->
          <div class="bg-gray-50 p-4 rounded-lg mb-4">
            <div class="flex justify-between items-center mb-3">
              <h3 class="font-medium text-gray-800">报销付款</h3>
              <button
                type="button"
                @click="addPaymentItem('reimbursement')"
                class="text-blue-600 hover:text-blue-800 text-sm flex items-center"
              >
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                添加
              </button>
            </div>
            <div v-for="(item, index) in formData.reimbursementPayments" :key="`reimbursement-${index}`" class="border border-gray-200 rounded-lg p-3 mb-3">
              <div class="grid grid-cols-12 gap-4">
                <div class="form-group mb-2 col-span-3">
                  <label class="form-label mb-1">付款时间</label>
                  <input type="date" v-model="item.date" class="form-input w-full" max="3000-12-31" />
                </div>
                <div class="form-group mb-2 col-span-3">
                  <label class="form-label mb-1">付款金额</label>
                  <input type="number" v-model="item.amount" class="form-input w-full" step="0.01" />
                </div>
                <div class="form-group mb-2 col-span-5">
                  <label class="form-label mb-1">备注</label>
                  <input type="text" v-model="item.remarks" class="form-input w-full" />
                </div>
                <div class="form-group flex items-end justify-end col-span-1">
                  <button
                    v-if="formData.reimbursementPayments.length > 1"
                    type="button"
                    @click="removePaymentItem('reimbursement', index)"
                    class="text-red-600 hover:text-red-800"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 纯收入 -->
        <div class="form-section">
          <h2 class="section-title">纯收入</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
              <input type="number" v-model="formData.netIncome" class="form-input" step="0.01" placeholder="请输入纯收入金额" />
            </div>
          </div>
        </div>

        <!-- 附件上传 -->
        <div class="form-section">
          <h2 class="section-title">附件</h2>
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
            <div class="flex flex-col items-center">
              <svg class="w-12 h-12 text-gray-400 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
              <p class="text-sm text-gray-500 mb-2">点击或拖拽文件到此处上传</p>
              <input type="file" multiple @change="handleFileUpload" class="hidden" ref="fileInput" />
              <button type="button" @click="$refs.fileInput.click()" class="btn btn-secondary">
                选择文件
              </button>
            </div>
            <!-- 已上传文件列表 -->
            <div v-if="formData.attachments.length > 0" class="mt-4">
              <div v-for="(file, index) in formData.attachments" :key="index" class="flex items-center justify-between p-2 bg-gray-50 rounded-lg mb-2">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                  </svg>
                  <span class="text-sm text-gray-700">{{ file.name }}</span>
                </div>
                <button type="button" @click="removeAttachment(index)" class="text-red-600 hover:text-red-800">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作记录 -->
        <div class="form-section">
          <h2 class="section-title">操作记录</h2>
          <div class="bg-gray-50 rounded-lg p-4">
            <div v-for="(record, index) in operationRecords" :key="index" class="mb-4 pb-4 border-b border-gray-200 last:border-0">
              <div class="flex justify-between items-start">
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ record.operator }}</p>
                  <p class="text-xs text-gray-500">{{ record.time }}</p>
                </div>
                <span class="text-xs px-2 py-1 rounded-full" :class="{
                  'bg-green-100 text-green-800': record.type === 'create',
                  'bg-blue-100 text-blue-800': record.type === 'update',
                  'bg-red-100 text-red-800': record.type === 'delete'
                }">
                  {{ record.type === 'create' ? '创建' : record.type === 'update' ? '更新' : '删除' }}
                </span>
              </div>
              <p class="text-sm text-gray-600 mt-2">{{ record.content }}</p>
            </div>
          </div>
        </div>

        <!-- 表单操作按钮 -->
        <div class="flex justify-end space-x-4 mt-auto pt-8">
          <router-link to="/finance" class="btn btn-secondary" :disabled="submitting">取消</router-link>
          <button type="submit" class="btn btn-primary" :disabled="submitting">
            <span v-if="submitting" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              正在保存...
            </span>
            <span v-else>保存</span>
          </button>
        </div>
        
        <!-- 必填项提示 -->
        <div class="mt-4 text-sm text-gray-500">
          <span class="text-red-500">*</span> 表示必填项
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { usePaginationStore } from '@/stores/pagination'
import axios from 'axios'
import Divider from '@/components/Divider.vue'

const route = useRoute()
const router = useRouter()
const paginationStore = usePaginationStore()
const isEdit = ref(route.name === 'FinanceEdit' || route.path.includes('/finance/') && route.params.id)
const fileInput = ref(null)
const projectList = ref([])
const operationRecords = ref([])

// 加载和错误状态
const loading = ref(false)
const projectsLoading = ref(false)
const detailsLoading = ref(false)
const submitting = ref(false)
const error = ref(null)

// 获取当前页码
const currentPage = computed(() => paginationStore.getCurrentPage)

// 表单数据
const formData = ref({
  projectId: '',
  projectName: '',
  projectCode: '',
  buyerInfo: '',
  invoiceDetails: [{ date: '', taxRate: '', invoiceType: '增值税专用发票' }],
  sellerInfo: '',
  inputInvoiceDetails: [{ date: '', taxRate: '', invoiceType: '增值税专用发票' }],
  subcontractPayments: [{ date: '', amount: '', remarks: '' }],
  purchasePayments: [{ date: '', amount: '', remarks: '' }],
  reimbursementPayments: [{ date: '', amount: '', remarks: '' }],
  incomePayments: [{ date: '', amount: '', remarks: '' }],
  netIncome: '',
  attachments: []
})

// 获取财务记录详情
const fetchFinanceDetail = async (id) => {
  detailsLoading.value = true;
  error.value = null;

  try {
    console.log('正在获取财务记录详情，ID:', id);
    if (!id) {
      console.error('无效的财务记录ID');
      return;
    }

    const response = await axios.get(`/api/finance/${id}`);
    const data = response.data;

    // 更新表单数据，确保数组始终存在
    formData.value = {
      ...data,
      buyerInfo: data.buyerInfo || '',
      invoiceDetails: data.invoiceDetails || [{ date: '', taxRate: '', invoiceType: '增值税专用发票' }],
      inputInvoiceDetails: data.inputInvoiceDetails || [{ date: '', taxRate: '', invoiceType: '增值税专用发票' }],
      subcontractPayments: data.subcontractPayments || [{ date: '', amount: '', remarks: '' }],
      purchasePayments: data.purchasePayments || [{ date: '', amount: '', remarks: '' }],
      reimbursementPayments: data.reimbursementPayments || [{ date: '', amount: '', remarks: '' }],
      incomePayments: data.incomePayments || [{ date: '', amount: '', remarks: '' }]
    };

    // 获取操作记录
    try {
      const activityResponse = await axios.get(`/api/finance/${id}/activities`);
      operationRecords.value = activityResponse.data.results || [];
    } catch (activityErr) {
      console.error('获取操作记录失败:', activityErr);
    }

    // 找到对应的项目信息
    const project = projectList.value.find(p => p.id === data.projectId);
    if (project) {
      formData.value.projectName = project.name;
      formData.value.projectCode = project.code;
    }

    console.log('财务记录详情获取成功:', formData.value);
  } catch (err) {
    console.error('获取财务记录详情失败:', err);
    error.value = err.response?.data?.message || '获取财务记录详情失败，请重试';
  } finally {
    detailsLoading.value = false;
  }
}

// 获取项目列表
const fetchProjects = async () => {
  projectsLoading.value = true;
  try {
    const response = await axios.get('/api/projects', {
      params: {  } // status: 'active'
    });
    projectList.value = response.data.results || [];
    console.log('项目列表:', projectList.value);
  } catch (err) {
    console.error('获取项目列表失败:', err);
    error.value = err.response?.data?.message || '获取项目列表失败，请重试';
  } finally {
    projectsLoading.value = false;
  }
}

// 处理文件上传
const handleFileUpload = (event) => {
  const files = Array.from(event.target.files)
  formData.value.attachments = [...formData.value.attachments, ...files]
  event.target.value = null
}

// 移除附件
const removeAttachment = (index) => {
  formData.value.attachments.splice(index, 1)
}

// 处理API错误
function handleApiError(err, action) {
  console.error(`${action}财务记录失败:`, err);

  if (!err.response || !err.response.data) {
    return err.message || `${action}失败，请重试`;
  }

  const { data } = err.response;

  if (data.message) {
    return data.message;
  }

  if (data.errors) {
    const errorMessages = Object.values(data.errors).flat();
    return errorMessages.join('\n');
  }

  return `${action}失败，请重试`;
}

// 处理表单提交
const handleSubmit = async () => {
  submitting.value = true;
  error.value = null;

  const action = isEdit.value ? '更新' : '创建';

  try {
    // 处理税率，确保保留两位小数
    if (formData.value.invoiceDetails && formData.value.invoiceDetails.length > 0) {
      formData.value.invoiceDetails.forEach(item => {
        if (item.taxRate) {
          item.taxRate = Number(item.taxRate).toFixed(2);
        }
      });
    }
    
    if (formData.value.inputInvoiceDetails && formData.value.inputInvoiceDetails.length > 0) {
      formData.value.inputInvoiceDetails.forEach(item => {
        if (item.taxRate) {
          item.taxRate = Number(item.taxRate).toFixed(2);
        }
      });
    }
    
    // 准备要提交的数据
    const formDataToSubmit = { ...formData.value };

    // 处理文件上传
    if (formDataToSubmit.attachments && formDataToSubmit.attachments.length > 0) {
      // 如果有新文件需要上传，先上传文件
      const fileFormData = new FormData();
      formDataToSubmit.attachments.forEach(file => {
        if (file instanceof File) {
          fileFormData.append('files', file);
        }
      });

      if (fileFormData.has('files')) {
        try {
          const uploadResponse = await axios.post('/api/uploads', fileFormData, {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          });

          // 替换原来的文件列表为上传后的文件URL列表
          formDataToSubmit.attachments = [
            ...formDataToSubmit.attachments.filter(a => !(a instanceof File)),
            ...uploadResponse.data.files
          ];
        } catch (uploadErr) {
          console.error('文件上传失败:', uploadErr);
          error.value = uploadErr.response?.data?.message || '文件上传失败，请重试';
          submitting.value = false;
          return;
        }
      }
    }

    if (isEdit.value) {
      await axios.put(`/api/finance/${route.params.id}`, formDataToSubmit);
    } else {
      await axios.post('/api/finance', formDataToSubmit);
    }

    // 使用 store 中的页码返回列表
    router.push({
      path: '/finance',
      query: { page: currentPage.value }
    });
  } catch (err) {
    error.value = handleApiError(err, action);
  } finally {
    submitting.value = false;
  }
}

// 添加付款项目
const addPaymentItem = (type) => {
  const newItem = { date: '', amount: '', remarks: '' }
  const newInvoiceItem = { date: '', taxRate: '', invoiceType: '增值税专用发票' }

  // 确保数组存在
  if (!formData.value.subcontractPayments) formData.value.subcontractPayments = []
  if (!formData.value.purchasePayments) formData.value.purchasePayments = []
  if (!formData.value.reimbursementPayments) formData.value.reimbursementPayments = []
  if (!formData.value.incomePayments) formData.value.incomePayments = []
  if (!formData.value.invoiceDetails) formData.value.invoiceDetails = []
  if (!formData.value.inputInvoiceDetails) formData.value.inputInvoiceDetails = []

  if (type === 'subcontract') {
    formData.value.subcontractPayments.push(newItem)
  } else if (type === 'purchase') {
    formData.value.purchasePayments.push(newItem)
  } else if (type === 'reimbursement') {
    formData.value.reimbursementPayments.push(newItem)
  } else if (type === 'income') {
    formData.value.incomePayments.push(newItem)
  } else if (type === 'invoice') {
    formData.value.invoiceDetails.push(newInvoiceItem)
  } else if (type === 'inputInvoice') {
    formData.value.inputInvoiceDetails.push(newInvoiceItem)
  }
}

// 移除付款项目
const removePaymentItem = (type, index) => {
  if (type === 'subcontract') {
    formData.value.subcontractPayments.splice(index, 1)
  } else if (type === 'purchase') {
    formData.value.purchasePayments.splice(index, 1)
  } else if (type === 'reimbursement') {
    formData.value.reimbursementPayments.splice(index, 1)
  } else if (type === 'income') {
    formData.value.incomePayments.splice(index, 1)
  } else if (type === 'invoice') {
    formData.value.invoiceDetails.splice(index, 1)
  } else if (type === 'inputInvoice') {
    formData.value.inputInvoiceDetails.splice(index, 1)
  }
}

// 监听项目选择变化
watch(() => formData.value.projectId, (newProjectId) => {
  console.log('项目ID变化:', newProjectId)
  const selectedProject = projectList.value.find(p => p.id === newProjectId)
  console.log('选中的项目:', selectedProject)
  if (selectedProject) {
    formData.value.projectName = selectedProject.name
    formData.value.projectCode = selectedProject.code
  } else {
    formData.value.projectName = ''
    formData.value.projectCode = ''
  }
})

// 返回列表
const goBack = () => {
  // 使用 store 中的页码返回列表
  router.push({
    path: '/finance',
    query: { page: currentPage.value }
  })
}

// 页面加载时获取数据
onMounted(async () => {
  await fetchProjects()
  if (isEdit.value && route.params.id) {
    console.log('编辑模式，ID:', route.params.id)
    await fetchFinanceDetail(route.params.id)
  } else {
    console.log('创建模式')
  }
})
</script>

<style scoped>
.form-section {
  @apply space-y-4;
}

.section-title {
  @apply text-lg font-semibold text-gray-900 mb-2;
}

.form-group {
  @apply space-y-1;
}

.form-label {
  @apply block text-sm font-medium text-gray-700;
}

.form-input {
  @apply w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200;
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500;
}
</style>