/**
 * Migration Utilities
 * 
 * This module provides utility functions for database schema migration,
 * including data type mapping and value comparison.
 */

const { DataTypes } = require('sequelize');

/**
 * Maps Sequelize data types to PostgreSQL data types
 * @param {Object} attrDef - Sequelize attribute definition
 * @returns {Object} Mapped type information
 */
function mapDataType(attrDef) {
  if (!attrDef.type) return { type: 'text' };

  let dataType;
  let dataLength;

  const typeMapping = {
    [DataTypes.STRING]: () => ({ type: 'varchar', length: attrDef.type.options?.length }),
    [DataTypes.TEXT]: () => ({ type: 'text' }),
    [DataTypes.INTEGER]: () => ({ type: 'integer' }),
    [DataTypes.BIGINT]: () => ({ type: 'bigint' }),
    [DataTypes.FLOAT]: () => ({ type: 'float8' }),
    [DataTypes.REAL]: () => ({ type: 'float4' }),
    [DataTypes.DOUBLE]: () => ({ type: 'float8' }),
    [DataTypes.DECIMAL]: () => {
      const { precision, scale } = attrDef.type.options || {};
      return {
        type: precision !== undefined && scale !== undefined
          ? `decimal(${precision},${scale})`
          : 'decimal'
      };
    },
    [DataTypes.BOOLEAN]: () => ({ type: 'boolean' }),
    [DataTypes.DATE]: () => ({ type: 'date' }),
    [DataTypes.DATEONLY]: () => ({ type: 'date' }),
    [DataTypes.TIME]: () => ({ type: 'timetz' }),
    [DataTypes.UUID]: () => ({ type: 'uuid' }),
    [DataTypes.CIDR]: () => ({ type: 'cidr' }),
    [DataTypes.INET]: () => ({ type: 'inet' }),
    [DataTypes.MACADDR]: () => ({ type: 'macaddr' }),
    [DataTypes.JSON]: () => ({ type: 'json' }),
    [DataTypes.JSONB]: () => ({ type: 'jsonb' }),
    [DataTypes.ARRAY]: () => {
      const elementType = attrDef.type.type;
      const baseType = mapDataType({ type: elementType }).type;
      return { type: `${baseType}[]` };
    },
    [DataTypes.ENUM]: () => ({ type: 'varchar' }),
    [DataTypes.RANGE]: () => {
      const rangeType = attrDef.type.options.subtype;
      const typeMap = {
        [DataTypes.INTEGER]: 'int4',
        [DataTypes.BIGINT]: 'int8',
        [DataTypes.DECIMAL]: 'decimal',
        [DataTypes.DATE]: 'timestamp',
        [DataTypes.DATEONLY]: 'date'
      };
      const mappedType = typeMap[rangeType] || 'numeric';
      return { type: `${mappedType}_range` };
    },
    [DataTypes.GEOMETRY]: () => ({ type: 'geometry' }),
    [DataTypes.GEOGRAPHY]: () => ({ type: 'geography' })
  };

  const mapper = typeMapping[attrDef.type.constructor];
  return mapper ? mapper() : { type: attrDef.type.key?.toLowerCase() || 'text' };
}

/**
 * Compares default values between database and model definitions
 * @param {*} dbValue - Database default value
 * @param {*} modelValue - Model default value
 * @param {string} dbType - Database column type
 * @returns {boolean} Whether the values are equivalent
 */
function areDefaultValuesEqual(dbValue, modelValue, dbType) {
  // Handle null/undefined equivalence
  if (dbValue === null && modelValue === undefined) return true;
  if (dbValue === undefined && modelValue === null) return true;
  if (dbValue === null && modelValue === null) return true;
  if (dbValue === undefined && modelValue === undefined) return true;

  // Skip UUID type comparisons
  if (dbType === 'uuid') return true;

  // Handle string representations
  if (typeof dbValue === 'string') {
    // Remove type casting and quotes
    dbValue = dbValue.split('::')?.[0]?.replace(/^'|'$/g, '') || dbValue;
    dbValue = dbValue.replace(/^\(|\)$/g, '');

    // Handle boolean values
    if (dbValue === 'true' && modelValue === true) return true;
    if (dbValue === 'false' && modelValue === false) return true;

    // Handle numeric values
    if (!isNaN(dbValue) && typeof modelValue === 'number') {
      return Number(dbValue) === modelValue;
    }

    // Handle array/object values
    try {
      const parsedDbValue = JSON.parse(dbValue);
      return JSON.stringify(parsedDbValue) === JSON.stringify(modelValue);
    } catch (e) {
      // If parsing fails, compare as strings
      return dbValue === modelValue;
    }
  }

  // Direct comparison for other types
  return dbValue === modelValue;
}

module.exports = {
  mapDataType,
  areDefaultValuesEqual
};