// @ts-check
import { test, expect } from '@playwright/test';

test.describe('采购管理流程', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('采购列表页面', async ({ page }) => {
    // 访问采购列表页面
    await page.goto('/procurements');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('采购列表');

    // 验证采购表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加采购按钮存在
    expect(await page.isVisible('a[href="/procurements/create"]')).toBeTruthy();
  });

  test('创建采购单流程', async ({ page }) => {
    // 访问创建采购页面
    await page.goto('/procurements/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('创建采购单');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 填写表单
    await page.fill('input[name="purchaseNumber"]', `PO-${Date.now()}`);
    
    // 选择供应商
    await page.click('select[name="supplierId"]');
    await page.selectOption('select[name="supplierId"]', { index: 1 });
    
    // 选择项目
    await page.click('select[name="projectId"]');
    await page.selectOption('select[name="projectId"]', { index: 1 });
    
    // 设置采购日期
    await page.fill('input[name="purchaseDate"]', new Date().toISOString().split('T')[0]);
    
    // 添加采购项
    await page.click('button.add-item-button');
    await page.fill('input[name="items[0].name"]', 'Test Item');
    await page.fill('input[name="items[0].quantity"]', '10');
    await page.fill('input[name="items[0].unitPrice"]', '100');
    
    // 设置状态
    await page.selectOption('select[name="status"]', 'pending');
    
    // 添加备注
    await page.fill('textarea[name="notes"]', 'This is a test procurement created by E2E test');

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到采购列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/procurements');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('查看采购单详情', async ({ page }) => {
    // 访问采购列表页面
    await page.goto('/procurements');

    // 点击第一个采购单的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/procurements/');

    // 验证详情页面内容
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('采购单详情');
    expect(await page.isVisible('.procurement-info')).toBeTruthy();
    expect(await page.isVisible('.procurement-items')).toBeTruthy();
    expect(await page.isVisible('a.edit-button')).toBeTruthy();
  });

  test('编辑采购单流程', async ({ page }) => {
    // 访问采购列表页面
    await page.goto('/procurements');

    // 点击第一个编辑按钮
    await page.click('table tbody tr:first-child a.edit-button');

    // 等待页面跳转到编辑页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/procurements/');
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/edit');

    // 验证表单已预填充
    expect(await page.inputValue('input[name="purchaseNumber"]')).toBeTruthy();

    // 修改采购单状态
    await page.selectOption('select[name="status"]', 'approved');

    // 修改备注
    await page.fill('textarea[name="notes"]', 'Updated by E2E test');

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到采购列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/procurements');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('删除采购单流程', async ({ page }) => {
    // 访问采购列表页面
    await page.goto('/procurements');

    // 获取采购单数量
    const initialProcurementCount = await page.locator('table tbody tr').count();

    // 点击第一个删除按钮
    await page.click('table tbody tr:first-child button.delete-button');

    // 确认删除
    await page.click('button.confirm-delete');

    // 等待页面刷新
    await page.waitForTimeout(1000);

    // 验证采购单数量减少
    const newProcurementCount = await page.locator('table tbody tr').count();
    expect(newProcurementCount).toBeLessThan(initialProcurementCount);

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('采购单搜索功能', async ({ page }) => {
    // 访问采购列表页面
    await page.goto('/procurements');

    // 在搜索框中输入关键字
    await page.fill('input[placeholder*="搜索"]', 'PO-');

    // 点击搜索按钮
    await page.click('button.search-button');

    // 等待搜索结果加载
    await page.waitForTimeout(1000);

    // 验证搜索结果
    const purchaseNumbers = await page.locator('table tbody tr td:nth-child(2)').allTextContents();
    for (const number of purchaseNumbers) {
      expect(number).toContain('PO-');
    }
  });

  test('采购单状态筛选功能', async ({ page }) => {
    // 访问采购列表页面
    await page.goto('/procurements');

    // 选择状态
    await page.selectOption('select.status-filter', 'pending');

    // 等待筛选结果加载
    await page.waitForTimeout(1000);

    // 验证筛选结果
    const statuses = await page.locator('table tbody tr td:nth-child(5)').allTextContents();
    for (const status of statuses) {
      expect(status.toLowerCase()).toContain('pending');
    }
  });

  test('采购单日期范围筛选功能', async ({ page }) => {
    // 访问采购列表页面
    await page.goto('/procurements');

    // 设置开始日期
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 1);
    await page.fill('input[name="dateFrom"]', startDate.toISOString().split('T')[0]);

    // 设置结束日期
    const endDate = new Date();
    await page.fill('input[name="dateTo"]', endDate.toISOString().split('T')[0]);

    // 点击筛选按钮
    await page.click('button.filter-button');

    // 等待筛选结果加载
    await page.waitForTimeout(1000);

    // 验证筛选结果存在
    expect(await page.locator('table tbody tr').count()).toBeGreaterThan(0);
  });
});
