// @ts-check
import { test, expect } from '@playwright/test';

test.describe('个人资料管理测试', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
    
    // 确认已登录
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/dashboard');
  });

  test('访问个人资料页面', async ({ page }) => {
    // 点击导航中的个人资料链接
    await page.click('a[href="/profile"]');
    await page.waitForNavigation();
    
    // 验证个人资料页面加载
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/profile');
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('个人资料');
    
    // 验证个人资料表单存在
    expect(await page.isVisible('form')).toBeTruthy();
    
    // 验证基本字段存在
    expect(await page.isVisible('input[name="name"]')).toBeTruthy();
    expect(await page.isVisible('input[name="email"]')).toBeTruthy();
    expect(await page.isVisible('input[type="file"]')).toBeTruthy();
  });

  test('更新个人资料', async ({ page }) => {
    // 访问个人资料页面
    await page.goto('/profile');
    
    // 获取当前姓名值
    const currentName = await page.inputValue('input[name="name"]');
    
    // 更新姓名字段
    const newName = `Test User ${Date.now()}`;
    await page.fill('input[name="name"]', newName);
    
    // 更新手机号字段(如果存在)
    if (await page.isVisible('input[name="phone"]')) {
      await page.fill('input[name="phone"]', '13800138000');
    }
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 等待成功消息
    await page.waitForSelector('.success-message');
    expect(await page.isVisible('.success-message')).toBeTruthy();
    
    // 验证字段已更新
    expect(await page.inputValue('input[name="name"]')).toBe(newName);
    
    // 恢复原始姓名
    await page.fill('input[name="name"]', currentName);
    await page.click('button[type="submit"]');
    await page.waitForSelector('.success-message');
  });

  test('修改密码', async ({ page }) => {
    // 访问个人资料页面
    await page.goto('/profile');
    
    // 切换到密码修改选项卡(如果存在)
    if (await page.isVisible('button:has-text("修改密码")')) {
      await page.click('button:has-text("修改密码")');
    }
    
    // 填写密码修改表单
    if (await page.isVisible('input[name="currentPassword"]')) {
      await page.fill('input[name="currentPassword"]', 'password123');
      await page.fill('input[name="newPassword"]', 'password123'); // 使用相同密码避免实际修改
      await page.fill('input[name="confirmPassword"]', 'password123');
      
      // 提交密码修改表单
      await page.click('button:has-text("更新密码")');
      
      // 等待成功消息
      await page.waitForSelector('.success-message');
      expect(await page.isVisible('.success-message')).toBeTruthy();
    } else {
      // 如果密码修改在同一表单中
      if (await page.isVisible('input[name="password"]')) {
        await page.fill('input[name="password"]', 'password123');
        await page.fill('input[name="confirmPassword"]', 'password123');
        
        // 提交表单
        await page.click('button[type="submit"]');
        
        // 等待成功消息
        await page.waitForSelector('.success-message');
        expect(await page.isVisible('.success-message')).toBeTruthy();
      }
    }
  });

  test('验证个人资料验证功能', async ({ page }) => {
    // 访问个人资料页面
    await page.goto('/profile');
    
    // 清空姓名字段
    await page.fill('input[name="name"]', '');
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 验证表单未提交并显示错误
    expect(await page.isVisible('.error-message')).toBeTruthy();
    
    // 输入无效的电子邮件格式
    if (await page.isEnabled('input[name="email"]')) {
      await page.fill('input[name="email"]', 'invalid-email');
      
      // 提交表单
      await page.click('button[type="submit"]');
      
      // 验证表单未提交并显示错误
      expect(await page.isVisible('.error-message')).toBeTruthy();
    }
  });
});
