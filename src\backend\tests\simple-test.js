// Simple test file for backend
const assert = require('assert');
const { describe, it } = require('mocha');

describe('Basic Backend Tests', () => {
  it('true should be true', () => {
    assert.strictEqual(true, true);
  });

  it('1 + 1 should equal 2', () => {
    assert.strictEqual(1 + 1, 2);
  });

  it('string concatenation works', () => {
    assert.strictEqual('hello ' + 'world', 'hello world');
  });
});
