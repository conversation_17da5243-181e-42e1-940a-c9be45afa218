const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');


  const Department = sequelize.define('department', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
      },
    },
    code: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true,
      },
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },

    parentId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'department',
        key: 'id',
      },
    },
    status: {
      type: DataTypes.ENUM('active', 'inactive'),
      defaultValue: 'active',
    }
  }, {
    tableName: 'department',
    timestamps: true,
  });

  Department.associate = (models) => {
    // Department has many employees (User)
    Department.hasMany(models.User, {
      foreignKey: 'departmentId',
      as: 'employees'
    });

    // Self-referencing association for department hierarchy
    Department.belongsTo(Department, {
      foreignKey: 'parentId',
      as: 'parentDepartment'
    });

    Department.hasMany(Department, {
      foreignKey: 'parentId',
      as: 'childDepartments'
    });
  };

module.exports = { Department };