import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import WorkHoursList from '@/views/workhours/WorkHoursList.vue'
import WorkHoursCalendar from '@/views/workhours/WorkHoursCalendar.vue'
import WorkHoursStatistics from '@/views/workhours/WorkHoursStatistics.vue'
import WorkHoursRecording from '@/views/workhours/WorkHoursRecording.vue'
import WorkHoursEdit from '@/views/workhours/WorkHoursEdit.vue'
import ContractWorkers from '@/views/employees/ContractWorkers.vue'
import TemporaryWorkers from '@/views/employees/TemporaryWorkers.vue'
import EmployeeList from '@/views/employees/EmployeeList.vue'

// Route components
const Login = () => import('@/views/Login.vue')
const Register = () => import('@/views/Register.vue')
const Dashboard = () => import('@/views/Dashboard.vue')
const ForgotPassword = () => import('@/views/ForgotPassword.vue')
const ResetPassword = () => import('@/views/ResetPassword.vue')
const VerifyEmail = () => import('@/views/VerifyEmail.vue')
const Profile = () => import('@/views/Profile.vue')
const NotFound = () => import('@/views/NotFound.vue')
const UsersView = () => import('@/views/UsersView.vue')
const Settings = () => import('@/views/Settings.vue')
const UiExamples = () => import('@/views/examples/UiExamples.vue')

// Document routes
const DocumentList = () => import('@/views/documents/DocumentList.vue')
const DocumentDetail = () => import('@/views/documents/DocumentDetail.vue')
const DocumentEditor = () => import('@/views/documents/DocumentEditor.vue')

// Categories route
const CategoryList = () => import('@/views/categories/CategoryList.vue')

// 这里移除了未使用的页面引用

// Client routes
const ClientList = () => import('@/views/clients/ClientList.vue')
const ClientDetail = () => import('@/views/clients/ClientDetail.vue')
const ClientCreate = () => import('@/views/clients/ClientCreate.vue')
const ClientEdit = () => import('@/views/clients/ClientEdit.vue')

// Project routes
const ProjectList = () => import('@/views/projects/ProjectList.vue')

const ProjectCreate = () => import('@/views/projects/ProjectCreate.vue')
const ProjectEdit = () => import('@/views/projects/ProjectEdit.vue')
const ProjectStats = () => import('@/views/projects/ProjectStats.vue')

// Supplier routes
const SupplierList = () => import('@/views/suppliers/SupplierList.vue')
const SupplierDetail = () => import('@/views/suppliers/SupplierDetail.vue')

// Attendance routes
const AttendanceList = () => import('@/views/attendance/AttendanceList.vue')
const AttendanceDetail = () => import('@/views/attendance/AttendanceDetail.vue')
const AttendanceEdit = () => import('@/views/attendance/AttendanceEdit.vue')

// Admin routes
const UserManagement = () => import('@/views/admin/UserManagement.vue')
const UserDetail = () => import('@/views/admin/UserDetail.vue')
const RoleManagement = () => import('@/views/admin/RoleManagement.vue')

// Product routes
const ProductList = () => import('@/views/products/ProductList.vue')
const ProductCreate = () => import('@/views/products/ProductCreate.vue')

// Purchase routes
const PurchaseList = () => import('@/views/purchases/PurchaseList.vue')
const PurchaseDetail = () => import('@/views/purchases/PurchaseDetail.vue')
const PurchaseCreate = () => import('@/views/purchases/PurchaseCreate.vue')

// Inventory routes
const InventoryList = () => import('@/views/inventory/InventoryList.vue')
const InventoryDetail = () => import('@/views/inventory/InventoryDetail.vue')
const InventoryCreate = () => import('@/views/inventory/InventoryCreate.vue')
const InventoryTransactions = () => import('@/views/inventory/InventoryTransactions.vue')
const InventoryLowStock = () => import('@/views/inventory/InventoryLowStock.vue')
const InventoryValueReport = () => import('@/views/inventory/InventoryValueReport.vue')
const WarehouseManagement = () => import('@/views/inventory/WarehouseManagement.vue')

// Finance routes
const FinanceList = () => import('@/views/finance/FinanceList.vue')
const FinanceCreate = () => import('@/views/finance/FinanceCreate.vue')
const FinanceStats = () => import('@/views/finance/FinanceStats.vue')

// Contract routes
const ContractList = () => import('@/views/contracts/ContractList.vue')
const ContractDetail = () => import('@/views/contracts/ContractDetail.vue')
const ContractCreate = () => import('@/views/contracts/ContractCreate.vue')

// Business routes
const SubcontractList = () => import('@/views/subcontracts/SubcontractList.vue')
const SubcontractDetail = () => import('@/views/subcontracts/SubcontractDetail.vue')

// Reimbursement routes
const ReimbursementList = () => import('@/views/reimbursements/ReimbursementList.vue')
const ReimbursementDetail = () => import('@/views/reimbursements/ReimbursementDetail.vue')

// Public routes
const PublicProjectList = () => import('@/views/projects/ProjectList.vue')
const PublicSupplierList = () => import('@/views/suppliers/SupplierList.vue')
const PublicFinanceStats = () => import('@/views/finance/FinanceStats.vue')

// Department routes
const DepartmentList = () => import('@/views/departments/DepartmentList.vue')
const DepartmentDetail = () => import('@/views/departments/DepartmentDetail.vue')
const DepartmentCreate = () => import('@/views/departments/DepartmentCreate.vue')
const DepartmentEdit = () => import('@/views/departments/DepartmentEdit.vue')

// Message routes
const MessageList = () => import('@/views/MessageList.vue')
const TestMessageGenerator = () => import('@/views/TestMessageGenerator.vue')

// Routes configuration
const routes = [
  // Public routes that don't require authentication
  {
    path: '/public/projects',
    name: 'PublicProjectList',
    component: PublicProjectList,
    meta: { requiresAuth: false }
  },
  {
    path: '/public/suppliers',
    name: 'PublicSupplierList',
    component: PublicSupplierList,
    meta: { requiresAuth: false }
  },
  {
    path: '/public/finance/stats',
    name: 'PublicFinanceStats',
    component: PublicFinanceStats,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    redirect: (to) => {
      const authStore = useAuthStore()
      return authStore.isAuthenticated ? '/dashboard' : '/login'
    }
  },
  {
    path: '/login',
    name: '登录',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/register',
    name: '注册',
    component: Register,
    meta: { requiresGuest: true }
  },
  {
    path: '/forgot-password',
    name: '忘记密码',
    component: ForgotPassword,
    meta: { requiresGuest: true }
  },
  {
    path: '/reset-password',
    name: '重置密码',
    component: ResetPassword,
    meta: { requiresGuest: true }
  },
  {
    path: '/verify-email/:token',
    name: '验证邮箱',
    component: VerifyEmail,
    meta: { requiresAuth: false }
  },
  {
    path: '/dashboard',
    name: '仪表盘',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: '个人资料',
    component: Profile,
    meta: { requiresAuth: true }
  },
  {
    path: '/documents',
    name: '文档列表',
    component: DocumentList,
    meta: { requiresAuth: true }
  },
  {
    path: '/documents/create',
    name: '创建文档',
    component: DocumentEditor,
    meta: { requiresAuth: true }
  },
  {
    path: '/documents/:id',
    name: '文档详情',
    component: DocumentDetail,
    meta: { requiresAuth: true }
  },

  {
    path: '/admin/users',
    name: '用户管理',
    component: UserManagement,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/admin/users/:id',
    name: '用户详情',
    component: UserDetail,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/admin/roles',
    name: '角色管理',
    component: RoleManagement,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/users',
    name: '用户',
    component: UsersView,
    meta: { requiresAuth: true }
  },
  {
    path: '/clients',
    name: '客户列表',
    component: ClientList,
    meta: { requiresAuth: true }
  },
  {
    path: '/clients/create',
    name: '创建客户',
    component: ClientCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/clients/:id',
    name: '客户详情',
    component: ClientDetail,
    meta: { requiresAuth: true }
  },

  {
    path: '/projects',
    name: '项目列表',
    component: ProjectList,
    meta: { requiresAuth: true }
  },
  {
    path: '/projects/stats',
    name: '项目统计',
    component: ProjectStats,
    meta: { requiresAuth: true }
  },
  {
    path: '/projects/create',
    name: '创建项目',
    component: ProjectCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/projects/edit/:id',
    name: '编辑项目',
    component: ProjectEdit,
    meta: { requiresAuth: true }
  },
  {
    path: '/subcontracts',
    name: '分包列表',
    component: SubcontractList,
    meta: { requiresAuth: true }
  },
  {
    path: '/subcontracts/create',
    name: '创建分包',
    component: SubcontractDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/subcontracts/:id',
    name: '分包详情',
    component: SubcontractDetail,
    meta: { requiresAuth: true }
  },

  {
    path: '/suppliers',
    name: '供应商列表',
    component: SupplierList,
    meta: { requiresAuth: true }
  },
  {
    path: '/suppliers/create',
    name: '创建供应商',
    component: SupplierDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/suppliers/:id',
    name: '供应商详情',
    component: SupplierDetail,
    meta: { requiresAuth: true }
  },

  {
    path: '/products',
    name: '产品列表',
    component: ProductList,
    meta: { requiresAuth: true }
  },
  {
    path: '/products/create',
    name: '创建产品',
    component: ProductCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/products/edit/:id',
    name: '编辑产品',
    component: ProductCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/products/stats',
    name: '产品统计',
    component: () => import('@/views/products/ProductStats.vue'),
    meta: { requiresAuth: true }
  },

  {
    path: '/purchases',
    name: '采购列表',
    component: PurchaseList,
    meta: { requiresAuth: true }
  },
  {
    path: '/purchases/create',
    name: '创建采购',
    component: PurchaseCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/purchases/edit/:id',
    name: '编辑采购',
    component: PurchaseCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/purchases/:id',
    name: '采购详情',
    component: PurchaseDetail,
    meta: { requiresAuth: true }
  },

  {
    path: '/inventory',
    name: '库存列表',
    component: InventoryList,
    meta: { requiresAuth: true }
  },
  {
    path: '/inventory/low-stock',
    name: '库存不足物品',
    component: InventoryLowStock,
    meta: { requiresAuth: true }
  },
  {
    path: '/inventory/value',
    name: '库存价值报表',
    component: InventoryValueReport,
    meta: { requiresAuth: true }
  },
  {
    path: '/inventory/warehouse',
    name: '仓库管理',
    component: WarehouseManagement,
    meta: { requiresAuth: true }
  },
  {
    path: '/inventory/create',
    name: '创建库存',
    component: InventoryDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/inventory/records',
    name: '库存记录',
    component: InventoryTransactions,
    meta: { requiresAuth: true }
  },
  {
    path: '/inventory/edit/:id',
    name: '编辑库存',
    component: InventoryDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/inventory/:id',
    name: '库存详情',
    component: InventoryDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/inventory/:id/transactions',
    name: '库存交易',
    component: InventoryTransactions,
    meta: { requiresAuth: true }
  },

  {
    path: '/finance',
    name: '财务列表',
    component: FinanceList,
    meta: { requiresAuth: false }
  },
  {
    path: '/finance/stats',
    name: '财务统计',
    component: FinanceStats,
    meta: { requiresAuth: true }
  },
  {
    path: '/finance/create',
    name: '创建财务',
    component: FinanceCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/finance/edit/:id',
    name: '编辑财务',
    component: FinanceCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/contracts',
    name: '合同列表',
    component: ContractList,
    meta: { requiresAuth: true }
  },
  {
    path: '/contracts/create',
    name: '创建合同',
    component: ContractCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/contracts/edit/:id',
    name: '编辑合同',
    component: ContractCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/contracts/:id',
    name: '合同详情',
    component: ContractDetail,
    meta: { requiresAuth: true }
  },

  {
    path: '/attendance',
    name: '考勤列表',
    component: AttendanceList,
    meta: { requiresAuth: true }
  },
  {
    path: '/attendance/create',
    name: '创建考勤',
    component: AttendanceEdit,
    meta: { requiresAuth: true }
  },
  {
    path: '/attendance/edit/:id',
    name: '编辑考勤',
    component: AttendanceEdit,
    meta: { requiresAuth: true }
  },
  {
    path: '/attendance/:id',
    name: '考勤详情',
    component: AttendanceDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/categories',
    name: '分类列表',
    component: CategoryList,
    meta: { requiresAuth: true }
  },
  // 这里移除了未使用的路由
  {
    path: '/examples/ui',
    name: 'UI示例',
    component: UiExamples,
    meta: { requiresAuth: true }
  },
  {
    path: '/settings',
    name: '系统设置',
    component: Settings,
    meta: { requiresAuth: true }
  },
  {
    path: '/reimbursements',
    name: '报销列表',
    component: ReimbursementList,
    meta: { requiresAuth: true }
  },
  {
    path: '/reimbursements/create',
    name: '创建报销',
    component: ReimbursementDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/reimbursements/:id',
    name: '报销详情',
    component: ReimbursementDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/reimbursements/:id/edit',
    name: '编辑报销',
    component: ReimbursementDetail,
    meta: { requiresAuth: true }
  },

  {
    path: '/workhours',
    name: '工时列表',
    component: WorkHoursList,
    meta: { requiresAuth: true }
  },
  {
    path: '/workhours/calendar',
    name: '工时日历',
    component: WorkHoursCalendar,
    meta: { requiresAuth: true }
  },
  {
    path: '/workhours/statistics',
    name: '工时统计',
    component: WorkHoursStatistics,
    meta: { requiresAuth: true }
  },
  {
    path: '/workhours/recording',
    name: '工时记录',
    component: WorkHoursRecording,
    meta: { requiresAuth: true }
  },
  {
    path: '/workhours/edit/:id',
    name: '编辑工时',
    component: WorkHoursEdit,
    meta: { requiresAuth: true }
  },
  {
    path: '/employees',
    name: '员工管理',
    component: EmployeeList,
    meta: { requiresAuth: true }
  },
  {
    path: '/employees/contract',
    name: '合同工管理',
    component: ContractWorkers,
    meta: { requiresAuth: false }
  },
  {
    path: '/employees/temporary',
    name: '临时工管理',
    component: TemporaryWorkers,
    meta: { requiresAuth: false }
  },
  {
    path: '/employees/temporary/add',
    name: '添加临时工',
    component: () => import('@/views/employees/TemporaryWorkerEdit.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/employees/temporary/edit/:id',
    name: '编辑临时工',
    component: () => import('@/views/employees/TemporaryWorkerEdit.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/employees/contract/add',
    name: '添加合同工',
    component: () => import('@/views/employees/ContractWorkerEdit.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/employees/contract/edit/:id',
    name: '编辑合同工',
    component: () => import('@/views/employees/ContractWorkerEdit.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/departments',
    name: '部门列表',
    component: DepartmentList,
    meta: { requiresAuth: true }
  },
  {
    path: '/departments/create',
    name: '创建部门',
    component: DepartmentCreate,
    meta: { requiresAuth: true }
  },
  {
    path: '/departments/:id',
    name: '部门详情',
    component: DepartmentDetail,
    meta: { requiresAuth: true }
  },
  {
    path: '/departments/:id/edit',
    name: '编辑部门',
    component: DepartmentEdit,
    meta: { requiresAuth: true }
  },
  {
    path: '/departments/structure',
    name: '组织架构',
    component: () => import('@/views/departments/DepartmentStructure.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/messages',
    name: '消息中心',
    component: MessageList,
    meta: { requiresAuth: true }
  },
  {
    path: '/test-messages',
    name: '测试消息生成器',
    component: TestMessageGenerator,
    meta: { requiresAuth: true }
  },
  {
    path: '/:pathMatch(.*)*',
    name: '页面未找到',
    component: NotFound
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guard
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Wait for auth to initialize before proceeding with navigation
  if (!authStore.isInitialized) {
    await authStore.init()
  }

  // Allow public routes to pass through without authentication
  if (to.meta.requiresAuth === false) {
    return next()
  }

  // Handle routes that require guest access
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    return next('/dashboard')
  }

  // Handle routes that require authentication
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    return next('/login')
  }

  // Handle routes that require admin access
  if (to.meta.requiresAdmin && !authStore.isAdmin) {
    return next('/dashboard')
  }

  next()
})

export default router