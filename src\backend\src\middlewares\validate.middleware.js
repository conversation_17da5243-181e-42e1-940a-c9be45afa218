const Joi = require('joi');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');

/**
 * Middleware for request data validation
 * @param {Object} schema - Joi validation schema
 * @returns {Function} Express middleware function
 */
const validate = (schema) => (req, res, next) => {
  const validSchema = Object.keys(schema).reduce((acc, key) => {
    if (key === 'params' || key === 'query' || key === 'body') {
      acc[key] = schema[key];
    }
    return acc;
  }, {});

  const object = Object.keys(validSchema).reduce((acc, key) => {
    acc[key] = req[key];
    return acc;
  }, {});

  const { error, value } = Joi.compile(validSchema)
    .prefs({ abortEarly: false, errors: { label: 'key', wrap: { label: false } } })
    .validate(object);

  if (error) {
    const errorMessage = error.details
      .map((detail) => detail.message)
      .join(', ');

    // 使用明确的数字状态码，避免可能的undefined问题
    return next(new ApiError(400, errorMessage));
  }

  // Replace request data with validated data
  Object.assign(req, value);
  return next();
};

module.exports = validate;