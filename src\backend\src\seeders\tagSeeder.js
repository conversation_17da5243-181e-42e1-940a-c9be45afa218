const { v4: uuidv4 } = require('uuid');
const { Tag } = require('../models/tag.model');

async function seedTags() {
  try {
    // Check if tags already exist
    const tagCount = await Tag.count();
    if (tagCount > 0) {
      console.log('Tags already exist, skipping tag seeding');
      return;
    }

    // Create default tags
    const tags = [
      {
        id: uuidv4(),
        name: '重要',
        description: '重要文档或项目',
        color: '#FF5252' // Red
      },
      {
        id: uuidv4(),
        name: '紧急',
        description: '需要紧急处理',
        color: '#FF9800' // Orange
      },
      {
        id: uuidv4(),
        name: '进行中',
        description: '正在进行中的工作',
        color: '#2196F3' // Blue
      },
      {
        id: uuidv4(),
        name: '已完成',
        description: '已完成的工作',
        color: '#4CAF50' // Green
      },
      {
        id: uuidv4(),
        name: '待审核',
        description: '等待审核的内容',
        color: '#9C27B0' // Purple
      },
      {
        id: uuidv4(),
        name: '已审核',
        description: '已审核通过的内容',
        color: '#009688' // Teal
      },
      {
        id: uuidv4(),
        name: '技术',
        description: '技术相关内容',
        color: '#607D8B' // Blue Grey
      },
      {
        id: uuidv4(),
        name: '管理',
        description: '管理相关内容',
        color: '#795548' // Brown
      },
      {
        id: uuidv4(),
        name: '财务',
        description: '财务相关内容',
        color: '#8BC34A' // Light Green
      },
      {
        id: uuidv4(),
        name: '客户',
        description: '客户相关内容',
        color: '#00BCD4' // Cyan
      }
    ];

    // Bulk create tags
    await Tag.bulkCreate(tags);
    console.log('Successfully seeded tags');
  } catch (error) {
    console.error('Error seeding tags:', error);
    throw error;
  }
}

module.exports = seedTags;
