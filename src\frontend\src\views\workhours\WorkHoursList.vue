<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">工时管理</h1>
        <p class="page-subtitle">管理和跟踪员工工时信息</p>
      </div>
      <div class="flex space-x-2">
        <button
          @click="handleStatistics"
          class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zm6-4a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zm6-3a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
          </svg>
          记工统计
        </button>
        <button
          @click="goToWorkHoursRecording"
          class="btn btn-primary flex items-center shadow-md hover:shadow-lg transition-all duration-200"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          记工
        </button>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="mb-6">
      <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md shadow-sm">
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span class="font-medium">成功：</span>
        </div>
        <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6">
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm">
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="font-medium">错误：</span>
        </div>
        <p class="mt-2 whitespace-pre-line">{{ error }}</p>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-card">
      <div class="flex flex-wrap gap-4">
        <div class="flex-1 min-w-[200px]">
          <label for="search" class="form-label font-medium">查询</label>
          <div class="relative">
            <input
              id="search"
              v-model="filters.search"
              type="text"
              placeholder="查询项目名称或姓名"
              class="form-input pl-10 py-2 w-full"
            >
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>

  
        <div class="w-48">
          <label for="employmentType" class="form-label font-medium">用工类型</label>
          <select id="employmentType" v-model="filters.employmentType" class="form-input">
            <option value="">全部用工类型</option>
            <option value="合同工">合同工</option>
            <option value="临时工">临时工</option>
          </select>
        </div>
        <div class="w-48">
          <label for="month" class="form-label font-medium">月份</label>
          <input
            id="month"
            type="month"
            v-model="filters.month"
            class="form-input"
          >
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button 
          @click="resetFilters" 
          class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200"
          :disabled="loading"
        >
          重置
        </button>
        <button 
          @click="fetchWorkHours" 
          class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200"
          :disabled="loading"
        >
          <span v-if="loading" class="inline-block animate-spin mr-1">↻</span>
          {{ loading ? '加载中...' : '查询' }}
        </button>
      </div>
    </div>

    <!-- 工时记录表格 -->
    <div class="card overflow-hidden shadow-md">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工姓名</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用工类型</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工时</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日工资</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="record in currentPageRecords" :key="record.id" class="hover:bg-gray-50 transition-colors duration-150">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ record.projectName }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ record.employeeName }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="badge" :class="{
                  'badge-blue': record.employmentType === '合同工',
                  'badge-green': record.employmentType === '临时工'
                }">
                  {{ record.employmentType }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ record.date }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ record.workHours }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">¥{{ record.dailyWage }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ record.remarks }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <button
                  @click="viewEmployeeCalendar(record)"
                  class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                >
                  <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                  </svg>
                  <span class="font-medium">编辑</span>
                </button>
                <button
                  @click="handleDelete(record)"
                  class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                >
                  <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                  </svg>
                  <span class="font-medium">删除</span>
                </button>
              </td>
            </tr>
            <tr v-if="currentPageRecords.length === 0">
              <td colspan="8" class="px-6 py-10 text-center text-gray-500">
                <div class="empty-state">
                  <svg class="empty-state-icon mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <h3 class="empty-state-title">暂无工时记录</h3>
                  <p class="empty-state-description">没有找到符合条件的工时记录，请尝试调整筛选条件或添加新记录。</p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="px-6 py-4 bg-white border-t">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-700">
            共 {{ pagination.total }} 条记录
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="handlePageChange(pagination.currentPage - 1)"
              :disabled="pagination.currentPage === 1"
              class="px-3 py-1 border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <span class="px-3 py-1">
              第 {{ pagination.currentPage }} 页 / 共 {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
            </span>
            <button
              @click="handlePageChange(pagination.currentPage + 1)"
              :disabled="pagination.currentPage >= Math.ceil(pagination.total / pagination.pageSize)"
              class="px-3 py-1 border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 记工表单弹窗 -->
    <div v-if="showWorkHourForm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
      <div class="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">{{ editingRecord ? '编辑记工' : '新增记工' }}</h3>
          <button type="button" @click="showWorkHourForm = false" class="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
        <form @submit.prevent="submitWorkHours">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 左侧列 -->
            <div class="space-y-4">
              <!-- 员工信息 -->
              <div class="bg-gray-50 p-3 rounded-lg">
                <h4 class="font-medium text-gray-700 mb-2">员工信息</h4>
                <div class="space-y-3">
                  <div class="grid grid-cols-2 gap-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">员工姓名</label>
                      <select v-model="workHourForm.employeeId" class="w-full px-3 py-2 border rounded-lg text-sm" required @change="handleEmployeeChange">
                        <option value="" disabled>请选择员工</option>
                        <option v-for="employee in employees" :key="employee.id" :value="employee.id">
                          {{ employee.name }}
                        </option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">员工工号</label>
                      <input type="text" v-model="workHourForm.employeeId" class="w-full px-3 py-2 border rounded-lg text-sm" required>
                    </div>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">所属部门</label>
                    <select v-model="workHourForm.department" class="w-full px-3 py-2 border rounded-lg text-sm" required>
                      <option value="">请选择部门</option>
                      <option v-for="dept in departmentOptions" :key="dept.value" :value="dept.value">
                        {{ dept.label }}
                      </option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- 基本信息 -->
              <div class="bg-gray-50 p-3 rounded-lg">
                <h4 class="font-medium text-gray-700 mb-2">基本信息</h4>
                <div class="space-y-3">
                  <div class="grid grid-cols-2 gap-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">工作日期</label>
                      <input type="date" v-model="workHourForm.date" class="w-full px-3 py-2 border rounded-lg text-sm" required max="3000-12-31">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">总工时</label>
                      <input type="number" v-model="workHourForm.workHours" class="w-full px-3 py-2 border rounded-lg text-sm" required min="0" step="0.5">
                    </div>
                  </div>
                  <div class="grid grid-cols-2 gap-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">开始时间</label>
                      <input type="time" v-model="workHourForm.startTime" class="w-full px-3 py-2 border rounded-lg text-sm" required>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">结束时间</label>
                      <input type="time" v-model="workHourForm.endTime" class="w-full px-3 py-2 border rounded-lg text-sm" required>
                    </div>
                  </div>
                  <div class="grid grid-cols-2 gap-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">工作类型</label>
                      <select v-model="workHourForm.employmentType" class="w-full px-3 py-2 border rounded-lg text-sm" required>
                        <option value="合同工">合同工</option>
                        <option value="临时工">临时工</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">工作地点</label>
                      <select v-model="workHourForm.workLocation" class="w-full px-3 py-2 border rounded-lg text-sm" required>
                        <option value="办公室">办公室</option>
                        <option value="外勤">外勤</option>
                        <option value="远程">远程</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 项目信息 -->
              <div class="bg-gray-50 p-3 rounded-lg">
                <h4 class="font-medium text-gray-700 mb-2">项目信息</h4>
                <div class="space-y-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
                    <select v-model="workHourForm.projectName" class="w-full px-3 py-2 border rounded-lg text-sm" required>
                      <option value="项目1">项目1</option>
                      <option value="项目2">项目2</option>
                      <option value="项目3">项目3</option>
                      <option value="项目4">项目4</option>
                      <option value="项目5">项目5</option>
                    </select>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">任务描述</label>
                    <textarea v-model="workHourForm.remarks" class="w-full px-3 py-2 border rounded-lg text-sm" rows="2" required></textarea>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧列 -->
            <div class="space-y-4">
              <!-- 项目信息(续) -->
              <div class="bg-gray-50 p-3 rounded-lg">
                <h4 class="font-medium text-gray-700 mb-2">项目详情</h4>
                <div class="space-y-3">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">日工资</label>
                    <input type="number" v-model="workHourForm.dailyWage" class="w-full px-3 py-2 border rounded-lg text-sm" required min="0">
                  </div>
                </div>
              </div>

              <!-- 薪酬信息 -->
              <div class="bg-gray-50 p-3 rounded-lg">
                <h4 class="font-medium text-gray-700 mb-2">薪酬信息</h4>
                <div class="space-y-3">
                  <div class="grid grid-cols-2 gap-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">时薪 (¥)</label>
                      <input type="number" v-model="workHourForm.hourlyRate" class="w-full px-3 py-2 border rounded-lg text-sm" required min="0">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">应付金额 (¥)</label>
                      <input type="number" v-model="workHourForm.payableAmount" class="w-full px-3 py-2 border rounded-lg text-sm" required min="0">
                    </div>
                  </div>
                  <div class="flex gap-4">
                    <div class="flex items-center">
                      <input type="checkbox" id="overtime" v-model="workHourForm.overtime" class="h-4 w-4 text-blue-600 rounded">
                      <label for="overtime" class="ml-2 text-sm text-gray-700">加班</label>
                    </div>
                    <div class="flex items-center">
                      <input type="checkbox" id="absence" v-model="workHourForm.absence" class="h-4 w-4 text-blue-600 rounded">
                      <label for="absence" class="ml-2 text-sm text-gray-700">缺勤</label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 审批信息 -->
              <div class="bg-gray-50 p-3 rounded-lg">
                <h4 class="font-medium text-gray-700 mb-2">审批信息</h4>
                <div class="space-y-3">
                  <div class="flex justify-between">
                    <div class="flex items-center">
                      <input type="checkbox" id="employeeSign" v-model="workHourForm.employeeSign" class="h-4 w-4 text-blue-600 rounded">
                      <label for="employeeSign" class="ml-2 text-sm text-gray-700">员工确认</label>
                    </div>
                    <div class="flex items-center">
                      <input type="checkbox" id="supervisorSign" v-model="workHourForm.supervisorSign" class="h-4 w-4 text-blue-600 rounded">
                      <label for="supervisorSign" class="ml-2 text-sm text-gray-700">主管确认</label>
                    </div>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">绩效等级</label>
                    <select v-model="workHourForm.performanceLevel" class="w-full px-3 py-2 border rounded-lg text-sm">
                      <option value="">-- 请选择 --</option>
                      <option value="A">A</option>
                      <option value="B">B</option>
                      <option value="C">C</option>
                    </select>
                  </div>
                </div>
              </div>

              <!-- 备注 -->
              <div class="bg-gray-50 p-3 rounded-lg">
                <label class="block text-sm font-medium text-gray-700 mb-1">备注</label>
                <textarea v-model="workHourForm.notes" class="w-full px-3 py-2 border rounded-lg text-sm" rows="3"></textarea>
              </div>
            </div>
          </div>
          <div class="mt-6 flex justify-end space-x-3">
            <button
              type="button"
              @click="showWorkHourForm = false"
              class="px-4 py-2 border rounded-lg hover:bg-gray-50"
            >
              取消
            </button>
            <button
              type="submit"
              class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              提交
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { workhoursService } from '@/services/api.service';
import { useDepartmentStore } from '@/stores/department';
import apiService from '@/services/apiService';
import { useToast } from 'vue-toastification';

// 状态变量
const showWorkHourForm = ref(false);
const editingRecord = ref(null);
const currentPage = ref(1);
const pageSize = ref(10);
const totalRecords = ref(0);
const workHourRecords = ref([]);
const successMessage = ref(null);
const error = ref(null);
const router = useRouter();
const toast = useToast();

// 部门存储
const departmentStore = useDepartmentStore();
const departmentOptions = computed(() => departmentStore.getDepartmentOptions);

// 显示成功消息并自动隐藏
const showSuccessMessage = (message, duration = 5000) => {
  successMessage.value = message;
  setTimeout(() => {
    successMessage.value = null;
  }, duration);
};

// 筛选条件
const filters = ref({
  search: '',
  department: '',
  status: '',
  month: new Date().toISOString().slice(0, 7),
  employmentType: '',
  employeeId: ''
});

// 员工列表
const employees = ref([]);

// 表单数据
const workHourForm = ref({
  // 员工信息
  employeeName: '',
  employeeId: '',
  department: '',
  // 基本信息
  date: new Date().toISOString().split('T')[0],
  startTime: '09:00',
  endTime: '18:00',
  workHours: 8,
  employmentType: '合同工',
  workLocation: '办公室',
  // 项目信息
  projectName: '',
  remarks: '',
  // 薪酬信息
  hourlyRate: 100,
  payableAmount: 800,
  overtime: false,
  absence: false,
  // 审批信息
  employeeSign: false,
  supervisorSign: false,
  performanceLevel: '',
  // 其他信息
  dailyWage: 800
});

// 分页设置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 计算属性
const startIndex = computed(() => (currentPage.value - 1) * pageSize.value);
const endIndex = computed(() => startIndex.value + pageSize.value);
const totalPages = computed(() => Math.ceil(totalRecords.value / pageSize.value));

// 过滤记录
const filteredRecords = computed(() => {
  return workHourRecords.value.filter(record => {
    const matchesSearch = !filters.value.search ||
      record.projectName.toLowerCase().includes(filters.value.search.toLowerCase()) ||
      record.employeeName.toLowerCase().includes(filters.value.search.toLowerCase()) ||
      record.employmentType.toLowerCase().includes(filters.value.search.toLowerCase())

    const matchesMonth = !filters.value.month ||
      record.date.startsWith(filters.value.month)

    const matchesEmploymentType = !filters.value.employmentType ||
      record.employmentType === filters.value.employmentType

    return matchesSearch && matchesMonth && matchesEmploymentType
  })
})

// 计算当前页的记录
const currentPageRecords = computed(() => {
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  return filteredRecords.value.slice(start, end);
});

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 获取工作类型样式和文本
const getTypeClass = (type) => {
  const classes = {
    normal: 'bg-blue-100 text-blue-800',
    overtime: 'bg-yellow-100 text-yellow-800'
  };
  return 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' + (classes[type] || '');
};

const getTypeText = (type) => {
  const texts = {
    normal: '正常工时',
    overtime: '加班工时'
  };
  return texts[type] || type;
};

// 获取状态样式和文本
const getStatusClass = (status) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  };
  return 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ' + (classes[status] || '');
};

const getStatusText = (status) => {
  const texts = {
    pending: '待审核',
    approved: '已审核',
    rejected: '已驳回'
  };
  return texts[status] || status;
};

const getTaskStatusText = (status) => {
  const texts = {
    in_progress: '进行中',
    completed: '已完成'
  };
  return texts[status] || status;
};

// 处理员工选择
const handleEmployeeChange = () => {
  const selectedEmployee = employees.value.find(emp => emp.id === workHourForm.value.employeeId);
  if (selectedEmployee) {
    workHourForm.value.employeeName = selectedEmployee.name;
    workHourForm.value.department = selectedEmployee.department;
  }
};

// 获取员工列表
const fetchEmployees = async () => {
  try {
    const response = await apiService.getAllEmployees({ limit: 100 });
    employees.value = response.results || [];
  } catch (err) {
    console.error('获取员工列表失败:', err);
    error.value = '获取员工列表失败: ' + (err.response?.data?.message || err.message);
  }
};

// 处理编辑
const handleEdit = (record) => {
  editingRecord.value = record;

  // 确保正确映射字段
  workHourForm.value = {
    // 员工信息
    employeeName: record.employeeName || '',
    employeeId: record.employeeId || '',
    department: record.department || '研发部',
    // 基本信息
    date: record.date || new Date().toISOString().split('T')[0],
    startTime: record.startTime || '09:00',
    endTime: record.endTime || '18:00',
    workHours: record.workHours || 8,
    employmentType: record.employmentType || '合同工',
    workLocation: record.workLocation || '办公室',
    // 项目信息
    projectName: record.projectName || '',
    remarks: record.remarks || '',
    // 薪酬信息
    hourlyRate: record.hourlyRate || 100,
    payableAmount: record.payableAmount || 800,
    overtime: record.overtime || false,
    absence: record.absence || false,
    // 审批信息
    employeeSign: record.employeeSign || false,
    supervisorSign: record.supervisorSign || false,
    performanceLevel: record.performanceLevel || '',
    // 其他信息
    dailyWage: record.dailyWage || 800
  };

  showWorkHourForm.value = true;
};

// 处理删除
const handleDelete = async (record) => {
  const employeeName = record.employeeName || '此员工';
  const projectName = record.projectName || '此项目';
  const date = record.date || '记录日期';

  if (!confirm(`尊敬的用户，您确定要删除${employeeName}在${date}关于"${projectName}"的工时记录吗？\n\n请您注意以下影响：\n· 删除后将无法恢复此记录\n· 相关的工资计算数据将被删除\n· 项目工时统计将受到影响\n· 员工工时报表将不再包含此记录\n\n如果您确定要删除，请点击"确定"按钮继续操作。`)) return;

  try {
    await workhoursService.deleteWorkHour(record.id);
    
    // 显示Toast提示信息
    toast.success(`工时记录删除成功`);
    
    // 同时保留原有的详细成功信息
    showSuccessMessage(`工时记录已成功删除。\n\n员工：${record.employeeName}\n项目：${record.projectName}\n日期：${record.date}`);
    
    await fetchWorkHours();
  } catch (err) {
    console.error('删除失败:', err);
    
    // 显示Toast错误提示
    toast.error(err.response?.data?.message || '删除工时记录失败');
    
    // 同时保留原有的详细错误信息
    error.value = '很抱歉，删除工时记录时遇到了问题。\n\n可能的原因：\n· 网络连接暂时中断\n· 系统临时故障\n· 该记录可能已被用于工资结算\n· 您可能没有足够的权限执行此操作\n\n建议您：\n1. 检查网络连接\n2. 确认该记录未被锁定\n3. 稍后再次尝试\n\n如果问题持续存在，请联系系统管理员获取帮助。';
  }
};

// 提交表单
const submitWorkHours = async () => {
  try {
    if (editingRecord.value) {
      await workhoursService.updateWorkHour(editingRecord.value.id, workHourForm.value);
      
      // 显示Toast提示
      toast.success(`工时记录更新成功`);
      
      // 保留原有的详细成功信息
      showSuccessMessage(`工时记录已成功更新。\n\n员工：${workHourForm.value.employeeName}\n项目：${workHourForm.value.projectName}\n日期：${workHourForm.value.date}`);
    } else {
      await workhoursService.createWorkHour(workHourForm.value);
      
      // 显示Toast提示
      toast.success(`工时记录创建成功`);
      
      // 保留原有的详细成功信息
      showSuccessMessage(`工时记录已成功创建。\n\n员工：${workHourForm.value.employeeName}\n项目：${workHourForm.value.projectName}\n日期：${workHourForm.value.date}`);
    }
    showWorkHourForm.value = false;
    editingRecord.value = null;
    await fetchWorkHours();
  } catch (err) {
    console.error('提交失败:', err);
    
    // 显示Toast错误提示
    toast.error(err.response?.data?.message || '提交工时记录失败');
    
    // 保留原有的详细错误信息
    error.value = '很抱歉，提交工时记录时遇到了问题。请检查您的输入并稍后重试。';
  }
};

// 获取工时记录
const fetchWorkHours = async () => {
  try {
    error.value = null; // 清除之前的错误
    const params = {
      page: pagination.value.currentPage,
      limit: pagination.value.pageSize,
      search: filters.value.search,
      department: filters.value.department,
      status: filters.value.status,
      month: filters.value.month,
      employmentType: filters.value.employmentType,
      employeeId: filters.value.employeeId
    };

    const response = await workhoursService.getWorkHours(params);

    // 检查响应格式并适当处理
    if (response && response.data && response.data.results) {
      // 如果响应包含 data.results 结构
      workHourRecords.value = response.data.results;
      pagination.value.total = response.data.totalResults || 0;
      totalRecords.value = response.data.totalResults || 0;
    } else if (response && Array.isArray(response)) {
      // 如果响应直接是数组（processResponse 处理后的结果）
      workHourRecords.value = response;
      pagination.value.total = response.length;
      totalRecords.value = response.length;
    } else if (response && response.results) {
      // 如果响应有 results 但没有嵌套在 data 中
      workHourRecords.value = response.results;
      pagination.value.total = response.totalResults || 0;
      totalRecords.value = response.totalResults || 0;
    } else {
      // 如果都不是，使用空数组
      workHourRecords.value = [];
      pagination.value.total = 0;
      totalRecords.value = 0;
    }
  } catch (err) {
    console.error('获取记工记录失败:', err);
    error.value = '很抱歉，获取工时记录时遇到了问题。请检查您的网络连接并稍后重试。';
    workHourRecords.value = [];
    pagination.value.total = 0;
    totalRecords.value = 0;
  }
};

// 处理统计按钮点击
const handleStatistics = () => {
  router.push('/workhours/statistics');
};

// 跳转到工时记录页面
const goToWorkHoursRecording = () => {
  router.push('/workhours/recording');
};

// 处理页码变化
const handlePageChange = (page) => {
  pagination.value.currentPage = page;
};

// 查看员工月历
const viewEmployeeCalendar = (record) => {
  router.push({
    path: '/workhours/calendar',
    query: {
      employeeId: record.employeeId,
      employeeName: record.employeeName,
      month: filters.value.month || new Date().toISOString().slice(0, 7)
    }
  });
};

// 重置过滤器
const resetFilters = () => {
  filters.value.search = '';
  filters.value.department = '';
  filters.value.status = '';
  filters.value.month = new Date().toISOString().slice(0, 7);
  filters.value.employmentType = '';
  filters.value.employeeId = '';
  fetchWorkHours();
};

// 初始化
onMounted(() => {
  departmentStore.fetchDepartments();
  fetchEmployees();
  fetchWorkHours();
});
</script>