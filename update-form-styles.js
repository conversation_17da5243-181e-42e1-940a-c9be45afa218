const fs = require('fs');
const path = require('path');

// 读取todolist.md文件
const todolistPath = path.join(__dirname, 'todo.md');
const todolist = fs.readFileSync(todolistPath, 'utf8');

// 解析需要更新的文件列表
const filesToUpdate = todolist
  .split('\n')
  .filter(line => line.trim().startsWith('[ ]'))
  .map(line => line.replace('[ ] ', '').trim())
  .filter(file => file.length > 0);

console.log(`找到 ${filesToUpdate.length} 个需要更新的文件`);

// 表单元素样式替换规则
const formStyleReplacements = [
  // 输入框
  {
    from: /class="form-input p-2\.5 transition-colors duration-200"/g,
    to: 'class="form-input"'
  },
  // 文本区域
  {
    from: /class="form-input p-2\.5 transition-colors duration-200 w-full"/g,
    to: 'class="form-input"'
  },
  // 下拉菜单
  {
    from: /class="form-input p-2\.5 transition-colors duration-200"/g,
    to: 'class="form-input"'
  }
];

// 更新每个文件
filesToUpdate.forEach(filePath => {
  const fullPath = path.join(__dirname, filePath);
  
  // 检查文件是否存在
  if (!fs.existsSync(fullPath)) {
    console.error(`文件不存在: ${fullPath}`);
    return;
  }
  
  try {
    // 读取文件内容
    let content = fs.readFileSync(fullPath, 'utf8');
    let originalContent = content;
    
    // 应用所有替换规则
    formStyleReplacements.forEach(({ from, to }) => {
      content = content.replace(from, to);
    });
    
    // 如果内容有变化，则写入文件
    if (content !== originalContent) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`已更新: ${filePath}`);
    } else {
      console.log(`无需更新: ${filePath}`);
    }
  } catch (error) {
    console.error(`处理文件时出错 ${filePath}:`, error);
  }
});

console.log('表单样式统一更新完成！');
