<template>
  <div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-sm p-6">
      <h1 class="text-2xl font-bold mb-6">测试消息生成器</h1>

      <div v-if="status" :class="['mb-6 p-4 rounded-lg',
        status.type === 'success' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700']">
        {{ status.message }}
      </div>

      <form @submit.prevent="generateMessage" class="space-y-6">
        <div>
          <label for="title" class="block text-sm font-medium text-gray-700 mb-1">消息标题</label>
          <input
            type="text"
            id="title"
            v-model="form.title"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="输入消息标题"
            required
          />
        </div>

        <div>
          <label for="content" class="block text-sm font-medium text-gray-700 mb-1">消息内容</label>
          <textarea
            id="content"
            v-model="form.content"
            rows="4"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="输入消息内容"
            required
          ></textarea>
        </div>

        <div>
          <label for="type" class="block text-sm font-medium text-gray-700 mb-1">消息类型</label>
          <select
            id="type"
            v-model="form.type"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            required
          >
            <option value="system">系统消息</option>
            <option value="task">任务消息</option>
            <option value="notification">通知</option>
          </select>
        </div>

        <div>
          <label for="count" class="block text-sm font-medium text-gray-700 mb-1">生成数量</label>
          <input
            type="number"
            id="count"
            v-model.number="form.count"
            min="1"
            max="50"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            required
          />
          <p class="mt-1 text-sm text-gray-500">一次最多生成50条消息</p>
        </div>

        <div class="flex justify-between">
          <button
            type="submit"
            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            :disabled="loading"
          >
            <span v-if="loading">生成中...</span>
            <span v-else>生成测试消息</span>
          </button>

          <button
            type="button"
            @click="generateRandomMessages"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            :disabled="loading"
          >
            <span v-if="loading">生成中...</span>
            <span v-else>生成随机消息</span>
          </button>
        </div>
      </form>
    </div>

    <div class="mt-8 bg-white rounded-lg shadow-sm p-6">
      <h2 class="text-xl font-bold mb-4">最近生成的消息</h2>

      <div v-if="userMessageStore.loading" class="py-4 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-t-transparent" :style="{ borderColor: 'var(--primary-color) transparent var(--primary-color) var(--primary-color)' }"></div>
        <p class="mt-2 text-sm text-gray-500">加载中...</p>
      </div>

      <div v-else-if="userMessageStore.messages.length === 0" class="py-4 text-center">
        <p class="text-sm text-gray-500">暂无消息</p>
      </div>

      <div v-else class="space-y-4">
        <div
          v-for="message in userMessageStore.sortedMessages.slice(0, 5)"
          :key="message.id"
          class="p-4 border rounded-md"
          :class="{ 'bg-blue-50 border-blue-200': !message.isRead, 'bg-gray-50 border-gray-200': message.isRead }"
        >
          <div class="flex justify-between items-start">
            <h3 class="font-medium">{{ message.title }}</h3>
            <span class="text-xs text-gray-500">{{ formatTime(message.createdAt) }}</span>
          </div>
          <p class="mt-2 text-sm text-gray-600">{{ message.content }}</p>
          <div class="mt-2 flex justify-between items-center">
            <span
              class="px-2 py-1 text-xs rounded-full"
              :class="{
                'bg-blue-100 text-blue-800': message.type === 'system',
                'bg-green-100 text-green-800': message.type === 'task',
                'bg-yellow-100 text-yellow-800': message.type === 'notification'
              }"
            >
              {{ getMessageTypeText(message.type) }}
            </span>

            <div class="space-x-2">
              <button
                v-if="!message.isRead"
                @click="markAsRead(message.id)"
                class="text-xs text-indigo-600 hover:text-indigo-800"
              >
                标为已读
              </button>
              <button
                @click="deleteMessage(message.id)"
                class="text-xs text-red-600 hover:text-red-800"
              >
                删除
              </button>
            </div>
          </div>
        </div>

        <div class="text-center mt-4">
          <router-link
            to="/messages"
            class="text-indigo-600 hover:text-indigo-800 text-sm"
          >
            查看全部消息
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useUserMessageStore } from '../stores/userMessage';
import axios from 'axios';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const userMessageStore = useUserMessageStore();
const loading = ref(false);
const status = ref(null);

// 表单数据
const form = reactive({
  title: '',
  content: '',
  type: 'system',
  count: 1
});

// 格式化时间
const formatTime = (dateString) => {
  try {
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true, locale: zhCN });
  } catch (error) {
    return dateString;
  }
};

// 获取消息类型文本
const getMessageTypeText = (type) => {
  switch (type) {
    case 'system':
      return '系统消息';
    case 'task':
      return '任务消息';
    case 'notification':
      return '通知';
    default:
      return '其他';
  }
};

// 生成测试消息
const generateMessage = async () => {
  try {
    loading.value = true;
    status.value = null;

    // 验证表单
    if (!form.title || !form.content || !form.type || form.count < 1) {
      status.value = {
        type: 'error',
        message: '请填写完整的表单信息'
      };
      return;
    }

    // 限制生成数量
    const count = Math.min(form.count, 50);

    console.log(`准备生成 ${count} 条测试消息`);

    // 生成消息
    for (let i = 0; i < count; i++) {
      const messageData = {
        title: form.title + (count > 1 ? ` (${i + 1})` : ''),
        content: form.content,
        type: form.type
      };

      console.log(`生成第 ${i + 1} 条消息:`, messageData);

      try {
        // 直接使用axios调用API，以便获取更详细的错误信息
        const response = await axios.post('/api/messages/test', messageData);
        console.log(`消息 ${i + 1} 创建成功:`, response.data);
      } catch (err) {
        console.error(`消息 ${i + 1} 创建失败:`, err);
        console.error('错误详情:', err.response?.data);
        throw err;
      }
    }

    // 重新加载消息列表
    await userMessageStore.fetchMessages();

    status.value = {
      type: 'success',
      message: `成功生成 ${count} 条测试消息`
    };

    // 重置表单
    if (count > 1) {
      form.title = '';
      form.content = '';
      form.count = 1;
    }
  } catch (error) {
    console.error('生成测试消息失败:', error);

    let errorMessage = '生成测试消息失败';

    if (error.response) {
      console.error('API响应状态码:', error.response.status);
      console.error('API响应数据:', error.response.data);

      if (error.response.data?.error?.message) {
        errorMessage += ': ' + error.response.data.error.message;
      } else if (error.response.data?.message) {
        errorMessage += ': ' + error.response.data.message;
      } else {
        errorMessage += ': ' + error.message;
      }
    } else {
      errorMessage += ': ' + error.message;
    }

    status.value = {
      type: 'error',
      message: errorMessage
    };
  } finally {
    loading.value = false;
  }
};

// 生成随机消息
const generateRandomMessages = async () => {
  try {
    loading.value = true;
    status.value = null;

    // 消息类型
    const messageTypes = ['system', 'task', 'notification'];

    // 系统消息标题模板
    const systemTitles = [
      '系统维护通知',
      '账户安全提醒',
      '系统更新公告',
      '功能升级通知',
      '操作指南更新'
    ];

    // 任务消息标题模板
    const taskTitles = [
      '新任务分配',
      '任务截止提醒',
      '任务状态更新',
      '任务评审邀请',
      '任务完成确认'
    ];

    // 通知消息标题模板
    const notificationTitles = [
      '会议提醒',
      '文档更新通知',
      '项目进度更新',
      '团队公告',
      '重要事项提醒'
    ];

    // 系统消息内容模板
    const systemContents = [
      '尊敬的用户，系统将于明天进行例行维护，维护期间部分功能可能暂时无法使用，预计维护时间为2小时，请提前做好工作安排。',
      '我们检测到您的账户有异常登录尝试，请及时检查账户安全并考虑修改密码。',
      '系统已更新至v2.5.3版本，新版本修复了已知问题并优化了用户体验，详情请查看更新日志。',
      '我们很高兴地通知您，系统新增了数据分析功能，现在您可以更高效地完成工作。',
      '操作指南已更新，新增了报表导出的使用说明，请前往帮助中心查看详情。'
    ];

    // 任务消息内容模板
    const taskContents = [
      '您有一个新的任务"季度报表整理"已分配给您，请在下周五前完成。',
      '您的任务"客户资料更新"即将到期，截止日期为明天，请及时处理。',
      '您的任务"产品设计评审"状态已更新为"已完成"，请查看详情。',
      '您被邀请参与任务"市场调研分析"的评审，请在本周内提交您的评审意见。',
      '您的任务"销售数据统计"已被标记为完成，感谢您的辛勤工作！'
    ];

    // 通知消息内容模板
    const notificationContents = [
      '您有一个会议"项目进度汇报"将于明天下午2点在会议室A举行，请准时参加。',
      '文档"产品规划书"已由张经理更新，请查看最新版本。',
      '项目"客户管理系统升级"的进度已更新，当前完成度为75%，请查看详情。',
      '团队公告：本周五下午将举行团队建设活动，请所有成员准时参加。',
      '重要事项提醒：季度考核即将开始，请及时提交您的工作总结。'
    ];

    // 限制生成数量
    const count = Math.min(form.count, 50);

    // 生成随机消息
    for (let i = 0; i < count; i++) {
      // 随机选择消息类型
      const type = messageTypes[Math.floor(Math.random() * messageTypes.length)];

      // 根据类型选择标题和内容
      let titles, contents;
      switch (type) {
        case 'system':
          titles = systemTitles;
          contents = systemContents;
          break;
        case 'task':
          titles = taskTitles;
          contents = taskContents;
          break;
        case 'notification':
          titles = notificationTitles;
          contents = notificationContents;
          break;
      }

      // 随机选择标题和内容
      const title = titles[Math.floor(Math.random() * titles.length)];
      const content = contents[Math.floor(Math.random() * contents.length)];

      // 创建消息
      await userMessageStore.createTestMessage({
        title,
        content,
        type
      });
    }

    // 重新加载消息列表
    await userMessageStore.fetchMessages();

    status.value = {
      type: 'success',
      message: `成功生成 ${count} 条随机测试消息`
    };
  } catch (error) {
    console.error('生成随机测试消息失败:', error);
    status.value = {
      type: 'error',
      message: '生成随机测试消息失败: ' + (error.response?.data?.message || error.message)
    };
  } finally {
    loading.value = false;
  }
};

// 标记消息为已读
const markAsRead = async (id) => {
  try {
    await userMessageStore.markAsRead(id);
  } catch (error) {
    console.error('标记消息为已读失败:', error);
  }
};

// 删除消息
const deleteMessage = async (id) => {
  try {
    await userMessageStore.deleteMessage(id);
  } catch (error) {
    console.error('删除消息失败:', error);
  }
};

// 初始化
onMounted(async () => {
  await userMessageStore.fetchMessages();
});
</script>
