<template>
  <div class="relative">
    <!-- 消息通知图标 -->
    <button
      ref="toggleButtonRef"
      @click="toggleMessageMenu"
      class="relative p-1 rounded-full text-gray-500 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      :style="{ '--tw-ring-color': 'var(--primary-focus)' }"
    >
      <span class="sr-only">查看消息通知</span>
      <!-- 铃铛图标 -->
      <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
      </svg>

      <!-- 未读消息数量标记 -->
      <span
        v-if="userMessageStore.unreadCount > 0"
        class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 rounded-full"
        :style="{ backgroundColor: 'var(--error-color)' }"
      >
        {{ formatUnreadCount(userMessageStore.unreadCount) }}
      </span>
    </button>

    <!-- 消息下拉菜单 -->
    <div
      v-if="messageMenuOpen"
      id="message-menu"
      class="origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 focus:outline-none z-10"
      role="menu"
      aria-orientation="vertical"
      aria-labelledby="message-menu"
      :style="{ backgroundColor: 'var(--bg-secondary)' }"
    >
      <!-- 消息标题 -->
      <div class="px-4 py-2 border-b flex justify-between items-center" :style="{ borderColor: 'var(--secondary-border)' }">
        <h3 class="text-sm font-medium" :style="{ color: 'var(--text-primary)' }">消息通知</h3>
        <button
          v-if="userMessageStore.unreadCount > 0"
          @click="markAllAsRead"
          class="text-xs hover:underline"
          :style="{ color: 'var(--primary-color)' }"
        >
          全部标为已读
        </button>
      </div>

      <!-- 消息列表 -->
      <div class="max-h-80 overflow-y-auto">
        <div v-if="userMessageStore.loading" class="py-4 text-center text-sm" :style="{ color: 'var(--text-secondary)' }">
          加载中...
        </div>
        <div v-else-if="userMessageStore.error" class="py-4 px-3 text-center text-sm" :style="{ color: 'var(--error-color)' }">
          <div class="mb-2">{{ userMessageStore.error }}</div>
          <button
            @click="retryLoadMessages"
            class="text-xs px-2 py-1 rounded"
            :style="{
              backgroundColor: 'var(--primary-color)',
              color: 'white',
              cursor: 'pointer'
            }"
          >
            重试
          </button>
        </div>
        <div v-else-if="userMessageStore.messages.length === 0" class="py-4 text-center text-sm" :style="{ color: 'var(--text-secondary)' }">
          暂无消息
        </div>
        <template v-else>
          <div
            v-for="message in userMessageStore.sortedMessages.slice(0, 5)"
            :key="message.id"
            class="px-4 py-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
            :class="{ 'bg-blue-50': !message.isRead }"
            @click="viewMessage(message)"
            :style="{ borderColor: 'var(--secondary-border)' }"
          >
            <div class="flex justify-between items-start">
              <h4 class="text-sm font-medium" :style="{ color: 'var(--text-primary)' }">
                {{ message.title }}
                <span v-if="!message.isRead" class="ml-2 inline-block w-2 h-2 rounded-full" :style="{ backgroundColor: 'var(--primary-color)' }"></span>
              </h4>
              <span class="text-xs" :style="{ color: 'var(--text-secondary)' }">{{ formatTime(message.createdAt) }}</span>
            </div>
            <p class="text-xs mt-1 line-clamp-2" :style="{ color: 'var(--text-secondary)' }">
              {{ message.content }}
            </p>
          </div>
        </template>
      </div>

      <!-- 查看全部消息 -->
      <div class="px-4 py-2 border-t text-center" :style="{ borderColor: 'var(--secondary-border)' }">
        <router-link
          to="/messages"
          class="text-sm hover:underline"
          :style="{ color: 'var(--primary-color)' }"
          @click="messageMenuOpen = false"
        >
          查看全部消息
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { useUserMessageStore } from '../stores/userMessage';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const userMessageStore = useUserMessageStore();
const messageMenuOpen = ref(false);
const toggleButtonRef = ref(null); // Added for checking click target

// 格式化未读消息数量
const formatUnreadCount = (count) => {
  return count > 99 ? '99+' : count;
};

// 格式化时间
const formatTime = (dateString) => {
  try {
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true, locale: zhCN });
  } catch (error) {
    return dateString;
  }
};

// 切换消息菜单
const toggleMessageMenu = async () => {
  messageMenuOpen.value = !messageMenuOpen.value;

  // 如果打开菜单，加载消息列表
  if (messageMenuOpen.value) {
    try {
      await userMessageStore.fetchMessages({ limit: 10 });
    } catch (error) {
      console.error('加载消息失败:', error);
    }
  }
};

// 查看消息
const viewMessage = async (message) => {
  // 如果消息未读，标记为已读
  if (!message.isRead) {
    try {
      await userMessageStore.markAsRead(message.id);
    } catch (error) {
      console.error('标记消息为已读失败:', error);
    }
  }

  // 关闭菜单
  messageMenuOpen.value = false;

  // 跳转到消息详情页
  // router.push(`/messages/${message.id}`);
};

// 标记所有消息为已读
const markAllAsRead = async () => {
  try {
    await userMessageStore.markAllAsRead();
  } catch (error) {
    console.error('标记所有消息为已读失败:', error);
  }
};

// 重试加载消息
const retryLoadMessages = async () => {
  try {
    await userMessageStore.fetchMessages({ limit: 10 });
  } catch (error) {
    console.error('重试加载消息失败:', error);
  }
};

// 点击外部关闭菜单
const handleClickOutside = (event) => {
  const target = event.target;
  const messageMenuElement = document.getElementById('message-menu');

  // If the click is on the toggle button itself or its children, let toggleMessageMenu handle it
  if (toggleButtonRef.value && toggleButtonRef.value.contains(target)) {
    return;
  }

  // If the menu is open and the click is outside the menu (and not on the button)
  if (messageMenuOpen.value && messageMenuElement && !messageMenuElement.contains(target)) {
    messageMenuOpen.value = false;
  }
};

// 初始化
onMounted(async () => {
  try {
    // 初始化消息存储
    await userMessageStore.init();

    // 如果初始化后消息列表为空，尝试再次加载
    if (userMessageStore.messages.length === 0) {
      await userMessageStore.fetchMessages({ limit: 10 });
    }
  } catch (error) {
    console.error('初始化消息通知失败:', error);
  }

  // 添加点击外部关闭菜单的事件监听
  document.addEventListener('click', handleClickOutside);
});

// 清理
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
