const Joi = require('joi');
const { objectId } = require('./custom.validation');

const createProduct = {
  body: Joi.object().keys({
    name: Joi.string().required().messages({
      'string.empty': '产品名称不能为空',
      'any.required': '产品名称是必填项'
    }),
    code: Joi.string().required().messages({
      'string.empty': '产品编码不能为空',
      'any.required': '产品编码是必填项'
    }),
    category: Joi.string().required().messages({
      'string.empty': '产品类别不能为空',
      'any.required': '产品类别是必填项'
    }),
    description: Joi.string().allow('', null),
    price: Joi.number().min(0).required().messages({
      'number.base': '价格必须是数字',
      'number.min': '价格不能小于{#limit}',
      'any.required': '价格是必填项'
    }),
    unit: Joi.string().required().messages({
      'string.empty': '单位不能为空',
      'any.required': '单位是必填项'
    }),
    stock: Joi.number().integer().min(0).default(0).messages({
      'number.base': '库存必须是数字',
      'number.integer': '库存必须是整数',
      'number.min': '库存不能小于{#limit}'
    }),
    minStock: Joi.number().integer().min(0).allow(null).messages({
      'number.base': '最小库存必须是数字',
      'number.integer': '最小库存必须是整数',
      'number.min': '最小库存不能小于{#limit}'
    }),
    status: Joi.string().valid('active', 'inactive', 'discontinued').default('active').messages({
      'string.empty': '状态不能为空',
      'any.only': '状态必须是活跃(active)、非活跃(inactive)或停产(discontinued)'
    }),
    tags: Joi.array().items(Joi.string()),

    images: Joi.array().items(Joi.string())
  })
};

const getProduct = {
  params: Joi.object().keys({
    productId: Joi.string().custom(objectId).required().messages({
      'string.empty': '产品ID不能为空',
      'any.required': '产品ID是必填项'
    })
  })
};

const updateProduct = {
  params: Joi.object().keys({
    productId: Joi.string().custom(objectId).required().messages({
      'string.empty': '产品ID不能为空',
      'any.required': '产品ID是必填项'
    })
  }),
  body: Joi.object().keys({
    name: Joi.string().messages({
      'string.empty': '产品名称不能为空'
    }),
    code: Joi.string().messages({
      'string.empty': '产品编码不能为空'
    }),
    category: Joi.string().messages({
      'string.empty': '产品类别不能为空'
    }),
    description: Joi.string().allow('', null),
    price: Joi.number().min(0).messages({
      'number.base': '价格必须是数字',
      'number.min': '价格不能小于{#limit}'
    }),
    unit: Joi.string().messages({
      'string.empty': '单位不能为空'
    }),
    stock: Joi.number().integer().min(0).messages({
      'number.base': '库存必须是数字',
      'number.integer': '库存必须是整数',
      'number.min': '库存不能小于{#limit}'
    }),
    minStock: Joi.number().integer().min(0).allow(null).messages({
      'number.base': '最小库存必须是数字',
      'number.integer': '最小库存必须是整数',
      'number.min': '最小库存不能小于{#limit}'
    }),
    status: Joi.string().valid('active', 'inactive', 'discontinued').messages({
      'string.empty': '状态不能为空',
      'any.only': '状态必须是活跃(active)、非活跃(inactive)或停产(discontinued)'
    }),
    tags: Joi.array().items(Joi.string()),

    images: Joi.array().items(Joi.string())
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deleteProduct = {
  params: Joi.object().keys({
    productId: Joi.string().custom(objectId).required().messages({
      'string.empty': '产品ID不能为空',
      'any.required': '产品ID是必填项'
    })
  })
};

const getProducts = {
  query: Joi.object().keys({
    name: Joi.string().messages({
      'string.empty': '产品名称不能为空'
    }),
    code: Joi.string().messages({
      'string.empty': '产品编码不能为空'
    }),
    category: Joi.string().messages({
      'string.empty': '类别不能为空'
    }),
    status: Joi.string().valid('active', 'inactive', 'discontinued', '库存充足', '库存不足', '库存预警').messages({
      'string.empty': '状态不能为空',
      'any.only': '状态必须是活跃(active)、非活跃(inactive)、停产(discontinued)、库存充足、库存不足或库存预警'
    }),
    search: Joi.string().allow(null),
    minPrice: Joi.number().min(0).messages({
      'number.base': '最低价格必须是数字',
      'number.min': '最低价格不能小于{#limit}'
    }),
    maxPrice: Joi.number().min(0).messages({
      'number.base': '最高价格必须是数字',
      'number.min': '最高价格不能小于{#limit}'
    }),
    tags: Joi.alternatives().try(
      Joi.string().allow(null),
      Joi.array().items(Joi.string())
    ),
    sortBy: Joi.string().default('createdAt').messages({
      'string.empty': '排序字段不能为空'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    }),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于{#limit}'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    })
  })
};

/**
 * Validate data against schema
 * @param {Object} data - Data to validate
 * @param {string} schemaName - Name of the schema to validate against
 * @returns {Object} - Validation result
 */
const validate = (data, schemaName) => {
  const schema = module.exports[schemaName];
  if (!schema) {
    throw new Error(`Schema ${schemaName} not found`);
  }

  // For API validation, we use body schema
  const validationSchema = schema.body || schema;
  return validationSchema.validate(data, { abortEarly: false });
};

module.exports = {
  createProduct,
  getProduct,
  updateProduct,
  deleteProduct,
  getProducts,
  validate
};