import { test, expect } from '@playwright/test';

test.describe('Edge Cases and Error Handling Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 登录系统
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('表单验证测试 - 必填字段', async ({ page }) => {
    // 测试项目管理
    await page.goto('/projects');
    await page.click('button:has-text("新建项目")');
    await page.click('button[type="submit"]');
    
    // 验证错误提示
    await expect(page.locator('.error-message')).toBeVisible();
    await expect(page.locator('text=项目名称不能为空')).toBeVisible();
  });

  test('表单验证测试 - 特殊字符', async ({ page }) => {
    // 测试合同管理
    await page.goto('/contracts');
    await page.click('button:has-text("新建合同")');
    await page.fill('input[name="title"]', '!@#$%^&*()');
    await page.fill('input[name="amount"]', '-1000');
    await page.click('button[type="submit"]');
    
    // 验证错误提示
    await expect(page.locator('text=金额必须大于0')).toBeVisible();
  });

  test('数据分页测试', async ({ page }) => {
    // 测试员工列表分页
    await page.goto('/employees');
    
    // 验证分页控件存在
    await expect(page.locator('.pagination')).toBeVisible();
    
    // 点击下一页
    await page.click('button:has-text("下一页")');
    
    // 验证页面切换
    await expect(page.locator('.employee-list')).toBeVisible();
  });

  test('搜索功能测试', async ({ page }) => {
    // 测试库存搜索
    await page.goto('/inventory');
    
    // 输入搜索关键词
    await page.fill('input[type="search"]', '测试物品');
    await page.keyboard.press('Enter');
    
    // 验证搜索结果
    await expect(page.locator('.search-results')).toBeVisible();
  });

  test('权限控制测试', async ({ page }) => {
    // 测试访问管理员页面
    await page.goto('/admin');
    
    // 验证权限提示
    await expect(page.locator('text=无权访问')).toBeVisible();
  });

  test('并发操作测试', async ({ page }) => {
    // 测试同时提交多个表单
    await page.goto('/attendance');
    
    // 快速点击多次提交按钮
    for (let i = 0; i < 3; i++) {
      await page.click('button:has-text("记录考勤")');
    }
    
    // 验证重复提交处理
    await expect(page.locator('text=请勿重复提交')).toBeVisible();
  });

  test('文件上传限制测试', async ({ page }) => {
    // 测试文档上传
    await page.goto('/documents');
    
    // 尝试上传超大文件
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles('path/to/large-file.pdf');
    
    // 验证文件大小限制提示
    await expect(page.locator('text=文件大小超出限制')).toBeVisible();
  });

  test('数据导出测试', async ({ page }) => {
    // 测试导出功能
    await page.goto('/finance');
    
    // 点击导出按钮
    await page.click('button:has-text("导出数据")');
    
    // 验证下载开始
    const downloadPromise = page.waitForEvent('download');
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toContain('.xlsx');
  });

  test('会话超时测试', async ({ page }) => {
    // 等待会话超时
    await page.goto('/dashboard');
    await page.waitForTimeout(3600000); // 等待1小时
    
    // 尝试操作
    await page.click('button:has-text("新建项目")');
    
    // 验证会话超时提示
    await expect(page.locator('text=会话已过期，请重新登录')).toBeVisible();
  });

  test('网络错误处理测试', async ({ page }) => {
    // 模拟网络错误
    await page.route('**/api/*', route => route.abort());
    
    // 尝试加载数据
    await page.goto('/projects');
    
    // 验证错误处理
    await expect(page.locator('text=网络错误，请稍后重试')).toBeVisible();
  });
}); 