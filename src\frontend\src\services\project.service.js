import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || '';

const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor for adding auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;

    // Handle token expiration
    if (response && response.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

const projectService = {
  getProjects: (params = {}) => {
    // Ensure status is null or one of the allowed values
    // If status is undefined or empty string, set it to null explicitly
    if (params && (params.status === undefined || params.status === '')) {
      params.status = null;
    }
    return apiClient.get('/api/projects', { params });
  },
  
  getProject: (id) => {
    return apiClient.get(`/api/projects/${id}`);
  },
  
  createProject: (data) => {
    return apiClient.post('/api/projects', data);
  },
  
  updateProject: (id, data) => {
    return apiClient.put(`/api/projects/${id}`, data);
  },
  
  deleteProject: (id) => {
    return apiClient.delete(`/api/projects/${id}`);
  },
  
  getProjectStats: (params) => {
    return apiClient.get('/api/projects/stats', { params });
  },
  
  getProjectMembers: (projectId) => {
    return apiClient.get(`/api/projects/${projectId}/members`);
  },
  
  addProjectMember: (projectId, userId, role) => {
    return apiClient.post(`/api/projects/${projectId}/members`, { userId, role });
  },
  
  removeProjectMember: (projectId, userId) => {
    return apiClient.delete(`/api/projects/${projectId}/members/${userId}`);
  },
  
  getProjectsForDropdown: (search) => {
    return apiClient.get('/api/projects/dropdown', { params: { search } });
  }
};

export default projectService;
