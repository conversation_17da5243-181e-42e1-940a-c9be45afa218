const express = require('express');
const { authenticate } = require('../middlewares/auth.middleware');
const validate = require('../middlewares/validate');
const commentValidation = require('../validations/comment.validation');
const commentController = require('../controllers/comment.controller');

const router = express.Router();

router
  .route('/')
  .post(
    authenticate,
    validate(commentValidation.createComment),
    commentController.createComment
  );

router
  .route('/:commentId')
  .get(
    authenticate,
    validate(commentValidation.getComment),
    commentController.getComment
  )
  .patch(
    authenticate,
    validate(commentValidation.updateComment),
    commentController.updateComment
  )
  .delete(
    authenticate,
    validate(commentValidation.deleteComment),
    commentController.deleteComment
  );

module.exports = router;