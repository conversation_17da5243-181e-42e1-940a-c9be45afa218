/**
 * Custom Error class for API errors
 * @extends Error
 */
class ApiError extends Error {
  /**
   * Create an API error
   * @param {number} statusCode - HTTP status code for the error
   * @param {string} message - Error message
   * @param {boolean} isOperational - Whether the error is operational or programming
   * @param {string} stack - Error stack trace
   */
  constructor(statusCode, message, isOperational = true, stack = '') {
    // 转换常见的英文错误消息为中文
    const translatedMessage = ApiError.translateMessage(message);
    super(translatedMessage);
    
    // Validate and normalize status code
    this.statusCode = this.validateStatusCode(statusCode);
    this.isOperational = isOperational;
    
    if (stack) {
      this.stack = stack;
    } else {
      Error.captureStackTrace(this, this.constructor);
    }
  }

  /**
   * Translates common error messages to Chinese
   * @param {string} message - Original error message
   * @returns {string} - Translated message
   */
  static translateMessage(message) {
    const translations = {
      'Not found': '未找到资源',
      'Internal Server Error': '服务器内部错误',
      'Bad Request': '请求无效',
      'Unauthorized': '未授权访问',
      'Forbidden': '禁止访问',
      'Validation error': '数据验证错误',
      'Invalid credentials': '无效的凭据',
      'Password is incorrect': '密码不正确',
      'Token is required': '需要提供令牌',
      'Token is invalid': '令牌无效',
      'Token has expired': '令牌已过期',
      'User not found': '未找到用户',
      'Email already taken': '邮箱已被使用',
      'Invalid email': '邮箱格式不正确',
      'Invalid password': '密码格式不正确',
      'Invalid token': '令牌无效',
      'Token expired': '令牌已过期',
      'Missing field': '缺少必要字段',
      'Invalid field': '字段无效',
      'Invalid format': '格式无效',
      'Not found in database': '在数据库中未找到',
      'Already exists': '已存在',
      'Operation not allowed': '操作不允许',
      'Cannot be empty': '不能为空',
    };

    // 检查是否有直接匹配的完整消息
    if (translations[message]) {
      return translations[message];
    }

    // 检查消息是否包含特定关键字并替换
    for (const [key, value] of Object.entries(translations)) {
      if (message.includes(key)) {
        return message.replace(key, value);
      }
    }

    // 如果没有匹配项，返回原始消息
    return message;
  }

  /**
   * Validates and normalizes HTTP status code
   * @param {number} code - Status code to validate
   * @returns {number} - Valid HTTP status code
   */
  validateStatusCode(code) {
    // Convert to number if string
    const numCode = Number(code);
    
    // Check if valid HTTP status code
    if (isNaN(numCode) || numCode < 100 || numCode > 599) {
      console.warn(`无效的状态码: ${code}, 默认使用500`);
      return 500; // Internal Server Error
    }
    
    return numCode;
  }
}

module.exports = ApiError;