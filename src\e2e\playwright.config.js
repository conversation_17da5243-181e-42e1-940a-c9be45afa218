import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './src/e2e/tests',
  testIgnore: ['**/src/backend/tests/**'],
  timeout: 30000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
    video: 'on-first-retry',
    viewport: { width: 1920, height: 900 }
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

  ],
  webServer: [
    {
      command: 'cd ../frontend && npm run dev',
      port: 5173,
      reuseExistingServer: !process.env.CI,
    },
    {
      command: 'cd ../backend && npm run start',
      port: 3008,
      reuseExistingServer: !process.env.CI,
    },
  ],
});