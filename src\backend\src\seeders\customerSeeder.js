const { v4: uuidv4 } = require('uuid');
const { User } = require('../models');
const { Customer } = require('../models/customer.model');

async function seedCustomers() {
  try {
    // Check if customers already exist
    const customerCount = await Customer.count();
    if (customerCount > 0) {
      console.log('Customers already exist, skipping customer seeding');
      return;
    }

    // Get or create a default admin user for createdBy field
    let adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      adminUser = await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin'
      });
    }

    // Mock customer data based on frontend mock
    const customers = [
      {
        id: uuidv4(),
        name: '北京科技有限公司',
        code: 'C20220001',
        contactName: '张经理',
        contactTitle: '采购总监',
        phone: '13800138001',
        email: '<EMAIL>',
        type: 'company',
        status: 'active',
        address: '北京市海淀区中关村南大街5号',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '上海贸易有限公司',
        code: 'C20220002',
        contactName: '李总',
        contactTitle: '总经理',
        phone: '13900139002',
        email: '<EMAIL>',
        type: 'company',
        status: 'active',
        address: '上海市浦东新区陆家嘴金融贸易区',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '广州市政府',
        code: 'C20220003',
        contactName: '王科长',
        contactTitle: '采购科长',
        phone: '13700137003',
        email: '<EMAIL>',
        type: 'government',
        status: 'active',
        address: '广州市越秀区府前路1号',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '赵女士',
        code: 'C20220004',
        contactName: '赵女士',
        contactTitle: '个体经营',
        phone: '13600136004',
        email: '<EMAIL>',
        type: 'individual',
        status: 'inactive',
        address: '深圳市南山区科技园',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '成都建设集团',
        code: 'C20220005',
        contactName: '钱总',
        contactTitle: '项目经理',
        phone: '13500135005',
        email: '<EMAIL>',
        type: 'company',
        status: 'active',
        address: '成都市高新区天府大道',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '杭州智能科技有限公司',
        code: 'C20220006',
        contactName: '孙经理',
        contactTitle: '技术总监',
        phone: '13400134006',
        email: '<EMAIL>',
        type: 'company',
        status: 'active',
        address: '杭州市西湖区文三路',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '武汉市教育局',
        code: 'C20220007',
        contactName: '周局长',
        contactTitle: '局长',
        phone: '13300133007',
        email: '<EMAIL>',
        type: 'government',
        status: 'active',
        address: '武汉市江汉区解放大道',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '吴先生',
        code: 'C20220008',
        contactName: '吴先生',
        contactTitle: '个体经营',
        phone: '13200132008',
        email: '<EMAIL>',
        type: 'individual',
        status: 'active',
        address: '南京市鼓楼区中山北路',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '重庆建筑工程有限公司',
        code: 'C20220009',
        contactName: '郑总',
        contactTitle: '工程总监',
        phone: '13100131009',
        email: '<EMAIL>',
        type: 'company',
        status: 'active',
        address: '重庆市渝中区解放碑',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '西安市房管局',
        code: 'C20220010',
        contactName: '王局长',
        contactTitle: '局长',
        phone: '13000130010',
        email: '<EMAIL>',
        type: 'government',
        status: 'active',
        address: '西安市雁塔区小寨路',
        createdBy: adminUser.id
      }
    ];

    // Bulk create customers
    await Customer.bulkCreate(customers);
    console.log('Successfully seeded customers');
  } catch (error) {
    console.error('Error seeding customers:', error);
    throw error;
  }
}

module.exports = seedCustomers;
