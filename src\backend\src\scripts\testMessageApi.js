/**
 * 测试消息API的脚本
 * 
 * 此脚本用于测试消息API，需要先获取用户的JWT令牌
 * 运行方式：node src/scripts/testMessageApi.js
 */

const axios = require('axios');
const { sequelize, User } = require('../models');
const { tokenService } = require('../services');

async function testMessageApi() {
  try {
    console.log('开始测试消息API...');
    
    // 获取第一个用户
    const user = await User.findOne();
    
    if (!user) {
      console.error('没有找到用户，请先创建用户');
      return;
    }
    
    console.log(`找到用户: ${user.username}, ID: ${user.id}`);
    
    // 为用户生成JWT令牌
    const token = tokenService.generateToken(user.id);
    
    console.log('生成的JWT令牌:', token);
    
    // 设置API客户端
    const apiClient = axios.create({
      baseURL: 'http://localhost:3008',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    // 测试创建消息API
    const messageData = {
      title: '通过API测试创建的消息',
      content: '这是一条通过API测试脚本创建的消息',
      type: 'system'
    };
    
    console.log('准备通过API创建消息:', messageData);
    
    const response = await apiClient.post('/api/messages/test', messageData);
    
    console.log('API响应状态码:', response.status);
    console.log('API响应数据:', response.data);
    
    // 测试获取消息列表API
    const messagesResponse = await apiClient.get('/api/messages');
    
    console.log('获取消息列表API响应状态码:', messagesResponse.status);
    console.log(`获取到 ${messagesResponse.data.results.length} 条消息`);
    
    if (messagesResponse.data.results.length > 0) {
      console.log('最新消息:', messagesResponse.data.results[0]);
    }
    
  } catch (error) {
    console.error('测试消息API失败:', error);
    
    if (error.response) {
      console.error('API响应状态码:', error.response.status);
      console.error('API响应数据:', error.response.data);
    }
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行测试
testMessageApi();
