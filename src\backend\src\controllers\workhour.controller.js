const db = require('../models');
const WorkHour = db.WorkHour;
const { Op } = require('sequelize');
const sequelize = require('sequelize');

// 获取所有工时记录（带分页和筛选）
exports.getWorkHours = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      employeeId,
      employeeName,
      department,
      projectName,
      startDate,
      endDate,
      employmentType,
      month
    } = req.query;

    // 构建筛选条件
    const where = {};
    if (employeeId) where.employeeId = employeeId;
    if (employeeName) where.employeeName = { [Op.like]: `%${employeeName}%` };
    if (department) where.department = department;
    if (projectName) where.projectName = { [Op.like]: `%${projectName}%` };
    if (employmentType) where.employmentType = employmentType;

    // 日期范围筛选
    if (startDate || endDate) {
      where.date = {};
      if (startDate) where.date[Op.gte] = startDate;
      if (endDate) where.date[Op.lte] = endDate;
    }

    // 月份筛选
    if (month) {
      const startOfMonth = new Date(`${month}-01`);
      const endOfMonth = new Date(startOfMonth.getFullYear(), startOfMonth.getMonth() + 1, 0);
      where.date = {
        [Op.between]: [
          startOfMonth.toISOString().split('T')[0],
          endOfMonth.toISOString().split('T')[0]
        ]
      };
    }

    // 分页查询
    const offset = (page - 1) * limit;
    const { count, rows } = await WorkHour.findAndCountAll({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['date', 'DESC'], ['createdAt', 'DESC']]
    });

    return res.json({
      results: rows,
      totalResults: count,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(count / limit)
    });
  } catch (error) {
    console.error('获取工时记录失败:', error);
    return res.status(500).json({ message: '获取工时记录失败' });
  }
};

// 获取单个工时记录
exports.getWorkHour = async (req, res) => {
  try {
    const { id } = req.params;

    const workHour = await WorkHour.findByPk(id);

    if (!workHour) {
      return res.status(404).json({ message: '工时记录不存在' });
    }

    return res.json(workHour);
  } catch (error) {
    console.error('获取工时记录失败:', error);
    return res.status(500).json({ message: '获取工时记录失败' });
  }
};

// 创建新的工时记录
exports.createWorkHour = async (req, res) => {
  try {
    const workHourData = req.body;

    // 设置创建者
    if (req.user) {
      workHourData.createdBy = req.user.id;
    }

    // 创建工时记录
    const workHour = await WorkHour.create(workHourData);

    return res.status(201).json(workHour);
  } catch (error) {
    console.error('创建工时记录失败:', error);
    return res.status(500).json({ message: '创建工时记录失败' });
  }
};

// 更新工时记录
exports.updateWorkHour = async (req, res) => {
  try {
    const { id } = req.params;
    const workHourData = req.body;

    // 查找工时记录
    const workHour = await WorkHour.findByPk(id);

    if (!workHour) {
      return res.status(404).json({ message: '工时记录不存在' });
    }

    // 设置更新者
    if (req.user) {
      workHourData.updatedBy = req.user.id;
    }

    // 更新工时记录
    await workHour.update(workHourData);

    return res.json(workHour);
  } catch (error) {
    console.error('更新工时记录失败:', error);
    return res.status(500).json({ message: '更新工时记录失败' });
  }
};

// 删除工时记录
exports.deleteWorkHour = async (req, res) => {
  try {
    const { id } = req.params;

    const workHour = await WorkHour.findByPk(id);

    if (!workHour) {
      return res.status(404).json({ message: '工时记录不存在' });
    }

    await workHour.destroy();

    return res.json({ message: '工时记录删除成功' });
  } catch (error) {
    console.error('删除工时记录失败:', error);
    return res.status(500).json({ message: '删除工时记录失败' });
  }
};

// 获取工时统计数据
exports.getWorkHoursStatistics = async (req, res) => {
  try {
    const {
      startDate,
      endDate,
      employeeId,
      department,
      projectName,
      groupBy = 'date' // 默认按日期分组
    } = req.query;

    console.log('统计请求参数:', req.query);

    // 构建筛选条件
    const where = {};
    if (employeeId) where.employeeId = employeeId;
    if (department) where.department = department;
    if (projectName) where.projectName = { [Op.like]: `%${projectName}%` };

    // 日期范围筛选
    if (startDate || endDate) {
      where.date = {};
      if (startDate) where.date[Op.gte] = startDate;
      if (endDate) where.date[Op.lte] = endDate;
    }

    console.log('构建的查询条件:', where);

    // 定义分组字段
    let groupByField;
    let dateFormat;

    switch (groupBy) {
      case 'day':
        groupByField = 'date';
        dateFormat = '%Y-%m-%d';
        break;
      case 'week':
        groupByField = sequelize.fn('YEARWEEK', sequelize.col('date'), 1);
        dateFormat = '%Y-%u';
        break;
      case 'month':
        groupByField = sequelize.fn('DATE_FORMAT', sequelize.col('date'), '%Y-%m');
        dateFormat = '%Y-%m';
        break;
      case 'employee':
        groupByField = 'employeeId';
        break;
      case 'department':
        groupByField = 'department';
        break;
      case 'project':
        groupByField = 'projectName';
        break;
      default:
        groupByField = 'date';
        dateFormat = '%Y-%m-%d';
    }

    // 执行统计查询
    let statistics;

    if (['day', 'week', 'month'].includes(groupBy)) {
      // 按时间维度分组
      statistics = await WorkHour.findAll({
        attributes: [
          [sequelize.fn('DATE_FORMAT', sequelize.col('date'), dateFormat), 'timePeriod'],
          [sequelize.fn('SUM', sequelize.col('workHours')), 'totalHours'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'recordCount'],
          [sequelize.fn('AVG', sequelize.col('workHours')), 'averageHours'],
          [sequelize.fn('SUM', sequelize.col('payableAmount')), 'totalPayable']
        ],
        where,
        group: [groupByField],
        order: [[sequelize.col('timePeriod'), 'ASC']]
      });
    } else {
      // 按其他维度分组
      statistics = await WorkHour.findAll({
        attributes: [
          [groupByField, 'groupName'],
          [sequelize.fn('SUM', sequelize.col('workHours')), 'totalHours'],
          [sequelize.fn('COUNT', sequelize.col('id')), 'recordCount'],
          [sequelize.fn('AVG', sequelize.col('workHours')), 'averageHours'],
          [sequelize.fn('SUM', sequelize.col('payableAmount')), 'totalPayable']
        ],
        where,
        group: [groupByField],
        order: [[sequelize.col('totalHours'), 'DESC']]
      });
    }

    // 将统计结果转换为普通对象（解决可能的序列化问题）
    const statisticsJSON = statistics.map(item => item.get({ plain: true }));
    console.log('统计结果:', statisticsJSON);

    // 获取详细记录
    const detailRecords = await WorkHour.findAll({
      where,
      order: [['date', 'DESC'], ['createdAt', 'DESC']],
      limit: 100 // 限制返回的详细记录数量
    });

    // 将详细记录转换为普通对象
    const detailRecordsJSON = detailRecords.map(item => item.get({ plain: true }));

    // 计算总计
    const totals = await WorkHour.findAll({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('workHours')), 'totalHours'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'recordCount'],
        [sequelize.fn('AVG', sequelize.col('workHours')), 'averageHours'],
        [sequelize.fn('SUM', sequelize.col('payableAmount')), 'totalPayable'],
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('employeeId'))), 'employeeCount'],
        [sequelize.fn('COUNT', sequelize.fn('DISTINCT', sequelize.col('projectName'))), 'projectCount']
      ],
      where
    });

    // 将统计总计转换为普通对象
    const totalsJSON = totals[0].get({ plain: true });
    console.log('统计总计:', totalsJSON);

    return res.json({
      statistics: statisticsJSON,
      detailRecords: detailRecordsJSON,
      totals: totalsJSON,
      filters: {
        startDate,
        endDate,
        employeeId,
        department,
        projectName,
        groupBy
      }
    });
  } catch (error) {
    console.error('获取工时统计数据失败:', error);
    return res.status(500).json({ message: '获取工时统计数据失败', error: error.message });
  }
};

// 获取工时日历数据
exports.getWorkHoursCalendar = async (req, res) => {
  try {
    const { employeeId, month } = req.query;

    if (!month) {
      return res.status(400).json({ message: '月份参数是必需的' });
    }

    // 解析月份参数（格式：YYYY-MM）
    const [year, monthNum] = month.split('-').map(Number);
    const startDate = new Date(year, monthNum - 1, 1);
    const endDate = new Date(year, monthNum, 0);

    // 构建筛选条件
    const where = {
      date: {
        [Op.between]: [
          startDate.toISOString().split('T')[0],
          endDate.toISOString().split('T')[0]
        ]
      }
    };

    if (employeeId) {
      where.employeeId = employeeId;
    }

    // 获取该月的工时记录
    const workHours = await WorkHour.findAll({
      where,
      order: [['date', 'ASC']]
    });

    // 构建日历数据
    const calendarData = [];
    const daysInMonth = endDate.getDate();

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, monthNum - 1, day);
      const dateString = date.toISOString().split('T')[0];

      // 查找当天的工时记录
      const dayRecords = workHours.filter(record => record.date === dateString);

      calendarData.push({
        date: dateString,
        day,
        weekday: date.getDay(), // 0-6，0表示周日
        records: dayRecords,
        totalHours: dayRecords.reduce((sum, record) => sum + parseFloat(record.workHours), 0),
        hasRecords: dayRecords.length > 0
      });
    }

    // 计算汇总数据
    const summary = {
      totalDays: calendarData.filter(day => day.hasRecords).length,
      totalHours: workHours.reduce((sum, record) => sum + parseFloat(record.workHours), 0),
      averageHoursPerDay: calendarData.filter(day => day.hasRecords).length > 0
        ? workHours.reduce((sum, record) => sum + parseFloat(record.workHours), 0) / calendarData.filter(day => day.hasRecords).length
        : 0,
      totalRecords: workHours.length
    };

    return res.json({
      calendarData,
      summary,
      month,
      employeeId
    });
  } catch (error) {
    console.error('获取工时日历数据失败:', error);
    return res.status(500).json({ message: '获取工时日历数据失败' });
  }
};
