/**
 * Mock数据服务
 * 提供模拟数据，用于开发和测试环境
 */

// 用户数据模拟
const mockUsers = [
  { id: 1, name: '张三', email: 'z<PERSON><PERSON>@example.com', role: 'admin', status: 'active' },
  { id: 2, name: '李四', email: '<EMAIL>', role: 'user', status: 'active' },
  { id: 3, name: '王五', email: '<EMAIL>', role: 'user', status: 'inactive' },
  { id: 4, name: '赵六', email: 'zhao<PERSON><EMAIL>', role: 'user', status: 'pending' },
];

// 产品数据模拟
const mockProducts = [
  { id: 1, name: '笔记本电脑', price: 5999, stock: 100, category: '电子产品' },
  { id: 2, name: '智能手机', price: 2999, stock: 200, category: '电子产品' },
  { id: 3, name: '办公桌', price: 899, stock: 50, category: '家具' },
  { id: 4, name: '办公椅', price: 499, stock: 80, category: '家具' },
];

// 订单数据模拟
const mockOrders = [
  { id: 1, userId: 2, total: 5999, status: 'completed', items: [{ productId: 1, quantity: 1 }], createdAt: '2023-05-10' },
  { id: 2, userId: 3, total: 3498, status: 'processing', items: [{ productId: 2, quantity: 1 }, { productId: 4, quantity: 1 }], createdAt: '2023-05-15' },
  { id: 3, userId: 2, total: 6498, status: 'pending', items: [{ productId: 1, quantity: 1 }, { productId: 4, quantity: 1 }], createdAt: '2023-05-20' },
];

/**
 * 生成随机数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @returns {number} - 随机数
 */
function generateRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 生成单个项目数据
 * @param {string} projectId - 项目ID
 * @returns {Object} - 项目数据
 */
function mockProject(projectId) {
  const status = ['planning', 'in_progress', 'completed', 'on_hold', 'cancelled'][Math.floor(Math.random() * 5)];
  const progress = generateRandomNumber(0, 100);
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - generateRandomNumber(0, 365));
  const endDate = new Date(startDate);
  endDate.setDate(endDate.getDate() + generateRandomNumber(30, 365));
  
  return {
    id: projectId,
    name: `项目${projectId}`,
    code: `P${String(projectId).padStart(6, '0')}`,
    status,
    progress,
    manager: `负责人${generateRandomNumber(1, 10)}`,
    description: `这是项目${projectId}的详细描述，包含了项目的主要目标和范围。`,
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    tags: ['重要', '紧急', '进行中'].slice(0, generateRandomNumber(1, 3)),
    teamMembers: Array.from({ length: generateRandomNumber(3, 8) }, (_, i) => ({
      id: `member-${projectId}-${i + 1}`,
      name: `成员${i + 1}`,
      role: ['项目经理', '工程师', '设计师', '监理'][Math.floor(Math.random() * 4)],
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=member${i + 1}`
    })),
    members: `成员${generateRandomNumber(1, 5)}`,
    constructionUnit: `建设单位${generateRandomNumber(1, 20)}`,
    designUnit: `设计单位${generateRandomNumber(1, 20)}`,
    contractorUnit: `施工单位${generateRandomNumber(1, 20)}`,
    supervisorUnit: `监理单位${generateRandomNumber(1, 20)}`,
    reviewUnit: `审图单位${generateRandomNumber(1, 20)}`,
    address: `地址${generateRandomNumber(1, 100)}`,
    archiveNumber: `A${String(projectId).padStart(6, '0')}`,
    engineeringNumber: `E${String(projectId).padStart(6, '0')}`,
    usageType: ['商业', '住宅', '工业', '公共建筑'][Math.floor(Math.random() * 4)],
    engineeringType: ['建筑工程', '消防工程', '年度检测', '竣工检测', '消防安全评估', '消防维保'][Math.floor(Math.random() * 6)],
    constructionPermitNumber: `C${String(projectId).padStart(6, '0')}`,
    area: generateRandomNumber(100, 10000),
    specialSystem: `特殊系统${generateRandomNumber(1, 5)}`,
    companyManager: `公司负责人${generateRandomNumber(1, 10)}`,
    clientContact: `对接人${generateRandomNumber(1, 10)}`
  };
}

/**
 * 模拟项目数据
 */
const mockProjects = Array.from({ length: 12 }, (_, index) => mockProject(String(index + 1)));

/**
 * 延迟函数，模拟网络请求延迟
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise} - 延迟Promise
 */
const delay = (ms = 500) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 模拟获取用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} - 用户列表和分页信息
 */
const getUsers = async (params = {}) => {
  await delay();
  
  let result = [];
  
  // 简单过滤
  if (params.role) {
    result = result.filter(user => user.role === params.role);
  }
  
  if (params.status) {
    result = result.filter(user => user.status === params.status);
  }
  
  if (params.search) {
    const searchLower = params.search.toLowerCase();
    result = result.filter(user => 
      user.name.toLowerCase().includes(searchLower) || 
      user.email.toLowerCase().includes(searchLower)
    );
  }
  
  // 简单分页
  const page = params.page || 1;
  const limit = params.limit || 10;
  const total = result.length;
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  const paginatedResults = result.slice(offset, offset + limit);
  
  return {
    users: paginatedResults,
    totalPages,
    totalResults: total,
    currentPage: page
  };
};

/**
 * 模拟获取用户详情
 * @param {number} id - 用户ID
 * @returns {Promise<Object>} - 用户详情
 */
const getUserById = async (id) => {
  await delay();
  const user = mockUsers.find(user => user.id === Number(id));
  
  if (!user) {
    throw new Error('User not found');
  }
  
  return user;
};

/**
 * 模拟创建用户
 * @param {Object} userData - 用户数据
 * @returns {Promise<Object>} - 创建的用户
 */
const createUser = async (userData) => {
  await delay();
  
  // 检查邮箱是否已存在
  if (mockUsers.some(user => user.email === userData.email)) {
    throw new Error('Email already taken');
  }
  
  const newUser = {
    id: mockUsers.length + 1,
    ...userData,
    createdAt: new Date().toISOString()
  };
  
  mockUsers.push(newUser);
  return newUser;
};

/**
 * 模拟更新用户
 * @param {number} id - 用户ID
 * @param {Object} userData - 更新的用户数据
 * @returns {Promise<Object>} - 更新后的用户
 */
const updateUser = async (id, userData) => {
  await delay();
  
  const index = mockUsers.findIndex(user => user.id === Number(id));
  
  if (index === -1) {
    throw new Error('User not found');
  }
  
  // 检查邮箱是否已被其他用户使用
  if (userData.email && userData.email !== mockUsers[index].email) {
    const emailExists = mockUsers.some(user => user.email === userData.email && user.id !== Number(id));
    if (emailExists) {
      throw new Error('Email already taken');
    }
  }
  
  mockUsers[index] = {
    ...mockUsers[index],
    ...userData,
    updatedAt: new Date().toISOString()
  };
  
  return mockUsers[index];
};

/**
 * 模拟批量更新用户
 * @param {Object} criteria - 匹配条件
 * @param {Object} updateData - 更新数据
 * @returns {Promise<Object>} - 更新结果
 */
const updateBatchUsers = async (criteria, updateData) => {
  await delay();
  
  let count = 0;
  
  mockUsers.forEach((user, index) => {
    let match = true;
    
    for (const key in criteria) {
      if (user[key] !== criteria[key]) {
        match = false;
        break;
      }
    }
    
    if (match) {
      mockUsers[index] = {
        ...user,
        ...updateData,
        updatedAt: new Date().toISOString()
      };
      count++;
    }
  });
  
  return { count, message: `${count} users updated successfully` };
};

/**
 * 模拟删除用户
 * @param {number} id - 用户ID
 * @returns {Promise<void>}
 */
const deleteUser = async (id) => {
  await delay();
  
  const index = mockUsers.findIndex(user => user.id === Number(id));
  
  if (index === -1) {
    throw new Error('User not found');
  }
  
  mockUsers.splice(index, 1);
};

/**
 * 模拟获取产品列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} - 产品列表和分页信息
 */
const getProducts = async (params = {}) => {
  await delay();
  
  let result = [];
  
  // 简单过滤
  if (params.category) {
    result = result.filter(product => product.category === params.category);
  }
  
  if (params.minPrice) {
    result = result.filter(product => product.price >= params.minPrice);
  }
  
  if (params.maxPrice) {
    result = result.filter(product => product.price <= params.maxPrice);
  }
  
  if (params.search) {
    const searchLower = params.search.toLowerCase();
    result = result.filter(product => 
      product.name.toLowerCase().includes(searchLower)
    );
  }
  
  // 简单分页
  const page = params.page || 1;
  const limit = params.limit || 10;
  const total = result.length;
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  const paginatedResults = result.slice(offset, offset + limit);
  
  return {
    products: paginatedResults,
    totalPages,
    totalResults: total,
    currentPage: page
  };
};

/**
 * 模拟获取订单列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} - 订单列表和分页信息
 */
const getOrders = async (params = {}) => {
  await delay();
  
  let result = [];
  
  // 简单过滤
  if (params.userId) {
    result = result.filter(order => order.userId === Number(params.userId));
  }
  
  if (params.status) {
    result = result.filter(order => order.status === params.status);
  }
  
  // 简单分页
  const page = params.page || 1;
  const limit = params.limit || 10;
  const total = result.length;
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  const paginatedResults = result.slice(offset, offset + limit);
  
  return {
    orders: paginatedResults,
    totalPages,
    totalResults: total,
    currentPage: page
  };
};

/**
 * 模拟获取项目列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} - 项目列表和分页信息
 */
const getProjects = async (params = {}) => {
  await delay(700); // Slightly longer delay for mock service
  console.log('Mock getProjects params:', params);
  
  let result = [...mockProjects];
  
  // 筛选
  if (params.search) {
    const query = params.search.toLowerCase();
    result = result.filter(project => 
      project.name.toLowerCase().includes(query) || 
      project.code.toLowerCase().includes(query) || 
      project.manager.toLowerCase().includes(query)
    );
  }
  
  if (params.status) {
    result = result.filter(project => project.status === params.status);
  }
  
  // 排序
  if (params.sort) {
    const sortField = params.sort.startsWith('-') ? params.sort.substring(1) : params.sort;
    const sortDirection = params.sort.startsWith('-') ? -1 : 1;
    
    result.sort((a, b) => {
      if (a[sortField] < b[sortField]) return -1 * sortDirection;
      if (a[sortField] > b[sortField]) return 1 * sortDirection;
      return 0;
    });
  }
  
  // 计算总数
  const total = result.length;
  
  // 分页
  const page = parseInt(params.page, 10) || 1;
  const limit = parseInt(params.limit, 10) || 10;
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  const paginatedResults = result.slice(offset, offset + limit);
  
  // 使用与API服务相同的响应格式
  return {
    results: paginatedResults,
    totalResults: total,
    page: page,
    limit: limit,
    totalPages: totalPages
  };
};

/**
 * 模拟获取项目详情
 * @param {string} id - 项目ID
 * @returns {Promise<Object>} - 项目详情
 */
const getProjectById = async (id) => {
  await delay();
  const project = mockProjects.find(project => project.id === id);
  
  if (!project) {
    throw new Error('Project not found');
  }
  
  return project;
};

/**
 * 模拟创建项目
 * @param {Object} projectData - 项目数据
 * @returns {Promise<Object>} - 创建的项目
 */
const createProject = async (projectData) => {
  await delay();
  
  // 检查项目代码是否已存在
  if (mockProjects.some(project => project.code === projectData.code)) {
    throw new Error('Project code already exists');
  }
  
  const newProject = {
    id: String(mockProjects.length + 1),
    ...projectData,
    createdAt: new Date().toISOString()
  };
  
  mockProjects.push(newProject);
  return newProject;
};

/**
 * 模拟更新项目
 * @param {string} id - 项目ID
 * @param {Object} projectData - 更新的项目数据
 * @returns {Promise<Object>} - 更新后的项目
 */
const updateProject = async (id, projectData) => {
  await delay();
  
  const index = mockProjects.findIndex(project => project.id === id);
  
  if (index === -1) {
    throw new Error('Project not found');
  }
  
  // 检查项目代码是否已被其他项目使用
  if (projectData.code && projectData.code !== mockProjects[index].code) {
    const codeExists = mockProjects.some(project => project.code === projectData.code && project.id !== id);
    if (codeExists) {
      throw new Error('Project code already exists');
    }
  }
  
  mockProjects[index] = {
    ...mockProjects[index],
    ...projectData,
    updatedAt: new Date().toISOString()
  };
  
  return mockProjects[index];
};

/**
 * 模拟删除项目
 * @param {string} id - 项目ID
 * @returns {Promise<void>}
 */
const deleteProject = async (id) => {
  await delay();
  
  const index = mockProjects.findIndex(project => project.id === id);
  
  if (index === -1) {
    throw new Error('Project not found');
  }
  
  mockProjects.splice(index, 1);
};

export default {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  updateBatchUsers,
  deleteUser,
  getProducts,
  getProjects,
  getProjectById,
  createProject,
  updateProject,
  deleteProject,
  getOrders
}; 