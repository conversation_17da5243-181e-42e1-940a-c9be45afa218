<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">财务管理</h1>
        <p class="page-subtitle">管理和跟踪财务信息</p>
      </div>
      <div class="flex space-x-2">
        <router-link to="/finance/stats" class="btn btn-secondary flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          财务统计
        </router-link>
        <router-link to="/finance/create" class="btn btn-primary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
          </svg>
          新增财务记录
        </router-link>
      </div>
    </div>

    <!-- 筛选卡片 -->
    <div class="filter-card bg-white rounded-lg shadow-md p-6 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">单据类型</label>
          <select class="form-input" v-model="filters.type">
            <option value="">全部类型</option>
            <option value="income">收入</option>
            <option value="expense">支出</option>
          </select>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">状态</label>
          <select class="form-input" v-model="filters.status">
            <option value="">全部状态</option>
            <option value="pending">待处理</option>
            <option value="completed">已完成</option>
            <option value="cancelled">已取消</option>
          </select>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">开始日期</label>
          <input 
            type="date" 
            class="form-input" 
            v-model="filters.dateFrom" 
            max="3000-12-31"
            :min="'2000-01-01'"
          />
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700">结束日期</label>
          <input 
            type="date" 
            class="form-input" 
            v-model="filters.dateTo" 
            max="3000-12-31"
            :min="filters.dateFrom || '2000-01-01'"
          />
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button 
          @click="resetFilters" 
          class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200"
          :disabled="loading"
        >
          重置
        </button>
        <button 
          @click="search" 
          class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200"
          :disabled="loading"
        >
          <span v-if="loading" class="inline-block animate-spin mr-1">↻</span>
          {{ loading ? '加载中...' : '查询' }}
        </button>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
      <button @click="fetchFinances()" class="mt-3 text-blue-600 underline">重试</button>
    </div>

    <!-- 表格卡片 -->
    <div v-if="loading" class="card overflow-hidden shadow-md p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">正在加载财务数据...</p>
    </div>
    <div v-else-if="!loading && finances.length === 0" class="card overflow-hidden shadow-md p-8 text-center">
      <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无财务记录</h3>
      <p class="text-gray-500 mb-4">点击"新增财务记录"按钮创建新记录</p>
      <router-link to="/finance/create" class="btn btn-primary">新增财务记录</router-link>
    </div>
    <div v-else class="card overflow-hidden shadow-md">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" rowspan="2">项目</th>
            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" colspan="4">销项信息</th>
            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" rowspan="2">回款信息</th>
            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" colspan="3">进项信息</th>
            <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" colspan="3">付款信息</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" rowspan="2">纯收入</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" rowspan="2">附件</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" rowspan="2">操作</th>
          </tr>
          <tr>
            <!-- 销项信息二级表头 -->
            <th class="px-3 py-2 text-xs font-medium text-gray-500">开票时间</th>
            <th class="px-3 py-2 text-xs font-medium text-gray-500">税率</th>
            <th class="px-3 py-2 text-xs font-medium text-gray-500">购方信息</th>
            <th class="px-3 py-2 text-xs font-medium text-gray-500">发票类型</th>
            <!-- 进项信息二级表头 -->
            <th class="px-3 py-2 text-xs font-medium text-gray-500">税率</th>
            <th class="px-3 py-2 text-xs font-medium text-gray-500">售方信息</th>
            <th class="px-3 py-2 text-xs font-medium text-gray-500">发票类型</th>
            <!-- 付款信息二级表头 -->
            <th class="px-3 py-2 text-xs font-medium text-gray-500">分包</th>
            <th class="px-3 py-2 text-xs font-medium text-gray-500">采购</th>
            <th class="px-3 py-2 text-xs font-medium text-gray-500">报销</th>
          </tr>
          <tr>
            <td colspan="22" style="height:2px; padding:0; background:#e5e7eb; border:none;"></td>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="finance in finances" :key="finance.id" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
              <div>{{ finance.project?.name || '-' }}</div>
              <div class="text-xs text-gray-500">{{ finance.project?.projectNumber || '-' }}</div>
            </td>
            <!-- 销项信息数据 -->
            <td class="px-3 py-2 text-xs text-gray-500">{{ formatDate(finance.invoiceDate) || '-' }}</td>
            <td class="px-3 py-2 text-xs text-gray-500">{{ finance.invoiceTaxRate !== null && finance.invoiceTaxRate !== undefined ? formatCurrency(finance.invoiceTaxRate) : '-' }}</td>
            <td class="px-3 py-2 text-xs text-gray-500">{{ finance.buyerInfo || '-' }}</td>
            <td class="px-3 py-2 text-xs text-gray-500">{{ finance.invoiceType || '-' }}</td>
            <!-- 回款信息合并单元格 -->
            <td class="px-3 py-2 text-xs text-gray-500">
              <template v-if="finance.incomePayments && finance.incomePayments.length">
                <span v-for="(item, idx) in finance.incomePayments" :key="idx">
                  {{ formatDate(item.date) }}：{{ formatCurrency(item.amount) }}<span v-if="idx < finance.incomePayments.length - 1">；</span>
                </span>
              </template>
              <template v-else>-</template>
            </td>
            <!-- 进项信息数据 -->
            <td class="px-3 py-2 text-xs text-gray-500">{{ finance.inputInvoiceTaxRate !== null && finance.inputInvoiceTaxRate !== undefined ? formatCurrency(finance.inputInvoiceTaxRate) : '-' }}</td>
            <td class="px-3 py-2 text-xs text-gray-500">{{ finance.sellerInfo || '-' }}</td>
            <td class="px-3 py-2 text-xs text-gray-500">{{ finance.inputInvoiceType || '-' }}</td>
            <!-- 付款信息数据 -->
            <td class="px-3 py-2 text-xs text-gray-500">{{ finance.subcontractPayment !== null && finance.subcontractPayment !== undefined ? formatCurrency(finance.subcontractPayment) : '-' }}</td>
            <td class="px-3 py-2 text-xs text-gray-500">{{ finance.purchasePayment !== null && finance.purchasePayment !== undefined ? formatCurrency(finance.purchasePayment) : '-' }}</td>
            <td class="px-3 py-2 text-xs text-gray-500">{{ finance.reimbursementPayment !== null && finance.reimbursementPayment !== undefined ? formatCurrency(finance.reimbursementPayment) : '-' }}</td>
            <!-- 纯收入 -->
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ finance.netIncome !== null && finance.netIncome !== undefined ? formatCurrency(finance.netIncome) : '-' }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              <button v-if="finance.attachments?.length" class="text-blue-600 hover:text-blue-900" @click="viewAttachments(finance.attachments)">查看</button>
              <span v-else>-</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">              <router-link
                :to="`/finance/edit/${finance.id}`"
                class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
              >
                <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                </svg>
                <span class="font-medium">编辑</span>
              </router-link>
              <a
                @click.prevent="handleDelete(finance.id)"
                class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group cursor-pointer"
              >
                <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                </svg>
                <span class="font-medium">删除</span>
              </a>
            </td>
          </tr>
        </tbody>
      </table>
      </div>
      <!-- 分页 -->
      <div class="mt-4 flex justify-between items-center px-6 py-4 border-t border-gray-200">
        <div class="text-sm text-gray-500">
          <template v-if="total > 0">
            显示第 <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> 至 
            <span class="font-medium">{{ Math.min(currentPage * pageSize, total) }}</span> 条，共 
            <span class="font-medium">{{ total }}</span> 条记录
          </template>
          <template v-else>
            暂无数据
          </template>
        </div>
        <div class="flex items-center space-x-3">
          <button
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="currentPage === 1"
            @click="handlePageChange(currentPage - 1)"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <span class="text-sm font-medium text-gray-700">第 {{ currentPage }} 页，共 {{ Math.ceil(total / pageSize) }} 页</span>
          <button
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="currentPage >= Math.ceil(total / pageSize)"
            @click="handlePageChange(currentPage + 1)"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue';
import { useRouter } from 'vue-router';
import { usePaginationStore } from '@/stores/pagination';
import axios from 'axios';

// 使用分页 store
const paginationStore = usePaginationStore();

// 财务数据
const finances = ref([]);

// 加载和错误状态
const loading = ref(false);
const error = ref(null);
const submitting = ref(false);

// 使用计算属性获取分页状态
const currentPage = computed(() => paginationStore.getCurrentPage)
const pageSize = computed(() => paginationStore.getPageSize)
const total = computed(() => paginationStore.getTotal)

// 分页配置
const pagination = reactive({
  current: currentPage,
  pageSize: pageSize,
  total: total,
  showSizeChanger: false,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

const router = useRouter();

// 新增筛选条件
const filters = reactive({
  type: '',
  status: '',
  dateFrom: '',
  dateTo: ''
});

const search = () => {
  // TODO: 实现带筛选条件的查询
  fetchFinances();
};

const resetFilters = () => {
  filters.type = '';
  filters.status = '';
  filters.dateFrom = '';
  filters.dateTo = '';
  fetchFinances();
};

// 删除财务记录
const handleDelete = async (id) => {
  if (confirm('确定要删除这条财务记录吗？')) {
    submitting.value = true;

    try {
      await axios.delete(`/api/finance/${id}`);

      // 如果当前页只有一条记录，且不是第一页，则返回上一页
      if (finances.value.length === 1 && currentPage.value > 1) {
        paginationStore.setCurrentPage(currentPage.value - 1);
      }

      fetchFinances();
    } catch (err) {
      console.error('删除财务记录失败:', err);
      error.value = err.response?.data?.message || '删除财务记录失败，请重试';
    } finally {
      submitting.value = false;
    }
  }
}

// 获取财务记录列表
const fetchFinances = async () => {
  loading.value = true;
  error.value = null;

  try {
    console.log('获取财务记录，页码:', currentPage.value, '每页大小:', pageSize.value);

    // 构建查询参数
    const params = {
      page: currentPage.value,
      limit: pageSize.value
    };

    // 只有当类型不为空时才添加到查询参数
    if (filters.type) {
      params.type = filters.type;
    }

    // 只有当状态不为空时才添加到查询参数
    if (filters.status) {
      params.status = filters.status;
    }

    // 只有当日期不为空且格式正确时才添加到查询参数
    if (filters.dateFrom && isValidDate(filters.dateFrom)) {
      params.dateFrom = filters.dateFrom;
    }

    if (filters.dateTo && isValidDate(filters.dateTo)) {
      params.dateTo = filters.dateTo;
    }

    const response = await axios.get('/api/finance', { params });

    console.log('API 响应数据:', response.data);
    
    // 更新财务数据
    finances.value = response.data.results || [];
    
    // 更新分页信息
    if (response.data.pagination) {
      paginationStore.setTotal(response.data.pagination.totalResults || 0);
      // 如果当前页大于总页数，则重置为第一页
      if (currentPage.value > response.data.pagination.totalPages && response.data.pagination.totalPages > 0) {
        paginationStore.setCurrentPage(1);
        return; // 重新获取第一页数据
      }
    }
  } catch (err) {
    console.error('获取财务记录失败:', err);
    error.value = err.response?.data?.message || '获取财务记录失败，请重试';
    // 发生错误时清空数据
    finances.value = [];
    paginationStore.setTotal(0);
  } finally {
    loading.value = false;
  }
}

// 验证日期格式
const isValidDate = (dateString) => {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date);
};

// 处理页码变化
const handlePageChange = (page) => {
  console.log('页码变化:', page);
  paginationStore.setCurrentPage(page);
  fetchFinances();
}

// 处理编辑
const handleEdit = (id) => {
  // 保存当前页码到 store
  paginationStore.setCurrentPage(currentPage.value)
  router.push({
    path: `/finance/${id}`,
    query: { page: currentPage.value }
  })
}

// 页面加载时获取数据
onMounted(async () => {
  try {
    // 从路由参数中获取页码
    const page = parseInt(router.currentRoute.value.query.page) || 1;
    console.log('初始化页码:', page);
    // 设置 store 中的页码
    paginationStore.setCurrentPage(page);
    await fetchFinances();
    console.log('财务管理页面已加载，数据:', finances.value);
  } catch (err) {
    console.error('加载财务数据失败:', err);
    error.value = err.response?.data?.message || '加载财务数据失败，请重试';
  }
});

// 添加格式化函数
const formatDate = (date) => {
  if (!date) return null;
  return new Date(date).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
};

const formatCurrency = (amount) => {
  if (amount === null || amount === undefined) return '-';
  return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

const viewAttachments = (attachments) => {
  // TODO: 实现附件查看功能
  console.log('查看附件:', attachments);
};

// 添加类型和状态标签转换函数
const getTypeLabel = (type) => {
  const typeLabels = {
    income: '收入',
    expense: '支出'
  };
  return typeLabels[type] || type;
};

const getStatusLabel = (status) => {
  const statusLabels = {
    completed: '已完成',
    pending: '待处理',
    cancelled: '已取消'
  };
  return statusLabels[status] || status;
};

const categoryLabels = {
  office_supplies: '办公用品',
  salary: '工资薪酬',
  rent: '租金',
  utilities: '水电费',
  travel: '差旅费',
  sales: '销售收入',
  service: '服务收入',
  material: '材料费',
  equipment: '设备费',
  subcontract: '分包费',
  other: '其他',
};

const paymentMethodLabels = {
  cash: '现金',
  bank_transfer: '银行转账',
  credit_card: '信用卡',
  wechat: '微信支付',
  alipay: '支付宝',
};

const getCategoryLabel = (category) => {
  return categoryLabels[category] || category || '-';
};

const getPaymentMethodLabel = (method) => {
  return paymentMethodLabels[method] || method || '-';
};
</script>

<style scoped>
.page-header, .filter-card, .card {
  animation: fadeSlideIn 0.6s ease-out;
}

.page-header {
  animation-delay: 0s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.page-subtitle {
  font-size: 1rem;
  color: #6b7280;
}

.filter-card {
  animation-delay: 0.1s;
}

.card {
  animation-delay: 0.2s;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-input {
  @apply block w-full rounded-md border-gray-300 shadow-sm
    focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50
    transition-all duration-200;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent
    rounded-md shadow-sm text-sm font-medium focus:outline-none
    focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700
    focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-white text-gray-700 border-gray-300
    hover:bg-gray-50 focus:ring-blue-500;
}

tbody tr {
  transition: all 0.2s ease;
}
</style>