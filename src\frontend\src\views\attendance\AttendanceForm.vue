<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4" v-if="show">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden flex flex-col">
      <!-- Form Header -->
      <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
        <h2 class="text-xl font-bold text-gray-800">{{ formMode === 'add' ? '新增考勤记录' : '编辑考勤记录' }}</h2>
        <button @click="closeForm" class="text-gray-500 hover:text-gray-700 focus:outline-none">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Form Content -->
      <div class="px-6 py-4 overflow-y-auto">
        <!-- 错误提示 -->
        <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="font-medium">错误：</span>
          </div>
          <p class="mt-2 whitespace-pre-line">{{ error }}</p>
        </div>

        <form @submit.prevent="submitForm" novalidate>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 项目选择 -->
            <div class="form-group">
              <label for="project" class="form-label required">项目</label>
              <select
                id="project"
                v-model="form.projectId"
                class="form-input"
                :class="{ 'border-red-500': errors.projectId }"
                required
              >
                <option value="" disabled>请选择项目</option>
                <option v-for="project in projects" :key="project.id" :value="project.id">
                  {{ project.name }}
                </option>
              </select>
              <p v-if="errors.projectId" class="text-red-500 text-sm mt-1">{{ errors.projectId }}</p>
            </div>

            <!-- 员工姓名 -->
            <div class="form-group">
              <label for="employeeName" class="form-label required">员工姓名</label>
              <div class="relative">
                <input
                  type="text"
                  id="employeeNameFilter"
                  v-model="employeeFilter"
                  class="form-input mb-1"
                  placeholder="输入员工姓名筛选..."
                  @focus="showEmployeeDropdown = true"
                  @input="filterEmployees"
                />
                <div v-if="showEmployeeDropdown" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                  <div v-if="filteredEmployees.length === 0" class="px-4 py-2 text-gray-500">
                    未找到匹配的员工
                  </div>
                  <div
                    v-for="employee in filteredEmployees"
                    :key="employee.id"
                    class="px-4 py-2 hover:bg-blue-50 cursor-pointer"
                    @click="selectEmployee(employee)"
                  >
                    {{ employee.name }} ({{ employee.department || '无部门' }})
                  </div>
                </div>
              </div>
              <input
                type="text"
                id="employeeName"
                v-model="form.employeeName"
                class="form-input"
                :class="{ 'border-red-500': errors.employeeName }"
                readonly
                placeholder="已选择的员工"
              />
              <p v-if="errors.employeeName" class="text-red-500 text-sm mt-1">{{ errors.employeeName }}</p>
            </div>

            <!-- 员工编号 -->
            <div class="form-group">
              <label for="employeeId" class="form-label">员工编号</label>
              <input
                type="text"
                id="employeeId"
                v-model="form.employeeId"
                class="form-input"
                :class="{ 'border-red-500': errors.employeeId }"
                placeholder="请输入员工编号"
              />
              <p v-if="errors.employeeId" class="text-red-500 text-sm mt-1">{{ errors.employeeId }}</p>
            </div>

            <!-- 日期 -->
            <div class="form-group">
              <label for="date" class="form-label required">日期</label>
              <input
                type="date"
                id="date"
                v-model="form.date"
                class="form-input"
                :class="{ 'border-red-500': errors.date }"
                required
                max="3000-12-31"
              />
              <p v-if="errors.date" class="text-red-500 text-sm mt-1">{{ errors.date }}</p>
            </div>

            <!-- 上班时间 -->
            <div class="form-group">
              <label for="timeIn" class="form-label required">上班时间</label>
              <input
                type="time"
                id="timeIn"
                v-model="form.timeIn"
                class="form-input"
                :class="{ 'border-red-500': errors.timeIn }"
                required
              />
              <p v-if="errors.timeIn" class="text-red-500 text-sm mt-1">{{ errors.timeIn }}</p>
            </div>

            <!-- 下班时间 -->
            <div class="form-group">
              <label for="timeOut" class="form-label">下班时间</label>
              <input
                type="time"
                id="timeOut"
                v-model="form.timeOut"
                class="form-input"
                :class="{ 'border-red-500': errors.timeOut }"
              />
              <p v-if="errors.timeOut" class="text-red-500 text-sm mt-1">{{ errors.timeOut }}</p>
            </div>

            <!-- 工作时长 -->
            <div class="form-group">
              <label for="hoursWorked" class="form-label">工作时长（小时）</label>
              <input
                type="number"
                id="hoursWorked"
                v-model="form.hoursWorked"
                min="0"
                step="0.5"
                class="form-input"
                :class="{ 'border-red-500': errors.hoursWorked }"
                placeholder="自动计算或手动输入"
              />
              <p v-if="errors.hoursWorked" class="text-red-500 text-sm mt-1">{{ errors.hoursWorked }}</p>
            </div>

            <!-- 状态 -->
            <div class="form-group">
              <label for="status" class="form-label required">状态</label>
              <select
                id="status"
                v-model="form.status"
                class="form-input"
                :class="{ 'border-red-500': errors.status }"
                required
              >
                <option value="present">出勤</option>
                <option value="absent">缺勤</option>
                <option value="late">迟到</option>
                <option value="half-day">半天</option>
              </select>
              <p v-if="errors.status" class="text-red-500 text-sm mt-1">{{ errors.status }}</p>
            </div>

            <!-- 签到方式 -->
            <div class="form-group">
              <label for="checkInMethod" class="form-label">签到方式</label>
              <select
                id="checkInMethod"
                v-model="form.checkInMethod"
                class="form-input"
                :class="{ 'border-red-500': errors.checkInMethod }"
              >
                <option value="manual">手动签到</option>
                <option value="gps">GPS定位</option>
                <option value="wifi">WIFI定位</option>
                <option value="beacon">信标定位</option>
              </select>
              <p v-if="errors.checkInMethod" class="text-red-500 text-sm mt-1">{{ errors.checkInMethod }}</p>
            </div>
          </div>

          <!-- 位置相关信息 -->
          <div class="form-group mt-4">
            <label for="location" class="form-label" :class="{ 'required': form.checkInMethod === 'gps' }">签到位置</label>
            <input
              type="text"
              id="location"
              v-model="form.location"
              class="form-input"
              :class="{ 'border-red-500': errors.location }"
              placeholder="请输入签到位置"
            />
            <p v-if="errors.location" class="text-red-500 text-sm mt-1">{{ errors.location }}</p>
          </div>

          <div v-if="form.checkInMethod === 'gps'" class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            <!-- 纬度 -->
            <div class="form-group">
              <label for="latitude" class="form-label">纬度</label>
              <input
                type="number"
                id="latitude"
                v-model="form.latitude"
                class="form-input"
                :class="{ 'border-red-500': errors.latitude }"
                step="any"
                placeholder="例如：39.9042"
              />
              <p v-if="errors.latitude" class="text-red-500 text-sm mt-1">{{ errors.latitude }}</p>
            </div>

            <!-- 经度 -->
            <div class="form-group">
              <label for="longitude" class="form-label">经度</label>
              <input
                type="number"
                id="longitude"
                v-model="form.longitude"
                class="form-input"
                :class="{ 'border-red-500': errors.longitude }"
                step="any"
                placeholder="例如：116.4074"
              />
              <p v-if="errors.longitude" class="text-red-500 text-sm mt-1">{{ errors.longitude }}</p>
            </div>
          </div>

          <!-- 备注 -->
          <div class="form-group mt-4">
            <label for="notes" class="form-label">备注</label>
            <textarea
              id="notes"
              v-model="form.notes"
              class="form-input"
              :class="{ 'border-red-500': errors.notes }"
              rows="3"
              placeholder="请输入备注信息"
            ></textarea>
            <p v-if="errors.notes" class="text-red-500 text-sm mt-1">{{ errors.notes }}</p>
          </div>
        </form>
      </div>

      <!-- Form Footer -->
      <div class="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
        <button @click="closeForm" class="btn btn-secondary">取消</button>
        <button @click="submitForm" class="btn btn-primary" :disabled="isSubmitting">
          <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ formMode === 'add' ? '提交' : '保存' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import axios from 'axios';
import { useDepartmentStore } from '@/stores/department';
import apiService from '@/services/apiService';
import attendanceService from '@/services/attendance.service';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  formMode: {
    type: String,
    default: 'add', // 'add' or 'edit'
    validator: (value) => ['add', 'edit'].includes(value)
  },
  attendanceData: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:show', 'submit-success']);

// Form data
const form = reactive({
  projectId: '',
  employeeName: '',
  employeeId: '',
  date: new Date().toISOString().split('T')[0], // Default to today
  timeIn: '',
  timeOut: '',
  hoursWorked: null,
  status: 'present',
  // 签到位置相关字段
  location: '',
  latitude: null,
  longitude: null,
  checkInMethod: 'manual', // 默认手动签到
  notes: ''
});

// Validation errors
const errors = reactive({});

// Loading states
const isSubmitting = ref(false);
const isLoadingProjects = ref(false);
const isLoadingEmployees = ref(false);
const projects = ref([]);
const employees = ref([]);
const error = ref(null);

// 员工筛选相关
const employeeFilter = ref('');
const showEmployeeDropdown = ref(false);
const filteredEmployees = ref([]);

// 使用部门存储
const departmentStore = useDepartmentStore();

// 点击页面其他地方关闭下拉框
const closeDropdownOnOutsideClick = (event) => {
  const dropdown = document.getElementById('employeeNameFilter');
  if (dropdown && !dropdown.contains(event.target)) {
    showEmployeeDropdown.value = false;
  }
};

// 添加和移除全局点击事件监听器
onMounted(() => {
  document.addEventListener('click', closeDropdownOnOutsideClick);
});

// 组件卸载时移除事件监听器
const onBeforeUnmount = () => {
  document.removeEventListener('click', closeDropdownOnOutsideClick);
};

// Populate form with existing data when in edit mode
watch(
  () => props.attendanceData,
  (newValue) => {
    if (props.formMode === 'edit' && newValue && Object.keys(newValue).length > 0) {
      Object.keys(form).forEach(key => {
        if (key in newValue) {
          form[key] = newValue[key];
        }
      });
    }
  },
  { immediate: true }
);

// Reset form when dialog closes
watch(
  () => props.show,
  (newValue) => {
    if (!newValue || (newValue && props.formMode === 'add')) {
      // Reset form when closing or when opening for adding new record
      resetForm();
    }
  }
);

// Auto-calculate hours worked when both timeIn and timeOut are provided
watch([() => form.timeIn, () => form.timeOut], ([newTimeIn, newTimeOut]) => {
  if (newTimeIn && newTimeOut) {
    const [inHours, inMinutes] = newTimeIn.split(':').map(Number);
    const [outHours, outMinutes] = newTimeOut.split(':').map(Number);

    const inTotalMinutes = inHours * 60 + inMinutes;
    const outTotalMinutes = outHours * 60 + outMinutes;

    if (outTotalMinutes > inTotalMinutes) {
      const diffMinutes = outTotalMinutes - inTotalMinutes;
      form.hoursWorked = Math.round((diffMinutes / 60) * 2) / 2; // Round to nearest 0.5
    }
  }
});

// Load projects and employees when component is mounted
onMounted(async () => {
  await Promise.all([
    fetchProjects(),
    fetchEmployees(),
    departmentStore.fetchDepartments()
  ]);
});

// Fetch projects
const fetchProjects = async () => {
  isLoadingProjects.value = true;
  try {
    const response = await apiService.getProjects();
    projects.value = response.results || [];
  } catch (err) {
    console.error('获取项目列表失败:', err);
    error.value = '获取项目列表失败，请重试';
  } finally {
    isLoadingProjects.value = false;
  }
};

// Fetch employees
const fetchEmployees = async () => {
  isLoadingEmployees.value = true;
  try {
    const response = await apiService.getAllEmployees({ limit: 100 });
    employees.value = response.results || [];
    filteredEmployees.value = employees.value; // 初始化筛选列表
  } catch (err) {
    console.error('获取员工列表失败:', err);
    error.value = '获取员工列表失败，请重试';
  } finally {
    isLoadingEmployees.value = false;
  }
};

// 筛选员工
const filterEmployees = () => {
  if (!employeeFilter.value) {
    filteredEmployees.value = employees.value;
    return;
  }

  const searchTerm = employeeFilter.value.toLowerCase();
  filteredEmployees.value = employees.value.filter(emp =>
    emp.name.toLowerCase().includes(searchTerm) ||
    (emp.department && emp.department.toLowerCase().includes(searchTerm))
  );
};

// 选择员工
const selectEmployee = (employee) => {
  form.employeeId = employee.id;
  form.employeeName = employee.name;
  form.department = employee.department;
  employeeFilter.value = '';
  showEmployeeDropdown.value = false;
};

// Handle employee selection (保留原有函数以兼容其他地方的调用)
const handleEmployeeChange = () => {
  const selectedEmployee = employees.value.find(emp => emp.id === form.employeeId);
  if (selectedEmployee) {
    form.employeeName = selectedEmployee.name;
    form.department = selectedEmployee.department;
  }
};

// Form validation - 完整的中文表单验证
const validateForm = () => {
  const newErrors = {};

  if (!form.projectId) {
    newErrors.projectId = '项目是必填项，请选择项目';
  }

  if (!form.employeeName) {
    newErrors.employeeName = '员工姓名是必填项，请输入员工姓名';
  }

  if (!form.date) {
    newErrors.date = '日期是必填项，请选择日期';
  }

  if (!form.timeIn) {
    newErrors.timeIn = '签到时间是必填项，请输入上班时间';
  }

  if (form.hoursWorked !== null && form.hoursWorked < 0) {
    newErrors.hoursWorked = '工作时长必须大于或等于0';
  }

  // 检查状态
  if (!form.status) {
    newErrors.status = '考勤状态是必填项，请选择状态';
  }

  // 检查签到方式
  if (!form.checkInMethod) {
    newErrors.checkInMethod = '签到方式是必填项，请选择签到方式';
  }

  // 检查其他非必填但需验证的字段
  if (form.timeOut && form.timeIn && form.timeOut < form.timeIn) {
    newErrors.timeOut = '下班时间必须晚于上班时间';
  }
  
  // 如果选择了GPS签到方式但没有位置信息
  if (form.checkInMethod === 'gps' && !form.location) {
    newErrors.location = '使用GPS签到时，位置信息是必填项';
  }

  // 验证GPS坐标
  if (form.checkInMethod === 'gps') {
    if (form.latitude !== null && (form.latitude < -90 || form.latitude > 90)) {
      newErrors.latitude = '纬度必须在-90到90之间';
    }
    
    if (form.longitude !== null && (form.longitude < -180 || form.longitude > 180)) {
      newErrors.longitude = '经度必须在-180到180之间';
    }
  }

  Object.assign(errors, newErrors);
  return Object.keys(newErrors).length === 0;
};

// Submit form
const submitForm = async () => {
  // Clear previous errors
  Object.keys(errors).forEach(key => delete errors[key]);

  // Validate form
  if (!validateForm()) {
    return;
  }

  isSubmitting.value = true;

  try {
    let response;
    if (props.formMode === 'add') {
      // Create new attendance record
      response = await attendanceService.createAttendance(form);
    } else {
      // Update existing attendance record
      response = await attendanceService.updateAttendance(props.attendanceData.id, form);
    }

    // Emit success event
    emit('submit-success', response.data);

    // Close the form
    emit('update:show', false);
  } catch (error) {
    console.error('提交考勤记录失败:', error);

    // Handle validation errors from the server
    if (error.response && error.response.data && error.response.data.errors) {
      const serverErrors = error.response.data.errors;
      Object.keys(serverErrors).forEach(key => {
        errors[key] = serverErrors[key];
      });
    } else {
      error.value = props.formMode === 'add' ? '很抱歉，添加考勤记录时遇到了问题。这可能是由于网络连接不稳定或系统临时故障导致的。请您检查输入信息并再次尝试，如果问题持续存在，请联系技术支持团队获取帮助。' : '很抱歉，更新考勤记录时遇到了问题。这可能是由于网络连接不稳定或系统临时故障导致的。请您稍后再次尝试，如果问题持续存在，请联系技术支持团队获取帮助。';
    }
  } finally {
    isSubmitting.value = false;
  }
};

// Reset form to defaults
const resetForm = () => {
  Object.assign(form, {
    projectId: '',
    employeeName: '',
    employeeId: '',
    date: new Date().toISOString().split('T')[0],
    timeIn: '',
    timeOut: '',
    hoursWorked: null,
    status: 'present',
    // 重置签到位置相关字段
    location: '',
    latitude: null,
    longitude: null,
    checkInMethod: 'manual',
    notes: ''
  });

  // Clear all errors
  Object.keys(errors).forEach(key => delete errors[key]);
};

// Close form
const closeForm = () => {
  emit('update:show', false);
};
</script>

<style scoped>
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-label.required:after {
  content: " *";
  @apply text-red-500;
}

.form-input {
  @apply block w-full rounded-md border-gray-300 shadow-sm
    focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50
    transition-all duration-200;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent
    rounded-md shadow-sm text-sm font-medium focus:outline-none
    focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700
    focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-white text-gray-700 border-gray-300
    hover:bg-gray-50 focus:ring-blue-500;
}
</style>