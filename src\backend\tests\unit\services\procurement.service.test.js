const { expect } = require('chai');
const sinon = require('sinon');
const { Procurement, Project, Supplier, User } = require('../../../src/models');
const procurementService = require('../../../src/services/procurement.service');
const ApiError = require('../../../src/utils/ApiError');

describe('Procurement Service', () => {
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('createProcurement', () => {
    it('should create a procurement successfully', async () => {
      // Arrange
      const procurementData = {
        purchaseNumber: 'PO-001',
        supplierId: '1',
        projectId: '1',
        purchaseDate: '2023-01-01',
        totalAmount: 1000,
        status: 'pending',
        items: [
          { name: 'Item 1', quantity: 10, unitPrice: 50, totalPrice: 500 },
          { name: 'Item 2', quantity: 5, unitPrice: 100, totalPrice: 500 }
        ],
        createdBy: '00000000-0000-0000-0000-000000000000'
      };

      sandbox.stub(Project, 'findByPk').resolves({ id: '1', name: 'Test Project' });
      sandbox.stub(Supplier, 'findByPk').resolves({ id: '1', name: 'Test Supplier' });
      const createStub = sandbox.stub(Procurement, 'create').resolves(procurementData);

      // Act
      const result = await procurementService.createProcurement(procurementData);

      // Assert
      expect(createStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(procurementData);
    });

    it('should throw error if project not found', async () => {
      // Arrange
      const procurementData = {
        purchaseNumber: 'PO-001',
        supplierId: '1',
        projectId: '999',
        purchaseDate: '2023-01-01',
        totalAmount: 1000
      };

      sandbox.stub(Project, 'findByPk').resolves(null);

      // Act & Assert
      try {
        await procurementService.createProcurement(procurementData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('Project not found');
      }
    });

    it('should throw error if supplier not found', async () => {
      // Arrange
      const procurementData = {
        purchaseNumber: 'PO-001',
        supplierId: '999',
        projectId: '1',
        purchaseDate: '2023-01-01',
        totalAmount: 1000
      };

      sandbox.stub(Project, 'findByPk').resolves({ id: '1', name: 'Test Project' });
      sandbox.stub(Supplier, 'findByPk').resolves(null);

      // Act & Assert
      try {
        await procurementService.createProcurement(procurementData);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(400);
        expect(error.message).to.include('Supplier not found');
      }
    });
  });

  describe('getProcurementById', () => {
    it('should return procurement if found', async () => {
      // Arrange
      const procurementId = '1';
      const procurementData = {
        id: procurementId,
        purchaseNumber: 'PO-001',
        supplierId: '1',
        projectId: '1',
        purchaseDate: '2023-01-01',
        totalAmount: 1000,
        status: 'pending'
      };

      sandbox.stub(Procurement, 'findByPk').resolves(procurementData);

      // Act
      const result = await procurementService.getProcurementById(procurementId);

      // Assert
      expect(result).to.deep.equal(procurementData);
    });

    it('should throw error if procurement not found', async () => {
      // Arrange
      const procurementId = '999';
      sandbox.stub(Procurement, 'findByPk').resolves(null);

      // Act & Assert
      try {
        await procurementService.getProcurementById(procurementId);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(404);
        expect(error.message).to.include('Procurement not found');
      }
    });
  });

  describe('updateProcurementById', () => {
    it('should update procurement successfully', async () => {
      // Arrange
      const procurementId = '1';
      const procurementData = {
        id: procurementId,
        purchaseNumber: 'PO-001',
        supplierId: '1',
        projectId: '1',
        purchaseDate: '2023-01-01',
        totalAmount: 1000,
        status: 'pending',
        save: sandbox.stub().resolves()
      };

      const updateData = {
        status: 'approved',
        totalAmount: 1200
      };

      sandbox.stub(procurementService, 'getProcurementById').resolves(procurementData);

      // Act
      const result = await procurementService.updateProcurementById(procurementId, updateData);

      // Assert
      expect(result.status).to.equal(updateData.status);
      expect(result.totalAmount).to.equal(updateData.totalAmount);
      expect(procurementData.save.calledOnce).to.be.true;
    });
  });

  describe('deleteProcurementById', () => {
    it('should delete procurement successfully', async () => {
      // Arrange
      const procurementId = '1';
      const procurementData = {
        id: procurementId,
        purchaseNumber: 'PO-001',
        destroy: sandbox.stub().resolves()
      };

      sandbox.stub(procurementService, 'getProcurementById').resolves(procurementData);

      // Act
      await procurementService.deleteProcurementById(procurementId);

      // Assert
      expect(procurementData.destroy.calledOnce).to.be.true;
    });
  });

  describe('queryProcurements', () => {
    it('should return procurements and pagination info', async () => {
      // Arrange
      const filter = { 
        projectId: '1', 
        supplierId: '1',
        status: 'pending'
      };
      const options = { 
        page: 1, 
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };
      
      const procurements = [
        { id: '1', purchaseNumber: 'PO-001', totalAmount: 1000 },
        { id: '2', purchaseNumber: 'PO-002', totalAmount: 2000 }
      ];
      
      const findAndCountAllStub = sandbox.stub(Procurement, 'findAndCountAll').resolves({
        count: 2,
        rows: procurements
      });

      // Act
      const result = await procurementService.queryProcurements(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      expect(result.results).to.deep.equal(procurements);
      expect(result.pagination.totalResults).to.equal(2);
      
      // Verify filter was applied correctly
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause.projectId).to.equal('1');
      expect(whereClause.supplierId).to.equal('1');
      expect(whereClause.status).to.equal('pending');
    });

    it('should handle search filter correctly', async () => {
      // Arrange
      const filter = { search: 'test' };
      const options = { 
        page: 1, 
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };
      
      const findAndCountAllStub = sandbox.stub(Procurement, 'findAndCountAll').resolves({
        count: 0,
        rows: []
      });

      // Act
      await procurementService.queryProcurements(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      // Verify that the where clause includes the search condition
      // This is a simplified check, the actual implementation might be more complex
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause[Symbol.for('sequelize.Op.or')]).to.exist;
    });

    it('should handle date range filters correctly', async () => {
      // Arrange
      const filter = { 
        procurementDateFrom: '2023-01-01',
        procurementDateTo: '2023-12-31'
      };
      const options = { 
        page: 1, 
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      };
      
      const findAndCountAllStub = sandbox.stub(Procurement, 'findAndCountAll').resolves({
        count: 0,
        rows: []
      });

      // Act
      await procurementService.queryProcurements(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      // Verify that the where clause includes the date range conditions
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause.purchaseDate).to.exist;
    });
  });
});
