const { Token, User } = require('../models');
const { faker } = require('@faker-js/faker/locale/zh_CN');
const crypto = require('crypto');

async function seedTokens() {
  try {
    // Clear existing data
    await Token.destroy({ where: {} });

    // Get all users for reference
    const users = await User.findAll();

    if (!users.length) {
      console.log('Please seed users first');
      return;
    }

    const tokens = [];

    // Create tokens for each user
    for (const user of users) {
      // Create 1-2 tokens per user
      const tokenCount = faker.number.int({ min: 1, max: 2 });
      
      for (let i = 0; i < tokenCount; i++) {
        const token = {
          userId: user.id,
          token: crypto.randomBytes(32).toString('hex'),
          type: faker.helpers.arrayElement(['refresh', 'reset', 'verify']),
          expires: faker.date.future(),
          blacklisted: false,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        tokens.push(token);
      }
    }

    await Token.bulkCreate(tokens);
    console.log('Tokens seeder executed successfully');
  } catch (error) {
    console.error('Error seeding tokens:', error);
  }
}

module.exports = seedTokens; 