/**
 * PostgreSQL Utility Functions
 * 
 * This module provides utility functions for PostgreSQL database operations,
 * including schema querying and type mapping.
 */

const { QueryTypes } = require('sequelize');

/**
 * Get table information from PostgreSQL database
 * @param {Object} sequelize - Sequelize instance
 * @param {string} schema - Database schema name
 * @param {string} tableName - Table name
 * @returns {Promise<Object>} Table information including columns and constraints
 */
async function getTableInfo(sequelize, schema, tableName) {
  // Get column information
  const columnResults = await sequelize.query(`
    SELECT
      column_name,
      data_type,
      character_maximum_length,
      is_nullable,
      column_default,
      udt_name
    FROM information_schema.columns
    WHERE table_schema = :schema
    AND table_name = :tableName
  `, {
    replacements: { schema, tableName },
    type: QueryTypes.SELECT
  });

  // Get primary key information
  const pkResults = await sequelize.query(`
    SELECT kcu.column_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    WHERE tc.constraint_type = 'PRIMARY KEY'
      AND tc.table_schema = :schema
      AND tc.table_name = :tableName
  `, {
    replacements: { schema, tableName },
    type: QueryTypes.SELECT
  });

  // Get foreign key information
  const fkResults = await sequelize.query(`
    SELECT
      kcu.column_name,
      ccu.table_name AS foreign_table_name,
      ccu.column_name AS foreign_column_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY'
      AND tc.table_schema = :schema
      AND tc.table_name = :tableName
  `, {
    replacements: { schema, tableName },
    type: QueryTypes.SELECT
  });

  // Get unique constraints
  const uniqueResults = await sequelize.query(`
    SELECT
      kcu.column_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    WHERE tc.constraint_type = 'UNIQUE'
      AND tc.table_schema = :schema
      AND tc.table_name = :tableName
  `, {
    replacements: { schema, tableName },
    type: QueryTypes.SELECT
  });

  

  return {
    columns: columnResults,
    primaryKeys: pkResults,
    foreignKeys: fkResults,
    uniqueConstraints: uniqueResults
  };
}

/**
 * Get all tables in the specified schema
 * @param {Object} sequelize - Sequelize instance
 * @param {string} schema - Database schema name
 * @returns {Promise<Array<string>>} Array of table names
 */
async function getAllTables(sequelize, schema) {
  const results = await sequelize.query(`
    SELECT table_name
    FROM information_schema.tables
    WHERE table_schema = :schema
    AND table_type = 'BASE TABLE'
  `, {
    replacements: { schema },
    type: QueryTypes.SELECT
  });

  return results.map(row => row.table_name);
}

/**
 * Map PostgreSQL data type to Sequelize data type
 * @param {string} postgresType - PostgreSQL data type
 * @param {number} [length] - Length for string types
 * @returns {string} Sequelize data type definition
 */
function mapPostgresTypeToSequelize(postgresType, length) {
  const typeMap = {
    'character varying': 'Sequelize.DataTypes.STRING',
    'varchar': length ? `Sequelize.DataTypes.STRING(${length})` : 'Sequelize.DataTypes.STRING',
    'text': 'Sequelize.DataTypes.TEXT',
    'integer': 'Sequelize.DataTypes.INTEGER',
    'double precision': 'Sequelize.DataTypes.FLOAT',
    'boolean': 'Sequelize.DataTypes.BOOLEAN',
    'uuid': 'Sequelize.DataTypes.UUID',
    'json': 'Sequelize.DataTypes.JSON',
    'jsonb': 'Sequelize.DataTypes.JSONB'
  };

  // Handle array types
  if (postgresType.endsWith('[]')) {
    const baseType = postgresType.slice(0, -2).toUpperCase();
    return `Sequelize.DataTypes.ARRAY(Sequelize.DataTypes.${baseType === 'VARCHAR' ? 'STRING' : baseType})`;
  }

  return typeMap[postgresType] || 'Sequelize.DataTypes.STRING';
}

module.exports = {
  getTableInfo,
  getAllTables,
  mapPostgresTypeToSequelize
};