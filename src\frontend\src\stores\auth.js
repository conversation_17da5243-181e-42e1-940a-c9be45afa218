// auth.js
import { defineStore } from 'pinia';
import axios from 'axios';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    user: null,
    token: null,
    isInitialized: false,
    loading: false,
    error: null
  }),

  getters: {
    isAuthenticated: (state) => !!state.token,
    isAdmin: (state) => state.user && state.user.role === 'admin',
  },

  actions: {
    // Initialize auth from storage
    async init() {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          this.isInitialized = true;
          return;
        }

        // Set token for API calls
        this.token = token;
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

        // Get user data
        await this.fetchUserProfile();

      } catch (error) {
        console.error('Failed to initialize auth:', error);
        this.clearAuth();
      } finally {
        this.isInitialized = true;
      }
    },

    // Login user
    async login(email, password) {
      this.loading = true;
      this.error = null;

      try {
        // Original API call for real authentication
        console.log('Attempting to login with:', email);
        const response = await axios.post('/api/auth/login', { email, password });

        if (response.data && response.data.tokens) {
          // Save token and user data
          const { tokens, user } = response.data;
          const token = tokens.access.token;

          this.token = token;
          this.user = user;

          // Store token in localStorage
          localStorage.setItem('token', token);

          // Set authorization header for future API calls
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

          return { token, user };
        } else {
          throw new Error('Invalid response format from authentication server');
        }

      } catch (error) {
        console.error('Login error:', error);
        this.error = error.message || 'Failed to login. Please check your credentials.';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Register new user
    async register(userData) {
      this.loading = true;
      this.error = null;

      try {
        const response = await axios.post('/api/auth/register', userData);
        return response.data;
      } catch (error) {
        console.error('Registration error:', error);
        this.error = error.response?.data?.details || error.response?.data?.message || 'Failed to register. Please try again.';
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Fetch user profile
    async fetchUserProfile() {
      if (!this.token) return;

      try {
        // For demo purposes - mock user profile
        if (this.token === 'mock-jwt-token-for-demo') {
          const mockUser = {
            id: 1,
            name: 'Admin User',
            email: '<EMAIL>',
            role: 'admin',
            avatar: null
          };
          this.user = mockUser;
          return mockUser;
        }

        // Original API call for real user profile
        const response = await axios.get('/api/auth/me');
        this.user = response.data;
        return response.data;
      } catch (error) {
        console.error('Error fetching user profile:', error);
        // If unauthorized, clear auth
        if (error.response?.status === 401) {
          this.clearAuth();
        }
        throw error;
      }
    },

    // Update user profile
    async updateProfile(profileData) {
      this.loading = true;

      try {
        // 确保只发送后端接受的字段
        const allowedProfileData = {
          name: profileData.name,
          email: profileData.email  // 邮箱必须作为顶层字段
        };

        const response = await axios.patch('/api/users/profile', allowedProfileData);
        this.user = response.data;
        return response.data;
      } catch (error) {
        console.error('Error updating profile:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Change password
    async changePassword(currentPassword, newPassword) {
      this.loading = true;

      try {
        // 直接调用真实的API，移除演示模式逻辑
        const response = await axios.post('/api/auth/change-password', {
          currentPassword,
          newPassword
        });
        return response.data;
      } catch (error) {
        console.error('Error changing password:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Request password reset
    async requestPasswordReset(email) {
      this.loading = true;

      try {
        const response = await axios.post('/api/auth/forgot-password', { email });
        return response.data;
      } catch (error) {
        console.error('Error requesting password reset:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Reset password with token
    async resetPassword(token, password) {
      this.loading = true;

      try {
        const response = await axios.post('/api/auth/reset-password', {
          token,
          password
        });
        return response.data;
      } catch (error) {
        console.error('Error resetting password:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Verify email with token
    async verifyEmail(token) {
      this.loading = true;

      try {
        const response = await axios.post('/api/auth/verify-email', { token });
        return response.data;
      } catch (error) {
        console.error('Error verifying email:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    // Logout
    async logout() {
      try {
        // For demo purposes - simply log the action
        if (this.token === 'mock-jwt-token-for-demo') {
          console.log('Demo user logged out');
        }
        // Call logout API if available and not demo token
        else if (this.token) {
          await axios.post('/api/auth/logout').catch(() => {
            // Ignore error if logout API fails
          });
        }
      } finally {
        // Clear auth data regardless of API call result
        this.clearAuth();
      }
    },

    // Clear authentication data
    clearAuth() {
      this.user = null;
      this.token = null;
      localStorage.removeItem('token');
      delete axios.defaults.headers.common['Authorization'];
    }
  }
});