// @ts-check
import { test, expect } from '@playwright/test';

test.describe('供应商管理流程', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('供应商列表页面', async ({ page }) => {
    // 访问供应商列表页面
    await page.goto('/suppliers');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('供应商列表');

    // 验证供应商表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加供应商按钮存在
    expect(await page.isVisible('a[href="/suppliers/create"]')).toBeTruthy();
  });

  test('创建供应商流程', async ({ page }) => {
    // 访问创建供应商页面
    await page.goto('/suppliers/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('创建供应商');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 填写表单
    await page.fill('input[name="name"]', 'E2E Test Supplier');
    await page.fill('input[name="code"]', `TS-${Date.now()}`);
    await page.selectOption('select[name="category"]', 'material');
    await page.fill('input[name="contactName"]', 'Test Contact');
    await page.fill('input[name="phone"]', '13800138000');
    await page.fill('input[name="address"]', 'Test Address');
    await page.fill('textarea[name="notes"]', 'This is a test supplier created by E2E test');

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到供应商列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/suppliers');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('查看供应商详情', async ({ page }) => {
    // 访问供应商列表页面
    await page.goto('/suppliers');

    // 点击第一个供应商的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/suppliers/');

    // 验证详情页面内容
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('供应商详情');
    expect(await page.isVisible('.supplier-info')).toBeTruthy();
    expect(await page.isVisible('a.edit-button')).toBeTruthy();
  });

  test('编辑供应商流程', async ({ page }) => {
    // 访问供应商列表页面
    await page.goto('/suppliers');

    // 点击第一个编辑按钮
    await page.click('table tbody tr:first-child a.edit-button');

    // 等待页面跳转到编辑页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/suppliers/');
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/edit');

    // 验证表单已预填充
    expect(await page.inputValue('input[name="name"]')).toBeTruthy();

    // 修改供应商名称
    const newName = `Updated Supplier ${Date.now()}`;
    await page.fill('input[name="name"]', newName);

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到供应商列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/suppliers');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('删除供应商流程', async ({ page }) => {
    // 访问供应商列表页面
    await page.goto('/suppliers');

    // 获取供应商数量
    const initialSupplierCount = await page.locator('table tbody tr').count();

    // 点击第一个删除按钮
    await page.click('table tbody tr:first-child button.delete-button');

    // 确认删除
    await page.click('button.confirm-delete');

    // 等待页面刷新
    await page.waitForTimeout(1000);

    // 验证供应商数量减少
    const newSupplierCount = await page.locator('table tbody tr').count();
    expect(newSupplierCount).toBeLessThan(initialSupplierCount);

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('供应商搜索功能', async ({ page }) => {
    // 访问供应商列表页面
    await page.goto('/suppliers');

    // 在搜索框中输入关键字
    await page.fill('input[placeholder*="搜索"]', 'Test');

    // 点击搜索按钮
    await page.click('button.search-button');

    // 等待搜索结果加载
    await page.waitForTimeout(1000);

    // 验证搜索结果
    const supplierNames = await page.locator('table tbody tr td:nth-child(2)').allTextContents();
    for (const name of supplierNames) {
      expect(name.toLowerCase()).toContain('test');
    }
  });

  test('供应商分类筛选功能', async ({ page }) => {
    // 访问供应商列表页面
    await page.goto('/suppliers');

    // 选择分类
    await page.selectOption('select.category-filter', 'material');

    // 等待筛选结果加载
    await page.waitForTimeout(1000);

    // 验证筛选结果
    const supplierCategories = await page.locator('table tbody tr td:nth-child(3)').allTextContents();
    for (const category of supplierCategories) {
      expect(category.toLowerCase()).toContain('material');
    }
  });
});
