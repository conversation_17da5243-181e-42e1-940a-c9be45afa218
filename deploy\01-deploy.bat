@echo off
setlocal
cd %~dp0
cd ..

REM Set environment variables
set DEPLOY_ENV=production
set SERVER_HOST=*************
set SERVER_USER=%user35%
set SERVER_PASSWORD=%pwd35%
set REMOTE_DIR=/root/docker/yihe-local-frontend


set PROJECT_ROOT=%cd%

REM Build frontend
echo Building frontend...
cd "%PROJECT_ROOT%\src\frontend"
call pnpm install
call pnpm build

REM Create deployment package
echo Creating deployment package...
cd "%PROJECT_ROOT%\deploy"
if exist dist rd /s /q dist
mkdir dist
mkdir dist\frontend


REM Copy frontend files
xcopy /E /I "%PROJECT_ROOT%\src\frontend\dist" dist\frontend\dist


REM Deploy to server using pscp
echo Deploying to server...
echo Checking today's backup on remote server...
set TODAY_BACKUP_DIR=%REMOTE_DIR%\%date:~0,4%%date:~5,2%%date:~8,2%

pscp -batch -pw %SERVER_PASSWORD% -r dist\frontend\dist\* %SERVER_USER%@%SERVER_HOST%:%REMOTE_DIR%/current


echo Deployment completed!
endlocal