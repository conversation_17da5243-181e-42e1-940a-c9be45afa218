const Joi = require('joi');
const { uuid } = require('./custom.validation');

// 合同工验证规则
const createContractWorker = {
  body: Joi.object().keys({
    name: Joi.string().required().messages({
      'string.empty': '姓名不能为空',
      'any.required': '姓名是必填项'
    }),
    idNumber: Joi.string().required().messages({
      'string.empty': '身份证号不能为空',
      'any.required': '身份证号是必填项'
    }),
    projectId: Joi.string().uuid().required().messages({
      'string.empty': '项目编号不能为空',
      'string.guid': '项目编号必须是有效的UUID格式',
      'any.required': '项目编号是必填项'
    }),
    contractNumber: Joi.string().required().messages({
      'string.empty': '合同编号不能为空',
      'any.required': '合同编号是必填项'
    }),
    startDate: Joi.date().required().messages({
      'date.base': '开始日期格式不正确',
      'any.required': '开始日期是必填项'
    }),
    endDate: Joi.date().required().messages({
      'date.base': '结束日期格式不正确',
      'any.required': '结束日期是必填项'
    }),
    position: Joi.string().required().messages({
      'string.empty': '职位不能为空',
      'any.required': '职位是必填项'
    }),
    departmentId: Joi.number().required().messages({
      'number.base': '部门必须是数字',
      'any.required': '部门是必填项'
    }),
       department: Joi.string(),
    salary: Joi.number().required().messages({
      'number.base': '薪资必须是数字',
      'any.required': '薪资是必填项'
    }),
    status: Joi.string().valid('active', 'inactive').default('active').messages({
      'any.only': '状态必须是活跃(active)或非活跃(inactive)'
    }),
    contactInfo: Joi.object({
      phone: Joi.string().required().messages({
        'string.empty': '电话号码不能为空',
        'any.required': '电话号码是必填项'
      }),
      email: Joi.string().email().allow(null, '').messages({
        'string.email': '邮箱格式不正确'
      }),
      address: Joi.string().allow(null, '')
    }).required().messages({
      'any.required': '联系信息是必填项'
    }),
    month: Joi.string().pattern(/^\d{4}-\d{2}$/).messages({
      'string.pattern.base': '月份格式必须是YYYY-MM格式'
    })
  })
};

const getContractWorkers = {
  query: Joi.object().keys({
    name: Joi.string().allow(null),
    idNumber: Joi.string().allow(null),
    contractNumber: Joi.string().allow(null),
    department: Joi.string().allow(null),
    status: Joi.string().allow(null),
    search: Joi.string().allow(null),
    month: Joi.string().pattern(/^\d{4}-\d{2}$/).messages({
      'string.pattern.base': '月份格式必须是YYYY-MM格式'
    }),
    sortBy: Joi.string().allow(null),
    limit: Joi.number().integer().messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数'
    }),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    })
  })
};

const getContractWorker = {
  params: Joi.object().keys({
    workerId: Joi.string().uuid().messages({
      'string.empty': '员工编号不能为空',
      'string.guid': '员工编号必须是有效的UUID格式'
    })
  })
};

const updateContractWorker = {
  params: Joi.object().keys({
    workerId: Joi.string().uuid().messages({
      'string.empty': '员工编号不能为空',
      'string.guid': '员工编号必须是有效的UUID格式'
    })
  }),
  body: Joi.object().keys({
    name: Joi.string().messages({
      'string.empty': '姓名不能为空'
    }),
        idNumber: Joi.string().messages({
      'string.empty': '身份证号不能为空'
    }),
      contractNumber: Joi.string().allow(null),
       department: Joi.string(),
           salary: Joi.number().messages({
      'number.base': '薪资必须是数字'
    }),
    projectId: Joi.string().uuid().messages({
      'string.guid': '项目编号必须是有效的UUID格式'
    }),
    startDate: Joi.date().messages({
      'date.base': '开始日期格式不正确'
    }),
    endDate: Joi.date().messages({
      'date.base': '预计结束日期格式不正确'
    }),
    position: Joi.string().messages({
      'string.empty': '职位不能为空'
    }),
    departmentId: Joi.number().messages({
      'number.base': '部门必须是数字'
    }),
    dailyRate: Joi.number().messages({
      'number.base': '日薪必须是数字'
    }),
    status: Joi.string().valid('active', 'inactive').messages({
      'any.only': '状态必须是活跃(active)或非活跃(inactive)'
    }),
    contactInfo: Joi.object({
      phone: Joi.string().messages({
        'string.empty': '电话号码不能为空'
      }),
      email: Joi.string().email().allow(null, '').messages({
        'string.email': '邮箱格式不正确'
      }),
      address: Joi.string().allow(null,'')
    }).messages({
      'object.base': '联系信息必须是一个对象'
    }),
    month: Joi.string().pattern(/^\d{4}-\d{2}$/).messages({
      'string.pattern.base': '月份格式必须是YYYY-MM格式'
    })
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deleteContractWorker = {
  params: Joi.object().keys({
    workerId: Joi.string().uuid().messages({
      'string.empty': '员工编号不能为空',
      'string.guid': '员工编号必须是有效的UUID格式'
    })
  })
};

const createTemporaryWorker = {
  body: Joi.object().keys({
    name: Joi.string().required().messages({
      'string.empty': '姓名不能为空',
      'any.required': '姓名是必填项'
    }),
    idNumber: Joi.string().required().messages({
      'string.empty': '身份证号不能为空',
      'any.required': '身份证号是必填项'
    }),
    projectId: Joi.string().uuid().required().messages({
      'string.empty': '项目编号不能为空',
      'string.guid': '项目编号必须是有效的UUID格式',
      'any.required': '项目编号是必填项'
    }),
    contractNumber: Joi.string().required().messages({
      'string.empty': '合同编号不能为空',
      'any.required': '合同编号是必填项'
    }),
    startDate: Joi.date().required().messages({
      'date.base': '开始日期格式不正确',
      'any.required': '开始日期是必填项'
    }),
    endDate: Joi.date().required().messages({
      'date.base': '结束日期格式不正确',
      'any.required': '结束日期是必填项'
    }),
    position: Joi.string().required().messages({
      'string.empty': '职位不能为空',
      'any.required': '职位是必填项'
    }),
    departmentId: Joi.number().messages({
      'number.base': '部门必须是数字'
    }),
    department: Joi.string().required().messages({
      'string.empty': '部门不能为空',
      'any.required': '部门是必填项'
    }),
    salary: Joi.number().messages({
      'number.base': '薪资必须是数字'
    }),
    dailyRate: Joi.number().messages({
      'number.base': '日薪必须是数字'
    }),
    status: Joi.string().valid('active', 'inactive').default('active').messages({
      'any.only': '状态必须是活跃(active)或非活跃(inactive)'
    }),
    contactInfo: Joi.object({
      phone: Joi.string().required().messages({
        'string.empty': '电话号码不能为空',
        'any.required': ''
      }),
      email: Joi.string().email().allow(null, '').messages({
        'string.email': '邮箱格式不正确'
      }),
      address: Joi.string().allow(null, '')
    }).messages({
      'any.required': '联系信息是必填项'
    })
  })
};

const getTemporaryWorkers = {
  query: Joi.object().keys({
    name: Joi.string().allow(null),
    idNumber: Joi.string().allow(null),
    projectId: Joi.string().messages({
      'string.guid': '项目编号必须是有效的UUID格式'
    }),
    department: Joi.string().allow(null),
    status: Joi.string().allow(null),
    search: Joi.string().allow(null),
    sortBy: Joi.string().allow(null),
    limit: Joi.number().integer().messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数'
    }),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    })
  })
};

const getTemporaryWorker = {
  params: Joi.object().keys({
    workerId: Joi.string().uuid().messages({
      'string.empty': '临时工编号不能为空',
      'string.guid': '临时工编号必须是有效的UUID格式'
    })
  })
};

const updateTemporaryWorker = {
  params: Joi.object().keys({
    workerId: Joi.string().uuid().messages({
      'string.empty': '临时工编号不能为空',
      'string.guid': '临时工编号必须是有效的UUID格式'
    })
  }),
  body: Joi.object().keys({
    name: Joi.string().messages({
      'string.empty': '姓名不能为空'
    }),
    idNumber: Joi.string().messages({
      'string.empty': '身份证号不能为空'
    }),

    contractNumber: Joi.string().allow(null),
    projectId: Joi.string().uuid().messages({
      'string.guid': '项目编号必须是有效的UUID格式'
    }),
    startDate: Joi.date().messages({
      'date.base': '开始日期格式不正确'
    }),
    endDate: Joi.date().messages({
      'date.base': '预计结束日期格式不正确'
    }),
    position: Joi.string().messages({
      'string.empty': '职位不能为空'
    }),
    department: Joi.string().messages({
      'string.empty': '部门不能为空'
    }),
    departmentId: Joi.number().messages({
      'number.base': '部门必须是数字'
    }),
    dailyRate: Joi.number().messages({
      'number.base': '日薪必须是数字'
    }),
    status: Joi.string().valid('active', 'inactive').messages({
      'any.only': '状态必须是活跃(active)或非活跃(inactive)'
    }),
    contactInfo: Joi.object({
      phone: Joi.string().messages({
        'string.empty': '电话号码不能为空'
      }),
      email: Joi.string().email().allow(null, '').messages({
        'string.email': '邮箱格式不正确'
      }),
      address: Joi.string().allow(null, '')
    }).messages({
      'object.base': '联系信息必须是一个对象'
    })
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deleteTemporaryWorker = {
  params: Joi.object().keys({
    workerId: Joi.string().uuid().messages({
      'string.empty': '临时工编号不能为空',
      'string.guid': '临时工编号必须是有效的UUID格式'
    })
  })
};

// 获取所有员工的验证规则
const getAllEmployees = {
  query: Joi.object().keys({
    name: Joi.string().allow(null),
    idNumber: Joi.string().allow(null),
    employeeType: Joi.string().valid('contract', 'temporary'),
    department: Joi.string().allow(null),
    status: Joi.string().allow(null),
    search: Joi.string().allow(null),
    month: Joi.string().pattern(/^\d{4}-\d{2}$/).messages({
      'string.pattern.base': '月份格式必须是YYYY-MM格式'
    }),
    sortBy: Joi.string().allow(null),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    }),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    })
  })
};

module.exports = {
  createContractWorker,
  getContractWorkers,
  getContractWorker,
  updateContractWorker,
  deleteContractWorker,
  createTemporaryWorker,
  getTemporaryWorkers,
  getTemporaryWorker,
  updateTemporaryWorker,
  deleteTemporaryWorker,
  getAllEmployees
};