const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * 用户消息模型
 * 用于存储系统发送给用户的消息通知
 */
const UserMessage = sequelize.define('usermessage', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'user',
      key: 'id'
    },
    field: 'userId'
  },
  title: {
    type: DataTypes.STRING,
    allowNull: false
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  isRead: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  type: {
    type: DataTypes.STRING,
    allowNull: false,
    defaultValue: 'system',
    comment: '消息类型：system-系统消息, task-任务消息, notification-通知'
  },
  sourceId: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: '消息来源ID，可以是任务ID、项目ID等'
  },
  sourceType: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: '消息来源类型，如task, project等'
  }
}, {
  timestamps: true,
  freezeTableName: true
});

// 定义关联关系
const setupAssociations = (models) => {
  const { User } = models;

  UserMessage.belongsTo(User, {
    foreignKey: 'userId',
    as: 'User'
  });
};

module.exports = { UserMessage, setupAssociations };
