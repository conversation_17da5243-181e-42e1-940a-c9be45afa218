<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">员工管理</h1>
        <p class="page-subtitle">管理和跟踪所有员工信息</p>
      </div>
      <div class="flex space-x-2">
        <router-link to="/employees/contract/add" class="btn btn-primary flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          添加合同工
        </router-link>
        <router-link to="/employees/temporary/add" class="btn btn-secondary flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          添加临时工
        </router-link>
      </div>
    </div>
    <!-- 查询区域 -->
    <div class="filter-card mb-6">
      <div class="flex justify-between gap-4">
        <div class="flex-1">
          <label for="search" class="form-label font-medium">姓名/身份证号</label>
          <div class="relative">
            <input
              type="text"
              id="search"
              v-model="filters.search"
              class="form-input pl-10 py-2 mt-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
              placeholder="请输入姓名或身份证号"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <label for="employeeType" class="form-label font-medium">员工类型</label>
          <select
            id="employeeType"
            v-model="filters.employeeType"
            class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
          >
            <option value="">全部类型</option>
            <option value="contract">合同工</option>
            <option value="temporary">临时工</option>
          </select>
        </div>
        <div class="flex-1">
          <label for="department" class="form-label font-medium">部门</label>
          <select
            id="department"
            v-model="filters.department"
            class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
          >
            <option value="">全部部门</option>
            <option v-for="dept in departmentOptions" :key="dept.value" :value="dept.value">
              {{ dept.label }}
            </option>
          </select>
        </div>
        <div class="flex-1">
          <label for="status" class="form-label font-medium">状态</label>
          <select
            id="status"
            v-model="filters.status"
            class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
          >
            <option value="">全部状态</option>
            <option value="active">在职</option>
            <option value="inactive">离职</option>
          </select>
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button @click="resetFilters" class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200">重置</button>
        <button @click="fetchEmployees" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200">查询</button>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6">
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="font-medium">错误：</span>
            <span class="ml-2">{{ error }}</span>
          </div>
          <button @click="fetchEmployees()" class="text-red-700 hover:text-red-900 font-medium">
            重试
          </button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="card overflow-hidden shadow-md">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">姓名</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">身份证号</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系电话</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">职位</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工类型</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-if="employees.length === 0">
            <td colspan="8" class="px-6 py-10 text-center text-gray-500">
              <div class="empty-state">
                <svg class="empty-state-icon mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <h3 class="empty-state-title">暂无员工数据</h3>
                <p class="empty-state-description">没有找到符合条件的员工记录，请尝试调整筛选条件或添加新员工。</p>
              </div>
            </td>
          </tr>
          <tr v-for="employee in employees" :key="employee.id" class="hover:bg-gray-50 transition-colors duration-150">
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ employee.name }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ employee.idNumber }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ employee.contactInfo ? employee.contactInfo.phone : '无' }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ employee.department }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              {{ employee.position }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <span class="badge" :class="{
                'badge-blue': employee.employeeType === 'contract',
                'badge-green': employee.employeeType === 'temporary'
              }">
                {{ getEmployeeTypeLabel(employee.employeeType) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
              <span class="badge" :class="{
                'badge-green': employee.status === 'active',
                'badge-red': employee.status === 'inactive'
              }">
                {{ getStatusLabel(employee.status) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <router-link
                :to="getEditRoute(employee)"
                class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
              >
                <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                </svg>
                <span class="font-medium">编辑</span>
              </router-link>
              <button
                @click="confirmDelete(employee)"
                class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
              >
                <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                </svg>
                <span class="font-medium">删除</span>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页 -->
    <div v-if="!loading && !error && pagination.totalPages > 1" class="px-6 py-4 bg-white border-t mt-0">
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-700">
          共 {{ pagination.totalResults }} 条记录
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="handlePageChange(pagination.currentPage - 1)"
            :disabled="pagination.currentPage === 1"
            class="px-3 py-1 border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            上一页
          </button>
          <span class="px-3 py-1">
            第 {{ pagination.currentPage }} 页 / 共 {{ pagination.totalPages }} 页
          </span>
          <button
            @click="handlePageChange(pagination.currentPage + 1)"
            :disabled="pagination.currentPage === pagination.totalPages"
            class="px-3 py-1 border rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { useDepartmentStore } from '@/stores/department';
import apiService from '@/services/apiService';

const router = useRouter();
const departmentStore = useDepartmentStore();

// 状态变量
const loading = ref(false);
const error = ref(null);
const employees = ref([]);
const filters = ref({
  search: '',
  employeeType: '',
  department: '',
  status: ''
});
const pagination = ref({
  currentPage: 1,
  totalPages: 0,
  totalResults: 0,
  limit: 10
});

// 部门选项
const departmentOptions = computed(() => {
  return departmentStore.departments.map(dept => ({
    value: dept.name,
    label: dept.name
  }));
});

// 分页范围
const paginationRange = computed(() => {
  const range = [];
  const start = Math.max(1, pagination.value.currentPage - 2);
  const end = Math.min(pagination.value.totalPages, pagination.value.currentPage + 2);

  for (let i = start; i <= end; i++) {
    range.push(i);
  }

  return range;
});

// 获取员工列表
const fetchEmployees = async () => {
  loading.value = true;
  error.value = null;

  try {
    const params = {
      page: pagination.value.currentPage,
      limit: pagination.value.limit
    };

    if (filters.value.search) {
      params.search = filters.value.search;
    }

    if (filters.value.employeeType) {
      params.employeeType = filters.value.employeeType;
    }

    if (filters.value.department) {
      params.department = filters.value.department;
    }

    if (filters.value.status) {
      params.status = filters.value.status;
    }

    const response = await apiService.getAllEmployees(params);

    employees.value = response.results || [];
    pagination.value = {
      currentPage: response.page || 1,
      totalPages: response.totalPages || 0,
      totalResults: response.totalResults || 0,
      limit: response.limit || 10
    };
  } catch (err) {
    console.error('获取员工数据失败:', err);
    error.value = err.response?.data?.message || '获取员工数据失败，请重试';
    employees.value = [];
  } finally {
    loading.value = false;
  }
};

// 重置筛选条件
const resetFilters = () => {
  filters.value = {
    search: '',
    employeeType: '',
    department: '',
    status: ''
  };
  pagination.value.currentPage = 1;
  fetchEmployees();
};

// 处理页码变化
const handlePageChange = (page) => {
  if (page < 1 || page > pagination.value.totalPages) {
    return;
  }
  pagination.value.currentPage = page;
  fetchEmployees();
};

// 确认删除
const confirmDelete = (employee) => {
  if (confirm(`确定要删除员工 ${employee.name} 吗？此操作不可撤销。`)) {
    deleteEmployee(employee);
  }
};

// 删除员工
const deleteEmployee = async (employee) => {
  try {
    if (employee.employeeType === 'contract') {
      await apiService.deleteContractWorker(employee.id);
    } else {
      await apiService.deleteTemporaryWorker(employee.id);
    }

    // 删除成功后刷新列表
    fetchEmployees();
  } catch (err) {
    console.error('删除员工失败:', err);
    error.value = '删除员工失败: ' + (err.response?.data?.message || '请重试');

    // 5秒后自动清除错误信息
    setTimeout(() => {
      error.value = null;
    }, 5000);
  }
};

// 获取编辑路由
const getEditRoute = (employee) => {
  return employee.employeeType === 'contract'
    ? `/employees/contract/edit/${employee.id}`
    : `/employees/temporary/edit/${employee.id}`;
};

// 获取员工类型标签
const getEmployeeTypeLabel = (type) => {
  const types = {
    'contract': '合同工',
    'temporary': '临时工'
  };
  return types[type] || '未知';
};

// 获取员工类型徽章样式
const getEmployeeTypeBadgeClass = (type) => {
  const classes = {
    'contract': 'bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs',
    'temporary': 'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs'
  };
  return classes[type] || 'bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs';
};

// 获取状态标签
const getStatusLabel = (status) => {
  const statuses = {
    'active': '在职',
    'inactive': '离职'
  };
  return statuses[status] || '未知';
};

// 获取状态徽章样式
const getStatusBadgeClass = (status) => {
  const classes = {
    'active': 'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs',
    'inactive': 'bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs'
  };
  return classes[status] || 'bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs';
};

// 在组件挂载时获取数据
onMounted(() => {
  departmentStore.fetchDepartments();
  fetchEmployees();
});
</script>
