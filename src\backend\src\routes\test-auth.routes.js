const express = require('express');
const testAuthController = require('../controllers/test-auth.controller');

const router = express.Router();

/**
 * @route POST /api/test/auth/login
 * @description Test login route
 * @access Public
 */
router.post('/login', testAuthController.login);

/**
 * @route GET /api/test/auth/verify
 * @description Test token verification route
 * @access Public
 */
router.get('/verify', testAuthController.verifyToken);

module.exports = router;
