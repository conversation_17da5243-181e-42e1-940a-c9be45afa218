const Joi = require('joi');
const { objectId, dateFormat } = require('./custom.validation');

const createTemporary = {
  body: Joi.object().keys({
    name: Joi.string().required().messages({
      'string.empty': '姓名不能为空',
      'any.required': '姓名是必填项'
    }),
    email: Joi.string().required().email().messages({
      'string.empty': '邮箱不能为空',
      'string.email': '邮箱格式不正确',
      'any.required': '邮箱是必填项'
    }),
    phone: Joi.string().required().messages({
      'string.empty': '电话号码不能为空',
      'any.required': '电话号码是必填项'
    }),
    position: Joi.string().required().messages({
      'string.empty': '职位不能为空',
      'any.required': '职位是必填项'
    }),
    department: Joi.string().required().messages({
      'string.empty': '部门不能为空',
      'any.required': '部门是必填项'
    }),
    startDate: Joi.date().custom(dateFormat).required().messages({
      'date.base': '开始日期格式不正确',
      'any.custom': '日期必须使用年-月-日格式 (YYYY-MM-DD)',
      'any.required': '开始日期是必填项'
    }),
    endDate: Joi.date().custom(dateFormat).required().messages({
      'date.base': '结束日期格式不正确',
      'any.custom': '日期必须使用年-月-日格式 (YYYY-MM-DD)',
      'any.required': '结束日期是必填项'
    }),
    shiftType: Joi.string().required().valid('day', 'night', 'flexible').messages({
      'string.empty': '班次类型不能为空',
      'any.only': '班次类型必须是白班(day)、夜班(night)或灵活班次(flexible)',
      'any.required': '班次类型是必填项'
    }),
    hourlyRate: Joi.number().required().messages({
      'number.base': '小时工资必须是数字',
      'any.required': '小时工资是必填项'
    }),
    maxHoursPerWeek: Joi.number().required().messages({
      'number.base': '每周最大工时必须是数字',
      'any.required': '每周最大工时是必填项'
    }),
    agency: Joi.string().required().messages({
      'string.empty': '中介机构不能为空',
      'any.required': '中介机构是必填项'
    }),
    status: Joi.string().required().valid('active', 'inactive', 'completed').messages({
      'string.empty': '状态不能为空',
      'any.only': '状态必须是活跃(active)、非活跃(inactive)或已完成(completed)',
      'any.required': '状态是必填项'
    })
  }),
};

const getTemporaries = {
  query: Joi.object().keys({
    name: Joi.string().allow(null),
    position: Joi.string().allow(null),
    department: Joi.string().allow(null),
    agency: Joi.string().allow(null),
    status: Joi.string().allow(null),
    sortBy: Joi.string().allow(null),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    }),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    }),
  }),
};

const getTemporary = {
  params: Joi.object().keys({
    workerId: Joi.string().custom(objectId).messages({
      'string.empty': '临时工ID不能为空',
      'any.custom': '临时工ID必须是有效的ObjectID格式'
    }),
  }),
};

const updateTemporary = {
  params: Joi.object().keys({
    workerId: Joi.string().custom(objectId).messages({
      'string.empty': '临时工ID不能为空',
      'any.custom': '临时工ID必须是有效的ObjectID格式'
    }),
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().messages({
        'string.empty': '姓名不能为空'
      }),
      email: Joi.string().email().messages({
        'string.empty': '邮箱不能为空',
        'string.email': '邮箱格式不正确'
      }),
      phone: Joi.string().messages({
        'string.empty': '电话号码不能为空'
      }),
      position: Joi.string().messages({
        'string.empty': '职位不能为空'
      }),
      department: Joi.string().messages({
        'string.empty': '部门不能为空'
      }),
      startDate: Joi.date().custom(dateFormat).messages({
        'date.base': '开始日期格式不正确',
        'any.custom': '日期必须使用年-月-日格式 (YYYY-MM-DD)'
      }),
      endDate: Joi.date().custom(dateFormat).messages({
        'date.base': '结束日期格式不正确',
        'any.custom': '日期必须使用年-月-日格式 (YYYY-MM-DD)'
      }),
      shiftType: Joi.string().valid('day', 'night', 'flexible').messages({
        'string.empty': '班次类型不能为空',
        'any.only': '班次类型必须是白班(day)、夜班(night)或灵活班次(flexible)'
      }),
      hourlyRate: Joi.number().messages({
        'number.base': '小时工资必须是数字'
      }),
      maxHoursPerWeek: Joi.number().messages({
        'number.base': '每周最大工时必须是数字'
      }),
      agency: Joi.string().messages({
        'string.empty': '中介机构不能为空'
      }),
      status: Joi.string().valid('active', 'inactive', 'completed').messages({
        'string.empty': '状态不能为空',
        'any.only': '状态必须是活跃(active)、非活跃(inactive)或已完成(completed)'
      })
    })
    .min(1).messages({
      'object.min': '至少需要提供一个要更新的字段'
    }),
};

const deleteTemporary = {
  params: Joi.object().keys({
    workerId: Joi.string().custom(objectId).messages({
      'string.empty': '临时工ID不能为空',
      'any.custom': '临时工ID必须是有效的ObjectID格式'
    }),
  }),
};

module.exports = {
  createTemporary,
  getTemporaries,
  getTemporary,
  updateTemporary,
  deleteTemporary,
};