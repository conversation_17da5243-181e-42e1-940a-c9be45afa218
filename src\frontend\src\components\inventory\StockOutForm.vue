<template>
  <div class="p-5">
    <form @submit.prevent="onSubmit">
      <div class="space-y-4">
        <!-- 产品信息展示 -->
        <div class="bg-gray-50 p-3 rounded-lg">
          <div class="flex items-center">
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-900">{{ item?.name || '' }}</p>
              <p class="text-xs text-gray-500">{{ item?.specification || '' }}</p>
            </div>
            <div class="text-right">
              <p class="text-sm font-medium text-gray-500">当前库存</p>
              <p class="text-lg font-bold">{{ item?.stock || 0 }} <span class="text-xs">{{ item?.unit || '' }}</span></p>
            </div>
          </div>
        </div>

        <!-- 出库数量 -->
        <div>
          <label for="outbound-quantity" class="block text-sm font-medium text-gray-700 mb-1">出库数量 <span class="text-red-500">*</span></label>
          <div class="flex">
            <input
              id="outbound-quantity"
              v-model.number="formData.quantity"
              type="number"
              min="1"
              :max="item?.stock || 0"
              class="flex-1 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              required
            >
            <span class="ml-2 inline-flex items-center px-3 rounded-md border border-gray-300 bg-gray-50 text-gray-500">
              {{ item?.unit || '' }}
            </span>
          </div>
          <p v-if="formData.quantity > (item?.stock || 0)" class="mt-1 text-sm text-red-600">
            出库数量不能超过当前库存
          </p>
        </div>

        <!-- 关联项目 -->
        <div>
          <label for="project" class="block text-sm font-medium text-gray-700 mb-1">关联项目 <span class="text-red-500">*</span></label>
          <select
            id="project"
            v-model="formData.projectId"
            class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            required
          >
            <option value="">选择项目</option>
            <option v-for="project in projectOptions" :key="project.id" :value="project.id">
              {{ project.name }}
            </option>
          </select>
          <p v-if="projectOptions.length === 0" class="mt-1 text-sm text-gray-500">
            加载项目列表中...
          </p>
        </div>

        <!-- 仓库位置 -->
        <div>
          <label for="outbound-warehouse" class="block text-sm font-medium text-gray-700 mb-1">出库仓位 <span class="text-red-500">*</span></label>
          <select
            id="outbound-warehouse"
            v-model="formData.warehouseLocation"
            class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            required
          >
            <option value="">选择仓库位置</option>
            <option v-for="(location, index) in item?.locations || []" :key="index"
              :value="`${location.warehouse}-${location.area}-${location.shelf}`"
              :disabled="location.quantity < formData.quantity">
              {{ location.warehouse }}-{{ location.area }}-{{ location.shelf }}
              (可用: {{ location.quantity }} {{ item?.unit || '' }})
            </option>
          </select>
        </div>

        <!-- 用途 -->
        <div>
          <label for="purpose" class="block text-sm font-medium text-gray-700 mb-1">用途 <span class="text-red-500">*</span></label>
          <select
            id="purpose"
            v-model="formData.purpose"
            class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            required
          >
            <option value="">选择用途</option>
            <option value="生产">生产</option>
            <option value="维修">维修</option>
            <option value="样品">样品</option>
            <option value="销售">销售</option>
            <option value="其他">其他</option>
          </select>
        </div>

        <!-- 领用人 -->
        <div>
          <label for="recipient" class="block text-sm font-medium text-gray-700 mb-1">领用人 <span class="text-red-500">*</span></label>
          <input
            id="recipient"
            v-model="formData.recipient"
            type="text"
            class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            required
          >
        </div>

        <!-- 备注 -->
        <div>
          <label for="outbound-remarks" class="block text-sm font-medium text-gray-700 mb-1">备注</label>
          <textarea
            id="outbound-remarks"
            v-model="formData.remarks"
            rows="2"
            class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
          ></textarea>
        </div>
      </div>

      <div class="mt-6 flex justify-end space-x-3">
        <button
          type="button"
          @click="onCancel"
          class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          取消
        </button>
        <button
          type="submit"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          :disabled="loading || formData.quantity > (item?.stock || 0)"
        >
          {{ loading ? '处理中...' : '确认出库' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue';
import axios from 'axios';

export default {
  name: 'StockOutForm',
  props: {
    item: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['submit', 'cancel'],
  setup(props, { emit }) {
    // 表单数据
    const formData = reactive({
      quantity: 1,
      projectId: '',
      warehouseLocation: '',
      purpose: '',
      recipient: '',
      remarks: ''
    });

    // 项目选项
    const projectOptions = ref([]);

    // 获取项目列表
    const fetchProjects = async () => {
      try {
        const response = await axios.get('/api/projects', {
          params: {
            status: 'in_progress',  // 只获取进行中的项目
            limit: 100  // 获取足够多的项目
          }
        });

        if (response.data && Array.isArray(response.data.results)) {
          projectOptions.value = response.data.results.map(project => ({
            id: project.id || project._id,
            name: project.name
          }));
        } else if (response.data && Array.isArray(response.data)) {
          projectOptions.value = response.data.map(project => ({
            id: project.id || project._id,
            name: project.name
          }));
        } else {
          console.warn('未找到项目数据');
          // 添加测试数据
          projectOptions.value = [
            { id: 'project1', name: '南京某商场改造工程' },
            { id: 'project2', name: '上海某办公楼项目' },
            { id: 'project3', name: '北京某小区维修工程' }
          ];
        }
      } catch (err) {
        console.error('获取项目列表失败', err);
        // 添加测试数据
        projectOptions.value = [
          { id: 'project1', name: '南京某商场改造工程' },
          { id: 'project2', name: '上海某办公楼项目' },
          { id: 'project3', name: '北京某小区维修工程' }
        ];
      }
    };

    // 提交表单
    const onSubmit = () => {
      // 查找选中的项目
      const project = projectOptions.value.find(p => p.id === formData.projectId);
      
      // 组装提交数据
      const submitData = {
        ...formData,
        projectName: project ? project.name : ''
      };

      emit('submit', submitData);
    };

    // 取消操作
    const onCancel = () => {
      emit('cancel');
    };

    // 组件挂载时获取项目列表
    onMounted(() => {
      fetchProjects();
    });

    return {
      formData,
      projectOptions,
      onSubmit,
      onCancel
    };
  }
};
</script> 