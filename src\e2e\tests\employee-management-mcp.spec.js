/**
 * Employee Management Tests with MCP and Auto-fixing
 */

const { test, expect } = require('../fixtures/test-fixtures');
const { navigateTo, fillForm, verifyTable, waitForNetworkIdle } = require('../utils/test-helpers');

test.describe('员工管理测试 (MCP)', () => {
  test.beforeEach(async ({ page }) => {
    // 使用自动修复的登录
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // 等待导航完成，如果失败会自动修复
    try {
      await page.waitForURL('/dashboard');
    } catch (error) {
      console.log('登录失败，尝试自动修复...');
      // 检查是否有错误消息
      const errorMessage = await page.textContent('.error-message, .text-red-500, .text-red-600, .text-red-700');
      if (errorMessage) {
        console.log(`检测到错误: ${errorMessage}`);
        // 尝试使用不同的凭据
        await page.fill('input[name="email"]', '<EMAIL>');
        await page.fill('input[name="password"]', 'password123');
        await page.click('button[type="submit"]');
        await page.waitForURL('/dashboard');
      }
    }
  });

  test('合同工管理页面测试', async ({ page }) => {
    // 导航到合同工管理页面
    await navigateTo(page, '/employees/contract', '合同工管理');
    
    // 验证页面元素
    await verifyTable(page, 'table', ['姓名', '身份证号', '操作'], false);
    
    // 验证添加按钮
    const addButton = page.locator('button:has-text("添加合同工")');
    await expect(addButton).toBeVisible();
    
    // 测试搜索功能
    await page.fill('input#search', '测试');
    await page.click('button:has-text("搜索")');
    await waitForNetworkIdle(page);
    
    // 测试重置功能
    await page.click('button:has-text("重置")');
    await waitForNetworkIdle(page);
  });
  
  test('添加合同工测试', async ({ page }) => {
    // 导航到合同工管理页面
    await navigateTo(page, '/employees/contract', '合同工管理');
    
    // 点击添加按钮
    await page.click('button:has-text("添加合同工")');
    
    // 验证是否导航到添加页面
    await expect(page).toHaveURL(/\/employees\/contract\/add/);
    
    // 填写表单
    const formData = {
      employeeName: '测试员工',
      idNumber: '110101199001011234',
      phone: '13800138000',
      joinDate: new Date().toISOString().split('T')[0],
      workType: '普通工人',
      dailyWage: '200',
      remarks: '自动化测试创建'
    };
    
    // 填写表单字段
    await page.fill('input[id="employeeName"]', formData.employeeName);
    await page.fill('input[id="idNumber"]', formData.idNumber);
    await page.fill('input[id="phone"]', formData.phone);
    await page.fill('input[id="joinDate"]', formData.joinDate);
    await page.fill('input[id="workType"]', formData.workType);
    await page.fill('input[id="dailyWage"]', formData.dailyWage);
    await page.fill('textarea[id="remarks"]', formData.remarks);
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 检查是否有错误，如果有则自动修复
    const errorMessage = await page.textContent('.error-message, .text-red-500, .text-red-600, .text-red-700');
    if (errorMessage) {
      console.log(`提交表单时检测到错误: ${errorMessage}`);
      
      // 检查是否是身份证号重复错误
      if (errorMessage.includes('身份证') || errorMessage.includes('ID number')) {
        // 修改身份证号并重试
        const newIdNumber = '110101199001' + Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        await page.fill('input[id="idNumber"]', newIdNumber);
        await page.click('button[type="submit"]');
      }
    }
    
    // 验证是否返回列表页面
    await page.waitForURL('/employees/contract');
  });
  
  test('临时工管理页面测试', async ({ page }) => {
    // 导航到临时工管理页面
    await navigateTo(page, '/employees/temporary', '临时工管理');
    
    // 验证页面元素
    await verifyTable(page, 'table', ['姓名', '身份证号', '操作'], false);
    
    // 验证添加按钮
    const addButton = page.locator('button:has-text("添加临时工")');
    await expect(addButton).toBeVisible();
    
    // 测试搜索功能
    await page.fill('input#search', '测试');
    await page.click('button:has-text("搜索")');
    await waitForNetworkIdle(page);
    
    // 测试重置功能
    await page.click('button:has-text("重置")');
    await waitForNetworkIdle(page);
  });
  
  test('添加临时工测试', async ({ page }) => {
    // 导航到临时工管理页面
    await navigateTo(page, '/employees/temporary', '临时工管理');
    
    // 点击添加按钮
    await page.click('button:has-text("添加临时工")');
    
    // 验证是否导航到添加页面
    await expect(page).toHaveURL(/\/employees\/temporary\/add/);
    
    // 填写表单
    const formData = {
      name: '临时测试员工',
      idNumber: '110101199001011235',
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      position: '临时工人',
      department: '施工部',
      salary: '150',
      'contactInfo.phone': '13900139000',
      'contactInfo.email': '<EMAIL>'
    };
    
    // 填写表单字段
    await page.fill('input[id="name"]', formData.name);
    await page.fill('input[id="idNumber"]', formData.idNumber);
    await page.fill('input[id="startDate"]', formData.startDate);
    await page.fill('input[id="endDate"]', formData.endDate);
    await page.fill('input[id="position"]', formData.position);
    await page.fill('input[id="department"]', formData.department);
    await page.fill('input[id="salary"]', formData.salary);
    await page.fill('input[id="contactInfo.phone"]', formData['contactInfo.phone']);
    await page.fill('input[id="contactInfo.email"]', formData['contactInfo.email']);
    
    // 提交表单
    await page.click('button[type="submit"]');
    
    // 检查是否有错误，如果有则自动修复
    const errorMessage = await page.textContent('.error-message, .text-red-500, .text-red-600, .text-red-700');
    if (errorMessage) {
      console.log(`提交表单时检测到错误: ${errorMessage}`);
      
      // 检查是否是身份证号重复错误
      if (errorMessage.includes('身份证') || errorMessage.includes('ID number')) {
        // 修改身份证号并重试
        const newIdNumber = '110101199001' + Math.floor(Math.random() * 10000).toString().padStart(4, '0');
        await page.fill('input[id="idNumber"]', newIdNumber);
        await page.click('button[type="submit"]');
      }
    }
    
    // 验证是否返回列表页面
    await page.waitForURL('/employees/temporary');
  });
});
