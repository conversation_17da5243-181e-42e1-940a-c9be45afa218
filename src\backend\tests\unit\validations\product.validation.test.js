const { expect } = require('chai');
const { validate } = require('../../../src/validations/product.validation');

describe('Product Validation', () => {
  describe('createProduct', () => {
    it('should validate a valid product', () => {
      const validProduct = {
        name: 'Test Product',
        code: 'TP001',
        category: 'electronics',
        description: 'Test description',
        price: 100,
        unit: '个',
        stock: 10,
        minStock: 5,
        status: 'active'
      };

      const { error } = validate(validProduct, 'createProduct');
      expect(error).to.be.undefined;
    });

    it('should require name field', () => {
      const invalidProduct = {
        code: 'TP001',
        category: 'electronics',
        price: 100,
        unit: '个',
        stock: 10
      };

      const { error } = validate(invalidProduct, 'createProduct');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('name');
    });

    it('should require code field', () => {
      const invalidProduct = {
        name: 'Test Product',
        category: 'electronics',
        price: 100,
        unit: '个',
        stock: 10
      };

      const { error } = validate(invalidProduct, 'createProduct');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('code');
    });

    it('should require category field', () => {
      const invalidProduct = {
        name: 'Test Product',
        code: 'TP001',
        price: 100,
        unit: '个',
        stock: 10
      };

      const { error } = validate(invalidProduct, 'createProduct');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('category');
    });

    it('should require price to be a number', () => {
      const invalidProduct = {
        name: 'Test Product',
        code: 'TP001',
        category: 'electronics',
        price: 'invalid',
        unit: '个',
        stock: 10
      };

      const { error } = validate(invalidProduct, 'createProduct');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('price');
    });

    it('should require price to be non-negative', () => {
      const invalidProduct = {
        name: 'Test Product',
        code: 'TP001',
        category: 'electronics',
        price: -10,
        unit: '个',
        stock: 10
      };

      const { error } = validate(invalidProduct, 'createProduct');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('price');
    });

    it('should require stock to be a number', () => {
      const invalidProduct = {
        name: 'Test Product',
        code: 'TP001',
        category: 'electronics',
        price: 100,
        unit: '个',
        stock: 'invalid'
      };

      const { error } = validate(invalidProduct, 'createProduct');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('stock');
    });

    it('should allow optional fields to be missing', () => {
      const minimalProduct = {
        name: 'Test Product',
        code: 'TP001',
        category: 'electronics',
        price: 100,
        unit: '个',
        stock: 10
      };

      const { error } = validate(minimalProduct, 'createProduct');
      expect(error).to.be.undefined;
    });

    it('should validate status field when provided', () => {
      const invalidProduct = {
        name: 'Test Product',
        code: 'TP001',
        category: 'electronics',
        price: 100,
        unit: '个',
        stock: 10,
        status: 'invalid'
      };

      const { error } = validate(invalidProduct, 'createProduct');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('status');
    });
  });

  describe('updateProduct', () => {
    it('should validate a valid product update', () => {
      const validUpdate = {
        name: 'Updated Product',
        price: 150,
        description: 'Updated description'
      };

      const { error } = validate(validUpdate, 'updateProduct');
      expect(error).to.be.undefined;
    });

    it('should require at least one field for update', () => {
      const emptyUpdate = {};

      const { error } = validate(emptyUpdate, 'updateProduct');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('at least');
    });

    it('should validate price when provided', () => {
      const invalidUpdate = {
        price: -10
      };

      const { error } = validate(invalidUpdate, 'updateProduct');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('price');
    });

    it('should validate stock when provided', () => {
      const invalidUpdate = {
        stock: 'invalid'
      };

      const { error } = validate(invalidUpdate, 'updateProduct');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('stock');
    });

    it('should validate status when provided', () => {
      const invalidUpdate = {
        status: 'invalid'
      };

      const { error } = validate(invalidUpdate, 'updateProduct');
      expect(error).to.exist;
      expect(error.details[0].message).to.include('status');
    });
  });
});
