/**
 * Type Utilities
 * 
 * This module provides utility functions for handling data types,
 * including type mapping, comparison, and validation.
 */

const { DataTypes } = require('sequelize');

/**
 * Maps database types to determine if they are equivalent
 * @param {string} dbType - Database column type
 * @param {string} modelType - Model column type
 * @returns {boolean} Whether the types are equivalent
 */
function areTypesEquivalent(dbType, modelType) {
  if (!dbType || !modelType) return false;

  if (/ARRAY/i.test(dbType) || /ARRAY/i.test(modelType)) {
    console.log('areTypesEquivalent: ARRAY', dbType, modelType);
    return true;
  }

  // Normalize types to lowercase
  dbType = dbType.toLowerCase();
  modelType = modelType.toLowerCase();

  // Direct match
  if (dbType === modelType) return true;

  // Special case mappings
  const equivalentTypes = {
    varchar: ['varchar', 'enum', 'string'],
    'time': ['time'],
    'timestamp': ['timestamp'],
    'float8': ['double'],
    'float8': ['double'],
    integer: ['int', 'integer'],
    boolean: ['bool', 'boolean'],
    text: ['string', 'text']
  };

  return equivalentTypes[dbType]?.includes(modelType) || false;
}

function areUdtNameEquivalent(udtName, modelUdtName) {
  if (!udtName || !modelUdtName) return false;



  // Normalize types to lowercase
  udtName = udtName.toLowerCase();
  modelUdtName = modelUdtName.toLowerCase();

  // Direct match
  if (udtName === modelUdtName) return true;

  if (udtName.startsWith('enum_') && modelUdtName.startsWith('varchar')) {
    return true;
  }

  // Special case mappings
  const equivalentUdtNames = {
    varchar: ['varchar', 'enum', 'string'],
    'time': ['time'],
    'timestamp': ['timestamp'],
    'float4': ['double'],
    'float8': ['double'],
    integer: ['int', 'integer'],
    boolean: ['bool', 'boolean'],
    text: ['string', 'text']
  };

  return equivalentUdtNames[udtName]?.includes(modelUdtName) || false;
}

/**
 * Normalizes default values for comparison
 * @param {*} value - The value to normalize
 * @param {string} type - The data type
 * @returns {*} Normalized value
 */
function normalizeDefaultValue(value, type) {
  if (value === null || value === undefined) return value;
  
  // Handle string values
  if (typeof value === 'string') {
    // Remove type casting and quotes
    value = value.split('::')?.[0]?.replace(/^'|'$/g, '') || value;
    value = value.replace(/^\(|\)$/g, '');
    
    // Convert to appropriate type
    if (['boolean', 'bool'].includes(type?.toLowerCase())) {
      return value.toLowerCase() === 'true';
    }
    if (['integer', 'int', 'bigint'].includes(type?.toLowerCase())) {
      return parseInt(value, 10);
    }
    if (['float', 'double', 'real', 'decimal'].includes(type?.toLowerCase())) {
      return parseFloat(value);
    }
  }
  
  return value;
}

/**
 * Compares default values between database and model definitions
 * @param {*} dbValue - Database default value
 * @param {*} modelValue - Model default value
 * @param {string} dbType - Database column type
 * @returns {boolean} Whether the values are equivalent
 */
function areDefaultValuesEqual(dbValue, modelValue, dbType) {
  // Skip comparison for certain types
  if (dbType?.toLowerCase() === 'uuid') return true;
  
  // Normalize values
  const normalizedDbValue = normalizeDefaultValue(dbValue, dbType);
  const normalizedModelValue = normalizeDefaultValue(modelValue, dbType);
  
  // Handle null/undefined equivalence
  if (normalizedDbValue === null && normalizedModelValue === undefined) return true;
  if (normalizedDbValue === undefined && normalizedModelValue === null) return true;
  if (normalizedDbValue === null && normalizedModelValue === null) return true;
  if (normalizedDbValue === undefined && normalizedModelValue === undefined) return true;
  
  // Compare normalized values
  return normalizedDbValue === normalizedModelValue;
}

module.exports = {
  areTypesEquivalent,
  areUdtNameEquivalent,
  normalizeDefaultValue,
  areDefaultValuesEqual
};