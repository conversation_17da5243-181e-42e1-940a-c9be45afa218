const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { commentService } = require('../services');
const pick = require('../utils/pick');

/**
 * Create a comment
 * @route POST /api/comments
 */
const createComment = catchAsync(async (req, res) => {
  const commentData = {
    ...req.body,
    authorId: req.user.id,
  };

  const comment = await commentService.createComment(commentData);
  res.status(201).send(comment);
});

/**
 * Get comments for a document
 * @route GET /api/documents/:documentId/comments
 */
const getDocumentComments = catchAsync(async (req, res) => {
  const options = pick(req.query, ['limit', 'page']);
  const result = await commentService.getCommentsByDocumentId(req.params.documentId, options);
  res.send(result);
});

/**
 * Get comment by id
 * @route GET /api/comments/:id
 */
const getComment = catchAsync(async (req, res) => {
  const comment = await commentService.getCommentById(req.params.commentId);
  if (!comment) {
    return res.status(404).send({ message: 'Comment not found' });
  }
  res.send(comment);
});

/**
 * Update comment
 * @route PATCH /api/comments/:id
 */
const updateComment = catchAsync(async (req, res) => {
  const comment = await commentService.getCommentById(req.params.commentId);

  // Check if the user is the author of the comment
  if (comment.authorId !== req.user.id) {
    return res.status(403).send({ message: 'You are not authorized to update this comment' });
  }

  const updatedComment = await commentService.updateCommentById(req.params.commentId, req.body);
  res.send(updatedComment);
});

/**
 * Delete comment
 * @route DELETE /api/comments/:id
 */
const deleteComment = catchAsync(async (req, res) => {
  const comment = await commentService.getCommentById(req.params.commentId);

  // Only allow deletion by the author of the comment or an admin
  if (comment.authorId !== req.user.id && req.user.role !== 'admin') {
    return res.status(403).send({ message: 'You are not authorized to delete this comment' });
  }

  await commentService.deleteCommentById(req.params.commentId);
  // 使用明确的数字状态码，避免可能的undefined问题
  res.status(204).send();
});

module.exports = {
  createComment,
  getDocumentComments,
  getComment,
  updateComment,
  deleteComment,
};