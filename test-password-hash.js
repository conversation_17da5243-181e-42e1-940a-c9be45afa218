const bcrypt = require('bcryptjs');

async function testPasswordHashing() {
  console.log('🔄 测试密码哈希功能...');
  
  const originalPassword = 'testpassword123';
  const newPassword = 'newpassword456';
  
  try {
    // 1. 测试密码哈希
    console.log('📝 原始密码:', originalPassword);
    const hashedOriginal = await bcrypt.hash(originalPassword, 10);
    console.log('🔒 哈希后的原始密码:', hashedOriginal);
    
    // 2. 测试密码验证
    const isOriginalValid = await bcrypt.compare(originalPassword, hashedOriginal);
    console.log('✅ 原始密码验证:', isOriginalValid ? '通过' : '失败');
    
    // 3. 测试新密码哈希
    console.log('📝 新密码:', newPassword);
    const hashedNew = await bcrypt.hash(newPassword, 10);
    console.log('🔒 哈希后的新密码:', hashedNew);
    
    // 4. 测试新密码验证
    const isNewValid = await bcrypt.compare(newPassword, hashedNew);
    console.log('✅ 新密码验证:', isNewValid ? '通过' : '失败');
    
    // 5. 测试交叉验证（应该失败）
    const isCrossValid1 = await bcrypt.compare(originalPassword, hashedNew);
    const isCrossValid2 = await bcrypt.compare(newPassword, hashedOriginal);
    console.log('🚫 交叉验证1 (原密码 vs 新哈希):', isCrossValid1 ? '意外通过' : '正确失败');
    console.log('🚫 交叉验证2 (新密码 vs 原哈希):', isCrossValid2 ? '意外通过' : '正确失败');
    
    if (isOriginalValid && isNewValid && !isCrossValid1 && !isCrossValid2) {
      console.log('\n🎉 密码哈希功能正常！');
      return true;
    } else {
      console.log('\n❌ 密码哈希功能异常！');
      return false;
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 运行测试
testPasswordHashing()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 测试执行出错:', error);
    process.exit(1);
  }); 