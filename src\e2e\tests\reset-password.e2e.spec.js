const { test, expect } = require('@playwright/test');

test.describe('Reset Password Page', () => {
  test('should load reset password page', async ({ page }) => {
    await page.goto('/reset-password');
    await expect(page).toHaveURL(/.*reset-password/);
    await expect(page.locator('form')).toBeVisible();
    await expect(page.locator('input[type="password"]')).toBeVisible();
  });

  test('should show error for mismatched passwords', async ({ page }) => {
    await page.goto('/reset-password');
    await page.fill('input[name="password"]', '123456');
    await page.fill('input[name="confirmPassword"]', '654321');
    await page.click('button[type="submit"]');
    await expect(page.locator('.error-message')).toBeVisible();
  });

  test('should show success for valid reset', async ({ page }) => {
    await page.goto('/reset-password');
    await page.fill('input[name="password"]', '123456');
    await page.fill('input[name="confirmPassword"]', '123456');
    await page.click('button[type="submit"]');
    await expect(page.locator('.success-message')).toBeVisible();
  });
}); 