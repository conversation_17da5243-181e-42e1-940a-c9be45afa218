const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const financeController = require('../controllers/finance.controller');
const financeValidation = require('../validations/finance.validation');

const router = express.Router();

router
  .route('/')
  .post(auth('manageFinances'), validate(financeValidation.createFinance), financeController.createFinance)
  .get(auth('getFinances'), validate(financeValidation.getFinances), financeController.getFinances);

router
  .route('/stats')
  .get(auth('getFinances'), financeController.getFinanceStats);

// Public finance stats route
router
  .route('/public/stats')
  .get(financeController.getFinanceStats);

router
  .route('/:financeId')
  .get(auth('getFinances'), validate(financeValidation.getFinance), financeController.getFinance)
  .patch(auth('manageFinances'), validate(financeValidation.updateFinance), financeController.updateFinance)
  .delete(auth('manageFinances'), validate(financeValidation.deleteFinance), financeController.deleteFinance);

module.exports = router;