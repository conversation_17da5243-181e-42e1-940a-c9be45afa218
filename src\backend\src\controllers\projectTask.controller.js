const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { projectTaskService } = require('../services');

/**
 * Create a new project task
 * @route POST /api/projects/:projectId/tasks
 */
const createProjectTask = catchAsync(async (req, res) => {
  const task = await projectTaskService.createProjectTask({
    ...req.body,
    projectId: req.params.projectId,
    createdBy: req.user.id
  });
  res.status(201).send(task);
});

/**
 * Get a project task by ID
 * @route GET /api/projects/tasks/:taskId
 */
const getProjectTask = catchAsync(async (req, res) => {
  const task = await projectTaskService.getProjectTaskById(req.params.taskId);
  res.send(task);
});

/**
 * Update a project task
 * @route PATCH /api/projects/tasks/:taskId
 */
const updateProjectTask = catchAsync(async (req, res) => {
  const task = await projectTaskService.updateProjectTaskById(req.params.taskId, req.body);
  res.send(task);
});

/**
 * Delete a project task
 * @route DELETE /api/projects/tasks/:taskId
 */
const deleteProjectTask = catchAsync(async (req, res) => {
  await projectTaskService.deleteProjectTaskById(req.params.taskId);
  // 使用明确的数字状态码，避免可能的undefined问题
  res.status(204).send();
});

/**
 * Get all tasks for a project
 * @route GET /api/projects/:projectId/tasks
 */
const getProjectTasks = catchAsync(async (req, res) => {
  const filter = {
    projectId: req.params.projectId,
    status: req.query.status,
    priority: req.query.priority,
    assigneeId: req.query.assigneeId,
    taskType: req.query.taskType,
    search: req.query.search
  };
  const options = {
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10,
    sortBy: req.query.sortBy || 'createdAt',
    sortOrder: req.query.sortOrder || 'desc'
  };
  const result = await projectTaskService.queryProjectTasks(filter, options);
  res.send(result);
});

module.exports = {
  createProjectTask,
  getProjectTask,
  updateProjectTask,
  deleteProjectTask,
  getProjectTasks
};