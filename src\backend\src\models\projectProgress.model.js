const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ProjectProgress = sequelize.define('projectprogress', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'project',
      key: 'id'
    }
  },
  stage: {
    type: DataTypes.STRING,
    allowNull: false
  },
  completionPercentage: {
    type: DataTypes.FLOAT,
    allowNull: false,
    defaultValue: 0,
    validate: {
      min: 0,
      max: 100
    }
  },
  currentStatus: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  deliveryDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  delayStatus: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  delayReason: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  updatedBy: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'user',
      key: 'id'
    }
  }
}, {
  timestamps: true,
});

module.exports = { ProjectProgress };