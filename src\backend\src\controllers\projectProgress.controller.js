const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { projectProgressService } = require('../services');

/**
 * Create a new project progress entry
 * @route POST /api/projects/:projectId/progress
 */
const createProjectProgress = catchAsync(async (req, res) => {
  const progress = await projectProgressService.createProjectProgress({
    ...req.body,
    projectId: req.params.projectId,
    updatedBy: req.user.id
  });
  res.status(201).send(progress);
});

/**
 * Get a project progress entry by ID
 * @route GET /api/projects/progress/:progressId
 */
const getProjectProgress = catchAsync(async (req, res) => {
  const progress = await projectProgressService.getProjectProgressById(req.params.progressId);
  res.send(progress);
});

/**
 * Update a project progress entry
 * @route PATCH /api/projects/progress/:progressId
 */
const updateProjectProgress = catchAsync(async (req, res) => {
  const progress = await projectProgressService.updateProjectProgressById(req.params.progressId, req.body);
  res.send(progress);
});

/**
 * Delete a project progress entry
 * @route DELETE /api/projects/progress/:progressId
 */
const deleteProjectProgress = catchAsync(async (req, res) => {
  await projectProgressService.deleteProjectProgressById(req.params.progressId);
  res.status(204).send();
});

/**
 * Get all progress entries for a project
 * @route GET /api/projects/:projectId/progress
 */
const getProjectProgressEntries = catchAsync(async (req, res) => {
  const filter = {
    projectId: req.params.projectId,
    stage: req.query.stage,
    delayStatus: req.query.delayStatus !== undefined ? req.query.delayStatus === 'true' : undefined
  };
  const options = {
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10,
    sortBy: req.query.sortBy || 'createdAt',
    sortOrder: req.query.sortOrder || 'desc'
  };
  const result = await projectProgressService.queryProjectProgress(filter, options);
  res.send(result);
});

module.exports = {
  createProjectProgress,
  getProjectProgress,
  updateProjectProgress,
  deleteProjectProgress,
  getProjectProgressEntries
}; 