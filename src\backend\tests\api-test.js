// Simple API test
const assert = require('assert');
const { describe, it, before, after } = require('mocha');
const http = require('http');
const express = require('express');

describe('API Server Tests', () => {
  let server;
  let app;

  before(function(done) {
    // Create a simple Express app for testing
    app = express();
    
    // Add a simple route
    app.get('/test', (req, res) => {
      res.json({ message: 'Test API is working' });
    });
    
    // Start the server
    server = app.listen(3001, () => {
      console.log('Test server started on port 3001');
      done();
    });
  });

  after(function(done) {
    // Close the server after tests
    server.close(() => {
      console.log('Test server closed');
      done();
    });
  });

  it('should respond to GET /test', (done) => {
    // Make a request to the test endpoint
    http.get('http://localhost:3001/test', (res) => {
      let data = '';
      
      // A chunk of data has been received
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      // The whole response has been received
      res.on('end', () => {
        const response = JSON.parse(data);
        assert.strictEqual(response.message, 'Test API is working');
        done();
      });
    }).on('error', (err) => {
      assert.fail(err);
    });
  });
});
