const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { attachmentService } = require('../services');

/**
 * Upload an attachment
 * @route POST /api/attachments
 */
const uploadAttachment = catchAsync(async (req, res) => {
  if (!req.file) {
    return res.status(400).send({ message: 'No file uploaded' });
  }
  
  const metadata = {
    documentId: req.body.documentId,
    uploadedBy: req.user.id,
  };
  
  const attachment = await attachmentService.createAttachment(req.file, metadata);
  res.status(201).send(attachment);
});

/**
 * Get attachment by id
 * @route GET /api/attachments/:id
 */
const getAttachment = catchAsync(async (req, res) => {
  const attachment = await attachmentService.getAttachmentById(req.params.attachmentId);
  if (!attachment) {
    return res.status(404).send({ message: 'Attachment not found' });
  }
  res.send(attachment);
});

/**
 * Download attachment
 * @route GET /api/attachments/:id/download
 */
const downloadAttachment = catchAsync(async (req, res) => {
  const attachment = await attachmentService.getAttachmentById(req.params.attachmentId);
  if (!attachment) {
    return res.status(404).send({ message: 'Attachment not found' });
  }
  
  res.setHeader('Content-Type', attachment.mimeType);
  res.setHeader('Content-Disposition', `attachment; filename="${attachment.originalName}"`);
  res.sendFile(attachment.path);
});

/**
 * Get attachments by document id
 * @route GET /api/documents/:documentId/attachments
 */
const getDocumentAttachments = catchAsync(async (req, res) => {
  const attachments = await attachmentService.getAttachmentsByDocumentId(req.params.documentId);
  res.send(attachments);
});

/**
 * Delete attachment
 * @route DELETE /api/attachments/:id
 */
const deleteAttachment = catchAsync(async (req, res) => {
  await attachmentService.deleteAttachmentById(req.params.attachmentId);
  res.status(204).send();
});

module.exports = {
  uploadAttachment,
  getAttachment,
  downloadAttachment,
  getDocumentAttachments,
  deleteAttachment,
};