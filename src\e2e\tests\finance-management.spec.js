// @ts-check
import { test, expect } from '@playwright/test';

test.describe('财务管理流程', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('财务概览页面', async ({ page }) => {
    // 访问财务概览页面
    await page.goto('/finance/overview');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('财务概览');

    // 验证财务卡片存在
    expect(await page.isVisible('.finance-card')).toBeTruthy();
    
    // 验证收入支出图表存在
    expect(await page.isVisible('.finance-chart')).toBeTruthy();
  });

  test('收入记录列表页面', async ({ page }) => {
    // 访问收入记录列表页面
    await page.goto('/finance/income');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('收入记录');

    // 验证收入表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加收入按钮存在
    expect(await page.isVisible('a[href="/finance/income/create"], button:has-text("添加收入")')).toBeTruthy();
  });

  test('创建收入记录流程', async ({ page }) => {
    // 访问创建收入记录页面
    await page.goto('/finance/income/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('添加收入');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 填写表单
    const incomeTitle = `测试收入 ${Date.now()}`;
    await page.fill('input[name="title"]', incomeTitle);
    
    // 设置金额
    await page.fill('input[name="amount"]', '10000');
    
    // 设置日期
    const incomeDate = new Date();
    await page.fill('input[name="date"]', incomeDate.toISOString().split('T')[0]);
    
    // 选择类别
    if (await page.isVisible('select[name="categoryId"]')) {
      await page.selectOption('select[name="categoryId"]', { index: 1 });
    }
    
    // 选择项目(如果存在)
    if (await page.isVisible('select[name="projectId"]')) {
      await page.selectOption('select[name="projectId"]', { index: 1 });
    }
    
    // 选择客户(如果存在)
    if (await page.isVisible('select[name="clientId"]')) {
      await page.selectOption('select[name="clientId"]', { index: 1 });
    }
    
    // 选择收款方式
    if (await page.isVisible('select[name="paymentMethod"]')) {
      await page.selectOption('select[name="paymentMethod"]', 'bank_transfer');
    }
    
    // 添加备注
    if (await page.isVisible('textarea[name="notes"]')) {
      await page.fill('textarea[name="notes"]', '这是一个由E2E测试创建的收入记录');
    }

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到收入列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/finance/income');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
    
    // 验证新收入已添加到列表
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain(incomeTitle);
  });

  test('支出记录列表页面', async ({ page }) => {
    // 访问支出记录列表页面
    await page.goto('/finance/expenses');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('支出记录');

    // 验证支出表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加支出按钮存在
    expect(await page.isVisible('a[href="/finance/expenses/create"], button:has-text("添加支出")')).toBeTruthy();
  });

  test('创建支出记录流程', async ({ page }) => {
    // 访问创建支出记录页面
    await page.goto('/finance/expenses/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('添加支出');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 填写表单
    const expenseTitle = `测试支出 ${Date.now()}`;
    await page.fill('input[name="title"]', expenseTitle);
    
    // 设置金额
    await page.fill('input[name="amount"]', '5000');
    
    // 设置日期
    const expenseDate = new Date();
    await page.fill('input[name="date"]', expenseDate.toISOString().split('T')[0]);
    
    // 选择类别
    if (await page.isVisible('select[name="categoryId"]')) {
      await page.selectOption('select[name="categoryId"]', { index: 1 });
    }
    
    // 选择项目(如果存在)
    if (await page.isVisible('select[name="projectId"]')) {
      await page.selectOption('select[name="projectId"]', { index: 1 });
    }
    
    // 选择供应商(如果存在)
    if (await page.isVisible('select[name="supplierId"]')) {
      await page.selectOption('select[name="supplierId"]', { index: 1 });
    }
    
    // 选择支付方式
    if (await page.isVisible('select[name="paymentMethod"]')) {
      await page.selectOption('select[name="paymentMethod"]', 'bank_transfer');
    }
    
    // 添加备注
    if (await page.isVisible('textarea[name="notes"]')) {
      await page.fill('textarea[name="notes"]', '这是一个由E2E测试创建的支出记录');
    }

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到支出列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/finance/expenses');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
    
    // 验证新支出已添加到列表
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain(expenseTitle);
  });

  test('财务报表页面', async ({ page }) => {
    // 访问财务报表页面
    await page.goto('/finance/reports');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('财务报表');

    // 设置报表参数
    // 选择报表类型
    if (await page.isVisible('select[name="reportType"]')) {
      await page.selectOption('select[name="reportType"]', 'income_expense');
    }
    
    // 设置开始日期
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 1);
    await page.fill('input[name="startDate"]', startDate.toISOString().split('T')[0]);
    
    // 设置结束日期
    const endDate = new Date();
    await page.fill('input[name="endDate"]', endDate.toISOString().split('T')[0]);
    
    // 点击生成报表按钮
    await page.click('button:has-text("生成报表")');
    
    // 等待报表生成
    await page.waitForTimeout(1000);
    
    // 验证报表图表存在
    expect(await page.isVisible('.report-chart')).toBeTruthy();
    
    // 验证导出按钮存在
    expect(await page.isVisible('button:has-text("导出报表")')).toBeTruthy();
  });

  test('发票管理页面', async ({ page }) => {
    // 访问发票管理页面
    await page.goto('/finance/invoices');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('发票管理');

    // 验证发票表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加发票按钮存在
    expect(await page.isVisible('a[href="/finance/invoices/create"], button:has-text("添加发票")')).toBeTruthy();
  });

  test('创建发票流程', async ({ page }) => {
    // 访问创建发票页面
    await page.goto('/finance/invoices/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('添加发票');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 填写表单
    await page.fill('input[name="invoiceNumber"]', `INV-${Date.now()}`);
    
    // 选择客户
    if (await page.isVisible('select[name="clientId"]')) {
      await page.selectOption('select[name="clientId"]', { index: 1 });
    }
    
    // 设置开票日期
    const invoiceDate = new Date();
    await page.fill('input[name="invoiceDate"]', invoiceDate.toISOString().split('T')[0]);
    
    // 设置金额
    await page.fill('input[name="amount"]', '12000');
    
    // 选择发票类型
    if (await page.isVisible('select[name="type"]')) {
      await page.selectOption('select[name="type"]', 'vat');
    }
    
    // 选择状态
    if (await page.isVisible('select[name="status"]')) {
      await page.selectOption('select[name="status"]', 'issued');
    }
    
    // 添加备注
    if (await page.isVisible('textarea[name="notes"]')) {
      await page.fill('textarea[name="notes"]', '这是一个由E2E测试创建的发票');
    }

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到发票列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/finance/invoices');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });

  test('财务设置页面', async ({ page }) => {
    // 访问财务设置页面
    await page.goto('/finance/settings');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('财务设置');

    // 验证设置表单存在
    expect(await page.isVisible('form')).toBeTruthy();
    
    // 验证税率设置存在
    if (await page.isVisible('input[name="taxRate"]')) {
      // 修改税率
      await page.fill('input[name="taxRate"]', '13');
    }
    
    // 验证保存按钮存在
    expect(await page.isVisible('button[type="submit"]')).toBeTruthy();
  });
});
