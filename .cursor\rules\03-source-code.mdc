---
description:
globs:
alwaysApply: true
---
# Source Code Organization

## Source Directory Structure
The main source code is located in the `src/` directory. This contains the core application code.

## Data Management
- [check-data-source.js](mdc:check-data-source.js) provides data source verification functionality
- The `uploads/` directory is used for file storage and management

## Testing
- [test-script.js](mdc:test-script.js) contains test utilities
- Additional test files may be found in relevant source directories

## Documentation
- Main project documentation is located in the `docs/` directory
- [todolist.md](mdc:todolist.md) contains development tasks and progress tracking

## Deployment
The `deploy/` directory contains deployment configurations and scripts for different environments.