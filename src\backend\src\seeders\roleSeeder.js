const { v4: uuidv4 } = require('uuid');
const { Role } = require('../models/role.model');
const { Permission } = require('../models/permission.model');

async function seedRoles() {
  try {
    // Check if roles already exist
    const roleCount = await Role.count();
    if (roleCount > 0) {
      console.log('Roles already exist, skipping role seeding');
      return;
    }

    // Create default roles
    const roles = [
      {
        id: uuidv4(),
        name: 'Administrator',
        description: '系统管理员，拥有所有权限',
        isActive: true
      },
      {
        id: uuidv4(),
        name: 'Manager',
        description: '管理者，拥有大部分管理权限',
        isActive: true
      },
      {
        id: uuidv4(),
        name: 'User',
        description: '普通用户，拥有基本操作权限',
        isActive: true
      },
      {
        id: uuidv4(),
        name: 'Guest',
        description: '访客，只有查看权限',
        isActive: true
      },
      {
        id: uuidv4(),
        name: 'ProjectManager',
        description: '项目经理，管理项目相关内容',
        isActive: true
      }
    ];

    // Bulk create roles
    await Role.bulkCreate(roles);
    console.log('Successfully seeded roles');

    // Create permissions if they don't exist
    const permissionCount = await Permission.count();
    if (permissionCount === 0) {
      const permissions = [
        {
          id: uuidv4(),
          name: 'create_user',
          description: '创建用户',
          category: 'user'
        },
        {
          id: uuidv4(),
          name: 'read_user',
          description: '查看用户',
          category: 'user'
        },
        {
          id: uuidv4(),
          name: 'update_user',
          description: '更新用户',
          category: 'user'
        },
        {
          id: uuidv4(),
          name: 'delete_user',
          description: '删除用户',
          category: 'user'
        },
        {
          id: uuidv4(),
          name: 'create_project',
          description: '创建项目',
          category: 'project'
        },
        {
          id: uuidv4(),
          name: 'read_project',
          description: '查看项目',
          category: 'project'
        },
        {
          id: uuidv4(),
          name: 'update_project',
          description: '更新项目',
          category: 'project'
        },
        {
          id: uuidv4(),
          name: 'delete_project',
          description: '删除项目',
          category: 'project'
        },
        {
          id: uuidv4(),
          name: 'create_document',
          description: '创建文档',
          category: 'document'
        },
        {
          id: uuidv4(),
          name: 'read_document',
          description: '查看文档',
          category: 'document'
        },
        {
          id: uuidv4(),
          name: 'update_document',
          description: '更新文档',
          category: 'document'
        },
        {
          id: uuidv4(),
          name: 'delete_document',
          description: '删除文档',
          category: 'document'
        }
      ];

      await Permission.bulkCreate(permissions);
      console.log('Successfully seeded permissions');
    }

  } catch (error) {
    console.error('Error seeding roles:', error);
    throw error;
  }
}

module.exports = seedRoles;
