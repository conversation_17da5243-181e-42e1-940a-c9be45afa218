// 测试各种密码修改场景
async function testPasswordScenarios() {
  const API_BASE_URL = process.env.API_URL || 'http://localhost:3009';
  
  try {
    console.log('🔄 开始测试各种密码修改场景...');
    
    // 1. 管理员登录
    console.log('🔐 管理员登录...');
    const adminLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    if (!adminLoginResponse.ok) {
      const errorData = await adminLoginResponse.text();
      throw new Error(`管理员登录失败: ${adminLoginResponse.status} - ${errorData}`);
    }
    
    const adminLoginData = await adminLoginResponse.json();
    console.log('✅ 管理员登录成功');
    const adminToken = adminLoginData.tokens?.access?.token;
    
    // 2. 创建测试用户
    const testUser = {
      username: 'scenario' + Date.now(),
      email: 'scenario' + Date.now() + '@example.com',
      password: 'originalpass123',
      lastName: '场景测试用户',
      role: 'user'
    };
    
    console.log('📝 创建测试用户:', testUser.username);
    const createResponse = await fetch(`${API_BASE_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify(testUser)
    });
    
    if (!createResponse.ok) {
      const errorData = await createResponse.text();
      throw new Error(`创建用户失败: ${createResponse.status} - ${errorData}`);
    }
    
    console.log('✅ 用户创建成功');
    
    // 3. 用户登录
    console.log('🔐 用户登录...');
    const userLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    if (!userLoginResponse.ok) {
      const errorData = await userLoginResponse.text();
      throw new Error(`用户登录失败: ${userLoginResponse.status} - ${errorData}`);
    }
    
    const userLoginData = await userLoginResponse.json();
    console.log('✅ 用户登录成功');
    const userToken = userLoginData.tokens?.access?.token;
    
    // 场景测试开始
    console.log('\n🧪 开始场景测试...');
    
    // 场景1: 错误的当前密码
    console.log('\n📋 场景1: 使用错误的当前密码');
    try {
      const wrongPasswordResponse = await fetch(`${API_BASE_URL}/api/auth/change-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userToken}`
        },
        body: JSON.stringify({
          currentPassword: 'wrongpassword',
          newPassword: 'newpass123'
        })
      });
      
      if (wrongPasswordResponse.ok) {
        console.log('❌ 错误：使用错误密码竟然成功了');
        return false;
      } else {
        console.log('✅ 正确：错误密码被拒绝');
      }
    } catch (error) {
      console.log('✅ 正确：错误密码被拒绝 -', error.message);
    }
    
    // 场景2: 正常修改密码
    console.log('\n📋 场景2: 正常修改密码');
    const newPassword = 'newpass456';
    const changeResponse = await fetch(`${API_BASE_URL}/api/auth/change-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken}`
      },
      body: JSON.stringify({
        currentPassword: testUser.password,
        newPassword: newPassword
      })
    });
    
    if (!changeResponse.ok) {
      const errorData = await changeResponse.text();
      throw new Error(`修改密码失败: ${changeResponse.status} - ${errorData}`);
    }
    console.log('✅ 密码修改成功');
    
    // 场景3: 立即用新密码登录
    console.log('\n📋 场景3: 立即用新密码登录');
    const newLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: newPassword
      })
    });
    
    if (!newLoginResponse.ok) {
      const errorData = await newLoginResponse.text();
      console.log('❌ 新密码登录失败:', errorData);
      return false;
    }
    console.log('✅ 新密码登录成功');
    
    // 场景4: 用原密码登录（应该失败）
    console.log('\n📋 场景4: 用原密码登录（应该失败）');
    const oldLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    if (oldLoginResponse.ok) {
      console.log('❌ 错误：原密码仍然有效');
      return false;
    } else {
      console.log('✅ 正确：原密码已失效');
    }
    
    // 场景5: 连续修改密码
    console.log('\n📋 场景5: 连续修改密码');
    const newLoginData = await newLoginResponse.json();
    const newUserToken = newLoginData.tokens?.access?.token;
    
    const finalPassword = 'finalpass789';
    const secondChangeResponse = await fetch(`${API_BASE_URL}/api/auth/change-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${newUserToken}`
      },
      body: JSON.stringify({
        currentPassword: newPassword,
        newPassword: finalPassword
      })
    });
    
    if (!secondChangeResponse.ok) {
      const errorData = await secondChangeResponse.text();
      throw new Error(`第二次修改密码失败: ${secondChangeResponse.status} - ${errorData}`);
    }
    console.log('✅ 第二次密码修改成功');
    
    // 验证最终密码
    const finalLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: finalPassword
      })
    });
    
    if (!finalLoginResponse.ok) {
      console.log('❌ 最终密码登录失败');
      return false;
    }
    console.log('✅ 最终密码登录成功');
    
    console.log('\n🎉 所有场景测试通过！');
    return true;
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 运行测试
testPasswordScenarios()
  .then(success => {
    if (success) {
      console.log('\n🎉 所有密码场景测试通过！');
    } else {
      console.log('\n💥 密码场景测试失败。');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 测试执行出错:', error);
    process.exit(1);
  }); 