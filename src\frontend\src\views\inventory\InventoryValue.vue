<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 页面标题 -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">库存价值报表</h1>
        <p class="mt-2 text-sm text-gray-600">查看库存总价值、分类统计和价值趋势分析</p>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>

      <!-- 主要内容 -->
      <div v-else>
        <!-- 统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <!-- 总价值 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">库存总价值</dt>
                    <dd class="text-lg font-medium text-gray-900">¥{{ formatCurrency(totalValue) }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <!-- 物品数量 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">物品种类</dt>
                    <dd class="text-lg font-medium text-gray-900">{{ totalItems }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <!-- 平均单价 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">平均单价</dt>
                    <dd class="text-lg font-medium text-gray-900">¥{{ formatCurrency(averagePrice) }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <!-- 价值趋势 -->
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                  </div>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">月度增长</dt>
                    <dd class="flex items-center">
                      <span class="text-lg font-medium text-gray-900">{{ valueTrend }}%</span>
                      <span v-if="valueTrend > 0" class="ml-2 text-sm text-green-600">↗</span>
                      <span v-else-if="valueTrend < 0" class="ml-2 text-sm text-red-600">↘</span>
                      <span v-else class="ml-2 text-sm text-gray-500">→</span>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分类价值分布 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          <!-- 分类价值图表 -->
          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">分类价值分布</h3>
            <div class="space-y-4">
              <div v-for="category in categoryStats" :key="category.name" class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="w-4 h-4 rounded-full mr-3" :style="{ backgroundColor: category.color }"></div>
                  <span class="text-sm font-medium text-gray-900">{{ category.name }}</span>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900">¥{{ formatCurrency(category.value) }}</div>
                  <div class="text-xs text-gray-500">{{ category.percentage }}%</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 仓库价值分布 -->
          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">仓库价值分布</h3>
            <div class="space-y-4">
              <div v-for="warehouse in warehouseStats" :key="warehouse.name" class="flex items-center justify-between">
                <div class="flex items-center">
                  <div class="w-4 h-4 rounded-full mr-3" :style="{ backgroundColor: warehouse.color }"></div>
                  <span class="text-sm font-medium text-gray-900">{{ warehouse.name }}</span>
                </div>
                <div class="text-right">
                  <div class="text-sm font-medium text-gray-900">¥{{ formatCurrency(warehouse.value) }}</div>
                  <div class="text-xs text-gray-500">{{ warehouse.percentage }}%</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 详细数据表格 -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">库存价值明细</h3>
          </div>
          
          <!-- 筛选器 -->
          <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">分类筛选</label>
                <select v-model="filters.category" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                  <option value="">全部分类</option>
                  <option v-for="category in categories" :key="category" :value="category">{{ category }}</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">仓库筛选</label>
                <select v-model="filters.warehouse" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                  <option value="">全部仓库</option>
                  <option v-for="warehouse in warehouses" :key="warehouse" :value="warehouse">{{ warehouse }}</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">价值范围</label>
                <select v-model="filters.valueRange" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                  <option value="">全部</option>
                  <option value="low">低价值 (< ¥1,000)</option>
                  <option value="medium">中价值 (¥1,000 - ¥10,000)</option>
                  <option value="high">高价值 (> ¥10,000)</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
                <input 
                  v-model="filters.search" 
                  type="text" 
                  placeholder="搜索物品名称或编码"
                  class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
                >
              </div>
            </div>
          </div>

          <!-- 表格 -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">物品信息</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库位置</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">库存数量</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单价</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">总价值</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="item in filteredItems" :key="item.id" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ item.name }}</div>
                      <div class="text-sm text-gray-500">{{ item.code }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.category }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.warehouseLocation }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.stock }} {{ item.unit }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">¥{{ formatCurrency(item.price) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">¥{{ formatCurrency(item.totalValue) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <router-link :to="`/inventory/${item.id}`" class="text-blue-600 hover:text-blue-900">查看详情</router-link>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- 分页 -->
          <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-700">
                显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, filteredItems.length) }} 条，
                共 {{ filteredItems.length }} 条记录
              </div>
              <div class="flex space-x-2">
                <button 
                  @click="currentPage--" 
                  :disabled="currentPage === 1"
                  class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  上一页
                </button>
                <button 
                  @click="currentPage++" 
                  :disabled="currentPage >= totalPages"
                  class="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  下一页
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { inventoryService } from '@/services/apiService'

export default {
  name: 'InventoryValue',
  setup() {
    const loading = ref(true)
    const inventoryItems = ref([])
    const filters = ref({
      category: '',
      warehouse: '',
      valueRange: '',
      search: ''
    })
    const currentPage = ref(1)
    const pageSize = ref(20)

    // 计算属性
    const totalValue = computed(() => {
      return inventoryItems.value.reduce((sum, item) => sum + item.totalValue, 0)
    })

    const totalItems = computed(() => inventoryItems.value.length)

    const averagePrice = computed(() => {
      if (inventoryItems.value.length === 0) return 0
      return totalValue.value / inventoryItems.value.reduce((sum, item) => sum + item.stock, 0)
    })

    const valueTrend = computed(() => {
      // 模拟价值趋势，实际应该从API获取
      return 12.5
    })

    const categories = computed(() => {
      return [...new Set(inventoryItems.value.map(item => item.category))]
    })

    const warehouses = computed(() => {
      return [...new Set(inventoryItems.value.map(item => item.warehouseLocation))]
    })

    const categoryStats = computed(() => {
      const stats = {}
      const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']
      
      inventoryItems.value.forEach(item => {
        if (!stats[item.category]) {
          stats[item.category] = { value: 0, count: 0 }
        }
        stats[item.category].value += item.totalValue
        stats[item.category].count++
      })

      return Object.entries(stats).map(([name, data], index) => ({
        name,
        value: data.value,
        count: data.count,
        percentage: ((data.value / totalValue.value) * 100).toFixed(1),
        color: colors[index % colors.length]
      })).sort((a, b) => b.value - a.value)
    })

    const warehouseStats = computed(() => {
      const stats = {}
      const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']
      
      inventoryItems.value.forEach(item => {
        const warehouse = item.warehouseLocation.split('-')[0] // 取仓库主名称
        if (!stats[warehouse]) {
          stats[warehouse] = { value: 0, count: 0 }
        }
        stats[warehouse].value += item.totalValue
        stats[warehouse].count++
      })

      return Object.entries(stats).map(([name, data], index) => ({
        name,
        value: data.value,
        count: data.count,
        percentage: ((data.value / totalValue.value) * 100).toFixed(1),
        color: colors[index % colors.length]
      })).sort((a, b) => b.value - a.value)
    })

    const filteredItems = computed(() => {
      let items = inventoryItems.value

      // 分类筛选
      if (filters.value.category) {
        items = items.filter(item => item.category === filters.value.category)
      }

      // 仓库筛选
      if (filters.value.warehouse) {
        items = items.filter(item => item.warehouseLocation.includes(filters.value.warehouse))
      }

      // 价值范围筛选
      if (filters.value.valueRange) {
        items = items.filter(item => {
          const value = item.totalValue
          switch (filters.value.valueRange) {
            case 'low': return value < 1000
            case 'medium': return value >= 1000 && value <= 10000
            case 'high': return value > 10000
            default: return true
          }
        })
      }

      // 搜索筛选
      if (filters.value.search) {
        const search = filters.value.search.toLowerCase()
        items = items.filter(item => 
          item.name.toLowerCase().includes(search) || 
          item.code.toLowerCase().includes(search)
        )
      }

      return items
    })

    const totalPages = computed(() => {
      return Math.ceil(filteredItems.value.length / pageSize.value)
    })

    // 方法
    const formatCurrency = (value) => {
      return new Intl.NumberFormat('zh-CN').format(value)
    }

    const loadInventoryData = async () => {
      try {
        loading.value = true
        const response = await inventoryService.getInventoryList()
        
        // 处理数据，计算总价值
        inventoryItems.value = response.data.results.map(item => ({
          ...item,
          totalValue: (item.price || 0) * (item.stock || 0)
        }))
        
      } catch (error) {
        console.error('加载库存数据失败:', error)
        // 使用模拟数据
        inventoryItems.value = [
          {
            id: 1,
            name: '钢筋',
            code: 'GJ001',
            category: '建材',
            warehouseLocation: '主仓库-A区-A01',
            stock: 100,
            unit: '吨',
            price: 4500,
            totalValue: 450000
          },
          {
            id: 2,
            name: '水泥',
            code: 'SN001',
            category: '建材',
            warehouseLocation: '主仓库-B区-B01',
            stock: 200,
            unit: '吨',
            price: 350,
            totalValue: 70000
          },
          {
            id: 3,
            name: '电缆',
            code: 'DL001',
            category: '电气设备',
            warehouseLocation: '分仓库-C区-C01',
            stock: 50,
            unit: '米',
            price: 25,
            totalValue: 1250
          }
        ]
      } finally {
        loading.value = false
      }
    }

    onMounted(() => {
      loadInventoryData()
    })

    return {
      loading,
      inventoryItems,
      filters,
      currentPage,
      pageSize,
      totalValue,
      totalItems,
      averagePrice,
      valueTrend,
      categories,
      warehouses,
      categoryStats,
      warehouseStats,
      filteredItems,
      totalPages,
      formatCurrency,
      loadInventoryData
    }
  }
}
</script> 