const { v4: uuidv4 } = require('uuid');
const { Category } = require('../models/category.model');

async function seedCategories() {
  try {
    // Check if categories already exist
    const categoryCount = await Category.count();
    if (categoryCount > 0) {
      console.log('Categories already exist, skipping category seeding');
      return;
    }

    // Create parent categories first
    const parentCategories = [
      {
        id: uuidv4(),
        name: '项目文档',
        description: '与项目相关的所有文档',
        level: 1,
        isActive: true,
        sortOrder: 1
      },
      {
        id: uuidv4(),
        name: '技术文档',
        description: '技术相关文档和指南',
        level: 1,
        isActive: true,
        sortOrder: 2
      },
      {
        id: uuidv4(),
        name: '管理文档',
        description: '管理相关文档',
        level: 1,
        isActive: true,
        sortOrder: 3
      },
      {
        id: uuidv4(),
        name: '财务文档',
        description: '财务相关文档',
        level: 1,
        isActive: true,
        sortOrder: 4
      },
      {
        id: uuidv4(),
        name: '其他文档',
        description: '其他类型文档',
        level: 1,
        isActive: true,
        sortOrder: 5
      }
    ];

    // Create parent categories
    const createdParents = await Category.bulkCreate(parentCategories);
    console.log('Successfully seeded parent categories');

    // Create child categories
    const childCategories = [
      // Project document subcategories
      {
        id: uuidv4(),
        name: '项目计划',
        description: '项目计划文档',
        parentId: createdParents[0].id,
        level: 2,
        isActive: true,
        sortOrder: 1
      },
      {
        id: uuidv4(),
        name: '项目报告',
        description: '项目报告文档',
        parentId: createdParents[0].id,
        level: 2,
        isActive: true,
        sortOrder: 2
      },
      {
        id: uuidv4(),
        name: '项目合同',
        description: '项目合同文档',
        parentId: createdParents[0].id,
        level: 2,
        isActive: true,
        sortOrder: 3
      },

      // Technical document subcategories
      {
        id: uuidv4(),
        name: '技术规范',
        description: '技术规范文档',
        parentId: createdParents[1].id,
        level: 2,
        isActive: true,
        sortOrder: 1
      },
      {
        id: uuidv4(),
        name: '操作手册',
        description: '操作手册文档',
        parentId: createdParents[1].id,
        level: 2,
        isActive: true,
        sortOrder: 2
      },
      {
        id: uuidv4(),
        name: '技术方案',
        description: '技术方案文档',
        parentId: createdParents[1].id,
        level: 2,
        isActive: true,
        sortOrder: 3
      },

      // Management document subcategories
      {
        id: uuidv4(),
        name: '规章制度',
        description: '公司规章制度',
        parentId: createdParents[2].id,
        level: 2,
        isActive: true,
        sortOrder: 1
      },
      {
        id: uuidv4(),
        name: '会议记录',
        description: '会议记录文档',
        parentId: createdParents[2].id,
        level: 2,
        isActive: true,
        sortOrder: 2
      },
      {
        id: uuidv4(),
        name: '工作计划',
        description: '工作计划文档',
        parentId: createdParents[2].id,
        level: 2,
        isActive: true,
        sortOrder: 3
      },

      // Financial document subcategories
      {
        id: uuidv4(),
        name: '财务报表',
        description: '财务报表文档',
        parentId: createdParents[3].id,
        level: 2,
        isActive: true,
        sortOrder: 1
      },
      {
        id: uuidv4(),
        name: '预算计划',
        description: '预算计划文档',
        parentId: createdParents[3].id,
        level: 2,
        isActive: true,
        sortOrder: 2
      },
      {
        id: uuidv4(),
        name: '费用报销',
        description: '费用报销文档',
        parentId: createdParents[3].id,
        level: 2,
        isActive: true,
        sortOrder: 3
      }
    ];

    // Create child categories
    await Category.bulkCreate(childCategories);
    console.log('Successfully seeded child categories');

  } catch (error) {
    console.error('Error seeding categories:', error);
    throw error;
  }
}

module.exports = seedCategories;
