const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { Attendance, Project, User } = require('../models');
const ExcelJS = require('exceljs'); // 需要安装该依赖：npm install exceljs

/**
 * Create an attendance record
 * @param {Object} attendanceBody
 * @returns {Promise<Attendance>}
 */
const createAttendance = async (attendanceBody) => {
  // Check if project exists if projectId is provided
  if (attendanceBody.projectId) {
    const project = await Project.findByPk(attendanceBody.projectId);
    if (!project) {
      throw new ApiError(400, 'Project not found');
    }
  }
  
  return Attendance.create(attendanceBody);
};

/**
 * Get attendance record by id
 * @param {string} id
 * @returns {Promise<Attendance>}
 */
const getAttendanceById = async (id) => {
  const attendance = await Attendance.findByPk(id, {
    include: [
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!attendance) {
    throw new ApiError(404, 'Attendance record not found');
  }
  return attendance;
};

/**
 * Update attendance record by id
 * @param {string} attendanceId
 * @param {Object} updateBody
 * @returns {Promise<Attendance>}
 */
const updateAttendanceById = async (attendanceId, updateBody) => {
  const attendance = await getAttendanceById(attendanceId);
  
  // Check if project exists if projectId is being updated
  if (updateBody.projectId) {
    const project = await Project.findByPk(updateBody.projectId);
    if (!project) {
      throw new ApiError(400, 'Project not found');
    }
  }
  
  Object.assign(attendance, updateBody);
  await attendance.save();
  return attendance;
};

/**
 * Delete attendance record by id
 * @param {string} attendanceId
 * @returns {Promise<void>}
 */
const deleteAttendanceById = async (attendanceId) => {
  const attendance = await getAttendanceById(attendanceId);
  await attendance.destroy();
};

/**
 * Query for attendance records
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Attendance records and pagination info
 */
const queryAttendances = async (filter, options) => {
  const { page, limit, sortBy, sortOrder } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};
  
  if (filter.projectId) {
    whereClause.projectId = filter.projectId;
  }
  
  if (filter.employeeName) {
    whereClause.employeeName = { [Op.like]: `%${filter.employeeName}%` };
  }
  
  if (filter.status) {
    whereClause.status = filter.status;
  }
  
  if (filter.dateFrom) {
    whereClause.date = {
      ...whereClause.date,
      [Op.gte]: new Date(filter.dateFrom)
    };
  }
  
  if (filter.dateTo) {
    whereClause.date = {
      ...whereClause.date,
      [Op.lte]: new Date(filter.dateTo)
    };
  }

  // Query with pagination
  const includeOptions = [
    {
      model: Project,
      attributes: ['id', 'name', 'projectNumber', 'status']
    },
    {
      model: User,
      as: 'Creator',
      attributes: ['id', 'username', 'lastName', 'email', 'departmentId']
    }
  ];

  // 处理部门筛选
  if (filter.department) {
    includeOptions.push({
      model: User,
      as: 'Employee',
      attributes: ['departmentId'],
      where: { departmentId: filter.department }
    });
  }

  const { count, rows } = await Attendance.findAndCountAll({
    where: whereClause,
    include: includeOptions,
    order: [[sortBy, sortOrder]],
    limit,
    offset,
    distinct: true
  });

  // 处理部门信息
  const results = rows.map(attendance => {
    const plainAttendance = attendance.get({ plain: true });
    
    // 从Creator中获取部门ID
    if (plainAttendance.Creator && plainAttendance.Creator.departmentId) {
      plainAttendance.department = plainAttendance.Creator.departmentId;
    }
    
    return plainAttendance;
  });

  return {
    results: results,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

/**
 * Export attendance records to Excel
 * @param {Object} filter - Filter options for the data
 * @param {Object} options - Query options
 * @returns {Promise<Buffer>} - Excel file buffer
 */
const exportAttendances = async (filter, options) => {
  // Build the where clause based on filters, similar to queryAttendances
  const whereClause = {};
  
  if (filter.projectId) {
    whereClause.projectId = filter.projectId;
  }
  
  if (filter.department) {
    whereClause.departmentId = filter.department;
  }
  
  if (filter.employeeName) {
    whereClause.employeeName = { [Op.like]: `%${filter.employeeName}%` };
  }
  
  if (filter.status) {
    whereClause.status = filter.status;
  }
  
  if (filter.dateFrom) {
    whereClause.date = {
      ...whereClause.date,
      [Op.gte]: new Date(filter.dateFrom)
    };
  }
  
  if (filter.dateTo) {
    whereClause.date = {
      ...whereClause.date,
      [Op.lte]: new Date(filter.dateTo)
    };
  }

  // Prepare include options
  const includeOptions = [
    {
      model: Project,
      attributes: ['id', 'name', 'projectNumber', 'status']
    },
    {
      model: User,
      as: 'Creator',
      attributes: ['id', 'username', 'lastName', 'email']
    }
  ];

  // Add department filter if provided
  if (filter.department) {
    includeOptions[1].where = { departmentId: filter.department };
    // Remove the departmentId from the where clause since it doesn't exist in the attendance table
    delete whereClause.departmentId;
  }

  // Fetch all data with associations
  const attendances = await Attendance.findAll({
    where: whereClause,
    include: includeOptions,
    order: [[options.sortBy, options.sortOrder]]
  });

  // Create a new Excel workbook
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('考勤数据');

  // Add headers
  worksheet.columns = [
    { header: '员工姓名', key: 'employeeName', width: 15 },
    { header: '项目名称', key: 'projectName', width: 20 },
    { header: '工作日期', key: 'date', width: 15 },
    { header: '工时', key: 'workHours', width: 10 },
    { header: '日工作量', key: 'dailyOutput', width: 15 },
    { header: '状态', key: 'status', width: 10 },
    { header: '备注', key: 'notes', width: 30 },
    { header: '创建时间', key: 'createdAt', width: 20 }
  ];

  // Style the headers
  worksheet.getRow(1).font = { bold: true };
  worksheet.getRow(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE0E0E0' }
  };

  // Add data rows
  attendances.forEach(attendance => {
    worksheet.addRow({
      employeeName: attendance.employeeName,
      projectName: attendance.Project ? attendance.Project.name : '未分配',
      date: attendance.date ? attendance.date.toISOString().split('T')[0] : '',
      workHours: attendance.workHours,
      dailyOutput: attendance.dailyOutput,
      status: attendance.status,
      notes: attendance.notes,
      createdAt: attendance.createdAt ? attendance.createdAt.toISOString().replace('T', ' ').substr(0, 19) : ''
    });
  });

  // Generate buffer
  return await workbook.xlsx.writeBuffer();
};

module.exports = {
  createAttendance,
  getAttendanceById,
  updateAttendanceById,
  deleteAttendanceById,
  queryAttendances,
  exportAttendances
}; 