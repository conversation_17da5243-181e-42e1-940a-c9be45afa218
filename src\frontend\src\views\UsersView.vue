<template>
  <div class="users-view">
    <div class="page-header">
      <h1 class="text-2xl font-semibold mb-4">用户管理</h1>
      <div class="flex items-center mb-4">
        <span class="text-sm mr-2"
          :class="{ 'font-bold': isMockData }"
          :style="{ color: isMockData ? '#722ed1' : IS_PROD ? '#52c41a' : '' }"
        >
          数据来源: {{ isMockData ? '模拟数据' : '真实API' }} {{ IS_PROD ? '(生产环境)' : '' }}
        </span>
      </div>
    </div>

    <!-- 搜索和过滤区域 -->
    <div class="mb-4 p-4 bg-white rounded-lg shadow" :style="{ backgroundColor: 'var(--bg-secondary)' }">
      <div class="flex flex-wrap items-center gap-4">
        <div class="flex-1 min-w-[200px]">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="搜索用户名或邮箱"
            class="w-full px-3 py-2 border rounded-md"
            :style="{ borderColor: 'var(--border-color)' }"
          />
        </div>
        <div>
          <select
            v-model="statusFilter"
            class="px-3 py-2 border rounded-md"
            :style="{ borderColor: 'var(--border-color)' }"
          >
            <option value="">所有状态</option>
            <option value="active">已激活</option>
            <option value="inactive">已禁用</option>
            <option value="pending">待验证</option>
          </select>
        </div>
        <div>
          <select
            v-model="roleFilter"
            class="px-3 py-2 border rounded-md"
            :style="{ borderColor: 'var(--border-color)' }"
          >
            <option value="">所有角色</option>
            <option value="admin">管理员</option>
            <option value="user">普通用户</option>
            <option value="manager">经理</option>
            <option value="editor">编辑者</option>
          </select>
        </div>
        <div class="flex space-x-2">
          <button
            class="px-4 py-2 rounded-md text-white"
            :style="{ backgroundColor: 'var(--primary-color)' }"
            @click="loadUsers"
            :disabled="loading"
          >
            <span v-if="loading" class="inline-block animate-spin mr-1">↻</span>
            {{ loading ? '加载中...' : '刷新数据' }}
          </button>

          <ExportButton
          v-if="0"
            :data="filteredUsers"
            :headers="exportHeaders"
            entity-name="users"
            button-text="导出数据"
            variant="secondary"
            :disabled="loading || filteredUsers.length === 0"
          />
        </div>
      </div>
    </div>

    <!-- 数据统计信息 -->
    <div class="mb-4 flex justify-between items-center">
      <div class="text-sm" :style="{ color: 'var(--text-secondary)' }">
        <span v-if="loading">正在加载数据...</span>
        <span v-else>已加载 {{ filteredUsers.length }} 条记录 {{ totalCount > 0 ? `(共 ${totalCount} 条)` : '' }}</span>
      </div>
      <div class="text-sm" :style="{ color: 'var(--text-secondary)' }">
        最后更新时间: {{ lastUpdated }}
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
      <button
        @click="loadUsers"
        class="mt-3 text-blue-600 underline"
      >
        点击重试
      </button>
    </div>

    <!-- 编辑用户提示 -->
    <div v-if="editMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">提示：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ editMessage }}</p>
    </div>

    <!-- 用户数据表格 -->
    <div class="bg-white rounded-lg shadow overflow-hidden" :style="{ backgroundColor: 'var(--bg-secondary)' }">
      <table class="min-w-full divide-y" :style="{ borderColor: 'var(--secondary-border)' }">
        <thead>
          <tr :style="{ backgroundColor: 'var(--bg-primary)', color: 'var(--text-primary)' }">
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">姓名</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">邮箱</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">角色</th>
            <th class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">状态</th>
            <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">操作</th>
          </tr>
        </thead>
        <tbody class="divide-y" :style="{ borderColor: 'var(--secondary-border)' }">
          <tr v-for="user in filteredUsers" :key="user.id" :style="{ color: 'var(--text-primary)' }">
            <td class="px-6 py-4 whitespace-nowrap text-sm">{{ user.id }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">{{ user.name }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">{{ user.email }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              <span
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                :style="getRoleStyle(user.role)"
              >
                {{ getRoleText(user.role) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
              <span
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                :style="getStatusStyle(user.status)"
              >
                {{ getStatusText(user.status) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
              <button
                class="text-blue-600 hover:text-blue-900 mr-2"
                :style="{ color: 'var(--primary-color)' }"
                @click="editUser(user)"
              >
                编辑
              </button>
            </td>
          </tr>
          <tr v-if="filteredUsers.length === 0">
            <td colspan="6" class="px-6 py-4 text-center text-sm" :style="{ color: 'var(--text-secondary)' }">
              <div v-if="loading" class="py-8">
                <div class="inline-block animate-spin text-xl mb-2">↻</div>
                <div>正在加载数据...</div>
              </div>
              <div v-else-if="searchQuery || statusFilter || roleFilter" class="py-8">
                <div class="text-lg mb-2">没有找到符合条件的用户</div>
                <div class="text-sm">请尝试修改搜索条件或清除过滤器</div>
              </div>
              <div v-else class="py-8">
                <div class="text-lg mb-2">暂无用户数据</div>
                <div class="text-sm">请稍后再试或联系管理员</div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 分页控件 -->
    <div v-if="totalPages > 1" class="mt-4 flex justify-center">
      <div class="flex items-center space-x-2">
        <button
          @click="changePage(currentPage - 1)"
          :disabled="currentPage === 1"
          class="px-3 py-1 rounded border"
          :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
          :style="{ borderColor: 'var(--border-color)' }"
        >
          上一页
        </button>

        <template v-for="page in displayedPages" :key="page">
          <span
            v-if="page === '...'"
            class="px-3 py-1"
          >
            ...
          </span>
          <button
            v-else
            @click="changePage(page)"
            class="px-3 py-1 rounded border"
            :class="{ 'font-bold': currentPage === page }"
            :style="{
              backgroundColor: currentPage === page ? 'var(--primary-color)' : 'transparent',
              color: currentPage === page ? 'white' : 'var(--text-primary)',
              borderColor: 'var(--border-color)'
            }"
          >
            {{ page }}
          </button>
        </template>

        <button
          @click="changePage(currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="px-3 py-1 rounded border"
          :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
          :style="{ borderColor: 'var(--border-color)' }"
        >
          下一页
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted, reactive } from 'vue';
import apiService from '@/services/apiService';
import ExportButton from '@/components/common/ExportButton.vue';
import { IS_PROD } from '@/utils/env';
    // 基本状态
    const users = ref([]);
    const loading = ref(false);
    const error = ref(null);
    const editMessage = ref(null);
    const lastUpdated = ref(new Date().toLocaleString());
    let refreshInterval = null;

    // 分页相关
    const currentPage = ref(1);
    const pageSize = ref(10);
    const totalCount = ref(0);
    const totalPages = ref(1);

    // 搜索和过滤
    const searchQuery = ref('');
    const statusFilter = ref('');
    const roleFilter = ref('');

    // 当前数据源类型
    const isMockData = computed(() => {
      // 检查全局数据源变量
      if (window.__currentDataSource) {
        return window.__currentDataSource === 'mock';
      }

      // 从 localStorage 获取
      const storedDataSource = localStorage.getItem('app_data_source');
      if (storedDataSource) {
        return storedDataSource === 'mock';
      }

      // 默认使用真实API
      return false;
    });

    // 过滤后的用户数据
    const filteredUsers = computed(() => {
      if (!users.value.length) return [];

      return users.value.filter(user => {
        // 搜索条件
        const matchesSearch = !searchQuery.value ||
          (user.name && user.name.toLowerCase().includes(searchQuery.value.toLowerCase())) ||
          (user.email && user.email.toLowerCase().includes(searchQuery.value.toLowerCase()));

        // 状态过滤
        const matchesStatus = !statusFilter.value || user.status === statusFilter.value;

        // 角色过滤
        const matchesRole = !roleFilter.value || user.role === roleFilter.value;

        return matchesSearch && matchesStatus && matchesRole;
      });
    });

    // 计算要显示的页码
    const displayedPages = computed(() => {
      if (totalPages.value <= 7) {
        // 如果总页数小于等于7，显示所有页码
        return Array.from({ length: totalPages.value }, (_, i) => i + 1);
      }

      // 否则显示带省略号的页码
      const pages = [];

      // 始终显示第一页
      pages.push(1);

      // 当前页靠近开始
      if (currentPage.value <= 3) {
        pages.push(2, 3, 4, '...', totalPages.value - 1, totalPages.value);
      }
      // 当前页靠近结尾
      else if (currentPage.value >= totalPages.value - 2) {
        pages.push('...', totalPages.value - 3, totalPages.value - 2, totalPages.value - 1, totalPages.value);
      }
      // 当前页在中间
      else {
        pages.push(
          '...',
          currentPage.value - 1,
          currentPage.value,
          currentPage.value + 1,
          '...',
          totalPages.value
        );
      }

      return pages;
    });

    // 角色相关函数
    const roleMap = reactive({
      'admin': '管理员',
      'user': '普通用户',
      'manager': '经理',
      'editor': '编辑者'
    });

    const getRoleText = (role) => roleMap[role] || role;

    const getRoleStyle = (role) => {
      const styles = {
        'admin': {
          backgroundColor: '#f6ffed',
          color: '#52c41a',
          borderColor: '#b7eb8f'
        },
        'user': {
          backgroundColor: '#f9f0ff',
          color: '#722ed1',
          borderColor: '#d3adf7'
        },
        'manager': {
          backgroundColor: '#e6f7ff',
          color: '#1890ff',
          borderColor: '#91d5ff'
        },
        'editor': {
          backgroundColor: '#fff7e6',
          color: '#fa8c16',
          borderColor: '#ffd591'
        }
      };

      return styles[role] || styles['user'];
    };

    // 状态相关函数
    const statusMap = reactive({
      'active': '已激活',
      'inactive': '已禁用',
      'pending': '待验证'
    });

    const getStatusText = (status) => statusMap[status] || status;

    const getStatusStyle = (status) => {
      const styles = {
        'active': {
          backgroundColor: '#f6ffed',
          color: '#52c41a',
          borderColor: '#b7eb8f'
        },
        'inactive': {
          backgroundColor: '#fff2f0',
          color: '#f5222d',
          borderColor: '#ffccc7'
        },
        'pending': {
          backgroundColor: '#fff7e6',
          color: '#fa8c16',
          borderColor: '#ffd591'
        }
      };

      return styles[status] || styles['pending'];
    };

    // 加载用户数据
    const loadUsers = async () => {
      loading.value = true;
      error.value = null;

      try {
        // 准备查询参数
        const params = {
          page: currentPage.value,
          limit: pageSize.value
        };

        // 添加搜索和过滤条件
        if (searchQuery.value) {
          params.search = searchQuery.value;
        }

        if (statusFilter.value) {
          params.status = statusFilter.value;
        }

        if (roleFilter.value) {
          params.role = roleFilter.value;
        }

        // 使用分页API获取数据
        const result = isMockData.value
          ? await apiService.getUsers(params)
          : await apiService.getUsersPaginated(params);

        // 处理响应数据
        if (Array.isArray(result)) {
          // 非分页响应
          users.value = result;
          totalCount.value = result.length;
          totalPages.value = 1;
        } else if (result && Array.isArray(result.results)) {
          // 分页响应
          users.value = result.results;
          totalCount.value = result.totalResults || result.results.length;
          totalPages.value = result.totalPages || Math.ceil(totalCount.value / pageSize.value);
          currentPage.value = result.page || currentPage.value;
        } else {
          console.warn('意外的数据格式:', result);
          users.value = [];
          totalCount.value = 0;
          totalPages.value = 1;
          error.value = '返回的数据格式不正确';
        }

        // 更新最后加载时间
        lastUpdated.value = new Date().toLocaleString();
      } catch (err) {
        console.error('加载用户数据失败:', err);
        error.value = err.message || '加载用户数据失败';
        users.value = [];
        totalCount.value = 0;
        totalPages.value = 1;
      } finally {
        loading.value = false;
      }
    };

    // 切换页面
    const changePage = (page) => {
      if (page < 1 || page > totalPages.value || page === currentPage.value) {
        return;
      }

      currentPage.value = page;
      loadUsers();
    };

    // 编辑用户
    const editUser = (user) => {
      // 这里只是一个占位函数，实际实现可以在后续开发中添加
      console.log('编辑用户:', user);
      editMessage.value = `编辑用户: ${user.name}`;

      // 3秒后自动清除消息
      setTimeout(() => {
        editMessage.value = null;
      }, 3000);
    };

    // 设置自动刷新
    const setupRefresh = () => {
      // 清除已有的定时器
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }

      // 生产环境且使用API时，每60秒自动刷新一次
      if (IS_PROD && !isMockData.value) {
        refreshInterval = setInterval(() => {
          loadUsers();
        }, 60000); // 60秒
      }
    };

    // 监听搜索和过滤条件变化
    watch([searchQuery, statusFilter, roleFilter], () => {
      // 重置到第一页
      currentPage.value = 1;
      // 重新加载数据
      loadUsers();
    }, { debounce: 300 }); // 添加防抖动以避免频繁请求

    // 监听数据源变化
    watch(isMockData, () => {
      // 重置到第一页
      currentPage.value = 1;
      // 重新加载数据
      loadUsers();
      // 重新设置自动刷新
      setupRefresh();
    });

    // 页面加载时获取用户数据并设置自动刷新
    onMounted(() => {
      loadUsers();
      setupRefresh();

      // 添加数据源变化事件监听
      window.addEventListener('datasource-changed', () => {
        loadUsers();
        setupRefresh();
      });
    });

    // 页面卸载时清除定时器和事件监听器
    onUnmounted(() => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }

      window.removeEventListener('datasource-changed', () => {});
    });

// 导出相关
const exportHeaders = [
  { title: 'ID', key: 'id' },
  { title: '姓名', key: 'name' },
  { title: '邮箱', key: 'email' },
  { title: '角色', key: 'role' },
  { title: '状态', key: 'status' },
  { title: '创建时间', key: 'createdAt' },
  { title: '最后更新', key: 'updatedAt' }
];
</script>