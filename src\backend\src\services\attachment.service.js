const httpStatus = require('http-status');
const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const { Attachment } = require('../models');
const ApiError = require('../utils/ApiError');

// Configure upload directory
const UPLOAD_DIR = path.resolve(__dirname, '../../uploads');

// Ensure upload directory exists
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

/**
 * Save uploaded file and create attachment record
 * @param {Object} file - Uploaded file object from multer middleware
 * @param {Object} metadata - Additional attachment metadata
 * @returns {Promise<Attachment>}
 */
const createAttachment = async (file, metadata) => {
  // Generate unique filename to prevent collisions
  const filename = `${uuidv4()}-${file.originalname}`;
  const filePath = path.join(UPLOAD_DIR, filename);
  
  // Create a write stream to save the file
  const writeStream = fs.createWriteStream(filePath);
  
  return new Promise((resolve, reject) => {
    writeStream.on('error', (error) => {
      reject(new ApiError(500, `File upload failed: ${error.message}`));
    });
    
    writeStream.on('finish', async () => {
      try {
        // Create attachment record in database
        const attachment = await Attachment.create({
          filename,
          originalName: file.originalname,
          mimeType: file.mimetype,
          size: file.size,
          path: filePath,
          documentId: metadata.documentId,
          uploadedBy: metadata.uploadedBy,
        });
        
        resolve(attachment);
      } catch (error) {
        // Clean up file if database operation fails
        fs.unlinkSync(filePath);
        reject(error);
      }
    });
    
    // Copy the file buffer to the write stream
    writeStream.write(file.buffer);
    writeStream.end();
  });
};

/**
 * Get attachment by id
 * @param {string} id
 * @returns {Promise<Attachment>}
 */
const getAttachmentById = async (id) => {
  return Attachment.findByPk(id);
};

/**
 * Get attachments by document id
 * @param {string} documentId
 * @returns {Promise<Array<Attachment>>}
 */
const getAttachmentsByDocumentId = async (documentId) => {
  return Attachment.findAll({
    where: { documentId },
    order: [['createdAt', 'DESC']],
  });
};

/**
 * Delete attachment by id
 * @param {string} attachmentId
 * @returns {Promise<void>}
 */
const deleteAttachmentById = async (attachmentId) => {
  const attachment = await getAttachmentById(attachmentId);
  
  if (!attachment) {
    throw new ApiError(404, 'Attachment not found');
  }
  
  // Delete the file from the filesystem
  try {
    fs.unlinkSync(attachment.path);
  } catch (error) {
    // Log error but continue (file might be already deleted or moved)
    console.error('Error deleting file:', error);
  }
  
  // Delete the database record
  await attachment.destroy();
};

module.exports = {
  createAttachment,
  getAttachmentById,
  getAttachmentsByDocumentId,
  deleteAttachmentById,
};