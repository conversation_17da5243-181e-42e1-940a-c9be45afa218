# PostgreSQL Setup Guide for Windows

This guide will help you set up PostgreSQL for your application.

## 1. Install PostgreSQL

1. Download PostgreSQL from the [official website](https://www.postgresql.org/download/windows/)
   - Choose the latest version for Windows
   - Use the installer provided by EnterpriseDB

2. Run the installer and follow these steps:
   - Select installation directory (default is fine)
   - Select components (select all)
   - Choose data directory (default is fine)
   - Set a password for the postgres user (remember this password!)
   - Set the port (default 5432 is recommended)
   - Select locale (default is fine)

3. When the installation is complete, you can uncheck the option to launch Stack Builder.

## 2. Verify PostgreSQL Installation

1. Open Command Prompt and run:
   ```
   "C:\Program Files\PostgreSQL\14\bin\psql.exe" -U postgres
   ```
   (Adjust the path if you installed to a different location or used a different version)

2. Enter the password you set during installation

3. If you see the PostgreSQL prompt (`postgres=#`), your installation is working!

4. Type `\q` to exit the PostgreSQL prompt

## 3. Create a Database for Your Application

1. Open Command Prompt as Administrator

2. Run the following command to create a database:
   ```
   "C:\Program Files\PostgreSQL\14\bin\createdb.exe" -U postgres yihe
   ```

3. Enter your PostgreSQL password when prompted

## 4. Configure Your Application

1. Update your `.env` file to use PostgreSQL:
   ```
   # Database Configuration
   DB_DIALECT=postgres
   
   # PostgreSQL Connection Parameters
   DB_HOST=localhost
   DB_PORT=5432
   DB_USER=postgres
   DB_PASSWORD=your_postgres_password
   DB_NAME=yihe
   DB_SCHEMA=public
   DB_LOGGING=false
   ```
   
   Replace `your_postgres_password` with the password you set during installation.

## 5. Run the PostgreSQL Setup Script

1. Run the setup script to initialize your database:
   ```
   npm run postgres:setup
   ```

2. If successful, you should see confirmation messages about the database being set up.

## 6. Start Your Application

1. Start your application:
   ```
   npm run dev
   ```

## Troubleshooting

### PostgreSQL Service Not Running

1. Open Services (search for "services" in Windows search)
2. Find the "postgresql-x64-14" service (may have a different version number)
3. Make sure its status is "Running"
4. If it's not running, right-click and select "Start"

### Connection Errors

If you get connection errors:

1. Make sure the PostgreSQL service is running
2. Verify your username and password are correct
3. Check that your database exists
4. Make sure your firewall isn't blocking the connection

### Command Not Found

If PostgreSQL commands aren't found, add the bin directory to your PATH:

1. Go to System Properties > Advanced > Environment Variables
2. Edit the PATH variable and add `C:\Program Files\PostgreSQL\14\bin` (adjust for your installation location)
3. Restart your command prompt

## PostgreSQL GUI Tools

For easier database management, consider using:

1. **pgAdmin 4** - Installed with PostgreSQL by default
2. **DBeaver** - A free, universal database tool
3. **DataGrip** - A powerful JetBrains IDE for databases (paid)

## Switching Back to SQLite

If you need to switch back to SQLite temporarily:

```
# Database Configuration
DB_DIALECT=sqlite
DB_STORAGE=./database.sqlite

# Comment out PostgreSQL parameters
# DB_HOST=localhost
# DB_PORT=5432
# ...
```
