const Joi = require('joi');
const { password } = require('./custom.validation');

const registerSchema = {
  body: Joi.object().keys({
    email: Joi.string().required().email().messages({
      'string.email': '必须是有效的电子邮箱地址',
      'string.empty': '电子邮箱不能为空'
    }),
    password: Joi.string().required().custom(password).messages({
      'string.empty': '密码不能为空'
    }),
    firstName: Joi.string().required().messages({
      'string.empty': '名字不能为空'
    }),
    lastName: Joi.string().required().messages({
      'string.empty': '姓氏不能为空'
    }),
    department: Joi.string().allow('', null),
    position: Joi.string().allow('', null)
  })
};

const loginSchema = {
  body: Joi.object().keys({
    email: Joi.string().required().messages({
      'string.empty': '电子邮箱不能为空'
    }),
    password: Joi.string().required().messages({
      'string.empty': '密码不能为空'
    })
  })
};

const logoutSchema = {
  body: Joi.object().keys({
    refreshToken: Joi.string().required().messages({
      'string.empty': '刷新令牌不能为空'
    })
  })
};

const refreshTokensSchema = {
  body: Joi.object().keys({
    refreshToken: Joi.string().required().messages({
      'string.empty': '刷新令牌不能为空'
    })
  })
};

const forgotPasswordSchema = {
  body: Joi.object().keys({
    email: Joi.string().email().required().messages({
      'string.email': '必须是有效的电子邮箱地址',
      'string.empty': '电子邮箱不能为空'
    })
  })
};

const resetPasswordSchema = {
  query: Joi.object().keys({
    token: Joi.string().required().messages({
      'string.empty': '重置密码令牌不能为空'
    })
  }),
  body: Joi.object().keys({
    password: Joi.string().required().custom(password).messages({
      'string.empty': '密码不能为空'
    })
  })
};

const verifyEmailSchema = {
  query: Joi.object().keys({
    token: Joi.string().required().messages({
      'string.empty': '邮箱验证令牌不能为空'
    })
  })
};

const changePasswordSchema = {
  body: Joi.object().keys({
    currentPassword: Joi.string().required().messages({
      'string.empty': '当前密码不能为空'
    }),
    newPassword: Joi.string().required().custom(password).messages({
      'string.empty': '新密码不能为空'
    })
  })
};

const register = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': '必须是有效的电子邮箱地址',
    'string.empty': '电子邮箱不能为空',
    'any.required': '电子邮箱是必填项'
  }),
  password: Joi.string().required().min(6).messages({
    'string.empty': '密码不能为空',
    'string.min': '密码长度必须至少为{#limit}个字符',
    'any.required': '密码是必填项'
  }),
  username: Joi.string().required().messages({
    'string.empty': '用户名不能为空',
    'any.required': '用户名是必填项'
  }),
  lastName: Joi.string().required().messages({
    'string.empty': '姓氏不能为空',
    'any.required': '姓氏是必填项'
  })
});

const login = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': '必须是有效的电子邮箱地址',
    'string.empty': '电子邮箱不能为空',
    'any.required': '电子邮箱是必填项'
  }),
  password: Joi.string().required().messages({
    'string.empty': '密码不能为空',
    'any.required': '密码是必填项'
  })
});

module.exports = {
  registerSchema,
  loginSchema,
  logoutSchema,
  refreshTokensSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
  verifyEmailSchema,
  changePasswordSchema,
  register,
  login
};