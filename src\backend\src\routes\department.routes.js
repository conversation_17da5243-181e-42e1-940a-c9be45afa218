const express = require('express');
const departmentController = require('../controllers/department.controller');
const { auth, roleMiddleware } = require('../middlewares');
const validate = require('../middlewares/validate');
const departmentValidation = require('../validations/department.validation');

const router = express.Router();

// Apply auth middleware to all routes
router.use(auth());

// Get department hierarchy
router.get('/hierarchy', departmentController.getDepartmentHierarchy);

// Get all departments
router.get('/', validate(departmentValidation.getDepartmentsSchema), departmentController.getAllDepartments);

// Get department by ID
router.get('/:id', validate(departmentValidation.getDepartmentSchema), departmentController.getDepartmentById);

// Only admin and manager can create, update, delete departments
router.post('/', 
  roleMiddleware(['admin', 'manager']), 
  validate(departmentValidation.createDepartmentSchema),
  departmentController.createDepartment
);

router.put('/:id', 
  roleMiddleware(['admin', 'manager']), 
  validate(departmentValidation.updateDepartmentSchema),
  departmentController.updateDepartment
);

router.delete('/:id', 
  roleMiddleware(['admin', 'manager']), 
  validate(departmentValidation.deleteDepartmentSchema),
  departmentController.deleteDepartment
);

module.exports = router;