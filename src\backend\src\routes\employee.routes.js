const express = require('express');
const router = express.Router();
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const employeeValidation = require('../validations/employee.validation');
const employeeController = require('../controllers/employee.controller');

// Contract Worker Routes
router
  .route('/contract')
  .post(auth(), validate(employeeValidation.createContractWorker), employeeController.createContractWorker)
  .get(auth(), validate(employeeValidation.getContractWorkers), employeeController.getContractWorkers);

router
  .route('/contract/:workerId')
  .get(auth(), validate(employeeValidation.getContractWorker), employeeController.getContractWorker)
  .patch(auth(), validate(employeeValidation.updateContractWorker), employeeController.updateContractWorker)
  .delete(auth(), validate(employeeValidation.deleteContractWorker), employeeController.deleteContractWorker);

// Temporary Worker Routes
router
  .route('/temporary')
  .post(auth(), validate(employeeValidation.createTemporaryWorker), employeeController.createTemporaryWorker)
  .get(auth(), validate(employeeValidation.getTemporaryWorkers), employeeController.getTemporaryWorkers);

router
  .route('/temporary/:workerId')
  .get(auth(), validate(employeeValidation.getTemporaryWorker), employeeController.getTemporaryWorker)
  .patch(auth(), validate(employeeValidation.updateTemporaryWorker), employeeController.updateTemporaryWorker)
  .delete(auth(), validate(employeeValidation.deleteTemporaryWorker), employeeController.deleteTemporaryWorker);

// All Employees Route
router
  .route('/')
  .get(auth(), validate(employeeValidation.getAllEmployees), employeeController.getAllEmployees);

module.exports = router;