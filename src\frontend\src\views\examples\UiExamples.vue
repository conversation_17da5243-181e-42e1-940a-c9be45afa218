<template>
  <div class="container mx-auto px-4 py-6">
    <div class="page-header mb-6">
      <h1 class="text-2xl font-bold">UI组件示例</h1>
      <p class="text-gray-600">展示系统中的各种UI组件和交互模式</p>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- 通知示例 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">通知示例</h2>
        <p class="mb-4">通知用于向用户显示操作结果、提示信息等。系统支持四种类型的通知：成功、错误、信息和警告。</p>
        
        <div class="space-y-3">
          <button @click="showSuccessNotification" class="btn btn-success w-full">显示成功通知</button>
          <button @click="showErrorNotification" class="btn btn-danger w-full">显示错误通知</button>
          <button @click="showInfoNotification" class="btn btn-primary w-full">显示信息通知</button>
          <button @click="showWarningNotification" class="btn btn-warning w-full">显示警告通知</button>
        </div>
      </div>
      
      <!-- 确认对话框示例 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">确认对话框示例</h2>
        <p class="mb-4">确认对话框用于在执行重要操作前进行确认，如删除、提交等。系统支持三种类型的确认对话框：普通、警告和危险。</p>
        
        <div class="space-y-3">
          <button @click="showNormalConfirm" class="btn btn-primary w-full">显示普通确认对话框</button>
          <button @click="showWarningConfirm" class="btn btn-warning w-full">显示警告确认对话框</button>
          <button @click="showDangerConfirm" class="btn btn-danger w-full">显示危险确认对话框</button>
          <button @click="showDeleteConfirm" class="btn btn-outline-danger w-full">显示删除确认对话框</button>
        </div>
      </div>
      
      <!-- 操作结果示例 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">操作结果示例</h2>
        <p class="mb-4">以下是某些操作的结果示例，展示了如何在实际场景中使用确认对话框和通知。</p>
        
        <div class="space-y-3">
          <button @click="simulateDelete" class="btn btn-outline-danger w-full">模拟删除操作</button>
          <button @click="simulateSave" class="btn btn-outline-success w-full">模拟保存操作</button>
          <button @click="simulateProcess" class="btn btn-outline-primary w-full">模拟处理操作</button>
        </div>
      </div>
      
      <!-- 使用说明 -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold mb-4">使用说明</h2>
        <p class="mb-4">这些组件在全局可用，可以通过以下方式使用：</p>
        
        <div class="bg-gray-100 p-4 rounded">
          <pre class="text-sm text-gray-800 whitespace-pre-wrap">
// 导入服务
import notificationService from '@/services/notification.service';
import confirmService from '@/services/confirm.service';

// 显示通知
notificationService.success('操作成功');
notificationService.error('操作失败');
notificationService.info('提示信息');
notificationService.warning('警告信息');

// 显示确认对话框
confirmService.confirm({
  title: '确认操作',
  message: '您确定要执行此操作吗？',
  type: 'warning' // 'warning', 'danger', 'info'
}).then(result => {
  if (result) {
    // 用户点击了确认
  } else {
    // 用户点击了取消
  }
});

// 显示删除确认对话框
confirmService.confirmDelete('项目名称').then(result => {
  if (result) {
    // 用户确认删除
  }
});
          </pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import notificationService from '../../services/notification.service';
import confirmService from '../../services/confirm.service';

// 通知示例
const showSuccessNotification = () => {
  notificationService.success('操作成功', '您的操作已成功完成');
};

const showErrorNotification = () => {
  notificationService.error('操作失败', '请检查输入并重试');
};

const showInfoNotification = () => {
  notificationService.info('信息提示', '这是一条提示信息');
};

const showWarningNotification = () => {
  notificationService.warning('警告提示', '请注意，此操作可能有风险');
};

// 确认对话框示例
const showNormalConfirm = async () => {
  const result = await confirmService.confirm({
    title: '确认操作',
    message: '您确定要执行此操作吗？',
    type: 'info'
  });
  
  if (result) {
    notificationService.success('操作已确认', '您选择了继续执行');
  } else {
    notificationService.info('操作已取消', '您选择了取消执行');
  }
};

const showWarningConfirm = async () => {
  const result = await confirmService.confirm({
    title: '警告确认',
    message: '此操作可能会导致数据变化，是否继续？',
    type: 'warning'
  });
  
  if (result) {
    notificationService.success('操作已确认', '您选择了继续执行');
  } else {
    notificationService.info('操作已取消', '您选择了取消执行');
  }
};

const showDangerConfirm = async () => {
  const result = await confirmService.confirm({
    title: '危险操作确认',
    message: '此操作将永久修改数据，无法撤销，是否继续？',
    type: 'danger'
  });
  
  if (result) {
    notificationService.success('操作已确认', '您选择了继续执行');
  } else {
    notificationService.info('操作已取消', '您选择了取消执行');
  }
};

const showDeleteConfirm = async () => {
  const result = await confirmService.confirmDelete('示例项目');
  
  if (result) {
    notificationService.success('删除成功', '示例项目已被删除');
  } else {
    notificationService.info('删除已取消', '示例项目未被删除');
  }
};

// 操作结果示例
const simulateDelete = async () => {
  try {
    const result = await confirmService.confirmDelete('重要数据');
    
    if (result) {
      // 模拟删除过程
      await new Promise(resolve => setTimeout(resolve, 1500));
      notificationService.success('删除成功', '数据已被成功删除');
    }
  } catch (error) {
    notificationService.error('删除失败', error.message || '发生未知错误');
  }
};

const simulateSave = async () => {
  try {
    const result = await confirmService.confirm({
      title: '保存确认',
      message: '您确定要保存当前更改吗？',
      type: 'info',
      confirmText: '保存',
      loadingText: '保存中...'
    });
    
    if (result) {
      // 模拟保存过程
      await new Promise(resolve => setTimeout(resolve, 1500));
      notificationService.success('保存成功', '您的更改已成功保存');
    }
  } catch (error) {
    notificationService.error('保存失败', error.message || '发生未知错误');
  }
};

const simulateProcess = async () => {
  try {
    const result = await confirmService.confirm({
      title: '处理确认',
      message: '数据处理可能需要一些时间，您确定要继续吗？',
      type: 'warning',
      confirmText: '开始处理',
      loadingText: '处理中...'
    });
    
    if (result) {
      // 模拟处理过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      notificationService.success('处理完成', '数据已成功处理');
    }
  } catch (error) {
    notificationService.error('处理失败', error.message || '发生未知错误');
  }
};
</script> 