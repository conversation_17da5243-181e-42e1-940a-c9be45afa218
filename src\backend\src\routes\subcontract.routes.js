const express = require('express');
const router = express.Router();
const subcontractController = require('../controllers/subcontract.controller');
const multer = require('multer');
const path = require('path');
const { authenticate } = require('../middlewares/auth.middleware');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/subcontracts/');
  },
  filename: function (req, file, cb) {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage: storage,
  limits: { fileSize: 10 * 1024 * 1024 } // 限制文件大小为10MB
});

// 公共路由 - 获取所有分包合同
router.get('/public', subcontractController.getSubcontracts);

// 公共路由 - 创建分包合同
router.post('/public', upload.array('attachments', 5), subcontractController.createSubcontract);

// 需要认证的路由
// 获取所有分包合同
router.get('/', authenticate, subcontractController.getSubcontracts);

// 获取单个分包合同
router.get('/:id', authenticate, subcontractController.getSubcontract);

// 创建新的分包合同
router.post('/', authenticate, upload.array('attachments', 5), subcontractController.createSubcontract);

// 更新分包合同
router.put('/:id', authenticate, upload.array('attachments', 5), subcontractController.updateSubcontract);

// 删除分包合同
router.delete('/:id', authenticate, subcontractController.deleteSubcontract);

module.exports = router;
