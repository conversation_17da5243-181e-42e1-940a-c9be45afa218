# Server Configuration
NODE_ENV=development
PORT=3008
CLIENT_URL=http://localhost:5173

# Database Configuration mssql, mariadb, mysql, oracle, postgres, db2 and sqlite.
DB_DIALECT=postgres
# DB_STORAGE=./234database.sqlite

# PostgreSQL Connection Parameters (commented out for now)
DB_HOST=127.0.0.1
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=yihe-local38
DB_SCHEMA=public
DB_LOGGING=true

# Database Pool Configuration
DB_POOL_MAX=5
DB_POOL_MIN=0
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000
DB_POOL_EVICT=1000

# JWT Configuration
JWT_SECRET=v8RA5P5hYxEHT5vB4eWsFJknpAdhtKGY73tZTbnJqMcqt9JVVW
JWT_ACCESS_EXPIRATION_MINUTES=10080
JWT_REFRESH_EXPIRATION_DAYS=30
JWT_RESET_PASSWORD_EXPIRATION_MINUTES=10
JWT_VERIFY_EMAIL_EXPIRATION_MINUTES=10

# Email Configuration
SMTP_HOST=smtp.qq.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=wphjhqjfpnnoccac
EMAIL_FROM=<EMAIL>