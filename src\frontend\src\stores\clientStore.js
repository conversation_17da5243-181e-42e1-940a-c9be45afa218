import { ref, readonly } from 'vue';

// 创建一个简单的客户信息存储
const clientName = ref('');

// 设置客户名称
function setClientName(name) {
  if (name && typeof name === 'string') {
    clientName.value = name;
    console.log('客户名称已更新:', name);
  }
}

// 清除客户名称
function clearClientName() {
  clientName.value = '';
}

// 导出只读的状态和修改函数
export default {
  clientName: readonly(clientName),
  setClientName,
  clearClientName
};
