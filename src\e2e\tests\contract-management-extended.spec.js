// @ts-check
import { test, expect } from '@playwright/test';

test.describe('合同管理扩展测试', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('合同列表页面', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('合同列表');

    // 验证合同表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加合同按钮存在
    expect(await page.isVisible('a[href="/contracts/create"]')).toBeTruthy();
  });

  test('创建合同流程', async ({ page }) => {
    // 访问创建合同页面
    await page.goto('/contracts/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('创建合同');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 填写表单
    const contractName = `测试合同 ${Date.now()}`;
    await page.fill('input[name="title"]', contractName);
    
    // 填写合同编号
    await page.fill('input[name="contractNumber"]', `CT-${Date.now()}`);
    
    // 选择客户
    if (await page.isVisible('select[name="clientId"]')) {
      await page.selectOption('select[name="clientId"]', { index: 1 });
    }
    
    // 选择项目
    if (await page.isVisible('select[name="projectId"]')) {
      await page.selectOption('select[name="projectId"]', { index: 1 });
    }
    
    // 设置开始日期
    const startDate = new Date();
    await page.fill('input[name="startDate"]', startDate.toISOString().split('T')[0]);
    
    // 设置结束日期
    const endDate = new Date();
    endDate.setFullYear(endDate.getFullYear() + 1);
    await page.fill('input[name="endDate"]', endDate.toISOString().split('T')[0]);
    
    // 设置合同金额
    await page.fill('input[name="amount"]', '100000');
    
    // 设置状态
    if (await page.isVisible('select[name="status"]')) {
      await page.selectOption('select[name="status"]', 'active');
    }
    
    // 添加描述
    if (await page.isVisible('textarea[name="description"]')) {
      await page.fill('textarea[name="description"]', '这是一个由E2E测试创建的测试合同');
    }

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到合同列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/contracts');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
    
    // 验证新合同已添加到列表
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain(contractName);
  });

  test('查看合同详情', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 点击第一个合同的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/contracts/');

    // 验证详情页面内容
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('合同详情');
    expect(await page.isVisible('.contract-info')).toBeTruthy();
    expect(await page.isVisible('a.edit-button')).toBeTruthy();
  });

  test('添加合同付款计划', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 点击第一个合同的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();
    
    // 点击添加付款计划按钮
    if (await page.isVisible('button:has-text("添加付款计划")')) {
      await page.click('button:has-text("添加付款计划")');
      
      // 等待模态框显示
      await page.waitForSelector('.modal');
      
      // 填写付款计划表单
      await page.fill('input[name="amount"]', '20000');
      
      // 设置计划日期
      const planDate = new Date();
      planDate.setMonth(planDate.getMonth() + 1);
      await page.fill('input[name="dueDate"]', planDate.toISOString().split('T')[0]);
      
      // 选择付款类型
      if (await page.isVisible('select[name="type"]')) {
        await page.selectOption('select[name="type"]', 'milestone');
      }
      
      // 添加描述
      if (await page.isVisible('textarea[name="description"]')) {
        await page.fill('textarea[name="description"]', '首期付款');
      }
      
      // 提交表单
      await page.click('button:has-text("保存")');
      
      // 等待操作完成
      await page.waitForTimeout(1000);
      
      // 验证付款计划已添加
      expect(await page.isVisible('.payment-schedule')).toBeTruthy();
    }
  });

  test('上传合同附件', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 点击第一个合同的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();
    
    // 点击附件选项卡
    if (await page.isVisible('button:has-text("附件")')) {
      await page.click('button:has-text("附件")');
      
      // 验证附件上传区域存在
      expect(await page.isVisible('.attachments-section')).toBeTruthy();
      
      // 注意：实际文件上传需要特殊处理，这里只验证UI元素
      expect(await page.isVisible('input[type="file"]')).toBeTruthy();
    }
  });

  test('记录合同变更', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 点击第一个合同的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();
    
    // 点击变更记录选项卡或按钮
    if (await page.isVisible('button:has-text("变更记录")')) {
      await page.click('button:has-text("变更记录")');
      
      // 点击添加变更按钮
      if (await page.isVisible('button:has-text("添加变更")')) {
        await page.click('button:has-text("添加变更")');
        
        // 等待模态框显示
        await page.waitForSelector('.modal');
        
        // 填写变更表单
        const changeDate = new Date();
        await page.fill('input[name="changeDate"]', changeDate.toISOString().split('T')[0]);
        
        // 填写变更内容
        await page.fill('textarea[name="description"]', '合同条款变更测试');
        
        // 提交表单
        await page.click('button:has-text("保存")');
        
        // 等待操作完成
        await page.waitForTimeout(1000);
        
        // 验证变更已添加
        expect(await page.isVisible('.change-records')).toBeTruthy();
      }
    }
  });

  test('合同搜索功能', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 在搜索框中输入关键字
    await page.fill('input[placeholder*="搜索"]', 'CT-');

    // 点击搜索按钮
    await page.click('button.search-button');

    // 等待搜索结果加载
    await page.waitForTimeout(1000);

    // 验证搜索结果
    const contractNumbers = await page.locator('table tbody tr td:nth-child(2)').allTextContents();
    for (const number of contractNumbers) {
      expect(number).toContain('CT-');
    }
  });

  test('合同状态筛选功能', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');

    // 如果存在状态筛选器
    if (await page.isVisible('select.status-filter')) {
      // 选择状态
      await page.selectOption('select.status-filter', 'active');

      // 等待筛选结果加载
      await page.waitForTimeout(1000);

      // 验证筛选结果存在
      expect(await page.locator('table tbody tr').count()).toBeGreaterThan(0);
    }
  });

  test('导出合同功能', async ({ page }) => {
    // 访问合同列表页面
    await page.goto('/contracts');
    
    // 点击导出按钮
    if (await page.isVisible('button:has-text("导出")')) {
      // 创建下载承诺
      const downloadPromise = page.waitForEvent('download');
      
      await page.click('button:has-text("导出")');
      
      // 等待下载开始
      const download = await downloadPromise;
      
      // 验证下载文件名包含"合同"
      expect(download.suggestedFilename()).toContain('合同');
    }
  });
});
