const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { userService } = require('../services');
const ApiError = require('../utils/ApiError');
const db = require('../models');
const logger = require('../config/logger');

/**
 * Create a new user
 * @route POST /api/users
 */
const createUser = catchAsync(async (req, res) => {
  const user = await userService.createUser(req.body);
  res.status(201).send(user);
});

/**
 * Get a user by ID
 * @route GET /api/users/:userId
 */
const getUser = catchAsync(async (req, res) => {
  const user = await userService.getUserById(req.params.userId);
  res.send(user);
});

/**
 * Get current logged in user information
 * @route GET /api/users/me
 */
const getMe = catchAsync(async (req, res) => {
  const user = await userService.getUserById(req.user.id);
  res.send(user);
});

/**
 * Update current logged in user profile
 * @route PATCH /api/users/profile
 */
const updateMyProfile = catchAsync(async (req, res) => {
  const updatedUser = await userService.updateUserById(req.user.id, req.body);
  res.send(updatedUser);
});

/**
 * Get current logged in user preferences
 * @route GET /api/users/me/preferences
 */
const getMyPreferences = catchAsync(async (req, res) => {
  // Assuming a service function to get user preferences by ID
  const preferences = await userService.getUserPreferencesById(req.user.id);
  if (!preferences) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Preferences not found');
  }
  res.send(preferences);
});

/**
 * Update a user
 * @route PATCH /api/users/:userId
 */
const updateUser = catchAsync(async (req, res) => {
  const updatedUser = await userService.updateUserById(req.params.userId, req.body);
  res.send(updatedUser);
});

/**
 * Delete a user
 * @route DELETE /api/users/:userId
 */
const deleteUser = catchAsync(async (req, res) => {
  await userService.deleteUserById(req.params.userId);
  res.status(204).send();
});

/**
 * Get all users with pagination
 * @route GET /api/users
 */
const getUsers = catchAsync(async (req, res) => {
  const filter = {
    role: req.query.role,
    isActive: req.query.isActive !== undefined ? req.query.isActive === 'true' : undefined,
    search: req.query.search,
    department: req.query.department
  };
  const options = {
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10,
    sortBy: req.query.sortBy || 'createdAt',
    sortOrder: req.query.sortOrder || 'desc'
  };
  const result = await userService.queryUsers(filter, options);
  res.send(result);
});

/**
 * Update user by id
 * @param {number} userId
 * @param {Object} updateBody
 * @returns {Promise<User>}
 */
const updateUserById = async (userId, updateBody) => {
  try {
    const user = await getUserById(userId);

    // Check if email is being updated and if it's already taken
    if (updateBody.email && updateBody.email !== user.email) {
      const emailTaken = await db.User.findOne({ where: { email: updateBody.email } });
      if (emailTaken) {
        throw new ApiError(400, 'Email already taken');
      }
    }

    // 移除密码哈希处理，依赖模型的beforeUpdate钩子
    // 密码哈希将由User模型的beforeUpdate钩子自动处理

    // Method 1: Update and save
    // Update user properties
    Object.assign(user, updateBody);

    // Save changes to the database
    await user.save();

    return user;
  } catch (error) {
    logger.error('Error updating user:', error);
    throw error;
  }
};

/**
 * Update multiple users based on criteria
 * @param {Object} criteria - The criteria to match users
 * @param {Object} updateData - The data to update
 * @returns {Promise<number>} - Number of updated records
 */
const updateUsers = async (criteria, updateData) => {
  try {
    // Method 2: Use the update method directly
    const [updatedCount] = await db.User.update(
      updateData,
      { where: criteria }
    );

    return updatedCount;
  } catch (error) {
    logger.error('Error updating multiple users:', error);
    throw error;
  }
};

/**
 * Get user by id
 * @param {number} id
 * @returns {Promise<User>}
 */
const getUserById = async (id) => {
  const user = await db.User.findByPk(id);
  if (!user) {
    throw new ApiError(404, 'User not found');
  }
  return user;
};

/**
 * Delete user by id
 * @param {number} userId
 * @returns {Promise<void>}
 */
const deleteUserById = async (userId) => {
  const user = await getUserById(userId);
  await user.destroy();
};

/**
 * Batch update users
 * @POST /users/update/batch
 */
const updateBatchUsers = catchAsync(async (req, res) => {
  const { criteria, updateData } = req.body;

  // Add validation to prevent mass updates without criteria
  if (!criteria || Object.keys(criteria).length === 0) {
    throw new ApiError(400, 'Update criteria cannot be empty');
  }

  const count = await updateUsers(criteria, updateData);
  res.send({ count, message: `${count} users updated successfully` });
});

/**
 * Reset user password (admin initiated)
 * @route POST /api/users/:userId/reset-password
 */
const resetUserPassword = catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { newPassword } = req.body;

  // First check if user exists
  const user = await userService.getUserById(userId);
  if (!user) {
    throw new ApiError(404, '用户不存在');
  }

  // Update the user's password
  await userService.updateUserById(userId, { password: newPassword });

  // Log the password reset action (don't log the actual password)
  logger.info(`Admin initiated password reset for user ID: ${userId}`);

  res.status(200).send({ message: '密码重置成功' });
});

/**
 * Get users for dropdown selection
 * @route GET /api/users/dropdown
 */
const getUsersForDropdown = catchAsync(async (req, res) => {
  const search = req.query.search || '';
  const users = await userService.getUsersForDropdown(search);
  res.send(users);
});

module.exports = {
  createUser,
  getUser,
  getMe,
  updateMyProfile,
  getMyPreferences,
  updateUser,
  deleteUser,
  getUsers,
  updateUserById,
  updateUsers,
  deleteUserById,
  updateBatchUsers,
  resetUserPassword,
  getUsersForDropdown,
};