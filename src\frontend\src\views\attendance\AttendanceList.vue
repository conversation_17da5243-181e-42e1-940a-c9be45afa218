<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">考勤管理</h1>
        <p class="page-subtitle">管理和跟踪员工出勤信息</p>
      </div>
      <div class="flex space-x-2">

        <router-link
          to="/attendance/create"
          class="btn btn-primary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          新增记录
        </router-link>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-card">
      <div class="flex justify-between gap-4">
        <div class="flex-1">
          <label for="employee-name" class="form-label font-medium">员工姓名</label>
          <div class="relative">
            <input
              type="text"
              id="employee-name"
              v-model="filters.employeeName"
              class="form-input pl-10 py-2 mt-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
              placeholder="请输入员工姓名"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
    
      </div>
      <div class="flex flex-col md:flex-row md:space-x-4 space-y-4 md:space-y-0">

        <div class="flex-1">
          <label for="date-from" class="form-label font-medium">日期范围</label>
          <div class="flex items-center space-x-2">
            <input
              type="date"
              id="date-from"
              v-model="filters.dateFrom"
              class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
            />
            <span class="mt-2">至</span>
            <input
              type="date"
              id="date-to"
              v-model="filters.dateTo"
              class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
            />
          </div>
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button @click="resetFilters" class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200">重置</button>
        <button @click="fetchAttendances(1)" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200">查询</button>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6">
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="font-medium">错误：</span>
            <span class="ml-2">{{ error }}</span>
          </div>
          <button @click="fetchAttendances()" class="text-red-700 hover:text-red-900 font-medium">
            重试
          </button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="card overflow-hidden shadow-md">
      <div class="overflow-x-auto border rounded-lg">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">员工姓名</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用工类型</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日期</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">工时</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200" v-if="!loading">
            <tr v-for="attendance in attendances" :key="attendance.id" class="hover:bg-gray-50 transition-colors duration-150">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ attendance.employeeName }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ getDepartmentName(attendance.department) || '—' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ attendance.employmentType || '—' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ attendance.project?.name || '—' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatDate(attendance.date) }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ attendance.hoursWorked || '—' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm">
                <span class="badge" :class="{
                  'badge-green': attendance.status === 'present',
                  'badge-red': attendance.status === 'absent',
                  'badge-yellow': attendance.status === 'late',
                  'badge-blue': attendance.status === 'half-day',
                  'badge-gray': !['present', 'absent', 'late', 'half-day'].includes(attendance.status)
                }">
                  {{ getStatusText(attendance.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-medium">
                <router-link
                  :to="`/attendance/edit/${attendance.id}`"
                  class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                >
                  <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                  </svg>
                  <span class="font-medium">编辑</span>
                </router-link>
                <button
                  @click="deleteAttendance(attendance.id)"
                  class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                >
                  <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                  </svg>
                  <span class="font-medium">删除</span>
                </button>
              </td>
            </tr>
            <tr v-if="attendances.length === 0">
              <td colspan="8" class="px-6 py-10 text-center text-gray-500">
                <div class="empty-state">
                  <svg class="empty-state-icon mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  <h3 class="empty-state-title">暂无考勤记录</h3>
                  <p class="empty-state-description">没有找到符合条件的考勤记录，请尝试调整筛选条件或添加新记录。</p>
                </div>
              </td>
            </tr>
          </tbody>
          <tbody v-else>
            <tr>
              <td class="px-6 py-10 text-center text-sm text-gray-500" colspan="8">
                <div class="flex flex-col items-center justify-center">
                  <svg class="animate-spin h-8 w-8 text-blue-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <p class="text-gray-600">正在加载数据，请稍候...</p>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="mt-4 flex justify-between items-center px-6 py-4 border-t border-gray-200">
        <div class="text-sm text-gray-500" v-if="pagination">
          显示第 <span class="font-medium">{{ (pagination.page - 1) * pagination.limit + 1 }}</span> 至
          <span class="font-medium">{{ Math.min(pagination.page * pagination.limit, pagination.totalResults) }}</span> 条，
          共 <span class="font-medium">{{ pagination.totalResults }}</span> 条记录
        </div>
        <div class="flex items-center space-x-3" v-if="pagination">
          <button
            @click="changePage(pagination.page - 1)"
            :disabled="pagination.page <= 1"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <span class="text-sm font-medium text-gray-700">第 {{ pagination.page }} 页，共 {{ pagination.totalPages }} 页</span>
          <button
            @click="changePage(pagination.page + 1)"
            :disabled="pagination.page >= pagination.totalPages"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import axios from 'axios';
import { useDepartmentStore } from '@/stores/department';
import attendanceService from '@/services/attendance.service';
import apiService from '@/services/apiService';

// 考勤数据
const attendances = ref([]);
const loading = ref(false);
const error = ref(null);
const successMessage = ref(null);
const pagination = ref(null);
// 用于前端筛选的原始数据
const allAttendances = ref([]);

// 显示成功消息并自动隐藏
const showSuccessMessage = (message, duration = 5000) => {
  successMessage.value = message;
  setTimeout(() => {
    successMessage.value = null;
  }, duration);
};

// 部门数据
const departmentStore = useDepartmentStore();
const departmentLoading = computed(() => departmentStore.isLoading);
const departmentOptions = computed(() => departmentStore.getDepartmentOptions);

// 筛选条件
const filters = ref({
  employeeName: '',
  department: '',
  employmentType: '',
  dateFrom: '',
  dateTo: ''
});

// 获取考勤列表
const fetchAttendances = async (page = 1) => {
  loading.value = true;
  error.value = null;

  try {
    // 构建查询参数
    const params = {
      page: page,
      limit: 10
    };

    if (filters.value.employeeName) params.employeeName = filters.value.employeeName;
    if (filters.value.department) params.department = filters.value.department;
    // 移除employmentType参数，后端不支持 - 将在前端进行筛选
    if (filters.value.dateFrom) params.dateFrom = filters.value.dateFrom;
    if (filters.value.dateTo) params.dateTo = filters.value.dateTo;

    // 调用API
    const response = await attendanceService.getAttendances(params);
    allAttendances.value = response.data.results || [];
    
    // 应用前端用工类型筛选
    applyEmploymentTypeFilter();
    
    pagination.value = response.data.pagination || null;
  } catch (err) {
    console.error('获取考勤数据失败:', err);
    error.value = '获取考勤数据失败，请重试';
  } finally {
    loading.value = false;
  }
};

// 前端用工类型筛选
const applyEmploymentTypeFilter = () => {
  if (!filters.value.employmentType) {
    // 如果没有选择用工类型，显示所有记录
    attendances.value = allAttendances.value;
  } else {
    // 按用工类型筛选
    attendances.value = allAttendances.value.filter(item => 
      item.employmentType === filters.value.employmentType
    );
  }
};

// 监听用工类型筛选变化
watch(() => filters.value.employmentType, () => {
  applyEmploymentTypeFilter();
});

// 分页
const changePage = (page) => {
  fetchAttendances(page);
};

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '—';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return dateString;

  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).replace(/\//g, '-');
};

// 获取状态显示文本
const getStatusText = (status) => {
  const statusMap = {
    'present': '出勤',
    'absent': '缺勤',
    'late': '迟到',
    'half-day': '半天'
  };
  return statusMap[status] || status;
};

// 获取状态显示样式
const getStatusClass = (status) => {
  const baseClasses = 'px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full';
  const statusClassMap = {
    'present': `${baseClasses} bg-green-100 text-green-800`,
    'absent': `${baseClasses} bg-red-100 text-red-800`,
    'late': `${baseClasses} bg-yellow-100 text-yellow-800`,
    'half-day': `${baseClasses} bg-blue-100 text-blue-800`
  };
  return statusClassMap[status] || `${baseClasses} bg-gray-100 text-gray-800`;
};

// 导出考勤数据
const exportAttendance = async () => {
  try {
    // 构建导出筛选参数，与查询相同
    const params = {};
    if (filters.value.employeeName) params.employeeName = filters.value.employeeName;
    if (filters.value.department) params.department = filters.value.department;
    if (filters.value.dateFrom) params.dateFrom = filters.value.dateFrom;
    if (filters.value.dateTo) params.dateTo = filters.value.dateTo;

    // 显示正在导出提示
    loading.value = true;
    successMessage.value = '尊敬的用户，系统正在为您准备导出数据，请您稍候片刻...';

    try {
      // 调用后端导出API
      const response = await attendanceService.exportAttendances(params);

      // 创建下载链接并模拟点击
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `考勤数据_${new Date().toISOString().split('T')[0]}.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      console.log('考勤数据导出成功');
    } catch (error) {
      console.error('导出API调用失败:', error);
      if (error.response && error.response.status === 401) {
        error.value = '很抱歉，您需要先登录系统才能导出数据。请您登录后再尝试此操作。';
      } else {
        error.value = '非常抱歉，导出功能暂时不可用。系统可能正在维护中，请您稍后再次尝试，感谢您的理解与耐心。';
      }
      loading.value = false;
    }
  } catch (err) {
    console.error('导出考勤数据失败:', err);
    error.value = '非常抱歉，导出考勤数据时遇到了问题。请检查您的网络连接并稍后再试，如果问题持续存在，请联系系统管理员获取帮助。';
    loading.value = false;
  }
};

// 删除考勤记录
const deleteAttendance = async (id) => {
  if (confirm('您确定要删除这条考勤记录吗？请注意，此操作执行后将无法恢复，相关的考勤数据将被永久移除。')) {
    try {
      await attendanceService.deleteAttendance(id);
      showSuccessMessage('恭喜！考勤记录已成功删除。系统已更新相关数据。');
      fetchAttendances();
    } catch (error) {
      console.error('删除考勤记录失败:', error);
      error.value = '很抱歉，删除考勤记录时遇到了问题。这可能是由于网络连接问题或系统暂时性故障，请稍后再次尝试。如果问题持续存在，请联系技术支持团队。';
    }
  }
};

// 重置筛选条件
const resetFilters = () => {
  filters.value = {
    employeeName: '',
    department: '',
    employmentType: '',
    dateFrom: '',
    dateTo: ''
  };
  fetchAttendances(1);
};

// 页面加载时获取数据
onMounted(() => {
  fetchAttendances();
  fetchDepartments();
});

// 获取部门数据
const fetchDepartments = async () => {
  try {
    await departmentStore.fetchDepartments();
  } catch (err) {
    console.error('获取部门数据失败:', err);
  }
};

// 根据部门ID获取部门名称
const getDepartmentName = (departmentId) => {
  if (!departmentId) return '未分配';
  
  const department = departmentStore.departments.find(dept => dept.id === departmentId);
  return department ? department.name : '未知部门';
};

// 使用防抖函数处理筛选条件变化
let debounceTimer;
watch(
  () => ({ ...filters.value }),
  () => {
    clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
      fetchAttendances(1); // 重置到第一页
    }, 500);
  },
  { deep: true }
);
</script>

<style scoped>
/* Page transitions */
.page-header, .filter-card, .card {
  animation: fadeSlideIn 0.6s ease-out;
}

.page-header {
  animation-delay: 0s;
}

.filter-card {
  animation-delay: 0.1s;
}

.card {
  animation-delay: 0.2s;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}
</style>