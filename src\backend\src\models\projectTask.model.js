const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

/**
 * @swagger
 * components:
 *   schemas:
 *     ProjectTask:
 *       type: object
 *       required:
 *         - projectId
 *         - name
 *         - taskType
 *         - assigneeId
 *         - plannedStartDate
 *         - plannedEndDate
 *         - createdBy
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: The auto-generated UUID of the project task
 *         projectId:
 *           type: string
 *           format: uuid
 *           description: The UUID of the project this task belongs to
 *         name:
 *           type: string
 *           description: The name of the project task
 *         taskType:
 *           type: string
 *           description: The type of the task
 *         status:
 *           type: string
 *           enum: [not_started, in_progress, completed, on_hold, cancelled]
 *           default: not_started
 *           description: The current status of the task
 *         priority:
 *           type: string
 *           enum: [low, medium, high, urgent]
 *           default: medium
 *           description: The priority level of the task
 *         assigneeId:
 *           type: string
 *           format: uuid
 *           description: The UUID of the user assigned to this task
 *         plannedStartDate:
 *           type: string
 *           format: date-time
 *           description: The planned start date of the task
 *         plannedEndDate:
 *           type: string
 *           format: date-time
 *           description: The planned end date of the task
 *         actualStartDate:
 *           type: string
 *           format: date-time
 *           description: The actual start date of the task
 *         actualEndDate:
 *           type: string
 *           format: date-time
 *           description: The actual end date of the task
 *         remainingWork:
 *           type: number
 *           format: float
 *           description: The estimated remaining work in hours
 *         description:
 *           type: string
 *           description: Detailed description of the task
 *         createdBy:
 *           type: string
 *           format: uuid
 *           description: The UUID of the user who created this task
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the task was created
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: The timestamp when the task was last updated
 *       example:
 *         id: 123e4567-e89b-12d3-a456-************
 *         projectId: 123e4567-e89b-12d3-a456-************
 *         name: Implement API endpoints
 *         taskType: Development
 *         status: in_progress
 *         priority: high
 *         assigneeId: 123e4567-e89b-12d3-a456-************
 *         plannedStartDate: 2023-01-01T00:00:00.000Z
 *         plannedEndDate: 2023-01-15T00:00:00.000Z
 *         actualStartDate: 2023-01-02T00:00:00.000Z
 *         actualEndDate: null
 *         remainingWork: 8.5
 *         description: Implement all required API endpoints for the project
 *         createdBy: 123e4567-e89b-12d3-a456-************
 *         createdAt: 2023-01-01T00:00:00.000Z
 *         updatedAt: 2023-01-05T00:00:00.000Z
 */

const ProjectTask = sequelize.define('projecttask', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'project',
      key: 'id'
    }
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true
    }
  },
  taskType: {
    type: DataTypes.STRING,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('not_started', 'in_progress', 'completed', 'on_hold', 'cancelled'),
    defaultValue: 'not_started'
  },
  priority: {
    type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
    defaultValue: 'medium'
  },
  assigneeId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'employee',
      key: 'id'
    }
  },
  plannedStartDate: {
    type: DataTypes.DATE,
    allowNull: false
  },
  plannedEndDate: {
    type: DataTypes.DATE,
    allowNull: false
  },
  actualStartDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  actualEndDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  remainingWork: {
    type: DataTypes.FLOAT,
    allowNull: true,
    defaultValue: 0
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  createdBy: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'employee',
      key: 'id'
    }
  }
}, {
  timestamps: true,
});

module.exports = { ProjectTask };