import { test, expect } from '@playwright/test';

test.describe('Performance Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 登录系统
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('页面加载性能测试', async ({ page }) => {
    // 测试仪表板加载性能
    const startTime = Date.now();
    await page.goto('/dashboard');
    const loadTime = Date.now() - startTime;
    
    // 验证加载时间在可接受范围内（例如3秒内）
    expect(loadTime).toBeLessThan(3000);
  });

  test('大数据列表渲染性能测试', async ({ page }) => {
    // 测试员工列表加载性能
    await page.goto('/employees');
    
    // 设置每页显示100条数据
    await page.selectOption('select[name="per_page"]', '100');
    
    // 测量列表渲染时间
    const startTime = Date.now();
    await page.waitForSelector('.employee-list');
    const renderTime = Date.now() - startTime;
    
    // 验证渲染时间在可接受范围内（例如2秒内）
    expect(renderTime).toBeLessThan(2000);
  });

  test('搜索响应性能测试', async ({ page }) => {
    // 测试库存搜索性能
    await page.goto('/inventory');
    
    // 测量搜索响应时间
    const startTime = Date.now();
    await page.fill('input[type="search"]', '测试物品');
    await page.keyboard.press('Enter');
    await page.waitForSelector('.search-results');
    const searchTime = Date.now() - startTime;
    
    // 验证搜索响应时间在可接受范围内（例如1秒内）
    expect(searchTime).toBeLessThan(1000);
  });

  test('表单提交性能测试', async ({ page }) => {
    // 测试项目创建性能
    await page.goto('/projects');
    await page.click('button:has-text("新建项目")');
    
    // 填写表单
    await page.fill('input[name="name"]', '性能测试项目');
    await page.fill('textarea[name="description"]', '这是一个性能测试项目');
    
    // 测量提交响应时间
    const startTime = Date.now();
    await page.click('button[type="submit"]');
    await page.waitForSelector('.success-message');
    const submitTime = Date.now() - startTime;
    
    // 验证提交响应时间在可接受范围内（例如2秒内）
    expect(submitTime).toBeLessThan(2000);
  });

  test('文件上传性能测试', async ({ page }) => {
    // 测试文档上传性能
    await page.goto('/documents');
    
    // 测量文件上传时间
    const startTime = Date.now();
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles('path/to/test-document.pdf');
    await page.waitForSelector('.upload-progress');
    await page.waitForSelector('.success-message');
    const uploadTime = Date.now() - startTime;
    
    // 验证上传时间在可接受范围内（例如5秒内）
    expect(uploadTime).toBeLessThan(5000);
  });

  test('数据导出性能测试', async ({ page }) => {
    // 测试财务数据导出性能
    await page.goto('/finance');
    
    // 测量导出时间
    const startTime = Date.now();
    await page.click('button:has-text("导出数据")');
    const downloadPromise = page.waitForEvent('download');
    const download = await downloadPromise;
    const exportTime = Date.now() - startTime;
    
    // 验证导出时间在可接受范围内（例如3秒内）
    expect(exportTime).toBeLessThan(3000);
  });

  test('并发请求性能测试', async ({ page }) => {
    // 测试多个并发请求的性能
    await page.goto('/dashboard');
    
    // 同时触发多个数据加载
    const startTime = Date.now();
    await Promise.all([
      page.goto('/projects'),
      page.goto('/employees'),
      page.goto('/inventory')
    ]);
    const concurrentTime = Date.now() - startTime;
    
    // 验证并发请求响应时间在可接受范围内（例如4秒内）
    expect(concurrentTime).toBeLessThan(4000);
  });

  test('内存使用性能测试', async ({ page }) => {
    // 测试长时间操作的内存使用
    await page.goto('/projects');
    
    // 执行多次操作
    for (let i = 0; i < 10; i++) {
      await page.click('button:has-text("新建项目")');
      await page.fill('input[name="name"]', `性能测试项目${i}`);
      await page.click('button[type="submit"]');
      await page.waitForSelector('.success-message');
    }
    
    // 验证页面仍然响应
    await expect(page.locator('.project-list')).toBeVisible();
  });
}); 