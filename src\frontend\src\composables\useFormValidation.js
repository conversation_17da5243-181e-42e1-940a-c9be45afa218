import { ref, reactive, computed } from 'vue';
import { validateForm } from '@/utils/validation';
import errorHandler from '@/services/errorHandler.service';

/**
 * Composable for form validation
 * @param {Object} initialFormData - Initial form data
 * @param {Object} validationSchema - Validation schema
 * @returns {Object} - Form validation utilities
 */
export default function useFormValidation(initialFormData, validationSchema) {
  // Create reactive form data
  const formData = reactive({ ...initialFormData });

  // Create reactive errors object
  const errors = reactive({});

  // Track if the form has been submitted
  const isSubmitted = ref(false);

  // Track if the form is currently being submitted
  const isSubmitting = ref(false);

  // Track if the form has been touched
  const touched = reactive({});

  // Computed property to check if the form is valid
  const isValid = computed(() => {
    const { isValid } = validateForm(formData, validationSchema);
    return isValid;
  });

  // Computed property to check if there are any errors
  const hasErrors = computed(() => Object.keys(errors).length > 0);

  /**
   * Validate the entire form
   * @returns {boolean} - True if the form is valid, false otherwise
   */
  const validateAll = () => {
    // 使用错误处理服务进行表单验证
    const { isValid, errors: validationErrors } = errorHandler.validateForm(formData, validationSchema);

    // Update errors object
    Object.keys(errors).forEach(key => {
      delete errors[key];
    });

    Object.keys(validationErrors).forEach(key => {
      errors[key] = validationErrors[key];
    });

    return isValid;
  };

  /**
   * Validate a specific field
   * @param {string} fieldName - The name of the field to validate
   * @returns {boolean} - True if the field is valid, false otherwise
   */
  const validateField = (fieldName) => {
    if (!validationSchema[fieldName]) {
      return true;
    }

    const fieldSchema = {
      [fieldName]: validationSchema[fieldName]
    };

    const fieldData = {
      [fieldName]: formData[fieldName]
    };

    // 使用错误处理服务进行字段验证
    const { isValid, errors: validationErrors } = errorHandler.validateForm(fieldData, fieldSchema);

    // Update errors object
    if (validationErrors[fieldName]) {
      errors[fieldName] = validationErrors[fieldName];
    } else {
      delete errors[fieldName];
    }

    return isValid;
  };

  /**
   * Mark a field as touched
   * @param {string} fieldName - The name of the field to mark as touched
   */
  const touchField = (fieldName) => {
    touched[fieldName] = true;
    validateField(fieldName);
  };

  /**
   * Reset the form to its initial state
   */
  const resetForm = () => {
    // Reset form data
    Object.keys(formData).forEach(key => {
      formData[key] = initialFormData[key];
    });

    // Reset errors
    Object.keys(errors).forEach(key => {
      delete errors[key];
    });

    // Reset touched state
    Object.keys(touched).forEach(key => {
      delete touched[key];
    });

    // Reset submission state
    isSubmitted.value = false;
    isSubmitting.value = false;
  };

  /**
   * Set form data
   * @param {Object} newData - The new form data
   */
  const setFormData = (newData) => {
    Object.keys(newData).forEach(key => {
      if (key in formData) {
        formData[key] = newData[key];
      }
    });
  };

  /**
   * Handle form submission
   * @param {Function} submitFn - The function to call when the form is submitted
   * @returns {Function} - A function that handles form submission
   */
  const handleSubmit = (submitFn) => {
    return async (event) => {
      // Prevent default form submission
      if (event) event.preventDefault();

      // Mark form as submitted
      isSubmitted.value = true;

      // Validate all fields
      const isFormValid = validateAll();

      if (!isFormValid) {
        return false;
      }

      // Mark form as submitting
      isSubmitting.value = true;

      try {
        // Call the submit function
        await submitFn(formData);
        return true;
      } catch (error) {
        // 使用错误处理服务处理表单提交错误
        console.error('Form submission error:', error);

        // 如果错误对象包含验证错误，则更新错误对象
        if (error.response?.data?.errors) {
          const validationErrors = error.response.data.errors;

          Object.keys(validationErrors).forEach(key => {
            if (key in formData) {
              errors[key] = validationErrors[key];
              touched[key] = true;
            }
          });
        }

        return false;
      } finally {
        // Mark form as no longer submitting
        isSubmitting.value = false;
      }
    };
  };

  return {
    formData,
    errors,
    touched,
    isValid,
    hasErrors,
    isSubmitted,
    isSubmitting,
    validateAll,
    validateField,
    touchField,
    resetForm,
    setFormData,
    handleSubmit
  };
}
