// 测试密码重置功能
async function testPasswordReset() {
  const API_BASE_URL = process.env.API_URL || 'http://localhost:3001';
  
  // 首先创建一个测试用户
  const testUser = {
    username: 'resettest' + Date.now(),
    email: 'resettest' + Date.now() + '@example.com',
    password: 'originalpassword123',
    lastName: '重置测试用户',
    role: 'user'
  };

  try {
    console.log('🔄 开始测试密码重置功能...');
    
    // 1. 创建用户
    console.log('📝 创建测试用户:', testUser.username);
    const createResponse = await fetch(`${API_BASE_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testUser)
    });
    
    if (!createResponse.ok) {
      const errorData = await createResponse.text();
      throw new Error(`创建用户失败: ${createResponse.status} - ${errorData}`);
    }
    
    const createData = await createResponse.json();
    console.log('✅ 用户创建成功');
    
    // 2. 验证原始密码可以登录
    console.log('🔐 验证原始密码登录...');
    const originalLoginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    if (!originalLoginResponse.ok) {
      throw new Error(`原始密码登录失败: ${originalLoginResponse.status}`);
    }
    
    const originalLoginData = await originalLoginResponse.json();
    console.log('✅ 原始密码登录成功');
    
    // 获取管理员token（假设需要管理员权限来重置密码）
    const adminToken = originalLoginData.tokens?.access?.token;
    
    // 3. 重置密码
    const newPassword = 'newpassword123';
    console.log('🔄 重置密码...');
    
    const resetResponse = await fetch(`${API_BASE_URL}/api/users/${createData.id}/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${adminToken}`
      },
      body: JSON.stringify({
        newPassword: newPassword
      })
    });
    
    if (!resetResponse.ok) {
      const errorData = await resetResponse.text();
      throw new Error(`密码重置失败: ${resetResponse.status} - ${errorData}`);
    }
    
    console.log('✅ 密码重置成功');
    
    // 4. 验证原始密码不能登录
    console.log('🚫 验证原始密码不能登录...');
    const oldPasswordResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    if (oldPasswordResponse.ok) {
      throw new Error('原始密码仍然可以登录，密码重置可能失败');
    }
    console.log('✅ 原始密码已失效');
    
    // 5. 验证新密码可以登录
    console.log('🔐 验证新密码登录...');
    const newPasswordResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: newPassword
      })
    });
    
    if (!newPasswordResponse.ok) {
      const errorData = await newPasswordResponse.text();
      throw new Error(`新密码登录失败: ${newPasswordResponse.status} - ${errorData}`);
    }
    
    const newLoginData = await newPasswordResponse.json();
    console.log('✅ 新密码登录成功!');
    console.log('👤 用户信息:', {
      id: newLoginData.user?.id,
      username: newLoginData.user?.username,
      email: newLoginData.user?.email
    });
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 运行测试
testPasswordReset()
  .then(success => {
    if (success) {
      console.log('\n🎉 密码重置功能测试通过！');
    } else {
      console.log('\n💥 密码重置功能测试失败。');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 测试执行出错:', error);
    process.exit(1);
  }); 