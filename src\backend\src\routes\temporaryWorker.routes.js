const express = require('express');
const router = express.Router();
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const employeeController = require('../controllers/employee.controller');
const employeeValidation = require('../validations/employee.validation');

/**
 * @route GET /api/employees/temporary-workers
 * @desc Get all temporary workers
 * @access Private
 */
router.get('/', auth('getEmployees'), validate(employeeValidation.getTemporaryWorkers), employeeController.getTemporaryWorkers);

/**
 * @route POST /api/employees/temporary-workers
 * @desc Create a new temporary worker
 * @access Private
 */
router.post('/', auth('manageEmployees'), validate(employeeValidation.createTemporaryWorker), employeeController.createTemporaryWorker);

/**
 * @route GET /api/employees/temporary-workers/:workerId
 * @desc Get a temporary worker by ID
 * @access Private
 */
router.get('/:workerId', auth('getEmployees'), validate(employeeValidation.getTemporaryWorker), employeeController.getTemporaryWorker);

/**
 * @route PUT /api/employees/temporary-workers/:workerId
 * @desc Update a temporary worker
 * @access Private
 */
router.put('/:workerId', auth('manageEmployees'), validate(employeeValidation.updateTemporaryWorker), employeeController.updateTemporaryWorker);

/**
 * @route DELETE /api/employees/temporary-workers/:workerId
 * @desc Delete a temporary worker
 * @access Private
 */
router.delete('/:workerId', auth('manageEmployees'), validate(employeeValidation.deleteTemporaryWorker), employeeController.deleteTemporaryWorker);

module.exports = router;
