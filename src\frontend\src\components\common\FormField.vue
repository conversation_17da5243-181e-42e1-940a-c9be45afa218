<template>
  <div class="form-field">
    <!-- 字段标签 -->
    <label :for="id" class="form-label">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    
    <!-- 文本输入框 -->
    <input
      v-if="type === 'text' || type === 'email' || type === 'password' || type === 'number' || type === 'tel' || type === 'url' || type === 'date'"
      :id="id"
      :type="type"
      :value="modelValue"
      @input="updateValue($event.target.value)"
      @blur="handleBlur"
      class="form-input"
      :class="fieldClasses"
      :placeholder="placeholder"
      :required="required"
      :min="min"
      :max="max"
      :step="step"
      :disabled="disabled"
      :readonly="readonly"
    />
    
    <!-- 文本区域 -->
    <textarea
      v-else-if="type === 'textarea'"
      :id="id"
      :value="modelValue"
      @input="updateValue($event.target.value)"
      @blur="handleBlur"
      class="form-input"
      :class="fieldClasses"
      :placeholder="placeholder"
      :required="required"
      :rows="rows || 3"
      :disabled="disabled"
      :readonly="readonly"
    ></textarea>
    
    <!-- 下拉选择框 -->
    <select
      v-else-if="type === 'select'"
      :id="id"
      :value="modelValue"
      @input="updateValue($event.target.value)"
      @blur="handleBlur"
      class="form-input"
      :class="fieldClasses"
      :required="required"
      :disabled="disabled"
    >
      <option v-if="placeholder" value="">{{ placeholder }}</option>
      <option
        v-for="option in options"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </option>
    </select>
    
    <!-- 复选框 -->
    <div v-else-if="type === 'checkbox'" class="flex items-center">
      <input
        :id="id"
        type="checkbox"
        :checked="!!modelValue"
        @input="updateValue($event.target.checked)"
        @blur="handleBlur"
        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        :disabled="disabled"
      />
      <label :for="id" class="ml-2 block text-sm text-gray-700">
        {{ checkboxLabel || label }}
      </label>
    </div>
    
    <!-- 单选按钮组 -->
    <div v-else-if="type === 'radio'" class="space-y-2">
      <div
        v-for="option in options"
        :key="option.value"
        class="flex items-center"
      >
        <input
          :id="`${id}-${option.value}`"
          type="radio"
          :name="id"
          :value="option.value"
          :checked="modelValue === option.value"
          @input="updateValue(option.value)"
          @blur="handleBlur"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
          :disabled="disabled"
        />
        <label :for="`${id}-${option.value}`" class="ml-2 block text-sm text-gray-700">
          {{ option.label }}
        </label>
      </div>
    </div>
    
    <!-- 错误消息 -->
    <p v-if="touched && error" class="mt-1 text-sm text-red-600">{{ error }}</p>
    
    <!-- 帮助文本 -->
    <p v-if="helpText && (!touched || !error)" class="mt-1 text-sm text-gray-500">{{ helpText }}</p>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';

// 组件属性
const props = defineProps({
  // 基本属性
  id: {
    type: String,
    required: true
  },
  label: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Number, Boolean, Array, Object],
    default: ''
  },
  type: {
    type: String,
    default: 'text',
    validator: (value) => [
      'text', 'email', 'password', 'number', 'tel', 'url', 'date',
      'textarea', 'select', 'checkbox', 'radio'
    ].includes(value)
  },
  placeholder: {
    type: String,
    default: ''
  },
  
  // 验证相关
  required: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: ''
  },
  touched: {
    type: Boolean,
    default: false
  },
  
  // 特定类型的属性
  options: {
    type: Array,
    default: () => []
  },
  rows: {
    type: Number,
    default: 3
  },
  min: {
    type: [Number, String],
    default: null
  },
  max: {
    type: [Number, String],
    default: null
  },
  step: {
    type: [Number, String],
    default: null
  },
  checkboxLabel: {
    type: String,
    default: ''
  },
  
  // 状态属性
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  
  // 辅助信息
  helpText: {
    type: String,
    default: ''
  }
});

// 事件
const emit = defineEmits(['update:modelValue', 'blur']);

// 计算字段的CSS类
const fieldClasses = computed(() => ({
  'border-red-500 focus:ring-red-500': props.touched && props.error,
  'border-gray-300 focus:ring-blue-500': !props.touched || !props.error,
  'bg-gray-100': props.disabled || props.readonly
}));

// 更新值
const updateValue = (value) => {
  // 对于数字类型，转换为数字
  if (props.type === 'number') {
    value = value === '' ? '' : Number(value);
  }
  
  emit('update:modelValue', value);
};

// 处理失去焦点事件
const handleBlur = () => {
  emit('blur');
};
</script>

<style scoped>
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 transition-colors duration-200;
}
</style>
