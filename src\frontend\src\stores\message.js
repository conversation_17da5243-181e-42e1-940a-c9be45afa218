import { defineStore } from 'pinia';
import { ref } from 'vue';

export const useMessageStore = defineStore('message', () => {
  const messages = ref([]);
  const messageId = ref(0);

  /**
   * 添加一条消息
   * @param {Object} config 消息配置
   * @param {String} config.type 消息类型: 'success', 'error', 'warning', 'info'
   * @param {String} config.title 消息标题
   * @param {String} [config.message] 消息内容
   * @param {Number} [config.duration] 显示时间 (毫秒)
   * @param {String} [config.position] 位置: 'top-right', 'top-left', 'top-center', 'bottom-right', 'bottom-left', 'bottom-center'
   * @param {Boolean} [config.closable] 是否可关闭
   * @param {Boolean} [config.autodismiss] 是否自动关闭
   * @returns {Number} 消息ID，可用于关闭特定消息
   */
  function addMessage(config) {
    const id = messageId.value++;
    const message = {
      id,
      ...config,
      type: config.type || 'info',
      duration: config.duration !== undefined ? config.duration : 3005,
      position: config.position || 'top-right',
      closable: config.closable !== undefined ? config.closable : true,
      autodismiss: config.autodismiss !== undefined ? config.autodismiss : true
    };
    
    messages.value.push(message);
    return id;
  }

  /**
   * 移除指定ID的消息
   * @param {Number} id 消息ID
   */
  function removeMessage(id) {
    const index = messages.value.findIndex(m => m.id === id);
    if (index !== -1) {
      messages.value.splice(index, 1);
    }
  }

  /**
   * 添加成功消息
   */
  function success(title, message, options = {}) {
    return addMessage({ type: 'success', title, message, ...options });
  }

  /**
   * 添加错误消息
   */
  function error(title, message, options = {}) {
    return addMessage({ type: 'error', title, message, ...options });
  }

  /**
   * 添加警告消息
   */
  function warning(title, message, options = {}) {
    return addMessage({ type: 'warning', title, message, ...options });
  }

  /**
   * 添加信息消息
   */
  function info(title, message, options = {}) {
    return addMessage({ type: 'info', title, message, ...options });
  }

  /**
   * 清除所有消息
   */
  function clearAll() {
    messages.value = [];
  }

  return {
    messages,
    addMessage,
    removeMessage,
    success,
    error,
    warning,
    info,
    clearAll
  };
}); 