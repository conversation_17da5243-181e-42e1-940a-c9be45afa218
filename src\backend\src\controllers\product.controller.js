const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const { productService } = require('../services');

/**
 * Create a new product
 * @route POST /api/products
 */
const createProduct = catchAsync(async (req, res) => {
  const product = await productService.createProduct({
    ...req.body,
    createdBy: req.user.id
  });
  res.status(201).send(product);
});

/**
 * Get a product by ID
 * @route GET /api/products/:productId
 */
const getProduct = catchAsync(async (req, res) => {
  const product = await productService.getProductById(req.params.productId);
  res.send(product);
});

/**
 * Update a product
 * @route PATCH /api/products/:productId
 */
const updateProduct = catchAsync(async (req, res) => {
  const product = await productService.updateProductById(req.params.productId, req.body);
  res.send(product);
});

/**
 * Delete a product
 * @route DELETE /api/products/:productId
 */
const deleteProduct = catchAsync(async (req, res) => {
  await productService.deleteProductById(req.params.productId);
  res.status(204).send();
});

/**
 * Get all products with filtering
 * @route GET /api/products
 */
const getProducts = catchAsync(async (req, res) => {
  const filter = req.query;
  const options = {
    sortBy: req.query.sortBy,
    limit: req.query.limit,
    page: req.query.page,
    sortOrder: req.query.sortOrder || 'asc',
  };
  const result = await productService.queryProducts(filter, options);
  res.send(result);
});

// New inventory management methods
const getInventoryOverview = catchAsync(async (req, res) => {
  const overview = await productService.getInventoryOverview();
  res.send(overview);
});

const getStockAlerts = catchAsync(async (req, res) => {
  const alerts = await productService.getStockAlerts();
  res.send(alerts);
});

const batchUpdateStock = catchAsync(async (req, res) => {
  const updates = await productService.batchUpdateStock(req.body.updates);
  res.send(updates);
});

const getStockLevelReport = catchAsync(async (req, res) => {
  const filter = req.query;
  const report = await productService.getStockLevelReport(filter);
  res.send(report);
});

module.exports = {
  createProduct,
  getProduct,
  getProducts,
  updateProduct,
  deleteProduct,
  getInventoryOverview,
  getStockAlerts,
  batchUpdateStock,
  getStockLevelReport,
}; 