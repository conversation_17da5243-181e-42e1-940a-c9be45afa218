const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { clientService } = require('../services');
const ApiError = require('../utils/ApiError');

/**
 * Create a new client
 * @route POST /api/clients
 */
const createClient = catchAsync(async (req, res) => {
  try {
    const client = await clientService.createClient({
      ...req.body,
      createdBy: req.user.id
    });
    res.status(200).send(client);
  } catch (error) {
    throw new ApiError(
      error.statusCode ,
      error.message 
    );
  }
});

/**
 * Get a client by ID
 * @route GET /api/clients/:id
 */
const getClient = catchAsync(async (req, res) => {
  try {
    const client = await clientService.getClientById(req.params.id);
    if (!client) {
      throw new ApiError(404, 'Client not found');
    }
    res.status(200).send(client);
  } catch (error) {
    throw new ApiError(
      error.statusCode || 500,
      error.message || 'Error retrieving client'
    );
  }
});

/**
 * Update a client
 * @route PATCH /api/clients/:id
 */
const updateClient = catchAsync(async (req, res) => {
  try {
    const client = await clientService.updateClientById(req.params.id, req.body);
    res.status(200).send(client);
  } catch (error) {
    throw new ApiError(
      error.statusCode || 500,
      error.message || 'Error updating client'
    );
  }
});

/**
 * Delete a client
 * @route DELETE /api/clients/:id
 */
const deleteClient = catchAsync(async (req, res) => {
  try {
    await clientService.deleteClientById(req.params.id);
    res.status(204).send();
  } catch (error) {
    throw new ApiError(
      error.statusCode || 500,
      error.message || 'Error deleting client'
    );
  }
});

/**
 * Get all clients with pagination
 * @route GET /api/clients
 */
const getClients = catchAsync(async (req, res) => {
  try {
    const filter = {
      type: req.query.type,
      status: req.query.status,
      search: req.query.search
    };
    const options = {
      page: parseInt(req.query.page, 10) || 1,
      limit: parseInt(req.query.limit, 10) || 10,
      sortBy: req.query.sortBy || 'createdAt',
      sortOrder: req.query.sortOrder || 'desc'
    };
    const result = await clientService.queryClients(filter, options);
    res.status(200).send(result);
  } catch (error) {
    throw new ApiError(
      error.statusCode || 500,
      error.message || 'Error retrieving clients'
    );
  }
});

module.exports = {
  createClient,
  getClient,
  updateClient,
  deleteClient,
  getClients
};
