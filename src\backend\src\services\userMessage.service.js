const httpStatus = require('http-status');
const { UserMessage, User } = require('../models');
const ApiError = require('../utils/ApiError');
const { Op } = require('sequelize');

/**
 * 创建用户消息
 * @param {Object} messageData - 消息数据
 * @returns {Promise<UserMessage>}
 */
const createUserMessage = async (messageData) => {
  try {
    console.log('服务层创建消息，数据:', messageData);

    // 确保userId是字符串类型
    if (messageData.userId && typeof messageData.userId !== 'string') {
      messageData.userId = String(messageData.userId);
    }

    // 验证必填字段
    if (!messageData.userId) {
      // 使用明确的数字状态码，避免可能的undefined问题
      throw new ApiError(400, '用户ID不能为空');
    }

    if (!messageData.title) {
      // 使用明确的数字状态码，避免可能的undefined问题
      throw new ApiError(400, '消息标题不能为空');
    }

    if (!messageData.content) {
      // 使用明确的数字状态码，避免可能的undefined问题
      throw new ApiError(400, '消息内容不能为空');
    }

    // 验证用户是否存在
    const user = await User.findByPk(messageData.userId);
    if (!user) {
      // 使用明确的数字状态码，避免可能的undefined问题
      throw new ApiError(404, '用户不存在');
    }

    // 创建消息
    return UserMessage.create(messageData);
  } catch (error) {
    console.error('创建消息失败:', error);
    throw error;
  }
};

/**
 * 获取用户消息列表
 * @param {Object} filter - 过滤条件
 * @param {Object} options - 查询选项
 * @returns {Promise<Object>} - 包含结果和分页信息的对象
 */
const getUserMessages = async (filter, options) => {
  const { userId, isRead, type } = filter;
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;

  // 构建查询条件
  const whereClause = { userId };

  if (isRead !== undefined) {
    whereClause.isRead = isRead === 'true';
  }

  if (type) {
    whereClause.type = type;
  }

  // 计算偏移量
  const offset = (page - 1) * limit;

  // 执行查询
  const { rows: messages, count } = await UserMessage.findAndCountAll({
    where: whereClause,
    offset,
    limit,
    order: [[sortBy, sortOrder]],
    include: [
      {
        model: User,
        as: 'User',
        attributes: ['id', 'username', 'email']
      }
    ]
  });

  return {
    results: messages,
    totalPages: Math.ceil(count / limit),
    totalResults: count,
    currentPage: page
  };
};

/**
 * 获取用户未读消息数量
 * @param {string} userId - 用户ID
 * @returns {Promise<number>} - 未读消息数量
 */
const getUnreadMessageCount = async (userId) => {
  return UserMessage.count({
    where: {
      userId,
      isRead: false
    }
  });
};

/**
 * 根据ID获取消息
 * @param {string} id - 消息ID
 * @returns {Promise<UserMessage>}
 */
const getUserMessageById = async (id) => {
  const message = await UserMessage.findByPk(id, {
    include: [
      {
        model: User,
        as: 'User',
        attributes: ['id', 'username', 'email']
      }
    ]
  });

  if (!message) {
    // 使用明确的数字状态码，避免可能的undefined问题
    throw new ApiError(404, '消息不存在');
  }

  return message;
};

/**
 * 标记消息为已读
 * @param {string} id - 消息ID
 * @returns {Promise<UserMessage>}
 */
const markMessageAsRead = async (id) => {
  const message = await getUserMessageById(id);

  if (message.isRead) {
    return message;
  }

  message.isRead = true;
  await message.save();

  return message;
};

/**
 * 标记用户所有消息为已读
 * @param {string} userId - 用户ID
 * @returns {Promise<number>} - 更新的消息数量
 */
const markAllMessagesAsRead = async (userId) => {
  const [updatedCount] = await UserMessage.update(
    { isRead: true },
    {
      where: {
        userId,
        isRead: false
      }
    }
  );

  return updatedCount;
};

/**
 * 删除消息
 * @param {string} id - 消息ID
 * @returns {Promise<boolean>}
 */
const deleteUserMessage = async (id) => {
  const message = await getUserMessageById(id);
  await message.destroy();
  return true;
};

module.exports = {
  createUserMessage,
  getUserMessages,
  getUserMessageById,
  markMessageAsRead,
  markAllMessagesAsRead,
  getUnreadMessageCount,
  deleteUserMessage
};
