const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { procurementService } = require('../services');

/**
 * Create a new procurement
 * @route POST /api/procurements
 */
const createProcurement = catchAsync(async (req, res) => {
  const procurement = await procurementService.createProcurement({
    ...req.body,
    createdBy: req.user.id
  });
  res.status(201).send(procurement);
});

/**
 * Get a procurement by ID
 * @route GET /api/procurements/:procurementId
 */
const getProcurement = catchAsync(async (req, res) => {
  const procurement = await procurementService.getProcurementById(req.params.procurementId);
  res.send(procurement);
});

/**
 * Update a procurement
 * @route PATCH /api/procurements/:procurementId
 */
const updateProcurement = catchAsync(async (req, res) => {
  const procurement = await procurementService.updateProcurementById(req.params.procurementId, req.body);
  res.send(procurement);
});

/**
 * Delete a procurement
 * @route DELETE /api/procurements/:procurementId
 */
const deleteProcurement = catchAsync(async (req, res) => {
  await procurementService.deleteProcurementById(req.params.procurementId);
  res.status(204).send();
});

/**
 * Get all procurements with pagination
 * @route GET /api/procurements
 */
const getProcurements = catchAsync(async (req, res) => {
  const filter = {
    projectId: req.query.projectId,
    supplierId: req.query.supplierId,
    status: req.query.status,
    search: req.query.search,
    procurementDateFrom: req.query.procurementDateFrom,
    procurementDateTo: req.query.procurementDateTo
  };
  const options = {
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10,
    sortBy: req.query.sortBy || 'createdAt',
    sortOrder: req.query.sortOrder || 'desc'
  };
  const result = await procurementService.queryProcurements(filter, options);
  res.send(result);
});

/**
 * Get all procurements for a project
 * @route GET /api/projects/:projectId/procurements
 */
const getProjectProcurements = catchAsync(async (req, res) => {
  const filter = {
    projectId: req.params.projectId,
    supplierId: req.query.supplierId,
    status: req.query.status,
    search: req.query.search
  };
  const options = {
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10,
    sortBy: req.query.sortBy || 'createdAt',
    sortOrder: req.query.sortOrder || 'desc'
  };
  const result = await procurementService.queryProcurements(filter, options);
  res.send(result);
});

module.exports = {
  createProcurement,
  getProcurement,
  updateProcurement,
  deleteProcurement,
  getProcurements,
  getProjectProcurements
}; 