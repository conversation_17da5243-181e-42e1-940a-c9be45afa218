const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { Supplier, User } = require('../models');
const { roles } = require('../config/roles');

/**
 * Create a supplier
 * @param {Object} supplierBody
 * @returns {Promise<Supplier>}
 */
const createSupplier = async (supplierBody) => {
  return Supplier.create(supplierBody);
};

/**
 * Get supplier by id
 * @param {string} id - UUID of the supplier
 * @returns {Promise<Supplier>}
 */
const getSupplierById = async (id) => {
  const supplier = await Supplier.findByPk(id, {
    include: [
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'email']
      }
    ]
  });

  if (!supplier) {
    throw new ApiError(404, 'Supplier not found');
  }
  return supplier;
};

/**
 * Update supplier by id
 * @param {string} supplierId - UUID of the supplier
 * @param {Object} updateBody
 * @returns {Promise<Supplier>}
 */
const updateSupplierById = async (supplierId, updateBody) => {
  const supplier = await getSupplierById(supplierId);
  Object.assign(supplier, updateBody);
  await supplier.save();
  return supplier;
};

/**
 * Delete supplier by id
 * @param {string} supplierId - UUID of the supplier
 * @returns {Promise<void>}
 */
const deleteSupplierById = async (supplierId) => {
  const supplier = await getSupplierById(supplierId);
  await supplier.destroy();
};

/**
 * Query for suppliers
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @param {Object} [user] - Current user (optional for public endpoints)
 * @returns {Promise<Object>} - Suppliers and pagination info
 */
const querySuppliers = async (filter, options, user) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};

  if (filter.category) {
    whereClause.category = filter.category;
  }

  if (filter.rating) {
    whereClause.rating = filter.rating;
  }

  if (filter.search) {
    whereClause[Op.or] = [
      { name: { [Op.iLike]: `%${filter.search}%` } },
      { contactName: { [Op.iLike]: `%${filter.search}%` } },
      { contactPhone: { [Op.iLike]: `%${filter.search}%` } },
      { contactEmail: { [Op.iLike]: `%${filter.search}%` } },
      { notes: { [Op.iLike]: `%${filter.search}%` } }
    ];
  }

  if (filter.tags && filter.tags.length > 0) {
    whereClause.tags = { [Op.overlap]: filter.tags };
  }

  try {
    // Check user role for access control
    // Admin can see all suppliers
    // Project managers and regular users can only see suppliers they created
    // For public endpoints, user might be undefined
    if (user && user.role !== 'admin') {
      whereClause.createdBy = user.id;
    }

    // Query with pagination
    const { count, rows } = await Supplier.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'username', 'email']
        }
      ],
      order: [[sortBy, sortOrder.toUpperCase()]],
      limit,
      offset,
      distinct: true
    });

    return {
      results: rows,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(count / limit),
        totalResults: count
      }
    };
  } catch (error) {
    console.error('Error in querySuppliers:', error);
    console.error('Original error stack:', error.stack);
    throw new ApiError(500, `Error querying suppliers: ${error.message}`);
  }
};

module.exports = {
  createSupplier,
  getSupplierById,
  updateSupplierById,
  deleteSupplierById,
  querySuppliers
};