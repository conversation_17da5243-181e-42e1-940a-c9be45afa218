import { defineStore } from 'pinia';
import { ref, computed, watch } from 'vue';

// Define available themes
const THEMES = {
  DEFAULT: 'default',
  DARK: 'dark',
  BLUE: 'blue',
  GREEN: 'green',
};

// Define color modes (light/dark)
const COLOR_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
};

export const useThemeStore = defineStore('theme', () => {
  // Theme variables
  const currentTheme = ref(localStorage.getItem('user-theme') || THEMES.DEFAULT);
  const colorMode = ref(localStorage.getItem('color-mode') || COLOR_MODES.LIGHT);
  const followSystem = ref(localStorage.getItem('follow-system') === 'true');
  const systemPrefersDark = ref(false);

  // Computed properties
  const isDarkMode = computed(() => colorMode.value === COLOR_MODES.DARK);
  const availableThemes = computed(() => Object.values(THEMES));

  // Initialize system preference detection
  if (window.matchMedia) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    systemPrefersDark.value = mediaQuery.matches;

    // Listen for changes in system preference
    mediaQuery.addEventListener('change', (e) => {
      systemPrefersDark.value = e.matches;
      if (followSystem.value) {
        setColorMode(e.matches ? COLOR_MODES.DARK : COLOR_MODES.LIGHT);
      }
    });
  }

  // Watch for changes in theme and update document
  watch(currentTheme, (newTheme) => {
    localStorage.setItem('user-theme', newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  });

  // Watch for changes in color mode and update document
  watch(colorMode, (newMode) => {
    localStorage.setItem('color-mode', newMode);
    updateColorMode(newMode);
  });

  // Watch for changes in followSystem
  watch(followSystem, (newValue) => {
    localStorage.setItem('follow-system', newValue);
    if (newValue) {
      setColorMode(systemPrefersDark.value ? COLOR_MODES.DARK : COLOR_MODES.LIGHT);
    }
  });

  // Set the theme
  function setTheme(theme) {
    if (Object.values(THEMES).includes(theme)) {
      currentTheme.value = theme;
    }
  }

  // Set the color mode
  function setColorMode(mode) {
    if (Object.values(COLOR_MODES).includes(mode)) {
      colorMode.value = mode;
    }
  }

  // Toggle between light and dark modes
  function toggleColorMode() {
    colorMode.value = colorMode.value === COLOR_MODES.LIGHT ? COLOR_MODES.DARK : COLOR_MODES.LIGHT;
    followSystem.value = false; // Disable follow system when manually toggling
  }

  // Set whether to follow system preference
  function setFollowSystem(value) {
    followSystem.value = value;
    if (value) {
      setColorMode(systemPrefersDark.value ? COLOR_MODES.DARK : COLOR_MODES.LIGHT);
    }
  }

  // Update the document color mode
  function updateColorMode(mode) {
    if (mode === COLOR_MODES.DARK) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }

  // Initialize theme on store creation
  function initTheme() {
    // Apply the theme to the document
    document.documentElement.setAttribute('data-theme', currentTheme.value);

    // Apply the color mode
    updateColorMode(colorMode.value);

    // If following system preference, set color mode accordingly
    if (followSystem.value) {
      setColorMode(systemPrefersDark.value ? COLOR_MODES.DARK : COLOR_MODES.LIGHT);
    }
  }

  // Initialize theme
  initTheme();

  return {
    // State
    currentTheme,
    colorMode,
    followSystem,
    systemPrefersDark,

    // Getters
    isDarkMode,
    availableThemes,

    // Actions
    setTheme,
    setColorMode,
    toggleColorMode,
    setFollowSystem,
    initTheme,

    // Constants
    THEMES,
    COLOR_MODES
  };
});