<template>
  <div class="page-container">
    <!-- 页面标题和操作按钮同一行 -->
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center">
        <h1 class="page-title text-2xl font-bold text-gray-800">{{ formTitle }}</h1>
      </div>
      <router-link to="/products" class="btn btn-secondary shadow-sm hover:shadow-md transition-all duration-200 flex items-center">
        <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        返回列表
      </router-link>
    </div>
    <p class="page-subtitle text-sm text-gray-500 mb-6">{{ isEditMode ? '修改现有产品信息' : '创建新的产品记录' }}</p>

    <!-- 错误提示 -->
    <div v-if="formMessage.show" class="mb-6 p-4 rounded-md border-l-4" :class="[
      formMessage.type === 'error' ? 'bg-red-100 border-red-500 text-red-700' : 
      formMessage.type === 'success' ? 'bg-green-100 border-green-500 text-green-700' :
      'bg-blue-100 border-blue-500 text-blue-700'
    ]">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="formMessage.icon"></path>
        </svg>
        <span class="font-medium">{{ 
          formMessage.type === 'error' ? '错误：' : 
          formMessage.type === 'success' ? '成功：' : '提示：'
        }}</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ formMessage.text }}</p>
    </div>

    <!-- 表单内容 -->
    <div class="card shadow-md">
      <form class="space-y-6">
        <!-- 基本信息 -->
        <div class="form-section">
          <h2 class="form-section-title">基本信息</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="name" class="form-label">产品名称 <span class="form-required">*</span></label>
              <input
                type="text"
                id="name"
                v-model="productForm.name"
                @blur="handleBlur('name')"
                class="form-input"
                :class="{
                  'border-red-500 focus:ring-red-500': touched.name && errors.name,
                  'border-gray-300': !touched.name || !errors.name
                }"
                placeholder="请输入产品名称"
              />
              <p v-if="touched.name && errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
            </div>

            <div>
              <label for="code" class="form-label">产品编码 <span class="form-required">*</span></label>
              <input
                type="text"
                id="code"
                v-model="productForm.code"
                @blur="handleBlur('code')"
                class="form-input"
                :class="{
                  'border-red-500 focus:ring-red-500': touched.code && errors.code,
                  'border-gray-300': !touched.code || !errors.code
                }"
                placeholder="请输入产品编码"
              />
              <p v-if="touched.code && errors.code" class="mt-1 text-sm text-red-600">{{ errors.code }}</p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            <div>
              <label for="category" class="form-label">产品分类 <span class="form-required">*</span></label>
              <select
                id="category"
                v-model="productForm.category"
                @blur="handleBlur('category')"
                class="form-input"
                :class="{
                  'border-red-500 focus:ring-red-500': touched.category && errors.category,
                  'border-gray-300': !touched.category || !errors.category
                }"
              >
                <option v-for="option in categories" :key="option.value" :value="option.value">{{ option.label }}</option>
              </select>
              <p v-if="touched.category && errors.category" class="mt-1 text-sm text-red-600">{{ errors.category }}</p>
            </div>

            <div>
              <label for="status" class="form-label">产品状态 <span class="form-required">*</span></label>
              <select
                id="status"
                v-model="productForm.status"
                @blur="handleBlur('status')"
                class="form-input"
                :class="{
                  'border-red-500 focus:ring-red-500': touched.status && errors.status,
                  'border-gray-300': !touched.status || !errors.status
                }"
              >
                <option v-for="option in statuses" :key="option.value" :value="option.value">{{ option.label }}</option>
              </select>
              <p v-if="touched.status && errors.status" class="mt-1 text-sm text-red-600">{{ errors.status }}</p>
            </div>
          </div>
        </div>

        <!-- 价格库存 -->
        <div class="form-section">
          <h2 class="form-section-title">价格库存</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label for="price" class="form-label">单价 <span class="form-required">*</span></label>
              <div class="mt-1 relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span class="text-gray-500 sm:text-sm">¥</span>
                </div>
                <input
                  type="number"
                  id="price"
                  v-model="productForm.price"
                  @blur="handleBlur('price')"
                  min="0"
                  step="0.01"
                  class="form-input pl-8"
                  :class="{
                    'border-red-500 focus:ring-red-500': touched.price && errors.price,
                    'border-gray-300': !touched.price || !errors.price
                  }"
                  placeholder="0.00"
                />
              </div>
              <p v-if="touched.price && errors.price" class="mt-1 text-sm text-red-600">{{ errors.price }}</p>
            </div>

            <div>
              <label for="unit" class="form-label">单位 <span class="form-required">*</span></label>
              <input
                type="text"
                id="unit"
                v-model="productForm.unit"
                @blur="handleBlur('unit')"
                class="form-input"
                :class="{
                  'border-red-500 focus:ring-red-500': touched.unit && errors.unit,
                  'border-gray-300': !touched.unit || !errors.unit
                }"
                placeholder="请输入单位"
              />
              <p v-if="touched.unit && errors.unit" class="mt-1 text-sm text-red-600">{{ errors.unit }}</p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            <div>
              <label for="stock" class="form-label">库存数量 <span class="form-required">*</span></label>
              <input
                type="number"
                id="stock"
                v-model="productForm.stock"
                @blur="handleBlur('stock')"
                min="0"
                class="form-input"
                :class="{
                  'border-red-500 focus:ring-red-500': touched.stock && errors.stock,
                  'border-gray-300': !touched.stock || !errors.stock
                }"
                placeholder="0"
              />
              <p v-if="touched.stock && errors.stock" class="mt-1 text-sm text-red-600">{{ errors.stock }}</p>
            </div>

            <div>
              <label for="minStock" class="form-label">最低库存</label>
              <input
                type="number"
                id="minStock"
                v-model="productForm.minStock"
                @blur="handleBlur('minStock')"
                min="0"
                class="form-input"
                :class="{
                  'border-red-500 focus:ring-red-500': touched.minStock && errors.minStock,
                  'border-gray-300': !touched.minStock || !errors.minStock
                }"
                placeholder="0"
              />
              <p v-if="touched.minStock && errors.minStock" class="mt-1 text-sm text-red-600">{{ errors.minStock }}</p>
            </div>
          </div>
        </div>

        <!-- 产品详情 -->
        <div class="form-section">
          <h2 class="form-section-title">产品详情</h2>
          <div>
            <label for="description" class="form-label">产品描述</label>
            <textarea
              id="description"
              v-model="productForm.description"
              @blur="handleBlur('description')"
              rows="4"
              class="form-input"
              :class="{
                'border-red-500 focus:ring-red-500': touched.description && errors.description,
                'border-gray-300': !touched.description || !errors.description
              }"
              placeholder="请输入产品描述"
            ></textarea>
            <p v-if="touched.description && errors.description" class="mt-1 text-sm text-red-600">{{ errors.description }}</p>
          </div>
        </div>
      </form>
    </div>
    
    <!-- 底部保存按钮区域 -->
    <div class="mt-8 flex justify-end">
      <button 
        @click="submitForm" 
        class="btn btn-primary shadow-sm hover:shadow-md transition-all duration-200 px-8" 
        :disabled="submitting"
      >
        <svg v-if="submitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        {{ submitButtonText }}
      </button>
    </div>
  </div>
</template>

<style>
.form-section {
  @apply p-6 border-b border-gray-200;
}

.form-section:last-child {
  @apply border-none;
}

.form-section-title {
  @apply text-lg font-medium text-gray-800 mb-4;
}
</style>

<script setup>
import { ref, onMounted, computed, reactive } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import apiService from '@/services/apiService';
import useFormValidation from '@/composables/useFormValidation';
import { createValidationSchema, isValidProductCode } from '@/utils/validation';
import notificationService from '@/services/notification.service';
import errorHandler from '@/services/errorHandler.service';

const router = useRouter();
const route = useRoute();
const submitting = ref(false);
const isEditMode = ref(false);

// Form message state
const formMessage = reactive({
  show: false,
  text: '',
  type: 'info',
  class: 'bg-blue-50 text-blue-800',
  icon: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z'
});

// Set form message
const setFormMessage = (type, text) => {
  formMessage.show = !!text;
  formMessage.text = text;
  formMessage.type = type;

  switch (type) {
    case 'success':
      formMessage.class = 'bg-green-50 text-green-800';
      formMessage.icon = 'M5 13l4 4L19 7';
      break;
    case 'error':
      formMessage.class = 'bg-red-50 text-red-800';
      formMessage.icon = 'M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
      break;
    case 'warning':
      formMessage.class = 'bg-yellow-50 text-yellow-800';
      formMessage.icon = 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z';
      break;
    case 'info':
    default:
      formMessage.class = 'bg-blue-50 text-blue-800';
      formMessage.icon = 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';
      break;
  }
};

// 初始表单数据 - 与后端模型字段保持一致
const initialFormData = {
  name: '',
  code: '',
  category: '',
  description: '',
  price: 0,
  unit: '个',
  stock: 0,
  minStock: 0,
  status: 'active',
  tags: [],
    images: []
};

// 验证规则
const validationSchema = createValidationSchema({
  name: {
    required: true,
    requiredMessage: '产品名称不能为空',
    length: true,
    minLength: 2,
    maxLength: 100,
    lengthMessage: '产品名称长度应为2-100个字符'
  },
  code: {
    required: true,
    requiredMessage: '产品编码不能为空',
    productCode: true,
    productCodeMessage: '产品编码必须为3-20个字符，只能包含字母、数字、下划线和连字符',
    validator: (value) => {
      // 自定义验证：检查产品编码格式
      if (!isValidProductCode(value)) {
        return '产品编码格式不正确';
      }
      return true;
    }
  },
  category: {
    required: true,
    requiredMessage: '请选择产品分类'
  },
  price: {
    required: true,
    requiredMessage: '产品价格不能为空',
    number: true,
    min: 0,
    numberMessage: '产品价格必须是非负数',
    price: true,
    priceMessage: '产品价格格式不正确（最多两位小数）'
  },
  unit: {
    required: true,
    requiredMessage: '产品单位不能为空'
  },
  stock: {
    required: true,
    requiredMessage: '库存数量不能为空',
    number: true,
    min: 0,
    numberMessage: '库存数量必须是非负整数'
  },
  minStock: {
    number: true,
    min: 0,
    numberMessage: '最低库存必须是非负整数'
  },
  status: {
    required: true,
    requiredMessage: '请选择产品状态'
  }
});

// 使用表单验证组合式函数
const {
  formData: productForm,
  errors,
  touched,
  isValid,
  validateField,
  touchField,
  validateAll,
  resetForm,
  setFormData
} = useFormValidation(initialFormData, validationSchema);

// 可用的产品分类
const categories = [
  { value: '', label: '-- 选择分类 --' },
  { value: '办公用品', label: '办公用品' },
  { value: '电子设备', label: '电子设备' },
  { value: '家具', label: '家具' },
  { value: '耗材', label: '耗材' },
  { value: '其他', label: '其他' }
];

// 可用的产品状态
const statuses = [
  { value: 'active', label: '在售' },
  { value: 'inactive', label: '停售' },
  { value: 'discontinued', label: '已下架' }
];

// 表单标题
const formTitle = computed(() => {
  return isEditMode.value ? '编辑产品' : '创建产品';
});

// 提交按钮文本
const submitButtonText = computed(() => {
  if (submitting.value) {
    return '提交中...';
  }
  return isEditMode.value ? '保存修改' : '保存产品';
});

// 提交表单
const submitForm = async () => {
  // 清除之前的消息
  setFormMessage('info', '');

  // 验证所有字段
  if (!validateAll()) {
    // 显示验证错误消息
    setFormMessage('error', '很抱歉，表单中存在错误。请检查标记为红色的字段，确保所有必填信息都已正确填写，然后再次提交。');
    return;
  }

  submitting.value = true;

  try {
    if (isEditMode.value) {
      await apiService.updateProduct(route.params.id, productForm);

      // 使用错误处理服务处理成功情况
      errorHandler.handleSuccess('更新', '产品', productForm.name);

      // 显示成功消息在表单中
      setFormMessage('success', '恭喜！产品信息已成功更新。系统已保存所有修改内容，您可以在产品列表中查看最新信息。');
    } else {
      // 确保提交的数据符合后端验证规则
      const productData = {
        name: productForm.name,
        code: productForm.code,
        category: productForm.category,
        description: productForm.description || '',
        price: Number(productForm.price),
        unit: productForm.unit,
        stock: Number(productForm.stock),
        minStock: productForm.minStock ? Number(productForm.minStock) : null,
        status: productForm.status,
        tags: productForm.tags || [],
        images: productForm.images || []
      };
      await apiService.createProduct(productData);

      // 使用错误处理服务处理成功情况
      errorHandler.handleSuccess('创建', '产品', productForm.name);

      // 显示成功消息在表单中
      setFormMessage('success', '恭喜！产品创建成功！系统已保存您提交的所有信息，您可以在产品列表中查看该产品。');
    }

    // 显示成功消息后延迟跳转
    setTimeout(() => {
      router.push('/products');
    }, 1500);
  } catch (err) {
    // 使用错误处理服务处理API错误
    const action = isEditMode.value ? '更新' : '创建';
    const errorMessage = errorHandler.handleApiError(err, action, '产品', false); // 不显示通知，因为我们会在表单中显示错误

    // 显示错误消息在表单中
    setFormMessage('error', errorMessage);

    // 全局通知
    notificationService.error(isEditMode.value ? '更新产品失败' : '创建产品失败', errorMessage);
  } finally {
    submitting.value = false;
  }
};

// 返回列表页
const goBack = () => {
  router.push('/products');
};

// 如果是编辑模式，加载产品数据
const loadProduct = async (id) => {
  try {
    const product = await apiService.getProductById(id);
    setFormData(product);
    // 显示加载成功消息
    setFormMessage('info', `正在编辑产品 "${product.name}"`);
  } catch (err) {
    console.error('获取产品详情失败:', err);
    const errorMessage = '很抱歉，获取产品详情时遇到了问题: ' + (err.message || '未知错误') + '。这可能是由于网络连接问题或系统临时故障导致的。系统将在几秒钟后返回产品列表页面。';
    // 显示错误消息
    setFormMessage('error', errorMessage);
    // 全局通知
    notificationService.error('加载产品失败', errorMessage);
    // 延迟跳转回列表页
    setTimeout(() => {
      router.push('/products');
    }, 2000);
  }
};

// 处理输入框失去焦点事件
const handleBlur = (field) => {
  touchField(field);
};

onMounted(() => {
  // 检查是否是编辑模式
  if (route.params.id) {
    isEditMode.value = true;
    loadProduct(route.params.id);
  }
});
</script>
