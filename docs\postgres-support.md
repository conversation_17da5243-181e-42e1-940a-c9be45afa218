# PostgreSQL Support Documentation

This document provides detailed information on using PostgreSQL with the application.

## Table of Contents

1. [Configuration](#configuration)
2. [Migrations](#migrations)
3. [Schema Management](#schema-management)
4. [Extensions](#extensions)
5. [Backup and Restore](#backup-and-restore)
6. [Performance Optimization](#performance-optimization)
7. [Troubleshooting](#troubleshooting)

## Configuration

PostgreSQL support is enabled by setting the environment variable `DB_DIALECT=postgres` in your `.env` file. A sample configuration file is available at `.env.postgres.example`.

### Essential Configuration Parameters

```
# Basic Connection Settings
DB_DIALECT=postgres
DB_HOST=localhost
DB_PORT=5432
DB_NAME=yihe
DB_USER=postgres
DB_PASSWORD=your_password
DB_SCHEMA=public
DB_LOGGING=false

# Connection Pool Settings
DB_POOL_MAX=5
DB_POOL_MIN=0
DB_POOL_ACQUIRE=30000
DB_POOL_IDLE=10000

# PostgreSQL Timeouts (in milliseconds)
DB_STATEMENT_TIMEOUT=30000
DB_IDLE_TIMEOUT=60000

# SSL Configuration (if needed)
DB_SSL=false
DB_SSL_REJECT_UNAUTHORIZED=true
DB_SSL_CA=path/to/ca/cert.pem
```

### Connection Pooling

The application uses a connection pool to efficiently manage database connections. The following parameters control the pool behavior:

- `DB_POOL_MAX`: Maximum number of connections in the pool
- `DB_POOL_MIN`: Minimum number of connections to keep open
- `DB_POOL_ACQUIRE`: Maximum time (ms) to try acquiring a connection
- `DB_POOL_IDLE`: Maximum time (ms) a connection can be idle before being released

### SSL Configuration

For secure connections, especially in production environments, you can enable SSL:

1. Set `DB_SSL=true` in your `.env` file
2. Set `DB_SSL_REJECT_UNAUTHORIZED=true` to validate the server certificate (recommended for production)
3. Optionally provide a CA certificate path with `DB_SSL_CA`

## Migrations

The application uses a migration system to manage database schema changes. Migrations are stored in the `src/migrations/postgres` directory.

### Creating Migrations

To create a new migration file:

```bash
npm run postgres:migration:create <migration-name>
```

This will generate a timestamped JavaScript file with `up` and `down` methods.

### Writing Migrations

Here's an example migration that creates a users table:

```javascript
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('user', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v4()'),
        primaryKey: true
      },
      username: {
        type: Sequelize.DataTypes.STRING(50),
        allowNull: false,
        unique: true
      },
      email: {
        type: Sequelize.DataTypes.STRING(100),
        allowNull: false,
        unique: true
      },
      password: {
        type: Sequelize.DataTypes.STRING(100),
        allowNull: false
      },
      active: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: true
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updated_at: {
        type: Sequelize.DataTypes.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });
    
    // Create index on frequently queried columns
    await queryInterface.addIndex('user', ['email']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('user');
  }
};
```

### Running Migrations

To apply pending migrations:

```bash
npm run postgres:migration:run
```

### Rolling Back Migrations

To rollback the most recent migration:

```bash
npm run postgres:migration:rollback
```

### Listing Migration Status

To see which migrations have been applied:

```bash
npm run postgres:migration:list
```

## Schema Management

### Using Multiple Schemas

PostgreSQL supports multiple schemas within a single database. By default, the application uses the `public` schema.

To use a different schema:

1. Set `DB_SCHEMA=your_schema_name` in the `.env` file
2. The setup script will automatically create the schema if it doesn't exist

### Schema Considerations

- Schemas provide logical separation of tables and other database objects
- They can be used for multi-tenant applications or separating modules
- The application automatically handles schema qualification in SQL queries

## Extensions

The setup script automatically enables the following PostgreSQL extensions:

### uuid-ossp

Enables UUID generation functions like `uuid_generate_v4()`. This is useful for creating unique identifiers that are more distributed than sequential numbers.

Using UUID as primary keys in your tables:

```javascript
id: {
  type: Sequelize.DataTypes.UUID,
  defaultValue: Sequelize.literal('uuid_generate_v4()'),
  primaryKey: true
}
```

### Adding More Extensions

To add more PostgreSQL extensions, you can modify the `setup-postgres.js` file and add calls to `createExtensionIfNotExists()`.

Common useful extensions include:

- `pg_stat_statements`: For query performance monitoring
- `pgcrypto`: For advanced cryptographic functions
- `postgis`: For geographic information system features
- `pg_trgm`: For text similarity and fast text search

## Backup and Restore

The application includes scripts for backing up and restoring your PostgreSQL database.

### Creating Backups

To create a backup:

```bash
npm run postgres:backup "Optional description"
```

Backups are stored in the `backups/postgres` directory. The backup script uses the `pg_dump` command with the custom format, which allows selective restores and is compressed.

### Listing Backups

To list all available backups:

```bash
npm run postgres:backup:list
```

### Restoring from Backup

To restore from a backup:

```bash
npm run postgres:backup:restore <backup-id>
```

The restore process uses the `pg_restore` command and performs a clean restore, which means it will drop existing objects before recreating them.

### Deleting Backups

To delete a backup:

```bash
npm run postgres:backup:delete <backup-id>
```

## Performance Optimization

### Query Performance

- Use indexes on columns frequently used in WHERE, JOIN, and ORDER BY clauses
- Use EXPLAIN ANALYZE to understand query execution plans
- Consider using materialized views for complex, frequently-accessed queries

### Connection Pooling

- Adjust pool settings based on workload:
  - For read-heavy workloads, increase max connections
  - For write-heavy workloads, ensure enough connections for transactions

### Statement Timeout

The application sets a statement timeout (`DB_STATEMENT_TIMEOUT`) to prevent long-running queries from monopolizing resources. Adjust this value based on your application's requirements.

## Troubleshooting

### Common Issues

#### Connection Errors

If you encounter connection errors:

1. Verify PostgreSQL is running: `pg_isready -h localhost -p 5432`
2. Check that credentials are correct
3. Ensure the specified database exists
4. Check firewall settings if connecting to a remote server

#### Migration Errors

If migrations fail:

1. Check the error message for specific syntax issues
2. Verify that tables or columns referenced in the migration exist
3. Try running migrations with `DB_LOGGING=true` for more detailed output

#### Backup/Restore Issues

For backup or restore issues:

1. Ensure `pg_dump` and `pg_restore` are installed and in your PATH
2. Check that the user has appropriate permissions
3. For large databases, consider using the `--jobs` parameter to parallelize operations

### Getting Help

If you need further assistance, consider:

- Checking the [Sequelize documentation](https://sequelize.org/docs/v6/)
- Reviewing the [PostgreSQL official documentation](https://www.postgresql.org/docs/)
- Opening an issue in the project repository with detailed error information
