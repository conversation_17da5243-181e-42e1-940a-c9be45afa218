/**
 * PostgreSQL Migration Script
 * 
 * This script helps with managing database migrations:
 * - Create migration files
 * - Run pending migrations
 * - Rollback migrations
 */

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const { Sequelize, DataTypes, QueryTypes } = require('sequelize');
const logger = require('../config/logger');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

// Validate environment
if (process.env.DB_DIALECT !== 'postgres') {
  console.error(`${colors.red}Error: This script is intended for PostgreSQL databases only.${colors.reset}`);
  process.exit(1);
}

// Migrations directory
const MIGRATIONS_DIR = path.join(__dirname, '../migrations/postgres');

// Ensure migrations directory exists
if (!fs.existsSync(MIGRATIONS_DIR)) {
  fs.mkdirSync(MIGRATIONS_DIR, { recursive: true });
  console.log(`${colors.green}Created migrations directory at: ${MIGRATIONS_DIR}${colors.reset}`);
}

console.log(`${colors.blue}Using migrations directory: ${MIGRATIONS_DIR}${colors.reset}`);

// Create Sequelize instance
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'postgres',
    logging: process.env.DB_LOGGING === 'true' ? console.log : false,
    schema: process.env.DB_SCHEMA || 'public'
  }
);

// Initialize migrations tracking table
async function initMigrationTable() {
  try {
    console.log(`${colors.blue}Initializing migration tracking table...${colors.reset}`);
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS "sequelize_meta" (
        "name" VARCHAR(255) NOT NULL PRIMARY KEY,
        "applied_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    console.log(`${colors.green}Migration tracking table initialized successfully${colors.reset}`);
  } catch (error) {
    console.error(`${colors.red}Failed to initialize migration table:${colors.reset}`, error);
    throw error;
  }
}

// Create a new migration file
function createMigration(name) {
  if (!name) {
    console.error(`${colors.red}Error: Migration name is required${colors.reset}`);
    return;
  }
  
  // Sanitize name for filename
  const safeName = name.replace(/[^a-zA-Z0-9_]/g, '_').toLowerCase();
  const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, 14);
  const filename = `${timestamp}_${safeName}.js`;
  const filePath = path.join(MIGRATIONS_DIR, filename);
  
  // Migration file template
  const template = `/**
 * Migration: ${name}
 * Created at: ${new Date().toISOString()}
 */
'use strict';

/** @type {import('sequelize').QueryInterface} */
module.exports = {
  // Apply the migration
  async up(queryInterface, Sequelize) {
    /**
     * Example: 
     * await queryInterface.createTable('user', {
     *   id: {
     *     type: Sequelize.DataTypes.INTEGER,
     *     primaryKey: true,
     *     autoIncrement: true
     *   },
     *   name: {
     *     type: Sequelize.DataTypes.STRING,
     *     allowNull: false
     *   },
     *   createdAt: {
     *     type: Sequelize.DataTypes.DATE,
     *     defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
     *   },
     *   updated_at: {
     *     type: Sequelize.DataTypes.DATE,
     *     defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
     *   }
     * });
     */
  },

  // Revert the migration
  async down(queryInterface, Sequelize) {
    /**
     * Example:
     * await queryInterface.dropTable('user');
     */
  }
};
`;

  fs.writeFileSync(filePath, template);
  console.log(`${colors.green}Created migration:${colors.reset} ${filename}`);
}

// Get all migration files
async function getMigrationFiles() {
  const files = fs.readdirSync(MIGRATIONS_DIR)
    .filter(file => file.endsWith('.js'))
    .sort();
  
  console.log(`${colors.blue}Found ${files.length} migration files${colors.reset}`);
  return files;
}

// Get applied migrations from database
async function getAppliedMigrations() {
  try {
    console.log(`${colors.blue}Checking applied migrations...${colors.reset}`);
    const result = await sequelize.query(
      'SELECT name FROM "sequelize_meta" ORDER BY name',
      { type: QueryTypes.SELECT }
    );
    
    console.log(`${colors.blue}Found ${result.length} applied migrations${colors.reset}`);
    return result.map(row => row.name);
  } catch (error) {
    console.error(`${colors.red}Error getting applied migrations:${colors.reset}`, error);
    throw error;
  }
}

// Run pending migrations
async function runMigrations() {
  try {
    // Initialize migration table
    await initMigrationTable();
    
    // Get files and applied migrations
    const files = await getMigrationFiles();
    const appliedMigrations = await getAppliedMigrations();
    
    // Filter out already applied migrations
    const pendingMigrations = files.filter(file => !appliedMigrations.includes(file));
    
    if (pendingMigrations.length === 0) {
      console.log(`${colors.green}No pending migrations to apply${colors.reset}`);
      return;
    }
    
    console.log(`${colors.blue}Running ${pendingMigrations.length} migrations...${colors.reset}`);
    console.log(`${colors.yellow}Pending migrations:${colors.reset}`);
    pendingMigrations.forEach(file => console.log(`- ${file}`));
    
    // Begin transaction
    const transaction = await sequelize.transaction();
    
    try {
      // Apply each migration
      for (const file of pendingMigrations) {
        console.log(`${colors.yellow}Applying migration:${colors.reset} ${file}`);
        
        try {
          const migrationPath = path.join(MIGRATIONS_DIR, file);
          console.log(`${colors.blue}Loading migration from:${colors.reset} ${migrationPath}`);
          
          const migration = require(migrationPath);
          
          if (!migration.up || typeof migration.up !== 'function') {
            console.error(`${colors.red}Invalid migration file:${colors.reset} ${file} - missing 'up' function`);
            throw new Error(`Invalid migration file: ${file} - missing 'up' function`);
          }
          
          // Execute the migration
          await migration.up(sequelize.getQueryInterface(), Sequelize);
          
          // Track applied migration
          await sequelize.query(
            'INSERT INTO "sequelize_meta" (name) VALUES (?)',
            {
              replacements: [file],
              type: QueryTypes.INSERT,
              transaction
            }
          );
          
          console.log(`${colors.green}Migration applied successfully:${colors.reset} ${file}`);
        } catch (migrationError) {
          console.error(`${colors.red}Error applying migration ${file}:${colors.reset}`, migrationError);
          throw migrationError;
        }
      }
      
      // Commit transaction
      await transaction.commit();
      console.log(`${colors.green}Successfully applied ${pendingMigrations.length} migrations${colors.reset}`);
    } catch (error) {
      // Rollback transaction on error
      console.error(`${colors.red}Migration failed:${colors.reset}`, error);
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error(`${colors.red}Migration process failed:${colors.reset}`, error);
    throw error;
  }
}

// Rollback the last migration
async function rollbackLastMigration() {
  try {
    // Initialize migration table if it doesn't exist
    await initMigrationTable();
    
    // Get applied migrations
    const appliedMigrations = await getAppliedMigrations();
    
    if (appliedMigrations.length === 0) {
      console.log(`${colors.yellow}No migrations to rollback${colors.reset}`);
      return;
    }
    
    // Get last applied migration
    const lastMigration = appliedMigrations[appliedMigrations.length - 1];
    console.log(`${colors.blue}Rolling back migration:${colors.reset} ${lastMigration}`);
    
    // Begin transaction
    const transaction = await sequelize.transaction();
    
    try {
      // Load migration file
      const migration = require(path.join(MIGRATIONS_DIR, lastMigration));
      
      if (!migration.down || typeof migration.down !== 'function') {
        console.error(`${colors.red}Invalid migration file:${colors.reset} ${lastMigration} - missing 'down' function`);
        throw new Error(`Invalid migration file: ${lastMigration} - missing 'down' function`);
      }
      
      // Execute rollback
      await migration.down(sequelize.getQueryInterface(), Sequelize);
      
      // Remove from tracking table
      await sequelize.query(
        'DELETE FROM "sequelize_meta" WHERE name = ?',
        {
          replacements: [lastMigration],
          type: QueryTypes.DELETE,
          transaction
        }
      );
      
      // Commit transaction
      await transaction.commit();
      console.log(`${colors.green}Successfully rolled back migration:${colors.reset} ${lastMigration}`);
    } catch (error) {
      // Rollback transaction on error
      await transaction.rollback();
      console.error(`${colors.red}Rollback failed:${colors.reset}`, error);
      throw error;
    }
  } catch (error) {
    console.error(`${colors.red}Rollback process failed:${colors.reset}`, error);
    throw error;
  }
}

// List all migrations and their status
async function listMigrations() {
  try {
    // Initialize migration table if it doesn't exist
    await initMigrationTable();
    
    // Get files and applied migrations
    const files = await getMigrationFiles();
    const appliedMigrations = await getAppliedMigrations();
    
    console.log(`${colors.blue}Migration status:${colors.reset}`);
    console.log(`${colors.blue}=================${colors.reset}`);
    
    for (const file of files) {
      const isApplied = appliedMigrations.includes(file);
      const status = isApplied ? `${colors.green}APPLIED${colors.reset}` : `${colors.yellow}PENDING${colors.reset}`;
      console.log(`${status} - ${file}`);
    }
  } catch (error) {
    console.error(`${colors.red}Error listing migrations:${colors.reset}`, error);
    throw error;
  }
}

// Main function to handle command line arguments
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  try {
    // Test database connection
    await sequelize.authenticate();
    console.log(`${colors.green}Database connection established successfully${colors.reset}`);
    
    switch (command) {
      case 'create':
        const name = args[1];
        if (!name) {
          console.error(`${colors.red}Error: Migration name is required${colors.reset}`);
          console.error(`${colors.yellow}Usage: node postgres-migration.js create <migration-name>${colors.reset}`);
          process.exit(1);
        }
        createMigration(name);
        break;
        
      case 'run':
        await runMigrations();
        break;
        
      case 'rollback':
        await rollbackLastMigration();
        break;
        
      case 'list':
        await listMigrations();
        break;
        
      default:
        console.error(`${colors.red}Error: Unknown command '${command}'${colors.reset}`);
        console.error(`${colors.yellow}Available commands: create, run, rollback, list${colors.reset}`);
        process.exit(1);
    }
    
    // Close database connection
    await sequelize.close();
  } catch (error) {
    console.error(`${colors.red}Database error:${colors.reset}`, error);
    
    // Try to close connection
    try {
      await sequelize.close();
    } catch (e) {
      // Ignore
    }
    
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error(`${colors.red}Unexpected error:${colors.reset}`, error);
  process.exit(1);
});
