const fs = require('fs');

const content = fs.readFileSync('src/views/projects/ProjectEdit.vue', 'utf8');
const templateMatch = content.match(/<template>([\s\S]*?)<\/template>/);

if (templateMatch) {
  const template = templateMatch[1];
  
  // Count div tags specifically
  const openDivs = (template.match(/<div[^>]*>/g) || []).length;
  const closeDivs = (template.match(/<\/div>/g) || []).length;
  
  console.log('Open div tags:', openDivs);
  console.log('Close div tags:', closeDivs);
  console.log('Difference:', openDivs - closeDivs);
  
  // Find all self-closing tags
  const selfClosing = template.match(/<[^>]*\/>/g) || [];
  console.log('Self-closing tags:', selfClosing.length);
  
  // Check for specific problematic patterns
  const lines = template.split('\n');
  lines.forEach((line, index) => {
    if (line.includes('<div') && !line.includes('</div>') && !line.includes('/>')) {
      const actualLineNumber = index + 2; // +1 for 0-based index, +1 for template tag
      if (actualLineNumber === 59) {
        console.log(`Line ${actualLineNumber}: ${line.trim()}`);
      }
    }
  });
} else {
  console.log('No template found');
} 