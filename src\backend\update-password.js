const bcrypt = require('bcryptjs');
const { User } = require('./src/models');

async function updatePassword() {
  try {
    // 查找用户
    const user = await User.findOne({ where: { email: '<EMAIL>' } });
    if (!user) {
      console.error('User not found');
      return;
    }

    // 生成新密码的哈希
    const password = 'password123';
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // 更新用户密码
    user.password = hashedPassword;
    await user.save();

    console.log('Password updated successfully');
    console.log('New password hash:', hashedPassword);
  } catch (error) {
    console.error('Error updating password:', error);
  }
}

updatePassword();
