import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import axios from 'axios'
import { useMenuStore } from './stores/menu'
import { useThemeStore } from './stores/theme'
import MessagePlugin from './plugins/message'
import { setDataSource, DataSourceType } from './services/dataSourceConfig'
import * as echarts from 'echarts'
import ECharts from './components/ECharts.vue'

// 导入 Toast 通知插件
import Toast from 'vue-toastification'
import 'vue-toastification/dist/index.css'

// Create the app instance
const app = createApp(App)

// Set up Pinia store
const pinia = createPinia()
app.use(pinia)

// Initialize menu store
const menuStore = useMenuStore()
menuStore.loadMenu()

// Initialize theme store
const themeStore = useThemeStore()
themeStore.initTheme()

// Register message plugin
app.use(MessagePlugin)

// Set up Vue Router
app.use(router)

// 注册 Toast 通知插件
app.use(Toast, {
  position: 'top-right',
  timeout: 3000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: 'button',
  icon: true,
  rtl: false
})

// Configure axios defaults
axios.defaults.baseURL = import.meta.env.VITE_API_URL || ''
axios.defaults.withCredentials = true

// Add axios interceptor for handling unauthorized responses
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    // Redirect to login page on 401 Unauthorized responses
    if (error.response && error.response.status === 401) {
      router.push('/login')
    }
    return Promise.reject(error)
  }
)

// Global error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Error:', err, info)
}

// Import Tailwind CSS and unified styles
import './style.css'
import './assets/css/unified-styles.css'

// Set data source to API (use real backend instead of mock data)
setDataSource(DataSourceType.API)
console.log('🌐 Using REAL API data source')

// Add event listener for data source changes
window.addEventListener('datasource-changed', (event) => {
  console.log(`💬 Data source changed to: ${event.detail.type}`)
  // Reload the page to apply the change
  window.location.reload()
})

// Make echarts available globally
app.config.globalProperties.$echarts = echarts

// Register ECharts component globally
app.component('ECharts', ECharts)

// Set document title with VITE_APP_TITLE prefix if available
const appTitle = import.meta.env.VITE_APP_TITLE
if (appTitle) {
  document.title = `${appTitle}`
}

// Mount the app
app.mount('#app')