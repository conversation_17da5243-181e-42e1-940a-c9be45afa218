const { Joi } = require('../config/joi');
const { objectId } = require('./custom.validation');

const createContract = {
  body: Joi.object().keys({
    name: Joi.string().required().messages({
      'string.empty': '姓名不能为空',
      'any.required': '姓名是必填项'
    }),
    email: Joi.string().required().email().messages({
      'string.empty': '邮箱不能为空',
      'string.email': '邮箱格式不正确',
      'any.required': '邮箱是必填项'
    }),
    phone: Joi.string().required().messages({
      'string.empty': '电话号码不能为空',
      'any.required': '电话号码是必填项'
    }),
    contractNumber: Joi.string().required().messages({
      'string.empty': '合同编号不能为空',
      'any.required': '合同编号是必填项'
    }),
    startDate: Joi.date().required().messages({
      'date.base': '开始日期格式不正确',
      'any.required': '开始日期是必填项'
    }),
    endDate: Joi.date().required().messages({
      'date.base': '结束日期格式不正确',
      'any.required': '结束日期是必填项'
    }),
    position: Joi.string().required().messages({
      'string.empty': '职位不能为空',
      'any.required': '职位是必填项'
    }),
    department: Joi.string().required().messages({
      'string.empty': '部门不能为空',
      'any.required': '部门是必填项'
    }),
    projectId: Joi.string().required().custom(objectId).messages({
      'string.empty': '项目ID不能为空',
      'any.custom': '项目ID必须是有效的ObjectID格式',
      'any.required': '关联项目是必填项'
    }),
    contractType: Joi.string().required().messages({
      'string.empty': '合同类型不能为空',
      'any.only': '合同类型必须是有效值',
      'any.required': '合同类型是必填项'
    }),
    hourlyRate: Joi.number().required().messages({
      'number.base': '小时工资必须是数字',
      'any.required': '小时工资是必填项'
    }),
    status: Joi.string().required().valid('active', 'inactive', 'completed').messages({
      'string.empty': '状态不能为空',
      'any.only': '状态必须是活跃(active)、非活跃(inactive)或已完成(completed)',
      'any.required': '状态是必填项'
    }),
    amount: Joi.number().required().messages({
      'number.base': '合同金额必须是数字',
      'any.required': '合同金额是必填项'
    }),
    ourParty: Joi.string().messages({
      'string.empty': '我方主体不能为空'
    }),
    counterparty: Joi.string().messages({
      'string.empty': '对方主体不能为空'
    })
  }),
};

const getContracts = {
  query: Joi.object().keys({
    name: Joi.string().allow(null),
    contractNumber: Joi.string().allow(null),
    status: Joi.string().allow(null),
    sortBy: Joi.string().allow(null),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    }),
    page: Joi.number().integer().messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数'
    }),
  }),
};

const getContract = {
  params: Joi.object().keys({
    contractId: Joi.string().custom(objectId).messages({
      'string.empty': '合同ID不能为空',
      'any.custom': '合同ID必须是有效的ObjectID格式'
    }),
  }),
};

const updateContract = {
  params: Joi.object().keys({
    contractId: Joi.string().custom(objectId).messages({
      'string.empty': '合同ID不能为空',
      'any.custom': '合同ID必须是有效的ObjectID格式'
    }),
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().messages({
        'string.empty': '姓名不能为空'
      }),
      email: Joi.string().email().messages({
        'string.empty': '邮箱不能为空',
        'string.email': '邮箱格式不正确'
      }),
      phone: Joi.string().messages({
        'string.empty': '电话号码不能为空'
      }),
      counterparty: Joi.string().messages({
        'string.empty': '对方主体不能为空'
      }),
      ourParty: Joi.string().messages({
        'string.empty': '我方主体不能为空'
      }),

      contractNumber: Joi.string().messages({
        'string.empty': '合同编号不能为空'
      }),
      startDate: Joi.date().messages({
        'date.base': '开始日期格式不正确'
      }),
      endDate: Joi.date().messages({
        'date.base': '结束日期格式不正确'
      }),
      position: Joi.string().messages({
        'string.empty': '职位不能为空'
      }),
      department: Joi.string().messages({
        'string.empty': '部门不能为空'
      }),
      projectId: Joi.string().custom(objectId).messages({
        'string.empty': '项目ID不能为空',
        'any.custom': '项目ID必须是有效的ObjectID格式'
      }),
      contractType: Joi.string().messages({
        'string.empty': '合同类型不能为空',
        'any.only': '合同类型必须是有效值'
      }),
      hourlyRate: Joi.number().messages({
        'number.base': '小时工资必须是数字'
      }),
      status: Joi.string().valid('active', 'inactive', 'completed').messages({
        'string.empty': '状态不能为空',
        'any.only': '状态必须是活跃(active)、非活跃(inactive)或已完成(completed)'
      }),
      amount: Joi.number().messages({
        'number.base': '合同金额必须是数字'
      })
    })
    .min(1).messages({
      'object.min': '至少需要提供一个要更新的字段'
    }),
};

const deleteContract = {
  params: Joi.object().keys({
    contractId: Joi.string().custom(objectId).messages({
      'string.empty': '合同ID不能为空',
      'any.custom': '合同ID必须是有效的ObjectID格式'
    }),
  }),
};

module.exports = {
  createContract,
  getContracts,
  getContract,
  updateContract,
  deleteContract,
};