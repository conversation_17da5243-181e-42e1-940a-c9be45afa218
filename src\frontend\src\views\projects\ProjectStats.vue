<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和按钮 -->
    <div class="flex justify-between items-center mb-8">
      <div class="flex items-center">
        <router-link to="/projects" class="text-blue-600 hover:text-blue-800 mr-3 transition-colors duration-200">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
        </router-link>
        <h1 class="text-2xl font-bold text-gray-900">项目信息统计</h1>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
      <button @click="fetchProjectStats" class="mt-2 text-red-700 hover:text-red-900 font-medium underline">
        重试
      </button>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>

    <!-- 统计内容 -->
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 项目总数卡片 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500">项目总数</p>
            <p class="text-2xl font-bold text-gray-900 mt-1">{{ stats.totalProjects }}</p>
          </div>
          <div class="bg-blue-100 rounded-full p-3">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- 进行中项目卡片 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500">进行中项目</p>
            <p class="text-2xl font-bold text-gray-900 mt-1">{{ stats.inProgressProjects }}</p>
          </div>
          <div class="bg-green-100 rounded-full p-3">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- 已完成项目卡片 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-500">已完成项目</p>
            <p class="text-2xl font-bold text-gray-900 mt-1">{{ stats.completedProjects }}</p>
          </div>
          <div class="bg-purple-100 rounded-full p-3">
            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- 项目类型分布 -->
      <div class="bg-white rounded-xl shadow-sm p-6 md:col-span-2">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">项目类型分布</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div v-for="(count, type) in stats.projectTypes" :key="type" class="text-center">
            <p class="text-sm text-gray-500">{{ type }}</p>
            <p class="text-xl font-bold text-gray-900 mt-1">{{ count }}</p>
          </div>
        </div>
      </div>

      <!-- 项目状态分布 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">项目状态分布</h3>
        <div class="space-y-4">
          <div v-for="(count, status) in stats.projectStatus" :key="status" class="flex items-center justify-between">
            <span class="text-sm text-gray-500">{{ getStatusText(status) }}</span>
            <div class="flex items-center">
              <div class="w-24 h-2 bg-gray-200 rounded-full mr-2">
                <div class="h-2 rounded-full bg-blue-600" :style="{ width: `${(count / stats.totalProjects) * 100}%` }"></div>
              </div>
              <span class="text-sm font-medium text-gray-900">{{ count }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目进度分布 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">项目进度分布</h3>
        <div class="space-y-4">
          <div v-for="(count, range) in stats.progressRanges" :key="range" class="flex items-center justify-between">
            <span class="text-sm text-gray-500">{{ range }}</span>
            <div class="flex items-center">
              <div class="w-24 h-2 bg-gray-200 rounded-full mr-2">
                <div class="h-2 rounded-full" :class="getProgressBarColor(range)" :style="{ width: `${(count / stats.totalProjects) * 100}%` }"></div>
              </div>
              <span class="text-sm font-medium text-gray-900">{{ count }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 项目负责人分布 -->
      <div class="bg-white rounded-xl shadow-sm p-6 md:col-span-2">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">项目负责人分布</h3>
          <div class="flex space-x-2">
            <button
              @click="managerChartType = 'count'"
              :class="[
                'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                managerChartType === 'count'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              数量
            </button>
            <button
              @click="managerChartType = 'area'"
              :class="[
                'px-3 py-1 rounded-md text-sm font-medium transition-colors',
                managerChartType === 'area'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              ]"
            >
              面积
            </button>
          </div>
        </div>
        <ECharts :option="managerChartOption" height="300px" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import axios from 'axios'

const isLoading = ref(true)
const managerChartType = ref('count') // 默认显示数量维度
const error = ref(null)
const stats = ref({
  totalProjects: 0,
  inProgressProjects: 0,
  completedProjects: 0,
  projectTypes: {},
  projectStatus: {},
  progressRanges: {
    '0-25%': 0,
    '26-50%': 0,
    '51-75%': 0,
    '76-100%': 0
  }
})
const projects = ref([])

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    planning: '规划中',
    in_progress: '进行中',
    on_hold: '已暂停',
    completed: '已完成',
    cancelled: '已取消'
  };
  return statusMap[status] || status;
}

// 获取进度条颜色
function getProgressBarColor(range) {
  const colorMap = {
    '0-25%': 'bg-red-600',
    '26-50%': 'bg-yellow-600',
    '51-75%': 'bg-blue-600',
    '76-100%': 'bg-green-600'
  };
  return colorMap[range] || 'bg-gray-600';
}

// 项目负责人分布 - 数量维度
const managerCountDistribution = computed(() => {
  const distribution = {}
  projects.value.forEach(project => {
    const manager = project.manager || '未分配'
    distribution[manager] = (distribution[manager] || 0) + 1
  })

  return Object.entries(distribution).map(([name, value]) => ({
    name,
    value
  }))
})

// 项目负责人分布 - 面积维度
const managerAreaDistribution = computed(() => {
  const distribution = {}
  projects.value.forEach(project => {
    const manager = project.manager || '未分配'
    // 确保 area 是数值类型，如果为空则默认为 0
    const area = parseFloat(project.area) || 0
    distribution[manager] = (distribution[manager] || 0) + area
  })

  // 过滤掉面积为 0 的数据
  return Object.entries(distribution)
    .filter(([_, value]) => value > 0)
    .map(([name, value]) => ({
      name,
      value: Math.round(value) // 四舍五入到整数
    }))
})

// 根据选择的维度获取分布数据
const managerDistribution = computed(() => {
  return managerChartType.value === 'count'
    ? managerCountDistribution.value
    : managerAreaDistribution.value
})

// 项目负责人分布图表选项
const managerChartOption = computed(() => {
  const title = managerChartType.value === 'count' ? '项目数量' : '项目面积(m²)'
  const data = managerChartType.value === 'count'
    ? managerCountDistribution.value
    : managerAreaDistribution.value

  // 确保有数据
  if (!data || data.length === 0) {
    return {
      title: {
        text: '暂无数据',
        left: 'center',
        top: 'center'
      }
    }
  }

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: data.map(item => item.name)
    },
    series: [
      {
        name: title,
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['40%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {c} ({d}%)'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 15
        },
        data: data
      }
    ]
  }
})

// 获取项目统计数据
const fetchProjectStats = async () => {
  try {
    isLoading.value = true
    // 请求更多项目数据，以便生成有意义的饼图
    const response = await axios.get('/api/projects', {
      params: { page: 1, limit: 100 }
    })
    projects.value = response.data.results || []

    // 计算统计数据
    stats.value.totalProjects = projects.value.length
    stats.value.inProgressProjects = projects.value.filter(p => p.status === 'in_progress').length
    stats.value.completedProjects = projects.value.filter(p => p.status === 'completed').length

    // 计算项目类型分布
    stats.value.projectTypes = projects.value.reduce((acc, project) => {
      acc[project.engineeringType] = (acc[project.engineeringType] || 0) + 1;
      return acc;
    }, {});

    // 计算项目状态分布
    stats.value.projectStatus = projects.value.reduce((acc, project) => {
      acc[project.status] = (acc[project.status] || 0) + 1;
      return acc;
    }, {});

    // 计算进度分布
    projects.value.forEach(project => {
      const progress = project.progress || 0
      if (progress <= 25) {
        stats.value.progressRanges['0-25%']++
      } else if (progress <= 50) {
        stats.value.progressRanges['26-50%']++
      } else if (progress <= 75) {
        stats.value.progressRanges['51-75%']++
      } else {
        stats.value.progressRanges['76-100%']++
      }
    })

    console.log('项目负责人分布数据:', managerDistribution.value)
  } catch (error) {
    console.error('获取项目统计数据失败:', error)
    // 重置统计数据
    projects.value = []
    stats.value.totalProjects = 0
    stats.value.inProgressProjects = 0
    stats.value.completedProjects = 0
    stats.value.projectTypes = {}
    stats.value.projectStatus = {}
    stats.value.progressRanges = {
      '0-25%': 0,
      '26-50%': 0,
      '51-75%': 0,
      '76-100%': 0
    }
    // 显示错误提示
    error.value = '很抱歉，获取项目统计数据时遇到了问题。\n\n可能的原因：\n· 网络连接暂时不稳定\n· 服务器正在维护中\n· 系统临时故障\n· 数据量过大，处理超时\n\n建议操作：\n1. 检查网络连接\n2. 刷新页面后重试\n3. 稍后再次尝试\n\n如果问题持续存在，请联系系统管理员获取帮助。我们将尽快解决此问题，感谢您的理解与耐心。'
  } finally {
    isLoading.value = false
  }
}

onMounted(() => {
  fetchProjectStats()
})
</script>