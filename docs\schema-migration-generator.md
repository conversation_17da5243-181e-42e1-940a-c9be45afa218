# Schema Migration Generator

This document explains how to use the schema migration generator to automatically create migration files based on differences between your database schema and model definitions.

## Overview

The schema migration generator compares the current database schema with the models defined in `src/models/` and generates migration files to align the database with the models. This is useful when:

1. You've made changes to your model definitions and need to update the database
2. You're working with an existing database and need to ensure it matches your models
3. You want to track schema changes in migration files for version control

## Usage

To generate a migration file:

```bash
npm run postgres:migration:generate
```

This will:
1. Connect to your database and retrieve the current schema
2. Parse your model files to understand the expected schema
3. Compare the two and identify differences
4. Generate a migration file in `src/migrations/postgres/` with the necessary changes

## Migration File Structure

The generated migration file includes:

- **Missing Tables**: Tables defined in models but not in the database
- **Missing Columns**: Columns defined in models but not in database tables
- **Different Columns**: Columns with different definitions (type, constraints, etc.)
- **Extra Columns**: Columns in the database that aren't defined in models

Each section includes both `up` and `down` migrations to apply and revert changes.

## Running the Migration

After generating the migration file, you can apply it using:

```bash
npm run postgres:migration:run
```

## Important Notes

1. **Review Before Running**: Always review the generated migration file before running it to ensure it makes the changes you expect.

2. **Extra Columns**: By default, commands to remove extra columns are commented out for safety. Uncomment them if you're sure you want to remove these columns.

3. **Data Loss Warning**: Some migrations (like changing column types) might cause data loss. Make sure to back up your database before running migrations:
   ```bash
   npm run postgres:backup "Pre-migration backup"
   ```

4. **Table Dependencies**: The generator sorts tables based on their dependencies to ensure they are created in the correct order. If you encounter foreign key constraint errors, you may need to adjust the order manually.

5. **Case Sensitivity**: The generator converts camelCase model names to snake_case database table names. Make sure your references use the correct case.

6. **Complex Changes**: The generator handles basic schema changes, but complex changes (like splitting tables) will need manual migration files.

## Troubleshooting

If you encounter issues:

1. **Connection Problems**: Ensure your database connection details in `.env` are correct
2. **Permission Issues**: Make sure your database user has the necessary permissions to read schema information
3. **Model Loading Errors**: Check that all model files follow the expected format and export pattern

## Example

Here's an example of what the generator might output:

```
Found 15 model files
Retrieving current database schema...
Parsing model files...
Comparing database schema with models...
Schema differences:
Missing tables: 0
Missing columns: 3
Different columns: 1
Extra columns: 2
Migration file generated successfully: 20230615123456_schema_sync.js
Run the migration with: npm run postgres:migration:run
```
