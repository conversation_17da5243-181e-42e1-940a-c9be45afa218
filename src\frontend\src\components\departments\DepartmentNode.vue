<template>
  <div class="node">
    <div 
      class="node-content" 
      :class="{ 'has-children': hasChildren }"
      @click="hasChildren ? $emit('toggle-expand', department.id) : null"
    >
      <div class="flex items-center">
        <h3 class="font-medium">{{ department.name }}</h3>
        <button 
          v-if="hasChildren" 
          class="ml-2 toggle-btn"
          @click.stop="$emit('toggle-expand', department.id)"
        >
          {{ isExpanded ? '−' : '+' }}
        </button>
      </div>
      <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
        {{ department.employeeCount || department.employee_count || 0 }} 名员工
      </div>
    </div>

    <div v-if="hasChildren && isExpanded" class="children">
      <template v-for="(child, index) in childDepartments" :key="child.id">
        <DepartmentNode 
          :department="child" 
          :all-departments="allDepartments"
          :expanded-departments="expandedDepartments"
          @toggle-expand="(id) => $emit('toggle-expand', id)"
        />
      </template>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  department: {
    type: Object,
    required: true
  },
  allDepartments: {
    type: Array,
    required: true
  },
  expandedDepartments: {
    type: Array,
    required: true
  }
});

const emit = defineEmits(['toggle-expand']);

// 计算该部门的子部门
const childDepartments = computed(() => {
  // 先检查 department 对象中是否已经包含了子部门数据
  if (props.department.children && Array.isArray(props.department.children)) {
    return props.department.children;
  }
  
  // 否则从 allDepartments 中过滤出子部门
  return props.allDepartments.filter(
    dept => dept.parentId === props.department.id || 
           dept.parent_id === props.department.id
  );
});

// 判断是否有子部门
const hasChildren = computed(() => {
  return childDepartments.value.length > 0;
});

// 判断是否已展开
const isExpanded = computed(() => {
  return props.expandedDepartments.includes(props.department.id);
});
</script>

<style scoped>
.node {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 10px;
}

.node-content {
  border: 1px solid #e5e7eb;
  padding: 10px 16px;
  border-radius: 8px;
  background-color: #f9fafb;
  min-width: 150px;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.dark .node-content {
  background-color: #1f2937;
  border-color: #374151;
}

.node-content.has-children {
  cursor: pointer;
}

.node-content.has-children:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
}

.dark .node-content.has-children:hover {
  background-color: #111827;
  border-color: #4b5563;
}

.toggle-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: #e5e7eb;
  color: #4b5563;
  border-radius: 50%;
  font-size: 14px;
  line-height: 1;
  font-weight: bold;
}

.dark .toggle-btn {
  background-color: #374151;
  color: #d1d5db;
}

.children {
  display: flex;
  margin-top: 30px;
  position: relative;
}

.children:before {
  content: '';
  position: absolute;
  top: -20px;
  left: 50%;
  width: 1px;
  height: 20px;
  background-color: #e5e7eb;
}

.dark .children:before {
  background-color: #374151;
}
</style> 