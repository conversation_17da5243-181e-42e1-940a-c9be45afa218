<template>
  <div class="theme-toggle">
    <div class="relative inline-block">
      <button
        @click="toggleMenu"
        class="flex items-center justify-center p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none"
        :aria-expanded="isOpen"
        aria-haspopup="true"
      >
        <!-- Sun icon (light mode) -->
        <svg
          v-if="!isDarkMode"
          class="w-5 h-5 text-yellow-500"
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
            clip-rule="evenodd"
          />
        </svg>

        <!-- Moon icon (dark mode) -->
        <svg
          v-else
          class="w-5 h-5 text-gray-300"
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
          />
        </svg>
      </button>

      <!-- Dropdown menu -->
      <div
        v-if="isOpen"
        class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"
      >
        <div class="py-1" role="menu" aria-orientation="vertical">


          <!-- Theme selection -->
          <div class="border-t border-gray-200 dark:border-gray-700 mt-1 pt-1">
            <div class="px-4 py-2 text-xs text-gray-500 dark:text-gray-400">
              主题选择
            </div>
            <button
              v-for="theme in availableThemes"
              :key="theme"
              @click="setTheme(theme)"
              class="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center"
              :class="{ 'bg-gray-100 dark:bg-gray-700': currentTheme === theme }"
              role="menuitem"
            >
              <span
                class="w-3 h-3 rounded-full mr-2"
                :class="getThemeColorClass(theme)"
              ></span>
              {{ getThemeName(theme) }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useThemeStore } from '@/stores/theme';

// Get the theme store
const themeStore = useThemeStore();
const { COLOR_MODES, THEMES } = themeStore;

// Dropdown state
const isOpen = ref(false);

// Computed properties from the store
const isDarkMode = computed(() => themeStore.isDarkMode);
const currentTheme = computed(() => themeStore.currentTheme);
const availableThemes = computed(() => themeStore.availableThemes);
const followSystem = computed(() => themeStore.followSystem);

// Methods
const toggleMenu = () => {
  isOpen.value = !isOpen.value;
};

const setColorMode = (mode) => {
  themeStore.setColorMode(mode);
  themeStore.setFollowSystem(false);
  isOpen.value = false;
};

const setTheme = (theme) => {
  themeStore.setTheme(theme);
  isOpen.value = false;
};

const setFollowSystem = (value) => {
  themeStore.setFollowSystem(value);
  isOpen.value = false;
};

// Helper methods
const getThemeName = (theme) => {
  const themeNames = {
    [THEMES.DEFAULT]: '默认主题',
    [THEMES.DARK]: '暗黑主题',
    [THEMES.BLUE]: '蓝色主题',
    [THEMES.GREEN]: '绿色主题'
  };
  return themeNames[theme] || theme;
};

const getThemeColorClass = (theme) => {
  const themeColors = {
    [THEMES.DEFAULT]: 'bg-blue-500',
    [THEMES.DARK]: 'bg-gray-800',
    [THEMES.BLUE]: 'bg-blue-600',
    [THEMES.GREEN]: 'bg-green-600'
  };
  return themeColors[theme] || 'bg-gray-500';
};

// Handle click outside to close dropdown
const handleClickOutside = (event) => {
  if (isOpen.value && !event.target.closest('.theme-toggle')) {
    isOpen.value = false;
  }
};

// Add and remove event listeners
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.theme-toggle {
  position: relative;
}
</style>
