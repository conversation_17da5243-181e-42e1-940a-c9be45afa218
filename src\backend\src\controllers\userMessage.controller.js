const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { userMessageService } = require('../services');
const ApiError = require('../utils/ApiError');

/**
 * 获取当前用户的消息列表
 * @route GET /api/messages
 */
const getUserMessages = catchAsync(async (req, res) => {
  const filter = {
    userId: req.user.id,
    isRead: req.query.isRead,
    type: req.query.type
  };

  const options = {
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10,
    sortBy: req.query.sortBy || 'createdAt',
    sortOrder: req.query.sortOrder || 'desc'
  };

  const result = await userMessageService.getUserMessages(filter, options);
  res.send(result);
});

/**
 * 获取当前用户的未读消息数量
 * @route GET /api/messages/unread-count
 */
const getUnreadMessageCount = catchAsync(async (req, res) => {
  const count = await userMessageService.getUnreadMessageCount(req.user.id);
  res.send({ count });
});

/**
 * 获取消息详情
 * @route GET /api/messages/:id
 */
const getUserMessageById = catchAsync(async (req, res) => {
  const message = await userMessageService.getUserMessageById(req.params.id);

  // 检查消息是否属于当前用户
  if (message.userId !== req.user.id) {
    throw new ApiError(403, '无权访问此消息');
  }

  res.send(message);
});

/**
 * 标记消息为已读
 * @route PATCH /api/messages/:id/read
 */
const markMessageAsRead = catchAsync(async (req, res) => {
  const message = await userMessageService.getUserMessageById(req.params.id);

  // 检查消息是否属于当前用户
  if (message.userId !== req.user.id) {
    throw new ApiError(403, '无权访问此消息');
  }

  const updatedMessage = await userMessageService.markMessageAsRead(req.params.id);
  res.send(updatedMessage);
});

/**
 * 标记所有消息为已读
 * @route PATCH /api/messages/read-all
 */
const markAllMessagesAsRead = catchAsync(async (req, res) => {
  const count = await userMessageService.markAllMessagesAsRead(req.user.id);
  res.send({ count });
});

/**
 * 删除消息
 * @route DELETE /api/messages/:id
 */
const deleteUserMessage = catchAsync(async (req, res) => {
  const message = await userMessageService.getUserMessageById(req.params.id);

  // 检查消息是否属于当前用户
  if (message.userId !== req.user.id) {
    throw new ApiError(403, '无权访问此消息');
  }

  await userMessageService.deleteUserMessage(req.params.id);
  // 使用明确的数字状态码而不是httpStatus常量，避免可能的undefined问题
  res.status(204).send();
});

/**
 * 创建测试消息（仅用于开发和测试）
 * @route POST /api/messages/test
 */
const createTestMessage = catchAsync(async (req, res) => {
  try {
    console.log('创建测试消息，用户ID:', req.user.id);
    console.log('请求体:', req.body);

    const messageData = {
      userId: req.user.id,
      title: req.body.title || '测试消息',
      content: req.body.content || '这是一条测试消息内容',
      type: req.body.type || 'system'
    };

    console.log('准备创建消息:', messageData);

    const message = await userMessageService.createUserMessage(messageData);
    console.log('消息创建成功:', message.id);

    res.status(201).send(message);
  } catch (error) {
    console.error('创建测试消息失败:', error);
    throw error;
  }
});

module.exports = {
  getUserMessages,
  getUnreadMessageCount,
  getUserMessageById,
  markMessageAsRead,
  markAllMessagesAsRead,
  deleteUserMessage,
  createTestMessage
};
