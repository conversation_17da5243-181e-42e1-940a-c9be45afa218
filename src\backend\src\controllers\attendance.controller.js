const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { attendanceService } = require('../services');

/**
 * Create a new attendance record
 * @route POST /api/attendances
 */
const createAttendance = catchAsync(async (req, res) => {
  const attendance = await attendanceService.createAttendance({
    ...req.body,
    createdBy: req.user.id
  });
  res.status(201).send(attendance);
});

/**
 * Get an attendance record by ID
 * @route GET /api/attendances/:id
 */
const getAttendance = catchAsync(async (req, res) => {
  const attendance = await attendanceService.getAttendanceById(req.params.id);
  res.send(attendance);
});

/**
 * Update an attendance record
 * @route PATCH /api/attendances/:id
 */
const updateAttendance = catchAsync(async (req, res) => {
  const attendance = await attendanceService.updateAttendanceById(req.params.id, req.body);
  res.send(attendance);
});

/**
 * Delete an attendance record
 * @route DELETE /api/attendances/:id
 */
const deleteAttendance = catchAsync(async (req, res) => {
  await attendanceService.deleteAttendanceById(req.params.id);
  res.status(204).send();
});

/**
 * Get all attendance records with filtering
 * @route GET /api/attendances
 */
const getAttendances = catchAsync(async (req, res) => {
  const filter = {
    projectId: req.query.projectId,
    department: req.query.department,
    employeeName: req.query.employeeName,
    status: req.query.status,
    dateFrom: req.query.dateFrom,
    dateTo: req.query.dateTo
  };
  
  const options = {
    sortBy: req.query.sortBy || 'date',
    sortOrder: req.query.sortOrder || 'desc',
    page: parseInt(req.query.page, 10) || 1,
    limit: parseInt(req.query.limit, 10) || 10
  };
  
  const result = await attendanceService.queryAttendances(filter, options);
  res.send(result);
});

/**
 * Export attendance records to Excel/CSV
 * @route GET /api/attendances/export
 */
const exportAttendances = catchAsync(async (req, res) => {
  const filter = {
    projectId: req.query.projectId,
    department: req.query.department,
    employeeName: req.query.employeeName,
    status: req.query.status,
    dateFrom: req.query.dateFrom,
    dateTo: req.query.dateTo
  };
  
  // Get all data for export without pagination
  const options = {
    sortBy: req.query.sortBy || 'date',
    sortOrder: req.query.sortOrder || 'desc',
    // No pagination for export - get all matching records
    paginate: false
  };
  
  try {
    const data = await attendanceService.exportAttendances(filter, options);
    
    // Set response headers for file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=attendance_export_${new Date().toISOString().split('T')[0]}.xlsx`);
    
    // Send the file buffer
    res.send(data);
  } catch (error) {
    console.error('Export attendance error:', error);
    res.status(500).send({ message: 'Failed to export attendance data' });
  }
});

module.exports = {
  createAttendance,
  getAttendance,
  updateAttendance,
  deleteAttendance,
  getAttendances,
  exportAttendances
}; 