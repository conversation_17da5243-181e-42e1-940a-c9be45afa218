// @ts-check
import { test, expect } from '@playwright/test';

test.describe('项目管理流程', () => {
  // 在每个测试前先登录
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForNavigation();
  });

  test('项目列表页面', async ({ page }) => {
    // 访问项目列表页面
    await page.goto('/projects');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('项目列表');

    // 验证项目表格存在
    expect(await page.isVisible('table')).toBeTruthy();

    // 验证添加项目按钮存在
    expect(await page.isVisible('a[href="/projects/create"]')).toBeTruthy();
  });

  test('创建项目流程', async ({ page }) => {
    // 访问创建项目页面
    await page.goto('/projects/create');

    // 验证页面标题
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('创建项目');

    // 验证表单存在
    expect(await page.isVisible('form')).toBeTruthy();

    // 填写表单
    const projectName = `测试项目 ${Date.now()}`;
    await page.fill('input[name="name"]', projectName);
    
    // 填写项目代码
    await page.fill('input[name="code"]', `PRJ-${Date.now()}`);
    
    // 选择客户
    if (await page.isVisible('select[name="clientId"]')) {
      await page.selectOption('select[name="clientId"]', { index: 1 });
    }
    
    // 设置开始日期
    const startDate = new Date();
    await page.fill('input[name="startDate"]', startDate.toISOString().split('T')[0]);
    
    // 设置结束日期
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + 3);
    await page.fill('input[name="endDate"]', endDate.toISOString().split('T')[0]);
    
    // 设置状态
    if (await page.isVisible('select[name="status"]')) {
      await page.selectOption('select[name="status"]', '进行中');
    }
    
    // 设置预算
    if (await page.isVisible('input[name="budget"]')) {
      await page.fill('input[name="budget"]', '100000');
    }
    
    // 添加描述
    if (await page.isVisible('textarea[name="description"]')) {
      await page.fill('textarea[name="description"]', '这是一个由E2E测试创建的测试项目');
    }

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到项目列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/projects');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
    
    // 验证新项目已添加到列表
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain(projectName);
  });

  test('查看项目详情', async ({ page }) => {
    // 访问项目列表页面
    await page.goto('/projects');

    // 点击第一个项目的详情按钮
    await page.click('table tbody tr:first-child a.view-button');

    // 等待页面跳转到详情页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/projects/');

    // 验证详情页面内容
    for (const h1 of await page.$$('h1')) 
      expect(await h1.textContent()).toContain('项目详情');
    expect(await page.isVisible('.project-info')).toBeTruthy();
    expect(await page.isVisible('a.edit-button')).toBeTruthy();
  });

  test('编辑项目流程', async ({ page }) => {
    // 访问项目列表页面
    await page.goto('/projects');

    // 点击第一个编辑按钮
    await page.click('table tbody tr:first-child a.edit-button');

    // 等待页面跳转到编辑页面
    await page.waitForNavigation();
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/projects/');
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/edit');

    // 验证表单已预填充
    expect(await page.inputValue('input[name="name"]')).toBeTruthy();

    // 修改项目名称
    const updatedName = `更新的项目 ${Date.now()}`;
    await page.fill('input[name="name"]', updatedName);

    // 修改项目状态
    if (await page.isVisible('select[name="status"]')) {
      await page.selectOption('select[name="status"]', '已完成');
    }

    // 提交表单
    await page.click('button[type="submit"]');

    // 等待页面跳转
    await page.waitForNavigation();

    // 验证跳转到项目列表页面
    await new Promise(resolve => setTimeout(resolve, 100));
    expect(page.url()).toContain('/projects');

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
    
    // 验证项目已更新
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain(updatedName);
  });

  test('项目搜索功能', async ({ page }) => {
    // 访问项目列表页面
    await page.goto('/projects');

    // 在搜索框中输入关键字
    await page.fill('input[placeholder*="搜索"]', 'PRJ-');

    // 点击搜索按钮
    await page.click('button.search-button');

    // 等待搜索结果加载
    await page.waitForTimeout(1000);

    // 验证搜索结果
    const projectCodes = await page.locator('table tbody tr td:nth-child(2)').allTextContents();
    for (const code of projectCodes) {
      expect(code).toContain('PRJ-');
    }
  });

  test('项目状态筛选功能', async ({ page }) => {
    // 访问项目列表页面
    await page.goto('/projects');

    // 选择状态
    if (await page.isVisible('select.status-filter')) {
      await page.selectOption('select.status-filter', '进行中');

      // 等待筛选结果加载
      await page.waitForTimeout(1000);

      // 验证筛选结果
      const statuses = await page.locator('table tbody tr td:nth-child(4)').allTextContents();
      for (const status of statuses) {
        expect(status.toLowerCase()).toContain('进行中');
      }
    }
  });

  test('删除项目流程', async ({ page }) => {
    // 访问项目列表页面
    await page.goto('/projects');

    // 获取项目数量
    const initialProjectCount = await page.locator('table tbody tr').count();

    // 点击第一个删除按钮
    await page.click('table tbody tr:first-child button.delete-button');

    // 确认删除
    await page.click('button.confirm-delete');

    // 等待页面刷新
    await page.waitForTimeout(1000);

    // 验证项目数量减少
    const newProjectCount = await page.locator('table tbody tr').count();
    expect(newProjectCount).toBeLessThan(initialProjectCount);

    // 验证成功消息显示
    expect(await page.isVisible('.success-message')).toBeTruthy();
  });
});
