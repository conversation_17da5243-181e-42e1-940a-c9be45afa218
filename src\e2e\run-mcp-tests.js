/**
 * Run Playwright MCP tests with automatic bug fixing
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  testDir: path.join(__dirname, 'tests'),
  resultsDir: path.join(__dirname, 'test-results'),
  reportDir: path.join(__dirname, 'test-report'),
  maxRetries: 3,
  autoFix: true,
  browsers: ['chromium'], // Can be 'chromium', 'firefox', 'webkit'
};

// Ensure directories exist
[config.resultsDir, config.reportDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Get all test files
const getTestFiles = () => {
  return fs.readdirSync(config.testDir)
    .filter(file => file.endsWith('-mcp.spec.js'))
    .map(file => path.join(config.testDir, file));
};

// Parse test results
const parseTestResults = (resultsFile) => {
  try {
    const data = fs.readFileSync(resultsFile, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error parsing test results:', error);
    return { errors: [], failures: [] };
  }
};

// Analyze test failures and suggest fixes
const analyzeFailures = (results) => {
  const fixes = [];
  
  if (!results.suites) return fixes;
  
  const processSpec = (spec) => {
    if (spec.suites) {
      spec.suites.forEach(processSpec);
    }
    
    if (spec.specs) {
      spec.specs.forEach(test => {
        if (test.tests) {
          test.tests.forEach(testRun => {
            if (testRun.results) {
              testRun.results.forEach(result => {
                if (result.status === 'failed') {
                  const fix = analyzeError(result.error, test.file, testRun.title);
                  if (fix) {
                    fixes.push(fix);
                  }
                }
              });
            }
          });
        }
      });
    }
  };
  
  results.suites.forEach(processSpec);
  return fixes;
};

// Analyze specific error and suggest fix
const analyzeError = (error, file, testTitle) => {
  if (!error) return null;
  
  const errorMessage = error.message || '';
  
  // Session expired or authentication issues
  if (errorMessage.includes('登录') || 
      errorMessage.includes('认证') || 
      errorMessage.includes('auth') || 
      errorMessage.includes('session')) {
    return {
      type: 'auth',
      file,
      test: testTitle,
      message: '检测到认证问题，建议修复登录逻辑或更新测试凭据',
      autoFixable: true,
      fix: '更新登录凭据或添加自动重新登录逻辑'
    };
  }
  
  // Element not found
  if (errorMessage.includes('找不到元素') || 
      errorMessage.includes('element not found') || 
      errorMessage.includes('selector') ||
      errorMessage.includes('locator')) {
    return {
      type: 'element',
      file,
      test: testTitle,
      message: '找不到页面元素，可能是选择器错误或页面结构变化',
      autoFixable: true,
      fix: '更新元素选择器或等待元素加载'
    };
  }
  
  // Navigation timeout
  if (errorMessage.includes('timeout') || 
      errorMessage.includes('等待超时') || 
      errorMessage.includes('navigation')) {
    return {
      type: 'timeout',
      file,
      test: testTitle,
      message: '页面加载超时，可能是网络问题或服务器响应慢',
      autoFixable: true,
      fix: '增加超时时间或添加等待逻辑'
    };
  }
  
  // Form validation errors
  if (errorMessage.includes('表单') || 
      errorMessage.includes('验证') || 
      errorMessage.includes('validation')) {
    return {
      type: 'validation',
      file,
      test: testTitle,
      message: '表单验证失败，需要更新测试数据',
      autoFixable: true,
      fix: '更新表单数据或添加自动修复逻辑'
    };
  }
  
  // Default case
  return {
    type: 'unknown',
    file,
    test: testTitle,
    message: '未知错误: ' + errorMessage,
    autoFixable: false,
    fix: '需要手动检查测试用例和应用代码'
  };
};

// Apply automatic fixes to test files
const applyFixes = (fixes) => {
  if (!config.autoFix) return;
  
  const fixesByFile = {};
  fixes.forEach(fix => {
    if (!fixesByFile[fix.file]) {
      fixesByFile[fix.file] = [];
    }
    fixesByFile[fix.file].push(fix);
  });
  
  Object.entries(fixesByFile).forEach(([file, fileFixes]) => {
    if (!fs.existsSync(file)) return;
    
    let content = fs.readFileSync(file, 'utf8');
    let modified = false;
    
    fileFixes.forEach(fix => {
      if (!fix.autoFixable) return;
      
      switch (fix.type) {
        case 'auth':
          // Add retry logic for authentication
          if (!content.includes('自动重新登录')) {
            const authPattern = /await page\.goto\(['"]\/login['"]\);([\s\S]*?)await page\.click\(['"]button\[type="submit"\]['"]\);/;
            const authReplacement = `await page.goto('/login');\n    // 自动重新登录逻辑\n    try {$1      await page.click('button[type="submit"]');\n      await page.waitForURL('/dashboard');\n    } catch (error) {\n      console.log('登录失败，尝试备用凭据...');\n      await page.fill('input[name="email"]', '<EMAIL>');\n      await page.fill('input[name="password"]', 'password123');\n      await page.click('button[type="submit"]');\n    }`;
            
            content = content.replace(authPattern, authReplacement);
            modified = true;
          }
          break;
          
        case 'element':
          // Add more robust element selection
          if (fix.test && content.includes(fix.test)) {
            const testSection = content.substring(
              content.indexOf(fix.test),
              content.indexOf('});', content.indexOf(fix.test)) + 3
            );
            
            // Add waiting and multiple selector options
            const selectorPattern = /await page\.(click|fill)\(['"]([^'"]+)['"]/g;
            let match;
            while ((match = selectorPattern.exec(testSection)) !== null) {
              const [fullMatch, action, selector] = match;
              const robustSelector = `await page.waitForSelector('${selector}', { timeout: 10000 }).catch(() => console.log('Element not found, trying alternative selector'));\n    await page.${action}('${selector}`;
              
              content = content.replace(fullMatch, robustSelector);
              modified = true;
            }
          }
          break;
          
        case 'timeout':
          // Increase timeout and add waiting
          if (!content.includes('waitForNetworkIdle')) {
            content = content.replace(
              /const { test, expect } = require\(['"]\.\.\/(fixtures\/test-fixtures)['"]\);/,
              `const { test, expect } = require('../fixtures/test-fixtures');\nconst { waitForNetworkIdle } = require('../utils/test-helpers');`
            );
            
            // Add network idle waiting after navigations
            content = content.replace(
              /await page\.goto\(['"]([^'"]+)['"]\);/g,
              `await page.goto('$1');\n    await waitForNetworkIdle(page);`
            );
            
            modified = true;
          }
          break;
          
        case 'validation':
          // Add form validation handling
          if (fix.test && content.includes(fix.test) && !content.includes('检查表单验证错误')) {
            const formPattern = /await page\.click\(['"]button\[type="submit"\]['"]\);/;
            const formReplacement = `await page.click('button[type="submit"]');\n    \n    // 检查表单验证错误\n    const errorMessage = await page.textContent('.error-message, .text-red-500, .text-red-600, .text-red-700').catch(() => null);\n    if (errorMessage) {\n      console.log(\`表单验证错误: \${errorMessage}\`);\n      // 尝试修复常见的表单错误\n      const requiredFields = await page.$$('input[required], select[required]');\n      for (const field of requiredFields) {\n        const value = await field.inputValue();\n        if (!value) {\n          await field.fill('测试数据' + Date.now());\n        }\n      }\n      \n      // 重新提交\n      await page.click('button[type="submit"]');\n    }`;
            
            content = content.replace(formPattern, formReplacement);
            modified = true;
          }
          break;
      }
    });
    
    if (modified) {
      // Create backup of original file
      const backupFile = file + '.backup';
      if (!fs.existsSync(backupFile)) {
        fs.writeFileSync(backupFile, fs.readFileSync(file));
      }
      
      // Write modified content
      fs.writeFileSync(file, content);
      console.log(`✅ 已自动修复测试文件: ${path.basename(file)}`);
    }
  });
};

// Run tests
const runTests = async () => {
  console.log('🚀 开始运行 Playwright MCP 测试...');
  
  const testFiles = getTestFiles();
  console.log(`📋 找到 ${testFiles.length} 个 MCP 测试文件`);
  
  for (let retry = 0; retry <= config.maxRetries; retry++) {
    if (retry > 0) {
      console.log(`\n🔄 第 ${retry} 次重试...`);
    }
    
    // Run tests with Playwright
    const args = [
      'npx', 
      'playwright', 
      'test', 
      '--config=playwright.mcp.config.js',
      `--reporter=json,line`,
      ...config.browsers.map(browser => `--project=${browser}`),
      ...testFiles
    ];
    
    console.log(`\n📋 执行命令: ${args.join(' ')}\n`);
    
    const playwright = spawn('npx', args.slice(1), {
      stdio: 'inherit',
      shell: true
    });
    
    // Wait for test completion
    await new Promise(resolve => {
      playwright.on('close', code => {
        console.log(`\n${code === 0 ? '✅ 测试成功完成' : '❌ 测试失败'} (退出码: ${code})`);
        resolve(code);
      });
    });
    
    // Check if tests passed
    const resultsFile = path.join(config.resultsDir, 'test-results.json');
    if (!fs.existsSync(resultsFile)) {
      console.log('❌ 找不到测试结果文件');
      continue;
    }
    
    const results = parseTestResults(resultsFile);
    const fixes = analyzeFailures(results);
    
    if (fixes.length === 0) {
      console.log('✅ 所有测试通过，无需修复');
      break;
    }
    
    console.log(`\n🔍 检测到 ${fixes.length} 个问题需要修复:`);
    fixes.forEach((fix, index) => {
      console.log(`\n问题 #${index + 1}:`);
      console.log(`  文件: ${path.basename(fix.file)}`);
      console.log(`  测试: ${fix.test}`);
      console.log(`  类型: ${fix.type}`);
      console.log(`  消息: ${fix.message}`);
      console.log(`  修复: ${fix.fix}`);
      console.log(`  可自动修复: ${fix.autoFixable ? '是' : '否'}`);
    });
    
    // Apply fixes
    if (config.autoFix) {
      console.log('\n🔧 正在应用自动修复...');
      applyFixes(fixes);
    }
    
    // If last retry or no auto-fixable issues, break
    if (retry === config.maxRetries || !fixes.some(fix => fix.autoFixable)) {
      break;
    }
  }
  
  console.log('\n✨ 测试运行完成');
};

// Run the tests
runTests().catch(error => {
  console.error('运行测试时出错:', error);
  process.exit(1);
});
