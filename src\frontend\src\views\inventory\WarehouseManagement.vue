<template>
  <div class="max-w-7xl mx-auto px-4 py-6">
    <!-- 页面标题 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">仓库管理</h1>
        <p class="page-subtitle">管理仓库信息、库位分配和存储详情</p>
      </div>
      <div class="flex space-x-3">
        <button 
          @click="refreshData" 
          :disabled="loading"
          class="btn btn-secondary flex items-center"
        >
          <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          {{ loading ? '刷新中...' : '刷新数据' }}
        </button>
        <router-link to="/inventory" class="btn btn-primary flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          返回库存列表
        </router-link>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-800">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- 仓库概览统计 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
      <!-- 总仓库数 -->
      <div class="bg-white overflow-hidden shadow-lg rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-blue-100 p-3 rounded-lg">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">仓库总数</dt>
                <dd class="text-2xl font-semibold text-gray-900">{{ warehouseStats.totalWarehouses }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- 库位总数 -->
      <div class="bg-white overflow-hidden shadow-lg rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-green-100 p-3 rounded-lg">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">库位总数</dt>
                <dd class="text-2xl font-semibold text-gray-900">{{ warehouseStats.totalLocations }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- 已使用库位 -->
      <div class="bg-white overflow-hidden shadow-lg rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-yellow-100 p-3 rounded-lg">
              <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">已使用库位</dt>
                <dd class="text-2xl font-semibold text-gray-900">{{ warehouseStats.usedLocations }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- 利用率 -->
      <div class="bg-white overflow-hidden shadow-lg rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-purple-100 p-3 rounded-lg">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">利用率</dt>
                <dd class="text-2xl font-semibold text-gray-900">{{ warehouseStats.utilizationRate }}%</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 仓库详情 -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
      <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">仓库详情</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">各仓库的存储情况和库位分布</p>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="px-4 py-12">
        <div class="flex justify-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
        <p class="text-center text-gray-500 mt-4">正在加载数据...</p>
      </div>

      <!-- 仓库列表 -->
      <div v-else-if="warehouses.length > 0" class="divide-y divide-gray-200">
        <div v-for="warehouse in warehouses" :key="warehouse.name" class="p-6">
          <div class="flex items-center justify-between mb-4">
            <div>
              <h4 class="text-lg font-medium text-gray-900">{{ warehouse.name }}</h4>
              <p class="text-sm text-gray-500">{{ warehouse.description }}</p>
            </div>
            <div class="text-right">
              <div class="text-sm text-gray-500">利用率</div>
              <div class="text-2xl font-semibold text-gray-900">{{ warehouse.utilizationRate }}%</div>
            </div>
          </div>
          
          <!-- 利用率进度条 -->
          <div class="mb-4">
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div 
                class="h-2 rounded-full transition-all duration-300"
                :class="getUtilizationColor(warehouse.utilizationRate)"
                :style="{ width: warehouse.utilizationRate + '%' }"
              ></div>
            </div>
          </div>

          <!-- 区域详情 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="area in warehouse.areas" :key="area.name" class="border border-gray-200 rounded-lg p-4">
              <div class="flex items-center justify-between mb-2">
                <h5 class="font-medium text-gray-900">{{ area.name }}</h5>
                <span class="text-sm text-gray-500">{{ area.usedShelves }}/{{ area.totalShelves }}</span>
              </div>
              <div class="space-y-1">
                <div v-for="shelf in area.shelves" :key="shelf.name" class="flex items-center justify-between text-sm">
                  <span class="text-gray-600">{{ shelf.name }}</span>
                  <div class="flex items-center">
                    <span 
                      class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      :class="shelf.isEmpty ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'"
                    >
                      {{ shelf.isEmpty ? '空闲' : '使用中' }}
                    </span>
                    <span v-if="!shelf.isEmpty" class="ml-2 text-gray-500">{{ shelf.productCount }}种产品</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="px-4 py-12 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无仓库数据</h3>
        <p class="mt-1 text-sm text-gray-500">系统中还没有配置仓库信息</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import apiService from '@/services/apiService'

// 响应式数据
const loading = ref(false)
const error = ref(null)
const products = ref([])

// 模拟仓库数据（实际项目中应该从API获取）
const warehouses = ref([
  {
    name: '主仓库',
    description: '主要存储仓库，用于存放常用物料',
    areas: [
      {
        name: 'A区',
        totalShelves: 10,
        usedShelves: 8,
        shelves: [
          { name: 'A01', isEmpty: false, productCount: 3 },
          { name: 'A02', isEmpty: false, productCount: 2 },
          { name: 'A03', isEmpty: true, productCount: 0 },
          { name: 'A04', isEmpty: false, productCount: 1 },
          { name: 'A05', isEmpty: false, productCount: 4 }
        ]
      },
      {
        name: 'B区',
        totalShelves: 8,
        usedShelves: 6,
        shelves: [
          { name: 'B01', isEmpty: false, productCount: 2 },
          { name: 'B02', isEmpty: false, productCount: 3 },
          { name: 'B03', isEmpty: true, productCount: 0 },
          { name: 'B04', isEmpty: false, productCount: 1 }
        ]
      }
    ]
  },
  {
    name: '分仓库',
    description: '辅助仓库，用于存放备用物料',
    areas: [
      {
        name: 'C区',
        totalShelves: 6,
        usedShelves: 3,
        shelves: [
          { name: 'C01', isEmpty: false, productCount: 2 },
          { name: 'C02', isEmpty: true, productCount: 0 },
          { name: 'C03', isEmpty: false, productCount: 1 }
        ]
      }
    ]
  }
])

// 计算仓库统计
const warehouseStats = computed(() => {
  const totalWarehouses = warehouses.value.length
  const totalLocations = warehouses.value.reduce((sum, w) => 
    sum + w.areas.reduce((areaSum, a) => areaSum + a.totalShelves, 0), 0)
  const usedLocations = warehouses.value.reduce((sum, w) => 
    sum + w.areas.reduce((areaSum, a) => areaSum + a.usedShelves, 0), 0)
  const utilizationRate = totalLocations > 0 ? Math.round((usedLocations / totalLocations) * 100) : 0

  // 为每个仓库计算利用率
  warehouses.value.forEach(warehouse => {
    const warehouseTotalShelves = warehouse.areas.reduce((sum, a) => sum + a.totalShelves, 0)
    const warehouseUsedShelves = warehouse.areas.reduce((sum, a) => sum + a.usedShelves, 0)
    warehouse.utilizationRate = warehouseTotalShelves > 0 ? 
      Math.round((warehouseUsedShelves / warehouseTotalShelves) * 100) : 0
  })

  return {
    totalWarehouses,
    totalLocations,
    usedLocations,
    utilizationRate
  }
})

// 获取利用率颜色
const getUtilizationColor = (rate) => {
  if (rate >= 90) return 'bg-red-500'
  if (rate >= 70) return 'bg-yellow-500'
  return 'bg-green-500'
}

// 获取库存数据（用于分析仓库使用情况）
const fetchInventoryData = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await apiService.getProducts({ limit: 1000 })
    products.value = response.results || response || []
    
    // 这里可以根据产品的位置信息更新仓库数据
    // 实际项目中应该有专门的仓库管理API
    
  } catch (err) {
    console.error('获取库存数据失败:', err)
    error.value = '获取仓库数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchInventoryData()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchInventoryData()
})
</script>
