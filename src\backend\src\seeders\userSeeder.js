const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcryptjs');
const { User } = require('../models/user.model');

async function hashPassword(password) {
  const salt = await bcrypt.genSalt(10);
  return await bcrypt.hash(password, salt);
}

async function seedUsers() {
  try {
    // Check if users already exist
    const userCount = await User.count();
    if (userCount > 0) {
      console.log('Users already exist, skipping user seeding');
      return;
    }

    // Create default users
    const users = [
      {
        id: uuidv4(),
        username: 'admin',
        email: '<EMAIL>',
        password: await hashPassword('password123'),
        firstName: '管理员',
        lastName: '系统',
        role: 'admin',
        gender: 'male',
        department: 'IT',
        position: '系统管理员',
        isActive: true,
        isAdmin: true
      },
      {
        id: uuidv4(),
        username: 'manager',
        email: '<EMAIL>',
        password: await hashPassword('Manager@123'),
        firstName: '项目',
        lastName: '经理',
        role: 'manager',
        gender: 'male',
        department: '项目部',
        position: '项目经理',
        isActive: true,
        isAdmin: false
      },
      {
        id: uuidv4(),
        username: 'user1',
        email: '<EMAIL>',
        password: await hashPassword('User@123'),
        firstName: '普通',
        lastName: '用户1',
        role: 'user',
        gender: 'female',
        department: '技术部',
        position: '工程师',
        isActive: true,
        isAdmin: false
      },
      {
        id: uuidv4(),
        username: 'user2',
        email: '<EMAIL>',
        password: await hashPassword('User@123'),
        firstName: '普通',
        lastName: '用户2',
        role: 'user',
        gender: 'male',
        department: '销售部',
        position: '销售代表',
        isActive: true,
        isAdmin: false
      },
      {
        id: uuidv4(),
        username: 'guest',
        email: '<EMAIL>',
        password: await hashPassword('Guest@123'),
        firstName: '访客',
        lastName: '用户',
        role: 'guest',
        gender: 'other',
        department: '外部',
        position: '访客',
        isActive: true,
        isAdmin: false
      }
    ];

    // Bulk create users
    await User.bulkCreate(users);
    console.log('Successfully seeded users');
  } catch (error) {
    console.error('Error seeding users:', error);
    throw error;
  }
}

module.exports = seedUsers;