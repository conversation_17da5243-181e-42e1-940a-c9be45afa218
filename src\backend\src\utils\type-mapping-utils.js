/**
 * Type Mapping Utilities
 * 
 * This module provides utilities for mapping between different data types
 * and handling default values in database migrations.
 */

const { DataTypes } = require('sequelize');

/**
 * Maps a database type to its corresponding Sequelize type
 * @param {string} type - The database type
 * @param {number} [length] - Optional length for types that support it
 * @returns {string} The corresponding Sequelize type definition
 */
function mapPostgresTypeToSequelize(type, length) {
  const typeMap = {
    'varchar': length ? `Sequelize.DataTypes.STRING(${length})` : 'Sequelize.DataTypes.STRING',
    'text': 'Sequelize.DataTypes.TEXT',
    'integer': 'Sequelize.DataTypes.INTEGER',
    'bigint': 'Sequelize.DataTypes.BIGINT',
    'smallint': 'Sequelize.DataTypes.SMALLINT',
    'decimal': 'Sequelize.DataTypes.DECIMAL',
    'numeric': 'Sequelize.DataTypes.DECIMAL',
    'real': 'Sequelize.DataTypes.REAL',
    'float': 'Sequelize.DataTypes.FLOAT',
    'float4': 'Sequelize.DataTypes.FLOAT',
    'float8': 'Sequelize.DataTypes.DOUBLE',
    'double precision': 'Sequelize.DataTypes.DOUBLE',
    'boolean': 'Sequelize.DataTypes.BOOLEAN',
    'date': 'Sequelize.DataTypes.DATEONLY',
    'timestamp': 'Sequelize.DataTypes.DATE',
    'timestamptz': 'Sequelize.DataTypes.DATE',
    'time': 'Sequelize.DataTypes.TIME',
    'uuid': 'Sequelize.DataTypes.UUID',
    'json': 'Sequelize.DataTypes.JSON',
    'jsonb': 'Sequelize.DataTypes.JSONB',
    'array': 'Sequelize.DataTypes.ARRAY',
    'bytea': 'Sequelize.DataTypes.BLOB'
  };

  // Handle array types
  if (type.endsWith('[]')) {
    const baseType = type.slice(0, -2);
    const sequelizeBaseType = mapPostgresTypeToSequelize(baseType, length);
    return `Sequelize.DataTypes.ARRAY(${sequelizeBaseType})`;
  }

  return typeMap[type.toLowerCase()] || 'Sequelize.DataTypes.STRING';
}

module.exports = {
  mapPostgresTypeToSequelize
};

/**
 * Maps a model's data type to its corresponding database type
 * @param {Object} columnDef - The column definition from the model
 * @returns {Object} Object containing type and length information
 */
function mapDataType(columnDef) {
  const type = columnDef.type;
  let result = {
    type: 'varchar',
    length: undefined
  };

  if (type instanceof DataTypes.STRING) {
    result.type = 'varchar';
    result.length = type._length;
  } else if (type instanceof DataTypes.TEXT) {
    result.type = 'text';
  } else if (type instanceof DataTypes.INTEGER) {
    result.type = 'integer';
  } else if (type instanceof DataTypes.BIGINT) {
    result.type = 'bigint';
  } else if (type instanceof DataTypes.FLOAT) {
    result.type = 'float8';
  } else if (type instanceof DataTypes.DOUBLE) {
    result.type = 'double';
  } else if (type instanceof DataTypes.DECIMAL) {
    result.type = 'numeric';
  } else if (type instanceof DataTypes.DATEONLY) {
    result.type = 'date';
  }
  else if (type instanceof DataTypes.DATE) {
    result.type = 'timestamptz';
  }
  else if (type instanceof DataTypes.TIME) {
    result.type = 'time';
  } else if (type instanceof DataTypes.BOOLEAN) {
    result.type = 'boolean';
  } else if (type instanceof DataTypes.UUID) {
    result.type = 'uuid';
  } else if (type instanceof DataTypes.JSON) {
    result.type = 'json';
  } else if (type instanceof DataTypes.JSONB) {
    result.type = 'jsonb';
  } else if (type instanceof DataTypes.BLOB) {
    result.type = 'bytea';
  } else if (type instanceof DataTypes.ARRAY) {
    const baseType = mapDataType({ type: type.type });
    result.type = `${baseType.type}[]`;
    result.length = baseType.length;
  }

  return result;
}

/**
 * Normalizes default values for comparison
 * @param {*} value - The default value to normalize
 * @param {string} type - The data type of the column
 * @returns {*} The normalized default value
 */
function normalizeDefaultValue(value, type) {
  if (value === null || value === undefined) {
    return null;
  }

  // Handle function/special default values
  if (typeof value === 'function' || typeof value === 'object') {
    if (type === 'uuid') {
      return 'UUIDV4';
    }
  }

  return value;
}

/**
 * Compares two default values for equality
 * @param {*} dbValue - The default value from the database
 * @param {*} modelValue - The default value from the model
 * @param {string} type - The data type of the column
 * @returns {boolean} Whether the default values are considered equal
 */
function areDefaultValuesEqual(dbValue, modelValue, type) {
  const normalizedDbValue = normalizeDefaultValue(dbValue, type);
  const normalizedModelValue = normalizeDefaultValue(modelValue, type);

  if (normalizedDbValue === null && normalizedModelValue === null) {
    return true;
  }

  return normalizedDbValue === normalizedModelValue;
}

module.exports = {
  mapPostgresTypeToSequelize,
  mapDataType,
  normalizeDefaultValue,
  areDefaultValuesEqual
};