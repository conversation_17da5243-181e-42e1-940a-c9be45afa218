import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL || '';

const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Request interceptor for adding auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const { response } = error;

    // Handle token expiration
    if (response && response.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }

    return Promise.reject(error);
  }
);

const attendanceService = {
  /**
   * 获取考勤记录列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回考勤记录列表
   */
  getAttendances: (params) => {
    return apiClient.get('/api/attendances', { params });
  },
  
  /**
   * 获取单个考勤记录
   * @param {string} id - 考勤记录ID
   * @returns {Promise} - 返回考勤记录详情
   */
  getAttendance: (id) => {
    return apiClient.get(`/api/attendances/${id}`);
  },
  
  /**
   * 创建考勤记录
   * @param {Object} data - 考勤记录数据
   * @returns {Promise} - 返回创建的考勤记录
   */
  createAttendance: (data) => {
    return apiClient.post('/api/attendances', data);
  },
  
  /**
   * 更新考勤记录
   * @param {string} id - 考勤记录ID
   * @param {Object} data - 更新的考勤记录数据
   * @returns {Promise} - 返回更新后的考勤记录
   */
  updateAttendance: (id, data) => {
    return apiClient.patch(`/api/attendances/${id}`, data);
  },
  
  /**
   * 删除考勤记录
   * @param {string} id - 考勤记录ID
   * @returns {Promise} - 返回删除结果
   */
  deleteAttendance: (id) => {
    return apiClient.delete(`/api/attendances/${id}`);
  },
  
  /**
   * 导出考勤记录
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回导出的文件
   */
  exportAttendances: (params) => {
    return apiClient.get('/api/attendances/export', { 
      params,
      responseType: 'blob' 
    });
  }
};

export default attendanceService;
