require('dotenv').config({ path: '.env.test' });
const { sequelize } = require('../../models');
const testUserSeeder = require('./20240422-test-users');

async function runTestSeeders() {
  try {
    console.log('Running test seeders...');
    
    // 连接数据库
    await sequelize.authenticate();
    console.log('Database connection established successfully.');
    
    // 运行测试用户种子
    const testUserId = await testUserSeeder.up(sequelize.getQueryInterface(), sequelize);
    console.log(`Test user ID: ${testUserId}`);
    
    console.log('Test seeders completed successfully.');
    process.exit(0);
  } catch (error) {
    console.error('Error running test seeders:', error);
    process.exit(1);
  }
}

runTestSeeders();
