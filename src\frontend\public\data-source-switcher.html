<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Data Source Switcher</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    h1 {
      color: #333;
    }
    .card {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
      background-color: #f9f9f9;
    }
    .button {
      background-color: #4CAF50;
      border: none;
      color: white;
      padding: 10px 20px;
      text-align: center;
      text-decoration: none;
      display: inline-block;
      font-size: 16px;
      margin: 4px 2px;
      cursor: pointer;
      border-radius: 4px;
    }
    .button.secondary {
      background-color: #2196F3;
    }
    .button.warning {
      background-color: #ff9800;
    }
    pre {
      background-color: #f5f5f5;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
    }
    .switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 34px;
    }
    .slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    input:checked + .slider {
      background-color: #722ed1;
    }
    input:checked + .slider:before {
      transform: translateX(26px);
    }
    .switch-container {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
    .switch-label {
      margin-left: 10px;
      font-weight: bold;
    }
    .api {
      color: #4CAF50;
    }
    .mock {
      color: #722ed1;
    }
  </style>
</head>
<body>
  <h1>Data Source Switcher</h1>
  
  <div class="card">
    <h2>Current Data Source</h2>
    <div class="switch-container">
      <label class="switch">
        <input type="checkbox" id="data-source-toggle">
        <span class="slider"></span>
      </label>
      <span class="switch-label" id="data-source-label">Loading...</span>
    </div>
    <button id="refresh-page" class="button">Refresh Page</button>
    <button id="reset-data-source" class="button warning">Reset to Default (API)</button>
  </div>
  
  <div class="card">
    <h2>Data Source Information</h2>
    <pre id="data-source-info">Loading...</pre>
  </div>
  
  <div class="card">
    <h2>Available Pages</h2>
    <p>Click on a link to open the page:</p>
    <ul>
      <li><a href="/" target="_blank">Home Page</a></li>
      <li><a href="/users" target="_blank">Users Page</a></li>
      <li><a href="/projects" target="_blank">Projects Page</a></li>
      <li><a href="/products" target="_blank">Products Page</a></li>
      <li><a href="/orders" target="_blank">Orders Page</a></li>
      <li><a href="/console-checker.html" target="_blank">Console Checker</a></li>
      <li><a href="/api-checker.html" target="_blank">API Checker</a></li>
    </ul>
  </div>
  
  <script>
    // Function to update the data source toggle
    function updateDataSourceToggle() {
      const toggle = document.getElementById('data-source-toggle');
      const label = document.getElementById('data-source-label');
      const dataSource = localStorage.getItem('app_data_source') || 'api';
      
      toggle.checked = dataSource === 'mock';
      label.textContent = dataSource === 'mock' ? 'Using Mock Data' : 'Using API';
      label.className = 'switch-label ' + (dataSource === 'mock' ? 'mock' : 'api');
      
      updateDataSourceInfo();
    }
    
    // Function to update data source information
    function updateDataSourceInfo() {
      const infoElement = document.getElementById('data-source-info');
      const dataSource = localStorage.getItem('app_data_source') || 'api';
      
      let info = `Current Data Source: ${dataSource.toUpperCase()}\n`;
      info += `localStorage Key: app_data_source\n`;
      info += `localStorage Value: ${dataSource}\n\n`;
      
      // Add information about the global variable if available
      try {
        const globalVar = window.parent.__currentDataSource;
        if (globalVar) {
          info += `Global Variable: window.__currentDataSource\n`;
          info += `Global Variable Value: ${globalVar}\n\n`;
        }
      } catch (e) {
        info += `Global Variable: Not accessible\n\n`;
      }
      
      // Add information about the API URL
      try {
        const apiUrl = window.location.origin;
        info += `API Base URL: ${apiUrl}\n`;
      } catch (e) {
        info += `API Base URL: Could not determine\n`;
      }
      
      infoElement.textContent = info;
    }
    
    // Function to toggle data source
    function toggleDataSource() {
      const currentDataSource = localStorage.getItem('app_data_source') || 'api';
      const newDataSource = currentDataSource === 'mock' ? 'api' : 'mock';
      
      localStorage.setItem('app_data_source', newDataSource);
      updateDataSourceToggle();
      
      // Dispatch event to notify the application
      try {
        window.parent.dispatchEvent(new CustomEvent('datasource-changed', {
          detail: { type: newDataSource }
        }));
      } catch (e) {
        console.error('Could not dispatch event to parent window:', e);
      }
    }
    
    // Function to reset data source to default (API)
    function resetDataSource() {
      localStorage.setItem('app_data_source', 'api');
      updateDataSourceToggle();
      
      // Dispatch event to notify the application
      try {
        window.parent.dispatchEvent(new CustomEvent('datasource-changed', {
          detail: { type: 'api' }
        }));
      } catch (e) {
        console.error('Could not dispatch event to parent window:', e);
      }
    }
    
    // Add event listeners
    document.getElementById('data-source-toggle').addEventListener('change', toggleDataSource);
    document.getElementById('refresh-page').addEventListener('click', function() {
      window.parent.location.reload();
    });
    document.getElementById('reset-data-source').addEventListener('click', resetDataSource);
    
    // Initialize the page
    window.addEventListener('DOMContentLoaded', updateDataSourceToggle);
  </script>
</body>
</html>
