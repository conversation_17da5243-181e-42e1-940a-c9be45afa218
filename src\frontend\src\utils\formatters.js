/**
 * Format a number as currency
 * @param {number} value - The number to format
 * @param {string} [currency='CNY'] - The currency code
 * @param {string} [locale='zh-CN'] - The locale to use
 * @returns {string} Formatted currency string
 */
export function formatCurrency(value, currency = 'CNY', locale = 'zh-CN') {
  if (value === null || value === undefined) return '-'
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value)
}

/**
 * Format a date string
 * @param {string|Date} date - The date to format
 * @param {string} [locale='zh-CN'] - The locale to use
 * @returns {string} Formatted date string
 */
export function formatDate(date, locale = 'zh-CN') {
  if (!date) return '-'
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(dateObj)
} 