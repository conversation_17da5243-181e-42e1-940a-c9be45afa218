/**
 * 员工管理增强测试 - 使用自动修复和智能测试功能
 */

const { test, expect } = require('../fixtures/test-fixtures');
const { generateTestData } = require('../utils/test-helpers');

// 使用增强版测试夹具
test.describe('员工管理功能测试 (增强版)', () => {
  // 在每个测试前登录并使用智能页面
  test.beforeEach(async ({ page, smartPage }) => {
    // 登录系统
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // 等待导航到仪表盘
    try {
      await page.waitForURL('/dashboard');
    } catch (error) {
      console.log('登录失败，尝试备用凭据...');
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'password123');
      await page.click('button[type="submit"]');
      await page.waitForURL('/dashboard');
    }
  });

  test('合同工管理页面测试', async ({ smartPage }) => {
    // 使用智能导航
    await smartPage.smartNavigateTo('/employees/contract', '合同工管理');
    
    // 验证页面元素
    const addButton = await smartPage.$('button:has-text("添加合同工")');
    expect(addButton).toBeTruthy();
    
    // 测试搜索功能
    await smartPage.fill('input#search', '测试');
    await smartPage.click('button:has-text("搜索")');
    
    // 等待搜索结果
    await smartPage.waitForLoadState('networkidle');
    
    // 测试重置功能
    await smartPage.click('button:has-text("重置")');
    await smartPage.waitForLoadState('networkidle');
  });
  
  test('添加合同工测试', async ({ smartPage }) => {
    // 导航到合同工管理页面
    await smartPage.smartNavigateTo('/employees/contract', '合同工管理');
    
    // 点击添加按钮
    await smartPage.click('button:has-text("添加合同工")');
    
    // 验证是否导航到添加页面
    await expect(smartPage).toHaveURL(/\/employees\/contract\/add/);
    
    // 准备表单数据
    const formData = {
      employeeName: generateTestData('name'),
      idNumber: generateTestData('idNumber'),
      phone: generateTestData('phone'),
      joinDate: generateTestData('date'),
      workType: '普通工人',
      dailyWage: generateTestData('number'),
      remarks: '自动化测试创建'
    };
    
    // 使用智能表单填充
    await smartPage.smartFillForm(formData);
    
    // 提交表单
    await smartPage.click('button[type="submit"]');
    
    // 等待导航回列表页面
    try {
      await smartPage.waitForURL('/employees/contract', { timeout: 10000 });
    } catch (error) {
      console.log('提交后未导航到列表页面，检查是否有错误...');
      
      // 检查是否有错误消息
      const errorText = await smartPage.textContent('.error-message, .text-red-500, .text-red-600, .text-red-700');
      if (errorText) {
        console.log(`检测到错误: ${errorText}`);
        
        // 如果是身份证号重复错误，修改身份证号并重试
        if (errorText.includes('身份证') || errorText.includes('ID number')) {
          const newIdNumber = generateTestData('idNumber');
          await smartPage.fill('input[id="idNumber"]', newIdNumber);
          await smartPage.click('button[type="submit"]');
          await smartPage.waitForURL('/employees/contract');
        }
      }
    }
    
    // 验证是否返回列表页面
    expect(smartPage.url()).toContain('/employees/contract');
  });
  
  test('临时工管理页面测试', async ({ smartPage }) => {
    // 导航到临时工管理页面
    await smartPage.smartNavigateTo('/employees/temporary', '临时工管理');
    
    // 验证页面元素
    const addButton = await smartPage.$('button:has-text("添加临时工")');
    expect(addButton).toBeTruthy();
    
    // 测试搜索功能
    await smartPage.fill('input#search', '测试');
    await smartPage.click('button:has-text("搜索")');
    
    // 等待搜索结果
    await smartPage.waitForLoadState('networkidle');
    
    // 测试重置功能
    await smartPage.click('button:has-text("重置")');
    await smartPage.waitForLoadState('networkidle');
  });
  
  test('添加临时工测试', async ({ smartPage }) => {
    // 导航到临时工管理页面
    await smartPage.smartNavigateTo('/employees/temporary', '临时工管理');
    
    // 点击添加按钮
    await smartPage.click('button:has-text("添加临时工")');
    
    // 验证是否导航到添加页面
    await expect(smartPage).toHaveURL(/\/employees\/temporary\/add/);
    
    // 准备表单数据
    const formData = {
      name: generateTestData('name'),
      idNumber: generateTestData('idNumber'),
      startDate: generateTestData('date'),
      endDate: generateTestData('futureDate'),
      position: '临时工人',
      department: '施工部',
      salary: generateTestData('number'),
      'contactInfo.phone': generateTestData('phone'),
      'contactInfo.email': generateTestData('email')
    };
    
    // 使用智能表单填充
    await smartPage.smartFillForm(formData);
    
    // 提交表单
    await smartPage.click('button[type="submit"]');
    
    // 等待导航回列表页面
    try {
      await smartPage.waitForURL('/employees/temporary', { timeout: 10000 });
    } catch (error) {
      console.log('提交后未导航到列表页面，检查是否有错误...');
      
      // 检查是否有错误消息
      const errorText = await smartPage.textContent('.error-message, .text-red-500, .text-red-600, .text-red-700');
      if (errorText) {
        console.log(`检测到错误: ${errorText}`);
        
        // 如果是身份证号重复错误，修改身份证号并重试
        if (errorText.includes('身份证') || errorText.includes('ID number')) {
          const newIdNumber = generateTestData('idNumber');
          await smartPage.fill('input[id="idNumber"]', newIdNumber);
          await smartPage.click('button[type="submit"]');
          await smartPage.waitForURL('/employees/temporary');
        }
      }
    }
    
    // 验证是否返回列表页面
    expect(smartPage.url()).toContain('/employees/temporary');
  });
  
  test('编辑合同工测试', async ({ smartPage }) => {
    // 导航到合同工管理页面
    await smartPage.smartNavigateTo('/employees/contract', '合同工管理');
    
    // 检查是否有合同工记录
    const hasRecords = await smartPage.$$eval('table tbody tr', rows => rows.length > 0);
    
    if (hasRecords) {
      // 点击第一个编辑按钮
      await smartPage.click('table tbody tr:first-child button:has-text("编辑"), table tbody tr:first-child a:has-text("编辑")');
      
      // 验证是否导航到编辑页面
      await expect(smartPage).toHaveURL(/\/employees\/contract\/edit\//);
      
      // 修改表单数据
      await smartPage.fill('input[id="dailyWage"]', generateTestData('number'));
      await smartPage.fill('textarea[id="remarks"]', '自动化测试更新 ' + new Date().toISOString());
      
      // 提交表单
      await smartPage.click('button[type="submit"]');
      
      // 等待导航回列表页面
      await smartPage.waitForURL('/employees/contract');
    } else {
      console.log('没有找到合同工记录，跳过编辑测试');
    }
  });
  
  test('删除合同工测试', async ({ smartPage }) => {
    // 导航到合同工管理页面
    await smartPage.smartNavigateTo('/employees/contract', '合同工管理');
    
    // 检查是否有合同工记录
    const hasRecords = await smartPage.$$eval('table tbody tr', rows => rows.length > 0);
    
    if (hasRecords) {
      // 设置对话框自动确认
      smartPage.on('dialog', dialog => dialog.accept());
      
      // 点击第一个删除按钮
      await smartPage.click('table tbody tr:first-child button:has-text("删除"), table tbody tr:first-child a:has-text("删除")');
      
      // 等待网络请求完成
      await smartPage.waitForLoadState('networkidle');
      
      // 验证是否仍在列表页面
      expect(smartPage.url()).toContain('/employees/contract');
    } else {
      console.log('没有找到合同工记录，跳过删除测试');
    }
  });
});
