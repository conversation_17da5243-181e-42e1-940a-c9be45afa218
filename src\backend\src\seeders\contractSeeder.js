const { Contract } = require('../models/contract.model');
const { Project } = require('../models/project.model');
const { User } = require('../models/user.model');
const { faker } = require('@faker-js/faker/locale/zh_CN');

const contractTypes = ['销售合同', '采购合同', '服务合同', '技术合同', '劳务合同'];

async function seedContracts() {
  try {
    // Clear existing data
    await Contract.destroy({ where: {} });

    // Get all projects and users for reference
    const projects = await Project.findAll();
    const users = await User.findAll();

    if (!projects.length || !users.length) {
      console.log('Please seed projects and users first');
      return;
    }

    const contracts = [];

    // Create contracts for each project
    for (const project of projects) {
      const startDate = faker.date.between({
        from: '2023-01-01',
        to: '2024-12-31'
      });

      const contract = {
        id: faker.string.uuid(),
        projectId: project.id,
        contractNumber: 'CT' + faker.string.numeric(8),
        name: project.name + ' - ' + faker.helpers.arrayElement(contractTypes),
        contractType: faker.helpers.arrayElement(contractTypes),
        status: faker.helpers.arrayElement(['draft', 'active', 'completed', 'terminated', 'expired']),
        amount: faker.number.float({ min: 50000, max: 5000000, precision: 2 }),
        invoiceInfo: faker.lorem.paragraph(),
        startDate: startDate,
        endDate: faker.date.between({
          from: startDate,
          to: new Date(startDate.getTime() + 365 * 24 * 60 * 60 * 1000)
        }),
        description: faker.lorem.paragraph(),
        createdBy: faker.helpers.arrayElement(users).id,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      contracts.push(contract);
    }

    await Contract.bulkCreate(contracts);
    console.log('Contracts seeder executed successfully');
  } catch (error) {
    console.error('Error seeding contracts:', error);
  }
}

module.exports = seedContracts; 