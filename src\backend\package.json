{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "dev:windsurf": "cross-env NODE_ENV=windsurf nodemon src/index.js", "dev:cursor": "cross-env NODE_ENV=cursor nodemon src/index.js", "dev:vscode": "cross-env NODE_ENV=vscode nodemon src/index.js", "dev:insider": "cross-env NODE_ENV=insider nodemon src/index.js", "dev:trae": "cross-env NODE_ENV=trae nodemon src/index.js", "test": "cross-env NODE_ENV=test mocha --require tests/test-setup.js tests/**/*.test.js --timeout 10000", "test:unit": "cross-env NODE_ENV=test mocha --require tests/test-setup.js tests/unit/**/*.test.js", "test:integration": "cross-env NODE_ENV=test mocha --require tests/test-setup.js tests/integration/**/*.test.js --timeout 10000", "test:prepare": "cross-env NODE_ENV=test node src/seeders/test/run-test-seeders.js", "test:coverage": "nyc --reporter=text --reporter=html --reporter=lcov npm test", "test:unit:coverage": "nyc --reporter=text --reporter=html --reporter=lcov npm run test:unit", "test:performance": "mocha tests/performance/**/*.test.js --timeout 60000", "test:security": "mocha tests/security/**/*.test.js --timeout 10000", "lint": "eslint src/ --ext .js", "lint:fix": "eslint src/ --ext .js --fix", "db:create": "node src/scripts/postgres-migration.js create", "db:gen": "node src/scripts/generate-migration.js", "db:run": "node src/scripts/postgres-migration.js run", "db:gr": "npm run db:gen && npm run db:run", "db:seed": "node src/scripts/seed-test-data.js", "db:list": "node src/scripts/postgres-migration.js list", "db:rollback": "node src/scripts/postgres-migration.js rollback", "backup": "node src/scripts/postgres-backup.js backup", "backup:list": "node src/scripts/postgres-backup.js list", "backup:restore": "node src/scripts/postgres-backup.js restore", "backup:delete": "node src/scripts/postgres-backup.js delete"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@faker-js/faker": "^9.7.0", "axios": "^1.8.4", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "express": "^4.21.2", "helmet": "^8.0.0", "http-status": "^2.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.1", "sequelize-cli": "^6.6.2", "uuid": "^11.1.0", "winston": "^3.17.0", "xss-clean": "^0.1.4"}, "devDependencies": {"autocannon": "^7.12.0", "chai": "^4.3.7", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "mocha": "^10.2.0", "nodemon": "^3.1.9", "nyc": "^15.1.0", "sinon": "^15.2.0", "supertest": "^6.3.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "packageManager": "pnpm@9.9.0+sha512.60c18acd138bff695d339be6ad13f7e936eea6745660d4cc4a776d5247c540d0edee1a563695c183a66eb917ef88f2b4feb1fc25f32a7adcadc7aaf3438e99c1"}