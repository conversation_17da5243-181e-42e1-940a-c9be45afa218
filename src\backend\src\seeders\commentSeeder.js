const { Comment } = require('../models/comment.model');
const { User } = require('../models/user.model');
const { Document } = require('../models/document.model');
const { faker } = require('@faker-js/faker/locale/zh_CN');

async function seedComments() {
  try {
    // Clear existing data
    await Comment.destroy({ where: {} });

    // Get all users and documents for reference
    const users = await User.findAll();
    const documents = await Document.findAll();

    if (!users.length || !documents.length) {
      console.log('Please seed users and documents first');
      return;
    }

    const comments = [];

    // Create comments for documents
    for (const document of documents) {
      // Create 1-3 comments per document
      const commentCount = faker.number.int({ min: 1, max: 3 });
      
      for (let i = 0; i < commentCount; i++) {
        const comment = {
          id: faker.string.uuid(), // 预先生成 UUID
          userId: faker.helpers.arrayElement(users).id,
          documentId: document.id,
          content: faker.lorem.paragraph(),
          parentId: null,
          isActive: true,
          isEdited: false,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        comments.push(comment);
      }
    }

    // Create all comments first
    await Comment.bulkCreate(comments);

    // Add some reply comments (nested comments)
    const replyComments = [];
    for (const comment of comments) {
      if (faker.number.int({ min: 1, max: 4 }) === 1) { // 25% chance to have replies
        const replyCount = faker.number.int({ min: 1, max: 3 });
        
        for (let j = 0; j < replyCount; j++) {
          const reply = {
            id: faker.string.uuid(),
            userId: faker.helpers.arrayElement(users).id,
            documentId: comment.documentId,
            content: faker.lorem.paragraph(),
            parentId: comment.id, // 使用父评论的 UUID
            isActive: true,
            isEdited: false,
            createdAt: new Date(),
            updatedAt: new Date()
          };
          replyComments.push(reply);
        }
      }
    }

    // Then create replies
    if (replyComments.length > 0) {
      await Comment.bulkCreate(replyComments);
    }

    console.log('Comments seeder executed successfully');
  } catch (error) {
    console.error('Error seeding comments:', error);
  }
}

module.exports = seedComments; 