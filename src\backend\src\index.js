// Load environment variables first, before any other imports or code
const fs = require("fs");
const dotenv = require("dotenv");

// First load the default .env file
dotenv.config();

// Then try to load environment-specific file if it exists
const nodeEnv = process.env.NODE_ENV;
if (nodeEnv) {
  const envPath = `.env.${nodeEnv}`;
  if (fs.existsSync(envPath)) {
    console.log(`Loading environment from: ${envPath}`);
    // The override option ensures variables from this file take precedence
    const envConfig = dotenv.config({ path: envPath, override: true });
    if (envConfig.error) {
      console.error(`Error loading ${envPath}:`, envConfig.error);
    } else {
      console.log(`Successfully loaded environment from ${envPath}`);
    }
  } else {
    console.log(`Environment file ${envPath} not found, using default .env`);
  }
}

// Now load other modules after environment variables are set
const app = require("./app");
const http = require("http");
const { testConnection, sequelize } = require("./config/database");
const logger = require("./config/logger");
const createAdminUser = require("./scripts/createAdminUser");

// Get port from environment variable or default to 5000
const PORT = process.env.PORT || 80;
console.log(`Server will start on port: ${PORT}`);

// Create HTTP server
const server = http.createServer(app);

// Start server
async function startServer() {
  try {
    logger.info("Starting server initialization...");

    // Test database connection
    logger.info("Testing database connection...");
    const isConnected = await testConnection();
    if (!isConnected) {
      logger.error("Database connection failed. Exiting application.");
      process.exit(1);
    }
    logger.info("Database connection successful");

    // Sync database models (in development mode only)
    if (process.env.NODE_ENV === "development") {
      logger.info("Syncing database models...");
      try {
        // Use a safer sync approach - don't alter tables automatically
        // This avoids SQLite foreign key constraint issues
        await sequelize.sync({ force: false, alter: false });
        logger.info("Database models synchronized successfully");
      } catch (syncError) {
        logger.warn(`Database sync warning: ${syncError.message}`);
        logger.info("Continuing with existing database schema");
      }
    }

    // Create admin user if it doesn't exist
    try {
      await createAdminUser();
    } catch (adminError) {
      logger.warn(`Error creating admin user: ${adminError.message}`);
      logger.info('Continuing with server startup');
    }

    // Start HTTP server
    logger.info(`Attempting to start server on port ${PORT}...`);
    server.listen(PORT, () => {
      logger.info(
        `Server running in ${
          process.env.NODE_ENV || "development"
        } mode on port ${PORT}`
      );
    });

    server.on("error", (error) => {
      if (error.code === "EADDRINUSE") {
        logger.error(
          `Port ${PORT} is already in use. Please use a different port.`
        );
      } else {
        logger.error(`Server error: ${error.message}`, { stack: error.stack });
      }
      process.exit(1);
    });
  } catch (error) {
    logger.error(`Error starting server: ${error.message}`, {
      stack: error.stack,
    });
    console.error("Startup error:", error); // Console log for direct viewing
    process.exit(1);
  }
}

// Check environment variables
function checkEnvironment() {
  logger.info("Checking environment variables...");

  // Base required variables
  const baseRequiredVars = ["NODE_ENV"];

  // Add database-specific required variables based on dialect
  let requiredVars = [...baseRequiredVars];

  // If using SQLite, we only need DB_DIALECT and optionally DB_STORAGE
  if (process.env.DB_DIALECT === "sqlite") {
    // No additional required vars for SQLite
    logger.info(
      "Using SQLite database, minimal environment variables required"
    );
  } else {
    // For other database types, we need connection details
    requiredVars = [
      ...requiredVars,
      "DB_NAME",
      "DB_USER",
      "DB_PASSWORD",
      "DB_HOST",
    ];
  }

  const missing = requiredVars.filter((varName) => !process.env[varName]);

  if (missing.length > 0) {
    logger.error(
      `Missing required environment variables: ${missing.join(", ")}`
    );
    console.error(
      `Missing required environment variables: ${missing.join(", ")}`
    );
    return false;
  }

  logger.info("Environment check passed");
  return true;
}

// Graceful shutdown function
const gracefulShutdown = async () => {
  logger.info("Received shutdown signal, closing server and connections...");

  // Set a timeout for forced shutdown
  const forceShutdownTimeout = setTimeout(() => {
    logger.error(
      "Could not close connections in time, forcefully shutting down"
    );
    process.exit(1);
  }, 10000);

  try {
    // First close the server (stop accepting new connections)
    await new Promise((resolve, reject) => {
      server.close((err) => {
        if (err) {
          logger.error(`Error closing HTTP server: ${err.message}`);
          reject(err);
        } else {
          logger.info("HTTP server closed");
          resolve();
        }
      });
    });

    // Then close database connection
    await sequelize.close();
    logger.info("Database connection closed");

    // Clear the timeout as we've successfully closed everything
    clearTimeout(forceShutdownTimeout);
    logger.info("Graceful shutdown completed");
    process.exit(0);
  } catch (err) {
    logger.error(`Error during graceful shutdown: ${err.message}`);
    // Don't clear the timeout - let it force exit if needed
  }
};

// Handle graceful shutdown
process.on("SIGTERM", gracefulShutdown);
process.on("SIGINT", gracefulShutdown);

// Handle unhandled promise rejections
process.on("unhandledRejection", (error) => {
  console.error("Unhandled Promise Rejection:", error);
  logger.error("Unhandled Promise Rejection:", error.stack);
});

// Add uncaught exception handler
process.on("uncaughtException", (error) => {
  console.error("Uncaught Exception:", error);
  logger.error("Uncaught Exception:", {
    error: error.message,
    stack: error.stack,
  });
});

// Start the server if environment is properly configured
if (checkEnvironment()) {
  logger.info("Starting application...");
  console.log("Starting application...");
  startServer().catch((err) => {
    logger.error("Failed to start server:", err);
    console.error("Failed to start server:", err);
    process.exit(1);
  });
} else {
  process.exit(1);
}
