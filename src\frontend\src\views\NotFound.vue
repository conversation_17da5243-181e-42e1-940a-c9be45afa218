<template>
  <div class="min-h-screen bg-gray-100 flex flex-col justify-center items-center px-4 py-12">
    <div class="text-center">
      <h1 class="text-9xl font-bold text-gray-900">404</h1>
      <p class="text-2xl font-semibold text-gray-700 mt-4">Page Not Found</p>
      <p class="text-gray-500 mt-2">The page you are looking for doesn't exist or has been moved.</p>
      
      <div class="mt-8">
        <router-link 
          to="/dashboard" 
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back to Dashboard
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NotFoundView'
}
</script>