/**
 * Migration: Schema Synchronization
 * Created at: 2025-05-24T06:15:07.513Z
 */
'use strict';

/** @type {import('sequelize').QueryInterface} */
module.exports = {
  // Apply the migration
  async up(queryInterface, Sequelize) {
    // Create missing tables
    await queryInterface.createTable('category', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      parentId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'category',
          key: 'id'
        },
      },
      path: {
        type: Sequelize.DataTypes.STRING(255),
      },
      level: {
        type: Sequelize.DataTypes.INTEGER,
        defaultValue: 1,
        allowNull: false,
      },
      isActive: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: true,
      },
      iconUrl: {
        type: Sequelize.DataTypes.STRING(255),
      },
      sortOrder: {
        type: Sequelize.DataTypes.INTEGER,
        defaultValue: 0,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('department', {
      id: {
        type: Sequelize.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      code: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      parentId: {
        type: Sequelize.DataTypes.INTEGER,
        references: {
          model: 'department',
          key: 'id'
        },
      },
      status: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "active",
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('user', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      username: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      email: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      password: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      lastName: {
        type: Sequelize.DataTypes.STRING(255),
      },
      role: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "guest",
        allowNull: false,
      },
      gender: {
        type: Sequelize.DataTypes.STRING,
      },
      departmentId: {
        type: Sequelize.DataTypes.INTEGER,
        references: {
          model: 'department',
          key: 'id'
        },
      },
      position: {
        type: Sequelize.DataTypes.STRING(255),
      },
      idNumber: {
        type: Sequelize.DataTypes.STRING(255),
        unique: true,
      },
      phoneNumber: {
        type: Sequelize.DataTypes.STRING(255),
      },
      address: {
        type: Sequelize.DataTypes.TEXT,
      },
      avatarUrl: {
        type: Sequelize.DataTypes.STRING(255),
      },
      isActive: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: true,
      },
      isAdmin: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
      lastLogin: {
        type: Sequelize.DataTypes.DATE,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('document', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      title: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      content: {
        type: Sequelize.DataTypes.TEXT,
      },
      summary: {
        type: Sequelize.DataTypes.TEXT,
      },
      categoryId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'category',
          key: 'id'
        },
      },
      authorId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      status: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "draft",
        allowNull: false,
      },
      viewCount: {
        type: Sequelize.DataTypes.INTEGER,
        defaultValue: 0,
      },
      isPublic: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
      publishDate: {
        type: Sequelize.DataTypes.DATE,
      },
      lastModifierId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      documentType: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "article",
        allowNull: false,
      },
      priority: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "medium",
        allowNull: false,
      },
      version: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "1.0",
      },
      metaData: {
        type: Sequelize.DataTypes.JSON,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('project', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      code: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      manager: {
        type: Sequelize.DataTypes.STRING(255),
      },
      constructionUnit: {
        type: Sequelize.DataTypes.STRING(255),
      },
      designUnit: {
        type: Sequelize.DataTypes.STRING(255),
      },
      contractorUnit: {
        type: Sequelize.DataTypes.STRING(255),
      },
      supervisorUnit: {
        type: Sequelize.DataTypes.STRING(255),
      },
      reviewUnit: {
        type: Sequelize.DataTypes.STRING(255),
      },
      address: {
        type: Sequelize.DataTypes.STRING(255),
      },
      startDate: {
        type: Sequelize.DataTypes.DATE,
      },
      endDate: {
        type: Sequelize.DataTypes.DATE,
      },
      archiveNumber: {
        type: Sequelize.DataTypes.STRING(255),
      },
      engineeringNumber: {
        type: Sequelize.DataTypes.STRING(255),
      },
      usageType: {
        type: Sequelize.DataTypes.STRING,
        allowNull: false,
      },
      engineeringType: {
        type: Sequelize.DataTypes.STRING,
        allowNull: false,
      },
      constructionPermitNumber: {
        type: Sequelize.DataTypes.STRING(255),
      },
      area: {
        type: Sequelize.DataTypes.DOUBLE,
      },
      specialSystem: {
        type: Sequelize.DataTypes.STRING(255),
      },
      companyManager: {
        type: Sequelize.DataTypes.STRING(255),
      },
      status: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "planning",
        allowNull: false,
      },
      projectNumber: {
        type: Sequelize.DataTypes.STRING(255),
        unique: true,
      },
      progress: {
        type: Sequelize.DataTypes.INTEGER,
        defaultValue: 0,
      },
      clientContact: {
        type: Sequelize.DataTypes.STRING(255),
      },
      tag: {
        type: Sequelize.DataTypes.ARRAY(Sequelize.DataTypes.STRING(255)),
        defaultValue: null,
      },
      members: {
        type: Sequelize.DataTypes.ARRAY(Sequelize.DataTypes.UUID),
        defaultValue: null,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('supplier', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      code: {
        type: Sequelize.DataTypes.STRING(255),
      },
      category: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      address: {
        type: Sequelize.DataTypes.TEXT,
        allowNull: false,
      },
      phone: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      contactName: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      contactPhone: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      contactEmail: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      rating: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      products: {
        type: Sequelize.DataTypes.JSON,
        defaultValue: null,
        allowNull: false,
      },
      notes: {
        type: Sequelize.DataTypes.TEXT,
      },
      tags: {
        type: Sequelize.DataTypes.JSON,
        defaultValue: null,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('reimbursement', {
      id: {
        type: Sequelize.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectName: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      reimbursementDate: {
        type: Sequelize.DataTypes.DATEONLY,
        allowNull: false,
      },
      reimbursementType: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      supplier: {
        type: Sequelize.DataTypes.STRING(255),
      },
      supplierId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'supplier',
          key: 'id'
        },
      },
      reason: {
        type: Sequelize.DataTypes.TEXT,
      },
      totalAmount: {
        type: Sequelize.DataTypes.DECIMAL,
        allowNull: false,
      },
      taxRate: {
        type: Sequelize.DataTypes.DECIMAL,
        allowNull: false,
      },
      invoiceType: {
        type: Sequelize.DataTypes.STRING(255),
      },
      paymentInfo: {
        type: Sequelize.DataTypes.JSON,
      },
      status: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "pending",
        allowNull: false,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
      },
      updatedBy: {
        type: Sequelize.DataTypes.UUID,
      },
      reimburserId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('subcontract', {
      id: {
        type: Sequelize.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      projectName: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      contractName: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      contractCode: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      contractType: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      contractStatus: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "draft",
        allowNull: false,
      },
      totalAmount: {
        type: Sequelize.DataTypes.DECIMAL,
        allowNull: false,
      },
      taxRate: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      invoiceDate: {
        type: Sequelize.DataTypes.DATEONLY,
      },
      paymentDate: {
        type: Sequelize.DataTypes.DATEONLY,
      },
      notes: {
        type: Sequelize.DataTypes.TEXT,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      updatedBy: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('attachment', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      documentId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'document',
          key: 'id'
        },
      },
      reimbursementId: {
        type: Sequelize.DataTypes.INTEGER,
        references: {
          model: 'reimbursement',
          key: 'id'
        },
      },
      subcontractId: {
        type: Sequelize.DataTypes.INTEGER,
        references: {
          model: 'subcontract',
          key: 'id'
        },
      },
      fileName: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      originalName: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      fileSize: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      mimeType: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      filePath: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      uploadedBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      isPublic: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('attendance', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      employeeName: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      employeeId: {
        type: Sequelize.DataTypes.STRING(255),
      },
      date: {
        type: Sequelize.DataTypes.DATEONLY,
        allowNull: false,
      },
      timeIn: {
        type: Sequelize.DataTypes.TIME,
        allowNull: false,
      },
      timeOut: {
        type: Sequelize.DataTypes.TIME,
      },
      hoursWorked: {
        type: Sequelize.DataTypes.DOUBLE,
      },
      status: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "present",
        allowNull: false,
      },
      location: {
        type: Sequelize.DataTypes.STRING(255),
      },
      latitude: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      longitude: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      checkInMethod: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "manual",
      },
      notes: {
        type: Sequelize.DataTypes.TEXT,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('client', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      code: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      type: {
        type: Sequelize.DataTypes.STRING(255),
      },
      status: {
        type: Sequelize.DataTypes.STRING(255),
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      contactName: {
        type: Sequelize.DataTypes.STRING(255),
      },
      contactTitle: {
        type: Sequelize.DataTypes.STRING(255),
      },
      phone: {
        type: Sequelize.DataTypes.STRING(255),
      },
      email: {
        type: Sequelize.DataTypes.STRING(255),
      },
      address: {
        type: Sequelize.DataTypes.TEXT,
      },
      taxId: {
        type: Sequelize.DataTypes.STRING(255),
      },
      bankAccount: {
        type: Sequelize.DataTypes.STRING(255),
      },
      bankName: {
        type: Sequelize.DataTypes.STRING(255),
      },
      bankCode: {
        type: Sequelize.DataTypes.STRING(255),
      },
      source: {
        type: Sequelize.DataTypes.STRING(255),
      },
      industry: {
        type: Sequelize.DataTypes.STRING(255),
      },
      notes: {
        type: Sequelize.DataTypes.TEXT,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('comment', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      documentId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'document',
          key: 'id'
        },
      },
      userId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      content: {
        type: Sequelize.DataTypes.TEXT,
        allowNull: false,
      },
      parentId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'comment',
          key: 'id'
        },
      },
      isActive: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: true,
      },
      isEdited: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('contract', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      contractNumber: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      contractType: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      ourParty: {
        type: Sequelize.DataTypes.STRING(255),
      },
      counterparty: {
        type: Sequelize.DataTypes.STRING(255),
      },
      status: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "draft",
      },
      amount: {
        type: Sequelize.DataTypes.DECIMAL,
        allowNull: false,
      },
      invoiceInfo: {
        type: Sequelize.DataTypes.TEXT,
      },
      startDate: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      endDate: {
        type: Sequelize.DataTypes.DATE,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('employee', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      idNumber: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      position: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      department: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      departmentId: {
        type: Sequelize.DataTypes.INTEGER,
      },
      status: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "active",
      },
      employeeType: {
        type: Sequelize.DataTypes.STRING,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('contractworker', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      employeeId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'employee',
          key: 'id'
        },
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      contractNumber: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      month: {
        type: Sequelize.DataTypes.STRING(255),
      },
      startDate: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      endDate: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      salary: {
        type: Sequelize.DataTypes.DECIMAL,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('customer', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      category: {
        type: Sequelize.DataTypes.STRING(255),
      },
      address: {
        type: Sequelize.DataTypes.TEXT,
      },
      phone: {
        type: Sequelize.DataTypes.STRING(255),
      },
      contactName: {
        type: Sequelize.DataTypes.STRING(255),
      },
      contactPhone: {
        type: Sequelize.DataTypes.STRING(255),
      },
      contactEmail: {
        type: Sequelize.DataTypes.STRING(255),
      },
      followUpStage: {
        type: Sequelize.DataTypes.STRING(255),
      },
      followUpContent: {
        type: Sequelize.DataTypes.TEXT,
      },
      tags: {
        type: Sequelize.DataTypes.TEXT,
      },
      level: {
        type: Sequelize.DataTypes.STRING(255),
      },
      notes: {
        type: Sequelize.DataTypes.TEXT,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('tag', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      color: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "#3498db",
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('documenttag', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      documentId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'document',
          key: 'id'
        },
      },
      tagId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'tag',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('temporaryworker', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      employeeId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'employee',
          key: 'id'
        },
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      month: {
        type: Sequelize.DataTypes.STRING(255),
      },
      startDate: {
        type: Sequelize.DataTypes.DATE,
      },
      endDate: {
        type: Sequelize.DataTypes.DATE,
      },
      dailyRate: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('contactinfo', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      phone: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      email: {
        type: Sequelize.DataTypes.STRING(255),
      },
      address: {
        type: Sequelize.DataTypes.STRING(255),
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      },
      employeeId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'employee',
          key: 'id'
        },
      }
    });

    await queryInterface.createTable('finance', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      supplierId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'supplier',
          key: 'id'
        },
      },
      type: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      category: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      amount: {
        type: Sequelize.DataTypes.DECIMAL,
        allowNull: false,
      },
      currency: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "USD",
        allowNull: false,
      },
      date: {
        type: Sequelize.DataTypes.DATE,
        defaultValue: null,
        allowNull: false,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      reference: {
        type: Sequelize.DataTypes.STRING(255),
      },
      paymentMethod: {
        type: Sequelize.DataTypes.STRING(255),
      },
      status: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "pending",
        allowNull: false,
      },
      attachments: {
        type: Sequelize.DataTypes.JSON,
        defaultValue: null,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      buyerInfo: {
        type: Sequelize.DataTypes.TEXT,
      },
      invoiceDate: {
        type: Sequelize.DataTypes.STRING(255),
      },
      invoiceTaxRate: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      invoiceType: {
        type: Sequelize.DataTypes.STRING(255),
      },
      incomePayments: {
        type: Sequelize.DataTypes.JSON,
        defaultValue: null,
      },
      sellerInfo: {
        type: Sequelize.DataTypes.TEXT,
      },
      inputInvoiceDate: {
        type: Sequelize.DataTypes.STRING(255),
      },
      inputInvoiceTaxRate: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      inputInvoiceType: {
        type: Sequelize.DataTypes.STRING(255),
      },
      subcontractPayment: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      purchasePayment: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      reimbursementPayment: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      netIncome: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('permission', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      category: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('procurement', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      content: {
        type: Sequelize.DataTypes.TEXT,
        allowNull: false,
      },
      supplierId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'supplier',
          key: 'id'
        },
      },
      quantity: {
        type: Sequelize.DataTypes.INTEGER,
        defaultValue: 1,
        allowNull: false,
      },
      amount: {
        type: Sequelize.DataTypes.DECIMAL,
        allowNull: false,
      },
      invoiceType: {
        type: Sequelize.DataTypes.STRING(255),
      },
      procurementDate: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      status: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "pending",
      },
      returnInfo: {
        type: Sequelize.DataTypes.TEXT,
      },
      returnBatch: {
        type: Sequelize.DataTypes.STRING(255),
      },
      returnInvoiceInfo: {
        type: Sequelize.DataTypes.TEXT,
      },
      paymentSchedule: {
        type: Sequelize.DataTypes.TEXT,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('product', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      code: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      category: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      price: {
        type: Sequelize.DataTypes.DECIMAL,
        defaultValue: 0,
        allowNull: false,
      },
      unit: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      stock: {
        type: Sequelize.DataTypes.INTEGER,
        defaultValue: 0,
        allowNull: false,
      },
      minStock: {
        type: Sequelize.DataTypes.INTEGER,
        defaultValue: 0,
      },
      status: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "active",
        allowNull: false,
      },
      tags: {
        type: Sequelize.DataTypes.JSON,
        defaultValue: null,
      },
      images: {
        type: Sequelize.DataTypes.JSON,
        defaultValue: null,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('producttransaction', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      productId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'product',
          key: 'id'
        },
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      type: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      quantity: {
        type: Sequelize.DataTypes.INTEGER,
        allowNull: false,
      },
      unitPrice: {
        type: Sequelize.DataTypes.DECIMAL,
        allowNull: false,
      },
      totalPrice: {
        type: Sequelize.DataTypes.DECIMAL,
        allowNull: false,
      },
      reference: {
        type: Sequelize.DataTypes.STRING(255),
      },
      notes: {
        type: Sequelize.DataTypes.TEXT,
      },
      transactionDate: {
        type: Sequelize.DataTypes.DATE,
        defaultValue: null,
        allowNull: false,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('projectfollowup', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      followUpDate: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      content: {
        type: Sequelize.DataTypes.TEXT,
        allowNull: false,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'employee',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('projectprogress', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      stage: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      completionPercentage: {
        type: Sequelize.DataTypes.DOUBLE,
        defaultValue: 0,
        allowNull: false,
      },
      currentStatus: {
        type: Sequelize.DataTypes.TEXT,
      },
      deliveryDate: {
        type: Sequelize.DataTypes.DATE,
      },
      delayStatus: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
      delayReason: {
        type: Sequelize.DataTypes.TEXT,
      },
      updatedBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('projecttask', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      taskType: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      status: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "not_started",
      },
      priority: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "medium",
      },
      assigneeId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'employee',
          key: 'id'
        },
      },
      plannedStartDate: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      plannedEndDate: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      actualStartDate: {
        type: Sequelize.DataTypes.DATE,
      },
      actualEndDate: {
        type: Sequelize.DataTypes.DATE,
      },
      remainingWork: {
        type: Sequelize.DataTypes.DOUBLE,
        defaultValue: 0,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'employee',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('purchase', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      supplierId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'supplier',
          key: 'id'
        },
      },
      purchaseNumber: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      purchaseDate: {
        type: Sequelize.DataTypes.DATE,
        defaultValue: null,
        allowNull: false,
      },
      status: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "draft",
        allowNull: false,
      },
      totalAmount: {
        type: Sequelize.DataTypes.DECIMAL,
        defaultValue: 0,
        allowNull: false,
      },
      currency: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "CNY",
        allowNull: false,
      },
      paymentStatus: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "unpaid",
        allowNull: false,
      },
      paymentDueDate: {
        type: Sequelize.DataTypes.DATE,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      notes: {
        type: Sequelize.DataTypes.TEXT,
      },
      attachments: {
        type: Sequelize.DataTypes.JSON,
        defaultValue: null,
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('role', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      name: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      isActive: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: true,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('task', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      title: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: Sequelize.DataTypes.TEXT,
      },
      status: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "pending",
        allowNull: false,
      },
      priority: {
        type: Sequelize.DataTypes.STRING,
        defaultValue: "medium",
        allowNull: false,
      },
      dueDate: {
        type: Sequelize.DataTypes.DATE,
      },
      assignedTo: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      projectId: {
        type: Sequelize.DataTypes.UUID,
        references: {
          model: 'project',
          key: 'id'
        },
      },
      createdBy: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('token', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      token: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      type: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      expires: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      blacklisted: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
      userId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('usermessage', {
      id: {
        type: Sequelize.DataTypes.UUID,
        defaultValue: null,
        primaryKey: true,
      },
      userId: {
        type: Sequelize.DataTypes.UUID,
        allowNull: false,
        references: {
          model: 'user',
          key: 'id'
        },
      },
      title: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      content: {
        type: Sequelize.DataTypes.TEXT,
        allowNull: false,
      },
      isRead: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
      type: {
        type: Sequelize.DataTypes.STRING(255),
        defaultValue: "system",
        allowNull: false,
      },
      sourceId: {
        type: Sequelize.DataTypes.UUID,
      },
      sourceType: {
        type: Sequelize.DataTypes.STRING(255),
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

    await queryInterface.createTable('workhour', {
      id: {
        type: Sequelize.DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      employeeId: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      employeeName: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      department: {
        type: Sequelize.DataTypes.STRING(255),
      },
      date: {
        type: Sequelize.DataTypes.DATEONLY,
        allowNull: false,
      },
      startTime: {
        type: Sequelize.DataTypes.TIME,
        allowNull: false,
      },
      endTime: {
        type: Sequelize.DataTypes.TIME,
        allowNull: false,
      },
      workHours: {
        type: Sequelize.DataTypes.DECIMAL,
        allowNull: false,
      },
      projectName: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      remarks: {
        type: Sequelize.DataTypes.TEXT,
      },
      employmentType: {
        type: Sequelize.DataTypes.STRING(255),
        allowNull: false,
      },
      hourlyRate: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      payableAmount: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      overtime: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
      absence: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
      employeeSign: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
      supervisorSign: {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
      },
      performanceLevel: {
        type: Sequelize.DataTypes.STRING(255),
      },
      dailyWage: {
        type: Sequelize.DataTypes.DECIMAL,
      },
      createdBy: {
        type: Sequelize.DataTypes.INTEGER,
      },
      updatedBy: {
        type: Sequelize.DataTypes.INTEGER,
      },
      createdAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DataTypes.DATE,
        allowNull: false,
      },
      deletedAt: {
        type: Sequelize.DataTypes.DATE,
      }
    });

  },

  // Revert the migration
  async down(queryInterface, Sequelize) {
    // Drop created tables
    await queryInterface.dropTable('workhour');
    await queryInterface.dropTable('usermessage');
    await queryInterface.dropTable('token');
    await queryInterface.dropTable('task');
    await queryInterface.dropTable('role');
    await queryInterface.dropTable('purchase');
    await queryInterface.dropTable('projecttask');
    await queryInterface.dropTable('projectprogress');
    await queryInterface.dropTable('projectfollowup');
    await queryInterface.dropTable('producttransaction');
    await queryInterface.dropTable('product');
    await queryInterface.dropTable('procurement');
    await queryInterface.dropTable('permission');
    await queryInterface.dropTable('finance');
    await queryInterface.dropTable('contactinfo');
    await queryInterface.dropTable('temporaryworker');
    await queryInterface.dropTable('documenttag');
    await queryInterface.dropTable('tag');
    await queryInterface.dropTable('customer');
    await queryInterface.dropTable('contractworker');
    await queryInterface.dropTable('employee');
    await queryInterface.dropTable('contract');
    await queryInterface.dropTable('comment');
    await queryInterface.dropTable('client');
    await queryInterface.dropTable('attendance');
    await queryInterface.dropTable('attachment');
    await queryInterface.dropTable('subcontract');
    await queryInterface.dropTable('reimbursement');
    await queryInterface.dropTable('supplier');
    await queryInterface.dropTable('project');
    await queryInterface.dropTable('document');
    await queryInterface.dropTable('user');
    await queryInterface.dropTable('department');
    await queryInterface.dropTable('category');
  }
};
