---
description: 
globs: 
alwaysApply: true
---
# Naming Conventions

## File and Directory Names
- Use kebab-case for file and directory names (e.g., `check-data-source.js`, `start-pnpm.bat`)
- Configuration files should use dot prefix (e.g., `.gitignore`, `.vscode`)
- Test files should end with `.test.js` or `.spec.js`

## Source Code Conventions

### JavaScript/TypeScript
- Use PascalCase for class and component names
  ```javascript
  class UserManager {}
  function DataProvider() {}
  ```
- Use camelCase for variables, functions, and methods
  ```javascript
  const userData = {};
  function getData() {}
  ```
- Use UPPER_SNAKE_CASE for constants
  ```javascript
  const API_BASE_URL = 'https://api.example.com';
  ```

### CSS/SCSS
- Use kebab-case for class names and IDs
  ```css
  .header-container {}
  #main-content {}
  ```
- Use BEM naming convention for components
  ```css
  .block {}
  .block__element {}
  .block--modifier {}
  ```

## Directory Structure
- Source code directories should be descriptive and singular
  - `component/` not `components/`
  - `util/` not `utilities/`
- Test directories should match their source counterparts
  - `src/component/Button.js` → `src/component/__tests__/Button.test.js`

## Documentation
- Documentation files should use Title Case with `.md` extension
  - `Getting-Started.md`
  - `API-Reference.md`
- Internal documentation should be prefixed with underscore
  - `_Internal-API.md`
  - `_Development-Guide.md`

## Branch Naming
- Feature branches: `feature/description`
- Bug fixes: `fix/issue-description`
- Releases: `release/version-number`
- Hotfixes: `hotfix/issue-description`

## Script Files
- Batch files should be descriptive and use kebab-case
  - `start-pnpm.bat`
  - `check-data-source.js`
- Include the technology in the name if specific
  - `start-pnpm.bat` vs `start.bat`