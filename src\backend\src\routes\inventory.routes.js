const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const productController = require('../controllers/product.controller');
const productTransactionController = require('../controllers/productTransaction.controller');
const productValidation = require('../validations/product.validation');
const productTransactionValidation = require('../validations/productTransaction.validation');

const router = express.Router();

// Inventory Overview
router.route('/overview')
  .get(
    auth('getProducts'),
    productController.getInventoryOverview
  );

// Stock Alerts
router.route('/alerts')
  .get(
    auth('getProducts'),
    productController.getStockAlerts
  );

// Stock Level Report
router.route('/report')
  .get(
    auth('getProducts'),
    productController.getStockLevelReport
  );

// 入库操作
router.route('/stock-in')
  .post(
    auth('manageProducts'),
    validate(productTransactionValidation.createStockInTransaction),
    productTransactionController.createStockInTransaction
  )
  .get(
    auth('getProducts'),
    validate(productTransactionValidation.getProductTransactions),
    productTransactionController.getStockInTransactions
  );

// 出库操作
router.route('/stock-out')
  .post(
    auth('manageProducts'),
    validate(productTransactionValidation.createStockOutTransaction),
    productTransactionController.createStockOutTransaction
  )
  .get(
    auth('getProducts'),
    validate(productTransactionValidation.getProductTransactions),
    productTransactionController.getStockOutTransactions
  );

// Stock Adjustments
router.route('/adjustments')
  .post(
    auth('manageProducts'),
    validate(productTransactionValidation.createProductTransaction),
    productTransactionController.createStockAdjustment
  )
  .get(
    auth('getProducts'),
    validate(productTransactionValidation.getProductTransactions),
    productTransactionController.getStockAdjustments
  );

// Batch Stock Update
router.route('/batch-update')
  .post(
    auth('manageProducts'),
    validate(productValidation.batchUpdateStock),
    productController.batchUpdateStock
  );

// Stock Movement History
router.route('/movements')
  .get(
    auth('getProducts'),
    validate(productTransactionValidation.getProductTransactions),
    productTransactionController.getStockMovements
  );

// 所有库存记录
router.route('/transactions')
  .get(
    auth('getProducts'),
    validate(productTransactionValidation.getProductTransactions),
    productTransactionController.getProductTransactions
  );

// Base inventory route
router.route('/')
  .get(
    auth('getProducts'),
    validate(productValidation.getProducts),
    productController.getProducts
  )
  .post(
    auth('manageProducts'),
    validate(productValidation.createProduct),
    productController.createProduct
  );

// Single inventory item routes - IMPORTANT: This must be last to avoid route conflicts
router.route('/:productId')
  .get(
    auth('getProducts'),
    validate(productValidation.getProduct),
    productController.getProduct
  )
  .patch(
    auth('manageProducts'),
    validate(productValidation.updateProduct),
    productController.updateProduct
  )
  .delete(
    auth('manageProducts'),
    validate(productValidation.deleteProduct),
    productController.deleteProduct
  );

module.exports = router;