<template>
  <div class="login-page min-h-screen flex items-center justify-center relative overflow-hidden">
    <!-- 背景图案 -->
    <div class="absolute inset-0 bg-pattern opacity-50 dark:opacity-10"></div>

    <!-- 装饰元素 -->
    <div class="absolute -top-20 -right-20 md:top-0 md:right-0 opacity-20 dark:opacity-5">
      <img src="@/assets/login-decoration.svg" alt="" class="w-96 h-96" />
    </div>
    <div class="absolute -bottom-20 -left-20 md:bottom-0 md:left-0 opacity-20 dark:opacity-5">
      <img src="@/assets/login-decoration.svg" alt="" class="w-96 h-96" />
    </div>

    <!-- 登录卡片 -->
    <div class="login-card max-w-md w-full mx-4 bg-white dark:bg-gray-800 rounded-xl shadow-xl z-10 overflow-hidden transition-all duration-300 transform hover:scale-[1.01]">
      <!-- 卡片头部 -->
      <div class="card-header px-8 pt-8 pb-4 text-center">
        <div class="flex justify-center mb-4">
          <img src="@/assets/logo.svg" alt="Logo" class="h-16 w-16" />
        </div>
        <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
          欢迎登录系统
        </h2>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          请使用您的账号和密码登录
        </p>
      </div>

      <!-- 成功提示 -->
      <div v-if="$route.query.registered === 'success'"
           class="mx-8 mb-4 bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-700 text-green-700 dark:text-green-400 px-4 py-3 rounded-lg relative animate-fadeIn">
        <div class="flex">
          <svg class="h-5 w-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          <span>账号创建成功！请使用您的凭据登录。</span>
        </div>
      </div>

      <!-- 登录表单 -->
      <form class="px-8 pt-4 pb-8 space-y-5" @submit.prevent="handleSubmit">
        <!-- 错误提示 -->
        <div v-if="error" class="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg relative animate-fadeIn">
          <div class="flex">
            <svg class="h-5 w-5 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span>{{ error }}</span>
          </div>
        </div>

        <!-- 邮箱输入框 -->
        <div class="form-group">
          <label for="email-address" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            邮箱地址 <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none icon-container">
              <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
              </svg>
            </div>
            <input id="email-address"
                   name="email"
                   type="email"
                   autocomplete="email"
                   required
                   v-model="email"
                   :disabled="loading"
                   class="form-input pl-10 w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 focus:pl-10"
                   placeholder="请输入邮箱地址">
          </div>
        </div>

        <!-- 密码输入框 -->
        <div class="form-group">
          <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            密码 <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none icon-container">
              <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd" />
              </svg>
            </div>
            <input id="password"
                   name="password"
                   type="password"
                   autocomplete="current-password"
                   required
                   v-model="password"
                   :disabled="loading"
                   class="form-input pl-10 w-full rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 focus:pl-10"
                   placeholder="请输入密码">
          </div>
        </div>

        <!-- 记住我和忘记密码 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input id="remember-me"
                   name="remember-me"
                   type="checkbox"
                   v-model="rememberMe"
                   :disabled="loading"
                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded disabled:cursor-not-allowed">
            <label for="remember-me" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              记住我
            </label>
          </div>

          <div class="text-sm">
            <router-link to="/forgot-password" class="font-medium text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300 flex items-center transition-colors duration-200">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
              </svg>
              忘记密码？
            </router-link>
          </div>
        </div>

        <!-- 演示账号信息 -->
        <div class="text-sm text-center text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-700/30 p-3 rounded-lg">
          <p>演示账号：邮箱: <strong><EMAIL></strong> 密码: <strong>password123</strong></p>
          <button type="button" @click="fillDemoAccount" class="mt-2 text-primary-600 hover:text-primary-500 dark:text-primary-400 dark:hover:text-primary-300 font-medium text-sm transition-colors duration-200">点击使用演示账号</button>
        </div>

        <!-- 登录按钮 -->
        <div>
          <button type="submit"
                  :disabled="loading"
                  class="login-btn relative w-full flex justify-center py-2.5 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-md hover:shadow-lg">
            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
              <svg v-if="!loading"
                   class="h-5 w-5 text-primary-500 group-hover:text-primary-400"
                   xmlns="http://www.w3.org/2000/svg"
                   viewBox="0 0 20 20"
                   fill="currentColor"
                   aria-hidden="true">
                <path fill-rule="evenodd"
                      d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                      clip-rule="evenodd" />
              </svg>
              <svg v-else
                   class="animate-spin h-5 w-5 text-white"
                   xmlns="http://www.w3.org/2000/svg"
                   fill="none"
                   viewBox="0 0 24 24">
                <circle class="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        stroke-width="4"></circle>
                <path class="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"></path>
              </svg>
            </span>
            {{ loading ? '登录中...' : '登录' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useThemeStore } from '../stores/theme'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const themeStore = useThemeStore()

const email = ref('')
const password = ref('')
const rememberMe = ref(false)
const loading = ref(false)
const error = ref('')

onMounted(() => {
  // 如果用户已登录，直接跳转到仪表盘
  if (authStore.isAuthenticated) {
    router.push('/dashboard')
  }

  // 如果有重定向参数，显示相应的错误信息
  if (route.query.session_expired) {
    error.value = '会话已过期，请重新登录'
  }

  // 自动聚焦到邮箱输入框
  document.getElementById('email-address')?.focus()
})

const handleSubmit = async () => {
  if (loading.value) return

  try {
    error.value = ''
    loading.value = true

    await authStore.login(email.value, password.value)

    // 获取重定向地址，如果没有则默认到仪表盘
    const redirectPath = route.query.redirect || '/dashboard'
    router.push(redirectPath)
  } catch (err) {
    console.error('Login error:', err)
    error.value = '登录失败，请检查您的账号和密码'
  } finally {
    loading.value = false
  }
}

const fillDemoAccount = () => {
  email.value = '<EMAIL>'
  password.value = 'password123'
}
</script>

<style scoped>
/* 背景图案 */
.bg-pattern {
  background-image: url('@/assets/login-bg-pattern.svg');
  background-repeat: repeat;
  background-size: 100px 100px;
}

.login-page {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  min-height: 100vh;
}

.login-card {
  position: relative;
  z-index: 10;
}

/* 动画效果 */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 登录按钮特效 */
.login-btn {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.login-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.login-btn:hover::after {
  width: 250%;
  height: 500%;
}

/* 输入框焦点效果 */
.form-group input:focus {
  box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb, 79, 70, 229), 0.2);
  transform: translateY(-1px);
}

/* 保证输入框图标在焦点时仍然可见 */
.icon-container {
  z-index: 10;
  display: flex !important;
  pointer-events: none;
}

/* 卡片头部样式 */
.card-header {
  position: relative;
  overflow: hidden;
}

.card-header::before {
  content: '';
  position: absolute;
  top: -50px;
  left: -50px;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(var(--primary-color-rgb, 79, 70, 229), 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.card-header::after {
  content: '';
  position: absolute;
  bottom: -50px;
  right: -50px;
  width: 100px;
  height: 100px;
  background: radial-gradient(circle, rgba(var(--primary-color-rgb, 79, 70, 229), 0.1) 0%, transparent 70%);
  border-radius: 50%;
}
</style>