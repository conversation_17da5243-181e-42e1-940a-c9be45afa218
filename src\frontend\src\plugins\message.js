import { useMessageStore } from '../stores/message';

const MessagePlugin = {
  install: (app) => {
    // 创建一个全局访问方法
    app.config.globalProperties.$message = {
      success(title, message, options = {}) {
        const messageStore = useMessageStore();
        return messageStore.success(title, message, options);
      },
      error(title, message, options = {}) {
        const messageStore = useMessageStore();
        return messageStore.error(title, message, options);
      },
      warning(title, message, options = {}) {
        const messageStore = useMessageStore();
        return messageStore.warning(title, message, options);
      },
      info(title, message, options = {}) {
        const messageStore = useMessageStore();
        return messageStore.info(title, message, options);
      }
    };

    // 注册一个 message 组合式 API，用于在 setup 中使用
    app.provide('message', {
      success(title, message, options = {}) {
        const messageStore = useMessageStore();
        return messageStore.success(title, message, options);
      },
      error(title, message, options = {}) {
        const messageStore = useMessageStore();
        return messageStore.error(title, message, options);
      },
      warning(title, message, options = {}) {
        const messageStore = useMessageStore();
        return messageStore.warning(title, message, options);
      },
      info(title, message, options = {}) {
        const messageStore = useMessageStore();
        return messageStore.info(title, message, options);
      }
    });
  }
};

export default MessagePlugin;

// 组合式 API 使用方法
export function useMessage() {
  const messageStore = useMessageStore();
  
  return {
    success(title, message, options = {}) {
      return messageStore.success(title, message, options);
    },
    error(title, message, options = {}) {
      return messageStore.error(title, message, options);
    },
    warning(title, message, options = {}) {
      return messageStore.warning(title, message, options);
    },
    info(title, message, options = {}) {
      return messageStore.info(title, message, options);
    }
  };
} 