<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="企业管理系统 - 高效的业务管理解决方案" />
    <title id="app-title">企业管理系统</title>
    <style>
      .app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f9fafb;
        z-index: 9999;
      }
      .app-loading.hidden {
        display: none;
      }
      .app-loading-spinner {
        width: 48px;
        height: 48px;
        border: 4px solid #e5e7eb;
        border-top-color: #4f46e5;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        to {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div id="app">
      <div class="app-loading">
        <div class="app-loading-spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
    <script>
      // 在 Vue 应用挂载后隐藏加载动画
      window.addEventListener('load', function() {
        const appLoading = document.querySelector('.app-loading');
        if (appLoading) {
          setTimeout(() => {
            appLoading.classList.add('hidden');
          }, 500); // 给一个短暂的延迟，确保 Vue 应用已经渲染
        }
      });
    </script>
  </body>
</html>
