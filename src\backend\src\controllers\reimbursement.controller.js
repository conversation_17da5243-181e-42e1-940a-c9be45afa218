const httpStatus = require('http-status');
const { Reimbursement, Attachment } = require('../models');
const reimbursementService = require('../services/reimbursement.service');
const { Op } = require('sequelize');
const fs = require('fs');
const path = require('path');
const catchAsync = require('../utils/catchAsync');

// 获取所有报销记录（带分页和筛选）
exports.getReimbursements = catchAsync(async (req, res) => {
  const filter = {
    projectName: req.query.projectName,
    reimbursementType: req.query.reimbursementType,
    supplier: req.query.supplier,
    startDate: req.query.startDate,
    endDate: req.query.endDate
  };

  const options = {
    page: parseInt(req.query.page) || 1,
    limit: parseInt(req.query.limit) || 10,
    sortBy: req.query.sortBy || 'createdAt',
    sortOrder: req.query.sortOrder || 'desc'
  };

  const result = await reimbursementService.queryReimbursements(filter, options);
  res.json(result);
});

// 获取单个报销记录
exports.getReimbursement = catchAsync(async (req, res) => {
  const reimbursement = await reimbursementService.getReimbursementById(req.params.id);
  res.json(reimbursement);
});

// 创建新的报销记录
exports.createReimbursement = catchAsync(async (req, res) => {
  const reimbursementData = req.body;

  // 处理文件上传（如果有）
  const attachments = [];
  if (req.files && req.files.length > 0) {
    for (const file of req.files) {
      const attachment = {
        fileName: file.originalname,
        originalName: file.originalname,
        mimeType: file.mimetype,
        filePath: file.path,
        fileSize: file.size,
        uploadedBy: req.user ? req.user.id : null
      };
      attachments.push(attachment);
    }
  }

  // 处理 existingAttachments 参数（如果有）
  if (reimbursementData.existingAttachments) {
    try {
      // 尝试解析 JSON 字符串
      if (typeof reimbursementData.existingAttachments === 'string') {
        try {
          reimbursementData.existingAttachments = JSON.parse(reimbursementData.existingAttachments);
        } catch (parseError) {
          console.error('Error parsing existingAttachments as JSON:', parseError);
          // 如果不是有效的 JSON，检查是否是逗号分隔的字符串
          if (reimbursementData.existingAttachments.includes(',')) {
            reimbursementData.existingAttachments = reimbursementData.existingAttachments.split(',');
          } else {
            // 作为单个值处理
            reimbursementData.existingAttachments = [reimbursementData.existingAttachments];
          }
        }
      } else if (!Array.isArray(reimbursementData.existingAttachments)) {
        // 如果不是字符串也不是数组，则转换为数组
        reimbursementData.existingAttachments = [reimbursementData.existingAttachments];
      }
    } catch (error) {
      console.error('Error handling existingAttachments:', error);
      // 如果处理失败，确保它是一个数组
      reimbursementData.existingAttachments = Array.isArray(reimbursementData.existingAttachments)
        ? reimbursementData.existingAttachments
        : [reimbursementData.existingAttachments];
    }
  }

  const reimbursement = await reimbursementService.createReimbursement(
    reimbursementData,
    attachments,
    req.user
  );

  res.status(201).json(reimbursement);
});

// 更新报销记录
exports.updateReimbursement = catchAsync(async (req, res) => {
  const { id } = req.params;
  const reimbursementData = req.body;

  // 处理文件上传（如果有）
  const newAttachments = [];
  if (req.files && req.files.length > 0) {
    for (const file of req.files) {
      const attachment = {
        fileName: file.originalname,
        originalName: file.originalname,
        mimeType: file.mimetype,
        filePath: file.path,
        fileSize: file.size,
        uploadedBy: req.user ? req.user.id : null
      };
      newAttachments.push(attachment);
    }
  }

  // 获取现有附件ID列表
  let existingAttachmentIds = [];

  if (reimbursementData.existingAttachments) {
    try {
      // 尝试解析 JSON 字符串
      if (typeof reimbursementData.existingAttachments === 'string') {
        try {
          existingAttachmentIds = JSON.parse(reimbursementData.existingAttachments);
        } catch (parseError) {
          console.error('Error parsing existingAttachments as JSON:', parseError);
          // 如果不是有效的 JSON，检查是否是逗号分隔的字符串
          if (reimbursementData.existingAttachments.includes(',')) {
            existingAttachmentIds = reimbursementData.existingAttachments.split(',');
          } else {
            // 作为单个值处理
            existingAttachmentIds = [reimbursementData.existingAttachments];
          }
        }
      } else if (Array.isArray(reimbursementData.existingAttachments)) {
        existingAttachmentIds = reimbursementData.existingAttachments;
      } else {
        // 如果不是字符串也不是数组，则转换为数组
        existingAttachmentIds = [reimbursementData.existingAttachments];
      }
    } catch (error) {
      console.error('Error handling existingAttachments:', error);
      // 如果处理失败，确保它是一个数组
      existingAttachmentIds = Array.isArray(reimbursementData.existingAttachments)
        ? reimbursementData.existingAttachments
        : [reimbursementData.existingAttachments];
    }
  }

  delete reimbursementData.existingAttachments;

  const updatedReimbursement = await reimbursementService.updateReimbursementById(
    id,
    reimbursementData,
    newAttachments,
    existingAttachmentIds,
    req.user
  );

  res.json(updatedReimbursement);
});

// 删除报销记录
exports.deleteReimbursement = catchAsync(async (req, res) => {
  await reimbursementService.deleteReimbursementById(req.params.id);
  // 使用明确的数字状态码而不是httpStatus常量，避免可能的undefined问题
  res.status(204).send();
});

// 获取项目下拉选项
exports.getProjectOptions = catchAsync(async (req, res) => {
  const projectOptions = await reimbursementService.getProjectsForDropdown();
  res.json(projectOptions);
});

// 获取供应商下拉选项
exports.getSupplierOptions = catchAsync(async (req, res) => {
  const supplierOptions = await reimbursementService.getSuppliersForDropdown();
  res.json(supplierOptions);
});

// 获取用户下拉选项
exports.getUserOptions = catchAsync(async (req, res) => {
  const userOptions = await reimbursementService.getUsersForDropdown();
  res.json(userOptions);
});
