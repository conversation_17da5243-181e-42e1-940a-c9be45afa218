<template>
  <div class="max-w-7xl mx-auto px-4 py-6">
    <!-- 页面标题 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">库存价值报表</h1>
        <p class="page-subtitle">查看详细的库存价值分析和统计</p>
      </div>
      <div class="flex space-x-3">
        <button 
          @click="refreshData" 
          :disabled="loading"
          class="btn btn-secondary flex items-center"
        >
          <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          {{ loading ? '刷新中...' : '刷新数据' }}
        </button>
        <router-link to="/inventory" class="btn btn-primary flex items-center">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
          </svg>
          返回库存列表
        </router-link>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-800">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
      <!-- 总价值 -->
      <div class="bg-white overflow-hidden shadow-lg rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-blue-100 p-3 rounded-lg">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">库存总价值</dt>
                <dd class="text-2xl font-semibold text-gray-900">¥{{ formatCurrency(stats.totalValue) }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- 产品数量 -->
      <div class="bg-white overflow-hidden shadow-lg rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-green-100 p-3 rounded-lg">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">产品种类</dt>
                <dd class="text-2xl font-semibold text-gray-900">{{ stats.totalProducts }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- 库存数量 -->
      <div class="bg-white overflow-hidden shadow-lg rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-yellow-100 p-3 rounded-lg">
              <svg class="w-8 h-8 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">总库存数量</dt>
                <dd class="text-2xl font-semibold text-gray-900">{{ stats.totalStock }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- 低库存产品 -->
      <div class="bg-white overflow-hidden shadow-lg rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-red-100 p-3 rounded-lg">
              <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">库存不足</dt>
                <dd class="text-2xl font-semibold text-gray-900">{{ stats.lowStockProducts }}</dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细报表 -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
      <div class="px-4 py-5 sm:px-6 border-b border-gray-200">
        <h3 class="text-lg leading-6 font-medium text-gray-900">详细库存价值报表</h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">按产品分类的库存价值详情</p>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="px-4 py-12">
        <div class="flex justify-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
        <p class="text-center text-gray-500 mt-4">正在加载数据...</p>
      </div>

      <!-- 数据表格 -->
      <div v-else-if="products.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品信息</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单价</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">库存数量</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">库存价值</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">占比</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="product in products" :key="product.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                  <div class="text-sm text-gray-500">{{ product.code }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ¥{{ formatCurrency(product.price) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ product.stock }} {{ product.unit }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                ¥{{ formatCurrency(product.totalValue) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusClass(product)">
                  {{ getStatusText(product) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ getValuePercentage(product.totalValue) }}%
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-else class="px-4 py-12 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无库存数据</h3>
        <p class="mt-1 text-sm text-gray-500">开始添加产品到库存中</p>
        <div class="mt-6">
          <router-link to="/inventory/create" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
            添加产品
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import apiService from '@/services/apiService'

// 响应式数据
const loading = ref(false)
const error = ref(null)
const products = ref([])

// 统计数据
const stats = computed(() => {
  const totalValue = products.value.reduce((sum, p) => sum + p.totalValue, 0)
  const totalProducts = products.value.length
  const totalStock = products.value.reduce((sum, p) => sum + p.stock, 0)
  const lowStockProducts = products.value.filter(p => p.stock <= (p.minStock || 0)).length

  return {
    totalValue,
    totalProducts,
    totalStock,
    lowStockProducts
  }
})

// 获取库存数据
const fetchInventoryData = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await apiService.getProducts({ limit: 1000 })
    const rawProducts = response.results || response || []
    
    // 处理产品数据，计算总价值
    products.value = rawProducts.map(product => ({
      ...product,
      totalValue: (product.price || 0) * (product.stock || 0)
    })).sort((a, b) => b.totalValue - a.totalValue) // 按价值降序排列
    
  } catch (err) {
    console.error('获取库存数据失败:', err)
    error.value = '获取库存数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = () => {
  fetchInventoryData()
}

// 格式化货币
const formatCurrency = (value) => {
  if (!value) return '0.00'
  return Number(value).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 获取状态样式
const getStatusClass = (product) => {
  if (product.stock <= 0) {
    return 'bg-red-100 text-red-800'
  } else if (product.stock <= (product.minStock || 0)) {
    return 'bg-yellow-100 text-yellow-800'
  } else {
    return 'bg-green-100 text-green-800'
  }
}

// 获取状态文本
const getStatusText = (product) => {
  if (product.stock <= 0) {
    return '缺货'
  } else if (product.stock <= (product.minStock || 0)) {
    return '库存不足'
  } else {
    return '库存充足'
  }
}

// 计算价值占比
const getValuePercentage = (value) => {
  if (stats.value.totalValue === 0) return '0.00'
  return ((value / stats.value.totalValue) * 100).toFixed(2)
}

// 组件挂载时获取数据
onMounted(() => {
  fetchInventoryData()
})
</script>
