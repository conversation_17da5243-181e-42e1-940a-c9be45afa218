<template>
  <div class="flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
    <div class="card w-full max-w-md">
      <div v-if="loading" class="text-center py-8">
        <div class="inline-flex items-center justify-center">
          <svg class="animate-spin h-12 w-12 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <p class="mt-4 text-gray-600">Verifying your email address...</p>
      </div>

      <div v-else-if="verificationSuccess" class="text-center py-8">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
          <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
        <h3 class="text-xl font-medium text-gray-900">Email Verified Successfully</h3>
        <p class="mt-2 text-gray-600">
          Your email address has been verified. You can now log in to your account.
        </p>
        <div class="mt-6">
          <router-link to="/login" class="btn btn-primary">
            Go to Login
          </router-link>
        </div>
      </div>

      <div v-else class="text-center py-8">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-red-100 mb-4">
          <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </div>
        <h3 class="text-xl font-medium text-gray-900">Verification Failed</h3>
        <p class="mt-2 text-gray-600">
          {{ error }}
        </p>
        <div class="mt-6">
          <router-link to="/login" class="btn btn-secondary mr-2">
            Go to Login
          </router-link>
          <router-link to="/register" class="btn btn-primary">
            Register
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useAuthStore } from '../stores/auth';

// Component state
const loading = ref(true);
const verificationSuccess = ref(false);
const error = ref('This verification link is invalid or has expired.');

// Router and store
const route = useRoute();
const authStore = useAuthStore();

onMounted(async () => {
  // Get the token from query parameters
  const token = route.query.token;

  // Check if token exists
  if (!token) {
    loading.value = false;
    error.value = 'Missing verification token. Please check your email link.';
    return;
  }

  try {
    // Send request to verify email
    await authStore.verifyEmail(token);
    verificationSuccess.value = true;
  } catch (err) {
    error.value = err.response?.data?.message || 'Failed to verify email. This link may have expired.';
  } finally {
    loading.value = false;
  }
});
</script>