const { Strategy: JwtStrategy, ExtractJwt } = require('passport-jwt');
const config = require('./config');
const { User } = require('../models');
const logger = require('./logger');

// Log JWT secret for debugging (only first few characters for security)
logger.info(`JWT Secret configured: ${config.jwt.secret.substring(0, 5)}...`);

const jwtOptions = {
  secretOrKey: config.jwt.secret,
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
};

const jwtVerify = async (payload, done) => {
  try {
    if (!payload || !payload.sub) {
      return done(null, false);
    }

    const user = await User.findByPk(payload.sub);
    if (!user) {
      return done(null, false);
    }

    // Ensure user has a role property
    if (!user.role) {
      user.role = 'user'; // Default role
      await user.save();
    }

    done(null, user);
  } catch (error) {
    logger.error('JWT Verify Error:', {
      errorName: error.name,
      errorMessage: error.message,
      stack: error.stack
    });
    done(error, false);
  }
};

const jwtStrategy = new JwtStrategy(jwtOptions, jwtVerify);

module.exports = {
  jwtStrategy,
};
