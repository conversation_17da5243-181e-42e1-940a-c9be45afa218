#!/bin/bash

echo "Starting Backend..."

# 设置控制台标题
echo -en "\033]0;Backend Starter\007"

# 定义颜色
FRONTEND_COLOR="\033[32m"
BACKEND_COLOR="\033[36m"
RESET_COLOR="\033[0m"

# 检查是否有 status 参数
if [ "$1" = "status" ]; then
    echo -e "${BACKEND_COLOR}Checking Backend health...${RESET_COLOR}"
    # 这里添加检查后端健康状态的命令，例如：
    # curl http://localhost:3000/health
    # 如果上面的命令返回成功，则显示健康状态
    echo -e "${BACKEND_COLOR}Backend is healthy${RESET_COLOR}"
    exit 0
fi

# 获取当前目录路径
CURRENT_DIR=$(pwd)

# 确定要运行的命令
if [[ "$CURRENT_DIR" == *"-windsurf"* ]]; then
    NPM_COMMAND="npm run dev:windsurf"
    echo -e "${BACKEND_COLOR}Detected -windsurf in path, using: $NPM_COMMAND${RESET_COLOR}"
elif [[ "$CURRENT_DIR" == *"-cursor"* ]]; then
    NPM_COMMAND="npm run dev:cursor"
    echo -e "${BACKEND_COLOR}Detected -cursor in path, using: $NPM_COMMAND${RESET_COLOR}"
elif [[ "$CURRENT_DIR" == *"-vscode"* ]]; then
    NPM_COMMAND="npm run dev:vscode"
    echo -e "${BACKEND_COLOR}Detected -vscode in path, using: $NPM_COMMAND${RESET_COLOR}"
elif [[ "$CURRENT_DIR" == *"-insider"* ]]; then
    NPM_COMMAND="npm run dev:insider"
    echo -e "${BACKEND_COLOR}Detected -insider in path, using: $NPM_COMMAND${RESET_COLOR}"
elif [[ "$CURRENT_DIR" == *"-trae"* ]]; then
    NPM_COMMAND="npm run dev:trae"
    echo -e "${BACKEND_COLOR}Detected -trae in path, using: $NPM_COMMAND${RESET_COLOR}"
else
    NPM_COMMAND="npm run dev"
    echo -e "${BACKEND_COLOR}No special path detected, using default: $NPM_COMMAND${RESET_COLOR}"
fi

# 启动后端
echo -e "${BACKEND_COLOR}Starting Backend...${RESET_COLOR}"
cd src/backend && echo '=== BACKEND ===' && $NPM_COMMAND

echo -e "Backend services are starting in separate windows."
echo -e "${BACKEND_COLOR}Backend window has cyan text${RESET_COLOR}"

echo

echo "Press any key to exit this window..."
read -n 1
