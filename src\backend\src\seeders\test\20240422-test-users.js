const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // 检查测试用户是否已存在
    const existingUsers = await queryInterface.sequelize.query(
      `SELECT * FROM "user" WHERE email = '<EMAIL>'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (existingUsers.length === 0) {
      // 创建测试用户
      const testUserId = uuidv4();
      const hashedPassword = await bcrypt.hash('Test@123', 8);
      
      await queryInterface.bulkInsert('user', [{
        id: testUserId,
        username: 'testuser',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'admin',
        isActive: true,
        isAdmin: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }], {});

      console.log('Test user created successfully');
      return testUserId;
    } else {
      console.log('Test user already exists');
      return existingUsers[0].id;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // 删除测试用户
    await queryInterface.bulkDelete('user', { email: '<EMAIL>' }, {});
  }
};
