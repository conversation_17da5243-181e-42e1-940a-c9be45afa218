<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">报销管理</h1>
        <p class="page-subtitle">管理和跟踪报销信息</p>
      </div>
      <div class="flex space-x-2">
        <router-link
          to="/reimbursements/create"
          class="btn btn-primary flex items-center shadow-md hover:shadow-lg transition-all duration-200"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          新建报销
        </router-link>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-card">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label for="project-name" class="form-label font-medium">项目名称</label>
          <div class="relative">
            <input
              id="project-name"
              v-model="filters.projectName"
              type="text"
              class="form-input pl-10 py-2 w-full"
              placeholder="请输入项目名称"
            >
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div>
          <label for="reimbursement-type" class="form-label font-medium">报销类型</label>
          <select
            id="reimbursement-type"
            v-model="filters.reimbursementType"
            class="form-input"
          >
            <option value="">全部</option>
            <option value="travel">差旅费</option>
            <option value="office">办公费</option>
            <option value="entertainment">招待费</option>
            <option value="other">其他</option>
          </select>
        </div>
        <div>
          <label for="supplier" class="form-label font-medium">供应商</label>
          <input
            id="supplier"
            v-model="filters.supplier"
            type="text"
            class="form-input"
            placeholder="请输入供应商"
          >
        </div>
        <div>
          <label for="start-date" class="form-label font-medium">开始时间</label>
          <input
            id="start-date"
            v-model="filters.startDate"
            type="date"
            max="3000-12-31"
            class="form-input"
          >
        </div>
        <div>
          <label for="end-date" class="form-label font-medium">结束时间</label>
          <input
            id="end-date"
            v-model="filters.endDate"
            type="date"
            max="3000-12-31"
            class="form-input"
          >
        </div>
        <div class="flex items-end justify-end space-x-4">
          <button
            @click="resetFilters"
            class="btn btn-secondary shadow-sm hover:shadow transition-all duration-200"
            :disabled="loading"
          >
            重置
          </button>
          <button
            @click="search"
            class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200"
            :disabled="loading"
          >
            <span v-if="loading" class="inline-block animate-spin mr-1">↻</span>
            {{ loading ? '加载中...' : '查询' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6">
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="font-medium">错误：</span>
            <span class="ml-2">{{ error }}</span>
          </div>
          <button @click="fetchReimbursements()" class="text-red-700 hover:text-red-900 font-medium">
            重试
          </button>
        </div>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="showSuccessAlert" class="mb-6">
      <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md shadow-sm">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            <span class="font-medium">成功：</span>
            <span class="ml-2">{{ successMessage }}</span>
          </div>
          <button @click="showSuccessAlert = false" class="text-green-700 hover:text-green-900 font-medium">
            关闭
          </button>
        </div>
      </div>
    </div>

    <!-- 列表卡片 -->
    <div class="card overflow-hidden shadow-md">
      <div v-if="loading" class="flex justify-center items-center p-8">
        <svg class="animate-spin h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
      <div v-else-if="reimbursements.length === 0" class="text-center p-8 text-gray-500">
        <div class="empty-state">
          <svg class="empty-state-icon mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="empty-state-title">暂无报销记录</h3>
          <p class="empty-state-description">没有找到符合条件的报销记录，请尝试调整筛选条件或添加新记录。</p>
        </div>
      </div>
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报销时间</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报销类型</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">供应商</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">报销人</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">价税合计</th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="reimbursement in reimbursements" :key="reimbursement.id" class="hover:bg-gray-50 transition-colors duration-150">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ reimbursement.projectName }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatDate(reimbursement.reimbursementDate) }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span class="badge" :class="{
                  'badge-blue': reimbursement.reimbursementType === 'travel',
                  'badge-green': reimbursement.reimbursementType === 'office',
                  'badge-purple': reimbursement.reimbursementType === 'entertainment',
                  'badge-gray': reimbursement.reimbursementType === 'other'
                }">
                  {{ getReimbursementTypeText(reimbursement.reimbursementType) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ reimbursement.supplier }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ reimbursement.reimburser?.username || '未指定' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">¥{{ formatCurrency(reimbursement.totalAmount) }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-2">                  <button
                    @click="router.push(`/reimbursements/${reimbursement.id}/edit`)"
                    class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                    </svg>
                    <span class="font-medium">编辑</span>
                  </button>
                  <button
                    @click="handleDeleteClick(reimbursement)"
                    class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                    <span class="font-medium">删除</span>
                  </button>
                </div>
              </td>
            </tr>

          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="mt-4 flex justify-between items-center px-6 py-4 border-t border-gray-200" v-if="totalPages > 1">
      <div class="text-sm text-gray-500">
        显示第 <span class="font-medium">{{ (currentPage - 1) * 10 + 1 }}</span> 至
        <span class="font-medium">{{ Math.min(currentPage * 10, totalRecords) }}</span> 条，
        共 <span class="font-medium">{{ totalRecords }}</span> 条记录
      </div>
      <div class="flex items-center space-x-3">
        <button
          @click="currentPage--"
          :disabled="currentPage === 1"
          class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <span class="text-sm font-medium text-gray-700">第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
        <button
          @click="currentPage++"
          :disabled="currentPage === totalPages"
          class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">确认删除</h3>
        <p class="text-gray-600 mb-6">确定要删除这条报销记录吗？此操作不可撤销。</p>
        <div class="flex justify-end space-x-3">
          <button
            @click="showDeleteModal = false"
            class="btn btn-secondary"
          >
            取消
          </button>
          <button
            @click="deleteReimbursement"
            class="btn btn-primary bg-red-600 hover:bg-red-700 border-none"
          >
            删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { formatCurrency, formatDate } from '@/utils/formatters'
import { reimbursementService } from '@/services/api.service'

const router = useRouter()
const loading = ref(false)
const currentPage = ref(1)
const totalPages = ref(1)
const showDeleteModal = ref(false)
const selectedReimbursement = ref(null)
const error = ref(null)
const successMessage = ref(null)
const showSuccessAlert = ref(false)

const filters = ref({
  projectName: '',
  reimbursementType: '',
  supplier: '',
  startDate: '',
  endDate: ''
})

// 报销数据
const reimbursements = ref([])
const totalRecords = ref(0)

const getReimbursementTypeText = (type) => {
  const types = {
    travel: '差旅费',
    office: '办公费',
    entertainment: '招待费',
    other: '其他'
  }
  return types[type] || type
}

// 获取报销列表
const fetchReimbursements = async () => {
  loading.value = true
  error.value = null
  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      limit: 10,
      ...filters.value
    }

    const response = await reimbursementService.getReimbursements(params)
    // 使用分页响应中的数据
    reimbursements.value = response.results || []
    totalRecords.value = response.totalResults || 0
    totalPages.value = response.totalPages || Math.ceil(totalRecords.value / 10)
  } catch (err) {
    console.error('获取报销列表失败:', err)
    error.value = '获取报销数据失败，请重试'
  } finally {
    loading.value = false
  }
}

const search = () => {
  currentPage.value = 1
  fetchReimbursements()
}

const resetFilters = () => {
  filters.value = {
    projectName: '',
    reimbursementType: '',
    supplier: '',
    startDate: '',
    endDate: ''
  }
  currentPage.value = 1
  fetchReimbursements()
}

const handleDeleteClick = (reimbursement) => {
  selectedReimbursement.value = reimbursement
  showDeleteModal.value = true
}

const deleteReimbursement = async () => {
  loading.value = true
  try {
    await reimbursementService.deleteReimbursement(selectedReimbursement.value.id)
    showDeleteModal.value = false

    // 显示成功消息
    error.value = null
    showSuccessMessage('报销记录删除成功')

    // 重新加载数据
    fetchReimbursements()
  } catch (err) {
    console.error('删除报销失败:', err)

    // 显示错误消息
    let errorMessage = '删除报销记录失败'

    // 根据错误类型提供更具体的错误信息
    if (err.response) {
      const status = err.response.status
      const serverMessage = err.response.data?.message

      if (status === 404) {
        errorMessage = serverMessage || '报销记录不存在或已被删除'
      } else if (status === 403) {
        errorMessage = '您没有权限删除此报销记录'
      } else if (status >= 500) {
        errorMessage = '服务器错误，请稍后重试'
      } else {
        errorMessage = serverMessage || errorMessage
      }
    }

    error.value = errorMessage
    showDeleteModal.value = false
  } finally {
    loading.value = false
  }
}

// 监听页码变化
watch(currentPage, () => {
  fetchReimbursements()
})

// 显示成功消息的函数
const showSuccessMessage = (message) => {
  successMessage.value = message
  showSuccessAlert.value = true

  // 3秒后自动关闭
  setTimeout(() => {
    showSuccessAlert.value = false
  }, 3000)
}

onMounted(() => {
  fetchReimbursements()
})
</script>

<style scoped>
.page-header, .filter-card, .card {
  animation: fadeSlideIn 0.6s ease-out;
}

.page-header {
  animation-delay: 0s;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.page-subtitle {
  font-size: 1rem;
  color: #6b7280;
}

.filter-card {
  animation-delay: 0.1s;
}

.card {
  animation-delay: 0.2s;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.form-input {
  @apply block w-full rounded-md border-gray-300 shadow-sm
    focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50
    transition-all duration-200;
}

.btn {
  @apply inline-flex items-center px-4 py-2 border border-transparent
    rounded-md shadow-sm text-sm font-medium focus:outline-none
    focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700
    focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-white text-gray-700 border-gray-300
    hover:bg-gray-50 focus:ring-blue-500;
}

tbody tr {
  transition: all 0.2s ease;
}
</style>