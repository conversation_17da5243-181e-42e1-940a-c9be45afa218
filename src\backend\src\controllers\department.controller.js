const { Department, User } = require('../models');
const ApiError = require('../utils/ApiError');
const { Op } = require('sequelize');

/**
 * Create a new department
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createDepartment = async (req, res) => {
  try {
    const { name, code, description, parentId } = req.body;
    
    // Check if department with the same name or code already exists
    const existingDepartment = await Department.findOne({
      where: {
        [Op.or]: [
          { name },
          { code }
        ]
      }
    });
    
    if (existingDepartment) {
      throw new ApiError(400, 'Department with this name or code already exists');
    }
    
    // Create new department
    const department = await Department.create({
      name,
      code,
      description,
      parentId,
      createdBy: req.user.id,
    });
    
    return res.status(201).json({
      success: true,
      data: department
    });
  } catch (error) {
    console.error('Error creating department:', error);
    
    // 处理Sequelize唯一约束错误
    if (error.name === 'SequelizeUniqueConstraintError') {
      const field = Object.keys(error.fields)[0];
      const value = error.fields[field];
      return res.status(400).json({
        success: false,
        message: `Department with ${field} '${value}' already exists`,
        error: {
          name: error.name,
          fields: error.fields,
          code: error.parent?.code || 'UNIQUE_CONSTRAINT_ERROR'
        }
      });
    }
    
    return res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'An error occurred while creating the department',
      error: error.isOperational ? undefined : {
        name: error.name,
        code: error.parent?.code,
        detail: error.parent?.detail
      }
    });
  }
};

/**
 * Get all departments
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllDepartments = async (req, res) => {
  try {
    const departments = await Department.findAll({
      include: [
        {
          model: Department,
          as: 'parentDepartment',
          attributes: ['id', 'name', 'code']
        },
        {
          model: User,
          as: 'employees',
          attributes: ['id']
        }
      ],
      order: [['name', 'ASC']]
    });
    
    return res.status(200).json({
      success: true,
      data: departments
    });
  } catch (error) {
    console.error('Error fetching departments:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while fetching departments',
      error: {
        name: error.name,
        code: error.parent?.code,
        detail: error.parent?.detail
      }
    });
  }
};

/**
 * Get department by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDepartmentById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const department = await Department.findByPk(id, {
      include: [
        {
          model: Department,
          as: 'parentDepartment',
          attributes: ['id', 'name', 'code']
        },
        {
          model: Department,
          as: 'childDepartments',
          attributes: ['id', 'name', 'code']
        },
        {
          model: User,
          as: 'employees',
          attributes: ['id', 'username', 'email', 'lastName', 'position']
        }
      ]
    });
    
    if (!department) {
      throw new ApiError(404, 'Department not found');
    }
    
    return res.status(200).json({
      success: true,
      data: department
    });
  } catch (error) {
    console.error('Error fetching department:', error);
    return res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'An error occurred while fetching the department',
      error: error.isOperational ? undefined : {
        name: error.name,
        code: error.parent?.code,
        detail: error.parent?.detail
      }
    });
  }
};

/**
 * Update department
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, code, description, parentId, status } = req.body;
    
    const department = await Department.findByPk(id);
    
    if (!department) {
      throw new ApiError(404, 'Department not found');
    }
    
    // Check if another department with the same name or code exists
    if (name || code) {
      const existingDepartment = await Department.findOne({
        where: {
          id: { [Op.ne]: id },
          [Op.or]: [
            { name: name || null },
            { code: code || null }
          ]
        }
      });
      
      if (existingDepartment) {
        throw new ApiError(400, 'Another department with this name or code already exists');
      }
    }
    
    // Update department
    await department.update({
      name: name || department.name,
      code: code || department.code,
      description: description !== undefined ? description : department.description,
      parentId: parentId !== undefined ? parentId : department.parentId,
      status: status || department.status,
      updatedBy: req.user.id
    });
    
    return res.status(200).json({
      success: true,
      data: department
    });
  } catch (error) {
    console.error('Error updating department:', error);
    
    // 处理Sequelize唯一约束错误
    if (error.name === 'SequelizeUniqueConstraintError') {
      const field = Object.keys(error.fields)[0];
      const value = error.fields[field];
      return res.status(400).json({
        success: false,
        message: `Department with ${field} '${value}' already exists`,
        error: {
          name: error.name,
          fields: error.fields,
          code: error.parent?.code || 'UNIQUE_CONSTRAINT_ERROR'
        }
      });
    }
    
    return res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'An error occurred while updating the department',
      error: error.isOperational ? undefined : {
        name: error.name,
        code: error.parent?.code,
        detail: error.parent?.detail
      }
    });
  }
};

/**
 * Delete department
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    
    const department = await Department.findByPk(id);
    
    if (!department) {
      throw new ApiError(404, 'Department not found');
    }
    
    // Check if department has child departments
    const childDepartments = await Department.findAll({
      where: { parentId: id }
    });
    
    if (childDepartments.length > 0) {
      throw new ApiError(400, 'Cannot delete department with child departments');
    }
    
    // Check if department has employees
    const employees = await User.findAll({
      where: { departmentId: id }
    });
    
    if (employees.length > 0) {
      throw new ApiError(400, 'Cannot delete department with assigned employees');
    }
    
    // Delete department
    await department.destroy();
    
    return res.status(200).json({
      success: true,
      message: 'Department deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting department:', error);
    return res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'An error occurred while deleting the department',
      error: error.isOperational ? undefined : {
        name: error.name,
        code: error.parent?.code,
        detail: error.parent?.detail
      }
    });
  }
};

/**
 * Get department hierarchy
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getDepartmentHierarchy = async (req, res) => {
  try {
    // Get all top-level departments (no parent)
    const topLevelDepartments = await Department.findAll({
      where: { parentId: null },
      attributes: ['id', 'name', 'code']
    });
    
    // Function to recursively build the department tree
    const buildDepartmentTree = async (departments) => {
      const result = [];
      
      for (const dept of departments) {
        const children = await Department.findAll({
          where: { parentId: dept.id },
          attributes: ['id', 'name', 'code']
        });
        
        const deptData = {
          id: dept.id,
          name: dept.name,
          code: dept.code
        };
        
        if (children.length > 0) {
          deptData.children = await buildDepartmentTree(children);
        }
        
        result.push(deptData);
      }
      
      return result;
    };
    
    const hierarchy = await buildDepartmentTree(topLevelDepartments);
    
    return res.status(200).json({
      success: true,
      data: hierarchy
    });
  } catch (error) {
    console.error('Error fetching department hierarchy:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while fetching the department hierarchy',
      error: {
        name: error.name,
        code: error.parent?.code,
        detail: error.parent?.detail
      }
    });
  }
};

module.exports = {
  createDepartment,
  getAllDepartments,
  getDepartmentById,
  updateDepartment,
  deleteDepartment,
  getDepartmentHierarchy
}; 