const express = require('express');
const { authenticate } = require('../middlewares/auth.middleware');
const validate = require('../middlewares/validate');
const tagValidation = require('../validations/tag.validation');
const tagController = require('../controllers/tag.controller');

const router = express.Router();

router
  .route('/')
  .post(
    authenticate,
    validate(tagValidation.createTag),
    tagController.createTag
  )
  .get(
    validate(tagValidation.getTags),
    tagController.getTags
  );

router
  .route('/:tagId')
  .get(
    authenticate,
    validate(tagValidation.getTag),
    tagController.getTag
  )
  .patch(
    authenticate,
    validate(tagValidation.updateTag),
    tagController.updateTag
  )
  .delete(
    authenticate,
    validate(tagValidation.deleteTag),
    tagController.deleteTag
  );

module.exports = router;