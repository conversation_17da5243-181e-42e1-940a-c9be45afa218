@echo off
setlocal

REM Set environment variables
set DEPLOY_ENV=production
set SERVER_HOST=*************   
set SERVER_USER=root
set SERVER_PASSWORD=wu@20250327
set REMOTE_DIR=/root/www/yihe
set PROJECT_ROOT=D:\source\jiuan\5rmi0\yihe

REM Build frontend
echo Building frontend...
cd "%PROJECT_ROOT%\src\frontend"
call pnpm install
call pnpm build

REM Build backend
echo Building backend...
cd "%PROJECT_ROOT%\src\backend"
call pnpm install
call pnpm build

REM Create deployment package
echo Creating deployment package...
cd "%PROJECT_ROOT%\deploy"
if exist dist rd /s /q dist
mkdir dist
mkdir dist\frontend
mkdir dist\backend

REM Copy frontend files
xcopy /E /I "%PROJECT_ROOT%\src\frontend\dist" dist\frontend\dist
copy "%PROJECT_ROOT%\src\frontend\package.json" dist\frontend\
copy "%PROJECT_ROOT%\src\frontend\pnpm-lock.yaml" dist\frontend\

REM Copy backend files
xcopy /E /I "%PROJECT_ROOT%\src\backend\dist" dist\backend\dist
xcopy /E /I "%PROJECT_ROOT%\src\backend\src" dist\backend\src
copy "%PROJECT_ROOT%\src\backend\package.json" dist\backend\
copy "%PROJECT_ROOT%\src\backend\pnpm-lock.yaml" dist\backend\

REM Deploy to server using pscp
echo Deploying to server...
pscp -batch -pw %SERVER_PASSWORD% -r dist\* %SERVER_USER%@%SERVER_HOST%:%REMOTE_DIR%

REM Execute remote commands
echo Running remote deployment commands...
plink -batch -pw %SERVER_PASSWORD% %SERVER_USER%@%SERVER_HOST% "cd %REMOTE_DIR%/frontend && pnpm install && cd ../backend && pnpm install && pm2 restart all"

REM Clean up
rd /s /q dist

echo Deployment completed!
endlocal