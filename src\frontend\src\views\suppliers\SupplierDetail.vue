<template>
  <div class="page-container">
    <!-- 页面标题和按钮 -->
    <div class="page-header">
      <h1 class="page-title">{{ isNewSupplier ? '新建供应商' : '供应商详情' }}</h1>
      <div class="flex space-x-3">
        <router-link to="/suppliers" class="btn btn-outline-secondary">
          <span class="flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            返回列表
          </span>
        </router-link>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error && (hasAttemptedSave || !isNewSupplier)" class="error-alert">
      <div class="flex">
        <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        {{ error }}
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p class="loading-text">加载中...</p>
    </div>

    <!-- 表单内容 -->
    <div v-else>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- 左侧：基本信息 -->
        <div class="md:col-span-2 space-y-8">
          <!-- 基本信息卡片 -->
          <div class="card">
            <h2 class="card-header">
              <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
              基本信息
            </h2>
            <div class="form-group">
              <div class="form-grid">
                <div>
                  <label for="name" class="form-label">供应商名称 <span class="form-required">*</span></label>
                  <input id="name" v-model="supplierForm.name" type="text" class="form-input" required
                    placeholder="请输入供应商名称" />
                </div>
                <div>
                  <label for="code" class="form-label">供应商编号</label>
                  <input id="code" v-model="supplierForm.code" type="text" class="form-input"
                    placeholder="自动生成或手动输入" />
                </div>
              </div>

              <div class="form-grid">
                <div>
                  <label for="category" class="form-label">供应商类别</label>
                  <select id="category" v-model="supplierForm.category" class="form-input">
                    <option value="material">原材料</option>
                    <option value="equipment">设备</option>
                    <option value="service">服务</option>
                    <option value="logistics">物流</option>
                    <option value="other">其他</option>
                  </select>
                </div>
                <div>
                  <label for="status" class="form-label">合作状态</label>
                  <select id="status" v-model="supplierForm.status" class="form-input">
                    <option value="active">合作中</option>
                    <option value="inactive">已暂停</option>
                    <option value="potential">潜在</option>
                  </select>
                </div>
              </div>

              <div>
                <label for="description" class="form-label">供应商描述</label>
                <textarea id="description" v-model="supplierForm.description" rows="3" class="form-input"
                  placeholder="输入供应商的简要描述信息..."></textarea>
              </div>
            </div>
          </div>

          <!-- 联系信息卡片 -->
          <div class="card">
            <h2 class="card-header">
              <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
              </svg>
              联系信息
            </h2>
            <div class="form-group">
              <div class="form-grid">
                <div>
                  <label for="contactName" class="form-label">联系人姓名 <span class="form-required">*</span></label>
                  <input id="contactName" v-model="supplierForm.contactName" type="text" class="form-input"
                    placeholder="请输入联系人姓名" required />
                </div>
                <div>
                  <label for="contactTitle" class="form-label">联系人职位</label>
                  <input id="contactTitle" v-model="supplierForm.contactTitle" type="text" class="form-input"
                    placeholder="请输入联系人职位" />
                </div>
              </div>

              <div class="form-grid">
                <div>
                  <label for="phone" class="form-label">电话 <span class="form-required">*</span></label>
                  <input id="phone" v-model="supplierForm.phone" type="tel" class="form-input"
                    placeholder="请输入联系电话" required />
                </div>
                <div>
                  <label for="email" class="form-label">电子邮箱 <span class="form-required">*</span></label>
                  <input id="email" v-model="supplierForm.email" type="email" class="form-input"
                    placeholder="请输入电子邮箱" required />
                </div>
              </div>

              <div>
                <label for="address" class="form-label">地址 <span class="form-required">*</span></label>
                <textarea id="address" v-model="supplierForm.address" rows="2" class="form-input"
                  placeholder="请输入详细地址信息..." required></textarea>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：附加信息 -->
        <div class="space-y-8">
          <!-- 财务信息卡片 -->
          <div class="card">
            <h2 class="card-header">
              <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              财务信息
            </h2>
            <div class="form-group">
              <div>
                <label for="taxId" class="form-label">税号</label>
                <input id="taxId" v-model="supplierForm.taxId" type="text" class="form-input"
                  placeholder="请输入税号" />
              </div>
              <div>
                <label for="bankName" class="form-label">开户银行</label>
                <input id="bankName" v-model="supplierForm.bankName" type="text" class="form-input"
                  placeholder="请输入开户银行" />
              </div>
              <div>
                <label for="bankAccount" class="form-label">银行账号</label>
                <input id="bankAccount" v-model="supplierForm.bankAccount" type="text" class="form-input"
                  placeholder="请输入银行账号" />
              </div>
              <div>
                <label for="bankCode" class="form-label">联行号</label>
                <input id="bankCode" v-model="supplierForm.bankCode" type="text" class="form-input"
                  placeholder="请输入联行号" />
              </div>
            </div>
          </div>

          <!-- 评级信息卡片 -->
          <div class="card">
            <h2 class="card-header">
              <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
              </svg>
              评级信息
            </h2>
            <div class="form-group">
              <div>
                <label for="rating" class="form-label">供应商评级</label>
                <select id="rating" v-model="supplierForm.rating" class="form-input">
                  <option value="A">A - 优质</option>
                  <option value="B">B - 良好</option>
                  <option value="C">C - 一般</option>
                  <option value="D">D - 较差</option>
                </select>
              </div>
              <div>
                <label for="qualificationLevel" class="form-label">资质等级</label>
                <select id="qualificationLevel" v-model="supplierForm.qualificationLevel" class="form-input">
                  <option value="1">一级</option>
                  <option value="2">二级</option>
                  <option value="3">三级</option>
                </select>
              </div>
              <div>
                <label for="qualificationExpiry" class="form-label">资质到期日</label>
                <input id="qualificationExpiry" v-model="supplierForm.qualificationExpiry" type="date" class="form-input" max="3000-12-31" />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 底部操作栏 -->
      <div class="footer-actions mt-8 border-t pt-6">
        <div class="container mx-auto px-4 py-4 flex justify-end">
          <button @click="saveSupplier" class="btn btn-primary shadow-sm hover:shadow-md transition-all duration-200" :disabled="isSaving">
            <svg v-if="isSaving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isNewSupplier ? '创建' : '保存' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div class="bg-white rounded-lg shadow-2xl max-w-md w-full p-6 transform transition-all">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">确认删除</h3>
          <p class="text-gray-500 mb-6">
            确定要删除供应商 "{{ supplierForm.name }}" 吗？此操作不可撤销。
          </p>

          <div class="flex justify-center space-x-4">
            <button
              type="button"
              @click="showDeleteModal = false"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="button"
              @click="deleteSupplier"
              class="btn btn-danger shadow-sm hover:shadow-md transition-all duration-200"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                删除中...
              </span>
              <span v-else>确认删除</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { supplierService } from '@/services/api.service';
import notificationService from '@/services/notification.service';

const route = useRoute();
const router = useRouter();
const supplierId = computed(() => route.params.id);
const isNewSupplier = computed(() => supplierId.value === 'create');

// 状态变量
const loading = ref(false);
const error = ref('');
const isSaving = ref(false);
const isDeleting = ref(false);
const showDeleteModal = ref(false);
const hasAttemptedSave = ref(false);

// 表单数据 - 与后端模型保持一致
const supplierForm = reactive({
  name: '',
  category: 'material',
  address: '',
  phone: '',
  contactName: '',
  contactPhone: '',
  contactEmail: '',
  rating: 4, // 对应后端的 1-5 数字评分
  products: [],
  notes: '', // 对应前端的 description
  tags: [],
  // 扩展字段 - 前端特有
  code: '',
  status: 'active',
  taxId: '',
  bankName: '',
  bankAccount: '',
  bankCode: '',
  qualificationLevel: '2',
  qualificationExpiry: ''
});

// 获取供应商详情
onMounted(async () => {
  // 只有在编辑模式（id 存在且不是 'create'）时才获取详情
  if (supplierId.value && supplierId.value !== 'create') {
    await fetchSupplierDetails();
  }
});

// 获取供应商详情
const fetchSupplierDetails = async () => {
  if (isNewSupplier.value) return; // 如果是新供应商，直接返回

  loading.value = true;
  error.value = '';

  try {
    const response = await supplierService.getSupplier(supplierId.value);
    const supplier = response;

    // 直接填充与后端模型匹配的字段
    supplierForm.name = supplier.name || '';
    supplierForm.category = supplier.category || 'material';
    supplierForm.address = supplier.address || '';
    supplierForm.phone = supplier.phone || '';
    supplierForm.contactName = supplier.contactName || '';
    supplierForm.contactPhone = supplier.contactPhone || '';
    supplierForm.contactEmail = supplier.contactEmail || '';
    supplierForm.rating = supplier.rating || 4; // 直接使用数字评分
    supplierForm.products = supplier.products || [];
    supplierForm.notes = supplier.notes || ''; // 使用后端的 notes 字段
    supplierForm.tags = supplier.tags || [];

    // 处理前端特有字段
    if (supplier.code) supplierForm.code = supplier.code;
    if (supplier.status) supplierForm.status = supplier.status;
    if (supplier.taxId) supplierForm.taxId = supplier.taxId;
    if (supplier.bankName) supplierForm.bankName = supplier.bankName;
    if (supplier.bankAccount) supplierForm.bankAccount = supplier.bankAccount;
    if (supplier.bankCode) supplierForm.bankCode = supplier.bankCode;
    if (supplier.qualificationExpiry) {
      supplierForm.qualificationExpiry = new Date(supplier.qualificationExpiry).toISOString().split('T')[0];
    }

    // 兼容处理：如果后端返回的是 description 而不是 notes
    if (supplier.description && !supplier.notes) {
      supplierForm.notes = supplier.description;
    }
  } catch (err) {
    console.error('获取供应商信息失败:', err);
    error.value = err.response?.data?.message || '获取供应商信息失败';
    if (err.response?.status === 400 && err.response?.data?.details?.includes('ObjectId')) {
      error.value = '无效的供应商ID';
      router.push('/suppliers');
    }
  } finally {
    loading.value = false;
  }
};

// 保存供应商
const saveSupplier = async () => {
  if (isSaving.value) return;

  hasAttemptedSave.value = true;

  // 详细验证
  if (!supplierForm.name) {
    error.value = '供应商名称不能为空';
    return;
  }
  
  if (!supplierForm.address) {
    error.value = '地址不能为空';
    return;
  }
  
  if (!supplierForm.phone) {
    error.value = '电话不能为空';
    return;
  }
  
  if (!supplierForm.contactName) {
    error.value = '联系人名称不能为空';
    return;
  }
  
  if (!supplierForm.contactPhone) {
    error.value = '联系人电话不能为空';
    return;
  }
  
  if (!supplierForm.contactEmail) {
    error.value = '联系人邮箱不能为空';
    return;
  }

  isSaving.value = true;
  error.value = '';

  try {
    // 准备提交的数据，与后端模型字段保持一致
    const supplierData = {
      name: supplierForm.name,
      code: supplierForm.code,
      category: supplierForm.category,
      address: supplierForm.address || '',
      phone: supplierForm.phone || '',
      contactName: supplierForm.contactName || '',
      contactPhone: supplierForm.contactPhone || '',
      contactEmail: supplierForm.contactEmail || '',
      rating: supplierForm.rating, // 直接使用数字评分
      products: supplierForm.products || [],
      notes: supplierForm.notes || '', // 使用统一的 notes 字段
      tags: supplierForm.tags || []
    };

    if (isNewSupplier.value) {
      // 添加必要的 createdBy 字段，使用固定的 UUID 格式
      const response = await supplierService.createSupplier({
        ...supplierData,
        code: supplierForm.code, // 新建时传递 code
        createdBy: '00000000-0000-0000-0000-000000000000' // 使用一个有效的 UUID 格式
      });
      const newSupplierId = response.data.id;
      router.push(`/suppliers/${newSupplierId}`);

      // 显示成功消息
      notificationService.success('供应商创建成功', `供应商 "${supplierForm.name}" 已成功创建`);
    } else {
      await supplierService.updateSupplier(supplierId.value, supplierData); // 不传 code
      router.push('/suppliers');

      // 显示成功消息
      notificationService.success('供应商更新成功', `供应商 "${supplierForm.name}" 已成功更新`);
    }
  } catch (err) {
    console.error(isNewSupplier.value ? '创建供应商失败:' : '更新供应商失败:', err);
    error.value = err.response?.data?.message || (isNewSupplier.value ? '创建供应商失败' : '更新供应商失败');
  } finally {
    isSaving.value = false;
  }
};

// 确认删除
const confirmDelete = () => {
  showDeleteModal.value = true;
};

// 删除供应商
const deleteSupplier = async () => {
  if (isDeleting.value) return;

  isDeleting.value = true;

  try {
    await supplierService.deleteSupplier(supplierId.value);
    // 删除成功后导航回列表页
    router.push('/suppliers');
  } catch (err) {
    console.error('删除供应商失败:', err);
    // 根据错误状态码提供更具体的错误信息
    if (err.response?.status === 400) {
      if (err.response.data?.code === 'SUPPLIER_HAS_PROCUREMENTS') {
        error.value = '无法删除：该供应商存在关联的采购记录。请先处理相关采购记录后再尝试删除。';
      } else if (err.response.data?.code === 'SUPPLIER_HAS_CONTRACTS') {
        error.value = '无法删除：该供应商存在有效合同。请先终止或完成相关合同后再尝试删除。';
      } else {
        error.value = err.response.data?.message || '无法删除：供应商数据验证失败';
      }
    } else if (err.response?.status === 403) {
      error.value = '抱歉，您没有删除此供应商的权限。如需删除，请联系管理员。';
    } else if (err.response?.status === 404) {
      error.value = '供应商不存在或已被删除';
    } else {
      error.value = '删除供应商失败，请稍后重试或联系系统管理员';
    }
    showDeleteModal.value = false;
  } finally {
    isDeleting.value = false;
  }
};
</script>