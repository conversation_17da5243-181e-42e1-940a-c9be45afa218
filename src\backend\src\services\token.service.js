const jwt = require('jsonwebtoken');
const moment = require('moment');
const { v4: uuidv4 } = require('uuid');
const config = require('../config/config');
const { Token } = require('../models');

/**
 * Generate token
 * @param {string} userId
 * @param {moment.Moment} expires
 * @param {string} type
 * @param {string} [secret]
 * @returns {string}
 */
const generateToken = (userId, expires, type, secret = config.jwt.secret) => {
  if (!userId || !expires || !type) {
    throw new Error('User ID, expiration, and type are required to generate a token');
  }

  try {
    // Log the secret being used (only first few characters for security)
    console.log(`Generating token with secret: ${secret.substring(0, 5)}...`);

    const payload = {
      sub: userId,
      iat: moment().unix(),
      exp: expires.unix(),
      type,
    };
    return jwt.sign(payload, secret);
  } catch (error) {
    console.error('Error generating token:', error);
    throw new Error(`Failed to generate token: ${error.message}`);
  }
};

/**
 * Save a token
 * @param {string} token
 * @param {string} userId
 * @param {moment.Moment} expires
 * @param {string} type
 * @param {boolean} [blacklisted]
 * @returns {Promise<Token>}
 */
const saveToken = async (token, userId, expires, type, blacklisted = false) => {
  if (!token || !userId || !expires || !type) {
    throw new Error('Token, user ID, expiration, and type are required to save a token');
  }

  const tokenDoc = await Token.create({
    id: uuidv4(),
    token,
    userId: userId,
    expires: expires.toDate(),
    type,
    blacklisted,
  });
  return tokenDoc;
};

/**
 * Verify token and return token doc (or throw an error if it is not valid)
 * @param {string} token
 * @param {string} type
 * @returns {Promise<Token>}
 */
const verifyToken = async (token, type) => {
  if (!token) {
    throw new Error('Token is required');
  }

  if (!type) {
    throw new Error('Type is required to verify a token');
  }

  try {
    // Log the secret being used (only first few characters for security)
    console.log(`Verifying token with secret: ${config.jwt.secret.substring(0, 5)}...`);

    const payload = jwt.verify(token, config.jwt.secret);

    const tokenDoc = await Token.findOne({
      where: {
        token,
        type,
        userId: payload.sub,
        blacklisted: false,
      },
    });

    if (!tokenDoc) {
      throw new Error('Token not found in database');
    }

    return tokenDoc;
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      throw new Error(`Invalid token: ${error.message}`);
    } else if (error.name === 'TokenExpiredError') {
      throw new Error(`Token expired at ${error.expiredAt}`);
    } else {
      throw error;
    }
  }
};

/**
 * Generate auth tokens
 * @param {User} user
 * @returns {Promise<Object>}
 */
const generateAuthTokens = async (user) => {
  if (!user || !user.id) {
    throw new Error('Valid user is required to generate auth tokens');
  }

  const accessTokenExpires = moment().add(config.jwt.accessExpirationMinutes, 'minutes');
  const accessToken = generateToken(user.id, accessTokenExpires, 'access');

  const refreshTokenExpires = moment().add(config.jwt.refreshExpirationDays, 'days');
  const refreshToken = generateToken(user.id, refreshTokenExpires, 'refresh');
  await saveToken(refreshToken, user.id, refreshTokenExpires, 'refresh');

  return {
    access: {
      token: accessToken,
      expires: accessTokenExpires.toDate(),
    },
    refresh: {
      token: refreshToken,
      expires: refreshTokenExpires.toDate(),
    },
  };
};

/**
 * Generate reset password token
 * @param {string} email
 * @returns {Promise<string>}
 */
const generateResetPasswordToken = async (email) => {
  const user = await getUserByEmail(email);
  if (!user) {
    throw new Error('No users found with this email');
  }
  const expires = moment().add(config.jwt.resetPasswordExpirationMinutes, 'minutes');
  const resetPasswordToken = generateToken(user.id, expires, 'resetPassword');
  await saveToken(resetPasswordToken, user.id, expires, 'resetPassword');
  return resetPasswordToken;
};

/**
 * Generate verify email token
 * @param {User} user
 * @returns {Promise<string>}
 */
const generateVerifyEmailToken = async (user) => {
  if (!user || !user.id) {
    throw new Error('Valid user is required to generate verify email token');
  }

  const expires = moment().add(config.jwt.verifyEmailExpirationMinutes, 'minutes');
  const verifyEmailToken = generateToken(user.id, expires, 'verifyEmail');
  await saveToken(verifyEmailToken, user.id, expires, 'verifyEmail');
  return verifyEmailToken;
};

// Helper function to get user by email - importing directly would cause circular dependency
const getUserByEmail = async (email) => {
  const { User } = require('../models');
  return User.findOne({ where: { email } });
};

module.exports = {
  generateToken,
  saveToken,
  verifyToken,
  generateAuthTokens,
  generateResetPasswordToken,
  generateVerifyEmailToken,
};