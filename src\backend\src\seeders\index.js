const seedUsers = require('./userSeeder');
const seedSuppliers = require('./supplierSeeder');
const seedCategories = require('./categorySeeder');
const seedTags = require('./tagSeeder');
const seedRoles = require('./roleSeeder');
const seedProjects = require('./projectSeeder');
const seedDocuments = require('./documentSeeder');
const seedProducts = require('./productSeeder');
const seedCustomers = require('./customerSeeder');
const seedClients = require('./clientSeeder');
const seedFinances = require('./financeSeeder');
const seedProcurements = require('./procurementSeeder');
const seedPurchases = require('./purchaseSeeder');
const seedProductTransactions = require('./productTransactionSeeder');
const seedReimbursements = require('./20240601000001-demo-reimbursements');
const seedSubcontracts = require('./20240601000002-demo-subcontracts');
const seedWorkhours = require('./20240601000003-demo-workhours');

// New seeders
const seedEmployees = require('./employeeSeeder');
const seedProjectTasks = require('./projectTaskSeeder');
const seedProjectProgress = require('./projectProgressSeeder');
const seedProjectFollowUps = require('./projectFollowUpSeeder');
const seedDocumentTags = require('./documentTagSeeder');
const seedComments = require('./commentSeeder');
const seedContracts = require('./contractSeeder');
const seedPermissions = require('./permissionSeeder');
const seedTokens = require('./tokenSeeder');
const seedAttachments = require('./attachmentSeeder');
const seedAttendance = require('./attendanceSeeder');

async function runSeeders() {
  try {
    // First seed core data
    await seedPermissions();
    await seedRoles();
    await seedUsers();
    await seedCategories();
    await seedTags();
    await seedTokens();

    // Then seed employee data
    await seedEmployees();
    await seedAttendance();

    // Then seed business entities
    await seedSuppliers();
    await seedCustomers();
    await seedClients();
    await seedProjects();

    // Then seed project related data
    await seedProjectTasks();
    await seedProjectProgress();
    await seedProjectFollowUps();
    await seedContracts();

    // Then seed documents and related
    await seedDocuments();
    await seedDocumentTags();
    await seedAttachments();

    // Then seed comments
    await seedComments();

    // Then seed product related
    await seedProducts();
    await seedProductTransactions();

    // Then seed financial related
    await seedFinances();
    await seedProcurements();
    await seedPurchases();
    await seedReimbursements();
    await seedSubcontracts();
    await seedWorkhours();

    console.log('All seeders completed successfully');
  } catch (error) {
    console.error('Error running seeders:', error);
    throw error; // Re-throw to ensure the error is properly handled
  }
}

if (require.main === module) {
  runSeeders();
}

module.exports = {
  runSeeders
};
