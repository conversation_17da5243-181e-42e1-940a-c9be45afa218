/**
 * 测试创建消息的脚本
 * 
 * 此脚本用于测试创建消息功能，不依赖于API路由
 * 运行方式：node src/scripts/testCreateMessage.js
 */

const { sequelize, User, UserMessage } = require('../models');

async function testCreateMessage() {
  try {
    console.log('开始测试创建消息...');
    
    // 获取第一个用户
    const user = await User.findOne();
    
    if (!user) {
      console.error('没有找到用户，请先创建用户');
      return;
    }
    
    console.log(`找到用户: ${user.username}, ID: ${user.id}`);
    
    // 创建测试消息
    const messageData = {
      userId: user.id,
      title: '测试消息',
      content: '这是一条测试消息内容',
      type: 'system'
    };
    
    console.log('准备创建消息:', messageData);
    
    // 直接使用模型创建消息
    const message = await UserMessage.create(messageData);
    
    console.log('消息创建成功:', message.toJSON());
    
    // 查询所有消息
    const messages = await UserMessage.findAll({
      where: { userId: user.id },
      limit: 5
    });
    
    console.log(`用户 ${user.username} 的消息列表 (最多5条):`);
    messages.forEach((msg, index) => {
      console.log(`${index + 1}. ${msg.title} - ${msg.createdAt}`);
    });
    
  } catch (error) {
    console.error('测试创建消息失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行测试
testCreateMessage();
