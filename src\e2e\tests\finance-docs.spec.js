import { test, expect } from '@playwright/test';

test.describe('Financial and Document Management Tests', () => {
  test.beforeEach(async ({ page }) => {
    // 登录系统
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('财务管理测试', async ({ page }) => {
    // 导航到财务页面
    await page.goto('/finance');
    
    // 验证财务数据加载
    await expect(page.locator('.finance-dashboard')).toBeVisible();
    
    // 添加新财务记录
    await page.click('button:has-text("添加记录")');
    await page.fill('input[name="amount"]', '5000');
    await page.fill('input[name="description"]', '测试支出');
    await page.selectOption('select[name="type"]', '支出');
    await page.click('button[type="submit"]');
    
    // 验证记录添加成功
    await expect(page.locator('text=测试支出')).toBeVisible();
  });

  test('报销管理测试', async ({ page }) => {
    // 导航到报销页面
    await page.goto('/reimbursements');
    
    // 验证报销列表加载
    await expect(page.locator('.reimbursement-list')).toBeVisible();
    
    // 创建新报销申请
    await page.click('button:has-text("新建报销")');
    await page.fill('input[name="amount"]', '1000');
    await page.fill('input[name="purpose"]', '办公用品');
    await page.fill('textarea[name="description"]', '购买办公用品');
    await page.click('button[type="submit"]');
    
    // 验证报销申请创建成功
    await expect(page.locator('text=办公用品')).toBeVisible();
  });

  test('文档管理测试', async ({ page }) => {
    // 导航到文档页面
    await page.goto('/documents');
    
    // 验证文档列表加载
    await expect(page.locator('.document-list')).toBeVisible();
    
    // 上传新文档
    const fileInput = page.locator('input[type="file"]');
    await fileInput.setInputFiles('path/to/test-document.pdf');
    await page.fill('input[name="title"]', '测试文档');
    await page.fill('textarea[name="description"]', '这是一个测试文档');
    await page.click('button[type="submit"]');
    
    // 验证文档上传成功
    await expect(page.locator('text=测试文档')).toBeVisible();
  });

  test('采购管理测试', async ({ page }) => {
    // 导航到采购页面
    await page.goto('/purchases');
    
    // 验证采购列表加载
    await expect(page.locator('.purchase-list')).toBeVisible();
    
    // 创建新采购单
    await page.click('button:has-text("新建采购")');
    await page.fill('input[name="item"]', '测试物品');
    await page.fill('input[name="quantity"]', '50');
    await page.fill('input[name="price"]', '100');
    await page.fill('input[name="supplier"]', '测试供应商');
    await page.click('button[type="submit"]');
    
    // 验证采购单创建成功
    await expect(page.locator('text=测试物品')).toBeVisible();
  });

  test('供应商管理测试', async ({ page }) => {
    // 导航到供应商页面
    await page.goto('/suppliers');
    
    // 验证供应商列表加载
    await expect(page.locator('.supplier-list')).toBeVisible();
    
    // 添加新供应商
    await page.click('button:has-text("添加供应商")');
    await page.fill('input[name="name"]', '测试供应商');
    await page.fill('input[name="contact"]', '李四');
    await page.fill('input[name="phone"]', '13800138000');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.click('button[type="submit"]');
    
    // 验证供应商添加成功
    await expect(page.locator('text=测试供应商')).toBeVisible();
  });
}); 