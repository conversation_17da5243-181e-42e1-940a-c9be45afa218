const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { Procurement, Project, Supplier, User } = require('../models');

/**
 * Create a procurement
 * @param {Object} procurementBody
 * @returns {Promise<Procurement>}
 */
const createProcurement = async (procurementBody) => {
  // Check if project exists
  const project = await Project.findByPk(procurementBody.projectId);
  if (!project) {
    throw new ApiError(400, 'Project not found');
  }

  // Check if supplier exists
  const supplier = await Supplier.findByPk(procurementBody.supplierId);
  if (!supplier) {
    throw new ApiError(400, 'Supplier not found');
  }

  return Procurement.create(procurementBody);
};

/**
 * Get procurement by id
 * @param {string} id
 * @returns {Promise<Procurement>}
 */
const getProcurementById = async (id) => {
  const procurement = await Procurement.findByPk(id, {
    include: [
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: Supplier,
        attributes: ['id', 'name', 'contactName', 'contactPhone', 'contactEmail']
      },
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!procurement) {
    throw new ApiError(404, 'Procurement not found');
  }
  return procurement;
};

/**
 * Update procurement by id
 * @param {string} procurementId
 * @param {Object} updateBody
 * @returns {Promise<Procurement>}
 */
const updateProcurementById = async (procurementId, updateBody) => {
  const procurement = await getProcurementById(procurementId);

  // Check if supplier exists if being updated
  if (updateBody.supplierId) {
    const supplier = await Supplier.findByPk(updateBody.supplierId);
    if (!supplier) {
      throw new ApiError(400, 'Supplier not found');
    }
  }

  Object.assign(procurement, updateBody);
  await procurement.save();
  return procurement;
};

/**
 * Delete procurement by id
 * @param {string} procurementId
 * @returns {Promise<void>}
 */
const deleteProcurementById = async (procurementId) => {
  const procurement = await getProcurementById(procurementId);
  await procurement.destroy();
};

/**
 * Query for procurements
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Procurements and pagination info
 */
const queryProcurements = async (filter, options) => {
  const { page, limit, sortBy, sortOrder } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};
  
  if (filter.projectId) {
    whereClause.projectId = filter.projectId;
  }
  
  if (filter.supplierId) {
    whereClause.supplierId = filter.supplierId;
  }
  
  if (filter.status) {
    whereClause.status = filter.status;
  }
  
  if (filter.search) {
    whereClause[Op.or] = [
      { content: { [Op.like]: `%${filter.search}%` } },
      { description: { [Op.like]: `%${filter.search}%` } }
    ];
  }
  
  if (filter.procurementDateFrom) {
    whereClause.procurementDate = {
      ...whereClause.procurementDate,
      [Op.gte]: new Date(filter.procurementDateFrom)
    };
  }
  
  if (filter.procurementDateTo) {
    whereClause.procurementDate = {
      ...whereClause.procurementDate,
      [Op.lte]: new Date(filter.procurementDateTo)
    };
  }

  // Query with pagination
  const { count, rows } = await Procurement.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: Supplier,
        attributes: ['id', 'name', 'contactName', 'contactPhone', 'contactEmail']
      },
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ],
    order: [[sortBy, sortOrder]],
    limit,
    offset,
    distinct: true
  });

  return {
    results: rows,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

module.exports = {
  createProcurement,
  getProcurementById,
  updateProcurementById,
  deleteProcurementById,
  queryProcurements
}; 