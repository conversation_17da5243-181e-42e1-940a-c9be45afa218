version: '3.8'

services:
  app:
    image: yihe-app:latest
    container_name: yihe-app
    restart: unless-stopped
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=production
      - DB_DIALECT=postgres
      - DB_HOST=${DB_HOST:-localhost}
      - DB_PORT=${DB_PORT:-3306}
      - DB_NAME=${DB_NAME:-yihe}
      - DB_USER=${DB_USER:-root}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - JWT_SECRET=${JWT_SECRET:-your-jwt-secret}
      - JWT_ACCESS_EXPIRATION_MINUTES=${JWT_ACCESS_EXPIRATION_MINUTES:-10080}
      - JWT_REFRESH_EXPIRATION_DAYS=${JWT_REFRESH_EXPIRATION_DAYS:-30}
      - JWT_RESET_PASSWORD_EXPIRATION_MINUTES=${JWT_RESET_PASSWORD_EXPIRATION_MINUTES:-10}
      - JWT_VERIFY_EMAIL_EXPIRATION_MINUTES=${JWT_VERIFY_EMAIL_EXPIRATION_MINUTES:-10}
      - SMTP_HOST=${SMTP_HOST:-smtp.qq.com}
      - SMTP_PORT=${SMTP_PORT:-587}
      - SMTP_USERNAME=${SMTP_USERNAME:-<EMAIL>}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAIL_FROM=${EMAIL_FROM:-<EMAIL>}
    volumes:
      - ../uploads:/app/uploads
      - ../logs:/app/logs
      - ../database.sqlite:/app/database.sqlite
