const Joi = require('joi');

const createDocumentSchema = {
  body: Joi.object().keys({
    title: Joi.string().required().min(1).max(255).messages({
      'string.empty': '文档标题不能为空',
      'string.min': '文档标题至少需要{#limit}个字符',
      'string.max': '文档标题不能超过{#limit}个字符',
      'any.required': '文档标题是必填项'
    }),
    content: Joi.string().allow('', null),
    summary: Joi.string().allow('', null),
    categoryId: Joi.string().uuid().required().messages({
      'string.empty': '分类编号不能为空',
      'string.guid': '分类编号必须是有效的UUID格式',
      'any.required': '分类编号是必填项'
    }),
    status: Joi.string().valid('draft', 'published', 'archived').default('draft').messages({
      'any.only': '状态必须是草稿(draft)、已发布(published)或已存档(archived)'
    }),
    isPublic: Joi.boolean().default(false).messages({
      'boolean.base': '公开状态必须是布尔值'
    }),
    publishDate: Joi.date().allow(null).messages({
      'date.base': '发布日期格式不正确'
    }),
    documentType: Joi.string().valid('article', 'procedure', 'policy', 'guide', 'faq', 'other').default('article').messages({
      'any.only': '文档类型必须是文章(article)、程序(procedure)、政策(policy)、指南(guide)、常见问题(faq)或其他(other)'
    }),
    priority: Joi.string().valid('low', 'medium', 'high', 'urgent').default('medium').messages({
      'any.only': '优先级必须是低(low)、中(medium)、高(high)或紧急(urgent)'
    }),
    version: Joi.string().default('1.0'),
    metaData: Joi.object().allow(null),
    tags: Joi.array().items(Joi.string().uuid().messages({
      'string.guid': '标签编号必须是有效的UUID格式'
    })).allow(null)
  })
};

const updateDocumentSchema = {
  params: Joi.object().keys({
    documentId: Joi.string().uuid().required().messages({
      'string.empty': '文档编号不能为空',
      'string.guid': '文档编号必须是有效的UUID格式',
      'any.required': '文档编号是必填项'
    })
  }),
  body: Joi.object().keys({
    title: Joi.string().min(1).max(255).messages({
      'string.empty': '文档标题不能为空',
      'string.min': '文档标题至少需要{#limit}个字符',
      'string.max': '文档标题不能超过{#limit}个字符'
    }),
    content: Joi.string().allow('', null),
    summary: Joi.string().allow('', null),
    categoryId: Joi.string().uuid().messages({
      'string.guid': '分类编号必须是有效的UUID格式'
    }),
    status: Joi.string().valid('draft', 'published', 'archived').messages({
      'any.only': '状态必须是草稿(draft)、已发布(published)或已存档(archived)'
    }),
    isPublic: Joi.boolean().messages({
      'boolean.base': '公开状态必须是布尔值'
    }),
    publishDate: Joi.date().allow(null).messages({
      'date.base': '发布日期格式不正确'
    }),
    documentType: Joi.string().valid('article', 'procedure', 'policy', 'guide', 'faq', 'other').messages({
      'any.only': '文档类型必须是文章(article)、程序(procedure)、政策(policy)、指南(guide)、常见问题(faq)或其他(other)'
    }),
    priority: Joi.string().valid('low', 'medium', 'high', 'urgent').messages({
      'any.only': '优先级必须是低(low)、中(medium)、高(high)或紧急(urgent)'
    }),
    version: Joi.string().allow(null),
    metaData: Joi.object().allow(null),
    tags: Joi.array().items(Joi.string().uuid().messages({
      'string.guid': '标签编号必须是有效的UUID格式'
    })).allow(null)
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const getDocumentSchema = {
  params: Joi.object().keys({
    documentId: Joi.string().uuid().required().messages({
      'string.empty': '文档编号不能为空',
      'string.guid': '文档编号必须是有效的UUID格式',
      'any.required': '文档编号是必填项'
    })
  })
};

const deleteDocumentSchema = {
  params: Joi.object().keys({
    documentId: Joi.string().uuid().required().messages({
      'string.empty': '文档编号不能为空',
      'string.guid': '文档编号必须是有效的UUID格式',
      'any.required': '文档编号是必填项'
    })
  })
};

const getDocumentsSchema = {
  query: Joi.object().keys({
    categoryId: Joi.string().uuid().allow('', null).messages({
      'string.guid': '分类编号必须是有效的UUID格式'
    }),
    authorId: Joi.string().uuid().allow('', null).messages({
      'string.guid': '作者编号必须是有效的UUID格式'
    }),
    status: Joi.string().valid('draft', 'published', 'archived', 'deleted').allow('', null).messages({
      'any.only': '状态必须是草稿(draft)、已发布(published)、已存档(archived)或已删除(deleted)'
    }),
    documentType: Joi.string().allow('', null),
    priority: Joi.string().allow('', null),
    isPublic: Joi.boolean().allow('', null).messages({
      'boolean.base': '公开状态必须是布尔值'
    }),
    search: Joi.string().allow('', null),
    tags: Joi.array().items(Joi.string().uuid().messages({
      'string.guid': '标签编号必须是有效的UUID格式'
    })).single().allow(null),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于{#limit}'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    }),
    sortBy: Joi.string().valid('title', 'createdAt', 'updatedAt', 'publishDate', 'viewCount').default('updatedAt').messages({
      'any.only': '排序字段必须是标题(title)、创建时间(createdAt)、更新时间(updatedAt)、发布时间(publishDate)或查看次数(viewCount)'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    })
  })
};

const addTagsSchema = {
  params: Joi.object().keys({
    documentId: Joi.string().uuid().required().messages({
      'string.empty': '文档编号不能为空',
      'string.guid': '文档编号必须是有效的UUID格式',
      'any.required': '文档编号是必填项'
    })
  }),
  body: Joi.object().keys({
    tagIds: Joi.array().items(Joi.string().uuid().messages({
      'string.guid': '标签编号必须是有效的UUID格式'
    })).required().messages({
      'array.base': '标签列表必须是数组',
      'any.required': '标签列表是必填项'
    })
  })
};

const removeTagsSchema = {
  params: Joi.object().keys({
    documentId: Joi.string().uuid().required().messages({
      'string.empty': '文档编号不能为空',
      'string.guid': '文档编号必须是有效的UUID格式',
      'any.required': '文档编号是必填项'
    })
  }),
  body: Joi.object().keys({
    tagIds: Joi.array().items(Joi.string().uuid().messages({
      'string.guid': '标签编号必须是有效的UUID格式'
    })).required().messages({
      'array.base': '标签列表必须是数组',
      'any.required': '标签列表是必填项'
    })
  })
};

const getDocumentCommentsSchema = {
  params: Joi.object().keys({
    documentId: Joi.string().uuid().required().messages({
      'string.empty': '文档编号不能为空',
      'string.guid': '文档编号必须是有效的UUID格式',
      'any.required': '文档编号是必填项'
    })
  })
};

module.exports = {
  createDocumentSchema,
  updateDocumentSchema,
  getDocumentSchema,
  deleteDocumentSchema,
  getDocumentsSchema,
  addTagsSchema,
  removeTagsSchema,
  getDocumentCommentsSchema
};