/* Page Layout Components */
.page-header {
  @apply flex justify-between items-center mb-8 bg-white shadow-sm rounded-lg p-6;
  animation: fadeSlideIn 0.6s ease-out;
}

.page-title {
  @apply text-2xl font-bold text-gray-900;
}

.page-subtitle {
  @apply text-gray-500 mt-1;
}

.filter-card {
  @apply bg-white rounded-lg shadow-sm p-6 mb-6 mb-8 p-6 shadow-md;
  animation: fadeSlideIn 0.6s ease-out;
  animation-delay: 0.1s;
}

.card {
  @apply bg-white rounded-lg shadow-sm p-6 mb-6;
  animation: fadeSlideIn 0.6s ease-out;
  animation-delay: 0.2s;
}

/* Animation Keyframes */
@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Elements */
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply w-full rounded-lg shadow-sm border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50;
}

/* Buttons */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:ring-blue-500;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

/* Badges */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-blue {
  @apply bg-blue-100 text-blue-800;
}

.badge-green, .badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-red, .badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-yellow, .badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-purple {
  @apply bg-purple-100 text-purple-800;
}

.badge-gray {
  @apply bg-gray-100 text-gray-800;
}

/* Table Styles */
.table-container {
  @apply overflow-hidden rounded-lg shadow;
}

/* Common Animation Classes */
.hover-lift {
  @apply transition-all duration-200;
}

.hover-lift:hover {
  @apply transform -translate-y-1 shadow-md;
}

/* Empty State Styles */
.empty-state {
  @apply flex flex-col items-center justify-center py-12 text-center;
}

.empty-state-icon {
  @apply w-16 h-16 text-gray-400 mb-4;
}

.empty-state-title {
  @apply text-xl font-medium text-gray-900 mb-2;
}

.empty-state-description {
  @apply text-sm text-gray-600 max-w-md mx-auto mb-6;
}