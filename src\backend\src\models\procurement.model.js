const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Procurement = sequelize.define('procurement', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  projectId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'project',
      key: 'id'
    }
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: false
  },
  supplierId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'supplier',
      key: 'id'
    }
  },
  quantity: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1
  },
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false
  },
  invoiceType: {
    type: DataTypes.STRING,
    allowNull: true
  },
  procurementDate: {
    type: DataTypes.DATE,
    allowNull: false
  },
  status: {
    type: DataTypes.ENUM('pending', 'approved', 'rejected', 'completed'),
    defaultValue: 'pending'
  },
  returnInfo: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  returnBatch: {
    type: DataTypes.STRING,
    allowNull: true
  },
  returnInvoiceInfo: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  paymentSchedule: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  createdBy: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'user',
      key: 'id'
    }
  }
}, {
  timestamps: true,
});

module.exports = { Procurement };