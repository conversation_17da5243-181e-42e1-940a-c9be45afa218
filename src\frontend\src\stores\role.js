import { defineS<PERSON> } from 'pinia'
import axios from '@/plugins/axios'

export const useRoleStore = defineStore('role', {
  state: () => ({
    roles: [],
    permissions: []
  }),

  actions: {
    async getRoles() {
      try {
        const response = await axios.get('/api/roles')
        this.roles = response.data
        return this.roles
      } catch (error) {
        console.error('Failed to fetch roles:', error)
        throw error
      }
    },

    async getPermissions() {
      try {
        const response = await axios.get('/api/permissions')
        this.permissions = response.data
        return this.permissions
      } catch (error) {
        console.error('Failed to fetch permissions:', error)
        throw error
      }
    },

    async createRole(roleData) {
      try {
        const response = await axios.post('/api/roles', roleData)
        this.roles.push(response.data)
        return response.data
      } catch (error) {
        console.error('Failed to create role:', error)
        throw error
      }
    },

    async updateRole(roleData) {
      try {
        const response = await axios.put(`/api/roles/${roleData.id}`, roleData)
        const index = this.roles.findIndex(role => role.id === roleData.id)
        if (index !== -1) {
          this.roles[index] = response.data
        }
        return response.data
      } catch (error) {
        console.error('Failed to update role:', error)
        throw error
      }
    },

    async deleteRole(roleId) {
      try {
        await axios.delete(`/api/roles/${roleId}`)
        this.roles = this.roles.filter(role => role.id !== roleId)
      } catch (error) {
        console.error('Failed to delete role:', error)
        throw error
      }
    }
  }
})
