const { sequelize } = require('./src/models');

async function updateTestUserRole() {
  try {
    // 更新测试用户的角色
    const result = await sequelize.query(
      'UPDATE "user" SET role = :role, "isAdmin" = :isAdmin WHERE email = :email',
      {
        replacements: { 
          role: 'admin',
          isAdmin: true,
          email: '<EMAIL>'
        },
        type: sequelize.QueryTypes.UPDATE
      }
    );
    
    console.log('User role updated successfully');
    console.log('Rows affected:', result[1]);
    
    // 关闭数据库连接
    await sequelize.close();
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

updateTestUserRole();
