/**
 * 通知服务 - 用于在整个应用程序中显示通知消息
 * 使用Pinia状态管理，使得应用程序中的任何组件都可以触发通知
 */

import { useNotificationStore } from '@/stores/notification';

/**
 * 获取通知存储
 * @returns {Object} 通知存储实例
 */
const getStore = () => {
  return useNotificationStore();
};

/**
 * 添加新通知到列表
 * @param {Object} notification 通知对象
 */
const addNotification = (notification) => {
  const store = getStore();

  return store.addNotification({
    type: notification.type || 'info',
    message: notification.message || '',
    timeout: notification.duration || 5000,
    dismissible: notification.dismissible !== false
  });
};

/**
 * 从列表中移除通知
 * @param {string} id 通知ID
 */
const removeNotification = (id) => {
  const store = getStore();
  store.removeNotification(id);
};

/**
 * 移除所有通知
 */
const clearAllNotifications = () => {
  const store = getStore();
  store.clearAll();
};

/**
 * 显示成功通知
 * @param {string} message 通知消息
 * @param {string} description 详细描述（可选）
 * @param {number} duration 持续时间（毫秒）
 * @returns {string} 通知ID
 */
const success = (message, description = '', duration = 5000) => {
  const store = getStore();
  return store.addSuccess(description ? `${message}: ${description}` : message, duration);
};

/**
 * 显示错误通知
 * @param {string} message 通知消息
 * @param {string} description 详细描述（可选）
 * @param {number} duration 持续时间（毫秒）
 * @returns {string} 通知ID
 */
const error = (message, description = '', duration = 8000) => {
  const store = getStore();
  return store.addError(description ? `${message}: ${description}` : message, duration);
};

/**
 * 显示信息通知
 * @param {string} message 通知消息
 * @param {string} description 详细描述（可选）
 * @param {number} duration 持续时间（毫秒）
 * @returns {string} 通知ID
 */
const info = (message, description = '', duration = 5000) => {
  const store = getStore();
  return store.addInfo(description ? `${message}: ${description}` : message, duration);
};

/**
 * 显示警告通知
 * @param {string} message 通知消息
 * @param {string} description 详细描述（可选）
 * @param {number} duration 持续时间（毫秒）
 * @returns {string} 通知ID
 */
const warning = (message, description = '', duration = 7000) => {
  const store = getStore();
  return store.addWarning(description ? `${message}: ${description}` : message, duration);
};

// 兼容旧版API
const state = {
  get notifications() {
    return getStore().notifications;
  }
};

export default {
  // 状态 (兼容旧版API)
  state,

  // 操作方法
  addNotification,
  removeNotification,
  clearAllNotifications,

  // 快捷方法
  success,
  error,
  info,
  warning
};