const axios = require('axios');

// 配置
const API_URL = 'http://localhost:3005';
const API_ENDPOINTS = {
  reimbursements: '/api/reimbursements',
  subcontracts: '/api/subcontracts',
  workhours: '/api/workhours',
  workhourStatistics: '/api/workhours/statistics',
  workhourCalendar: '/api/workhours/calendar'
};

// 测试函数
async function testEndpoint(endpoint, method = 'GET', data = null) {
  try {
    console.log(`\n测试 ${method} ${endpoint}`);
    
    let response;
    if (method === 'GET') {
      response = await axios.get(`${API_URL}${endpoint}`);
    } else if (method === 'POST') {
      response = await axios.post(`${API_URL}${endpoint}`, data);
    } else if (method === 'PUT') {
      response = await axios.put(`${API_URL}${endpoint}`, data);
    } else if (method === 'DELETE') {
      response = await axios.delete(`${API_URL}${endpoint}`);
    }
    
    console.log(`状态码: ${response.status}`);
    console.log('响应数据:', JSON.stringify(response.data, null, 2).substring(0, 300) + '...');
    return { success: true, data: response.data };
  } catch (error) {
    console.error(`测试 ${method} ${endpoint} 失败:`, error.message);
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误响应:', error.response.data);
    }
    return { success: false, error };
  }
}

// 主测试函数
async function runTests() {
  console.log('开始API测试...');
  
  // 测试报销API
  console.log('\n===== 测试报销API =====');
  const reimbursements = await testEndpoint(API_ENDPOINTS.reimbursements);
  
  if (reimbursements.success && reimbursements.data.results && reimbursements.data.results.length > 0) {
    const reimbursementId = reimbursements.data.results[0].id;
    await testEndpoint(`${API_ENDPOINTS.reimbursements}/${reimbursementId}`);
  }
  
  // 测试分包合同API
  console.log('\n===== 测试分包合同API =====');
  const subcontracts = await testEndpoint(API_ENDPOINTS.subcontracts);
  
  if (subcontracts.success && subcontracts.data.results && subcontracts.data.results.length > 0) {
    const subcontractId = subcontracts.data.results[0].id;
    await testEndpoint(`${API_ENDPOINTS.subcontracts}/${subcontractId}`);
  }
  
  // 测试工时记录API
  console.log('\n===== 测试工时记录API =====');
  const workhours = await testEndpoint(API_ENDPOINTS.workhours);
  
  if (workhours.success && workhours.data.results && workhours.data.results.length > 0) {
    const workhourId = workhours.data.results[0].id;
    await testEndpoint(`${API_ENDPOINTS.workhours}/${workhourId}`);
  }
  
  // 测试工时统计API
  console.log('\n===== 测试工时统计API =====');
  await testEndpoint(API_ENDPOINTS.workhourStatistics);
  
  // 测试工时日历API
  console.log('\n===== 测试工时日历API =====');
  await testEndpoint(`${API_ENDPOINTS.workhourCalendar}?month=${new Date().toISOString().slice(0, 7)}`);
  
  console.log('\n测试完成!');
}

// 运行测试
runTests().catch(error => {
  console.error('测试过程中发生错误:', error);
});
