<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">工时记录</h1>
        <p class="page-subtitle">创建新的工时记录</p>
      </div>
      <router-link to="/workhours" class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        返回列表
      </router-link>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="card shadow-md p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">加载中...</p>
    </div>

    <!-- 表单区域 -->
    <div v-else class="card shadow-md">

      <form @submit.prevent="validateAndSubmit" class="space-y-6">
          <!-- 基本信息区块 -->
          <div class="form-section">
            <h2 class="form-section-title">基本信息</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 日期 -->
              <div>
                <label for="date" class="form-label">日期 <span class="text-red-500">*</span></label>
                <input
                  type="date"
                  id="date"
                  v-model="workHourForm.date"
                  class="form-input"
                  required
                  max="3000-12-31"
                >
              </div>

              <!-- 开始时间 -->
              <div>
                <label for="startTime" class="block text-sm font-medium text-gray-700">开始时间 <span class="text-red-500">*</span></label>
                <input
                  type="time"
                  id="startTime"
                  v-model="workHourForm.startTime"
                  class="form-input"
                  required
                  @change="calculateWorkHours"
                >
              </div>

              <!-- 结束时间 -->
              <div>
                <label for="endTime" class="block text-sm font-medium text-gray-700">结束时间 <span class="text-red-500">*</span></label>
                <input
                  type="time"
                  id="endTime"
                  v-model="workHourForm.endTime"
                  class="form-input"
                  required
                  @change="calculateWorkHours"
                >
              </div>

              <!-- 工作时长 -->
              <div>
                <label for="workHours" class="block text-sm font-medium text-gray-700">工作时长(小时) <span class="text-red-500">*</span></label>
                <input
                  type="number"
                  id="workHours"
                  v-model="workHourForm.workHours"
                  class="form-input"
                  step="0.5"
                  min="0.5"
                  max="24"
                  required
                >
              </div>

              <!-- 工作地点 -->
              <div>
                <label for="workLocation" class="block text-sm font-medium text-gray-700">工作地点</label>
                <select
                  id="workLocation"
                  v-model="workHourForm.workLocation"
                  class="form-input"
                >
                  <option value="办公室">办公室</option>
                  <option value="现场">现场</option>
                  <option value="远程">远程</option>
                  <option value="其他">其他</option>
                </select>
              </div>

              <!-- 加班标记 -->
              <div class="flex items-center h-full pt-6">
                <input
                  type="checkbox"
                  id="overtime"
                  v-model="workHourForm.overtime"
                  class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                >
                <label for="overtime" class="ml-2 block text-sm text-gray-700">加班</label>
              </div>
            </div>
          </div>

          <!-- 员工信息区块 -->
          <div class="form-section">
            <h2 class="form-section-title">员工信息</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 员工姓名 -->
              <div>
                <label for="employeeName" class="block text-sm font-medium text-gray-700">员工姓名 <span class="text-red-500">*</span></label>
                <div class="relative">
                  <input
                    type="text"
                    id="employeeNameFilter"
                    v-model="employeeFilter"
                    class="form-input mb-1 border-blue-400 focus:border-blue-600 focus:ring-blue-600"
                    placeholder="搜索并选择员工，部门和用工类型将自动填充"
                    @focus="showEmployeeDropdown = true"
                    @input="filterEmployees"
                  />
                  <div class="text-xs text-gray-500 mb-2">先选择员工，部门和用工类型将自动填充</div>
                  <div v-if="showEmployeeDropdown" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                    <div v-if="filteredEmployees.length === 0" class="px-4 py-2 text-gray-500">
                      未找到匹配的员工
                    </div>
                    <div
                      v-for="employee in filteredEmployees"
                      :key="employee.id"
                      class="px-4 py-2 hover:bg-blue-50 cursor-pointer"
                      @click="selectEmployee(employee)"
                    >
                      {{ employee.name }} 
                      <span class="text-gray-600 text-sm">({{ employee.department || '无部门' }} | {{ employee.employeeType === 'contract' ? '合同工' : '临时工' }})</span>
                    </div>
                  </div>
                </div>
                <input
                  type="text"
                  id="employeeName"
                  v-model="workHourForm.employeeName"
                  class="form-input"
                  readonly
                  placeholder="已选择的员工"
                />
                <!-- 员工ID作为隐藏字段 -->
                <input
                  type="hidden"
                  id="employeeId"
                  v-model="workHourForm.employeeId"
                >
              </div>

              <!-- 部门 -->
              <div>
                <label for="department" class="block text-sm font-medium text-gray-700">部门</label>
                <input
                  type="text"
                  id="department"
                  v-model="workHourForm.department"
                  class="form-input bg-gray-50"
                  readonly
                  placeholder="选择员工后自动填充"
                >
              </div>

              <!-- 用工类型 -->
              <div>
                <label for="employmentType" class="block text-sm font-medium text-gray-700">用工类型 <span class="text-red-500">*</span></label>
                <input
                  type="text"
                  id="employmentType"
                  v-model="workHourForm.employmentType"
                  class="form-input bg-gray-50"
                  readonly
                  required
                  placeholder="选择员工后自动填充"
                >
              </div>

              <!-- 缺勤标记 -->
              <div class="flex items-center h-full pt-6">
                <input
                  type="checkbox"
                  id="absence"
                  v-model="workHourForm.absence"
                  class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                >
                <label for="absence" class="ml-2 block text-sm text-gray-700">缺勤</label>
              </div>
            </div>
          </div>

          <!-- 项目信息区块 -->
          <div class="form-section">
            <h2 class="form-section-title">项目信息</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 项目名称 -->
              <div>
                <label for="projectName" class="block text-sm font-medium text-gray-700">项目名称 <span class="text-red-500">*</span></label>
                <select
                  id="projectName"
                  v-model="workHourForm.projectName"
                  class="form-input"
                  required
                >
                  <option value="">选择项目</option>
                  <option v-for="project in projects" :key="project.id" :value="project.name">{{ project.name }}</option>
                </select>
              </div>

              <!-- 备注 -->
              <div>
                <label for="remarks" class="block text-sm font-medium text-gray-700">备注</label>
                <textarea
                  id="remarks"
                  v-model="workHourForm.remarks"
                  rows="3"
                  class="form-input"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- 薪酬信息区块 -->
          <div class="form-section">
            <h2 class="form-section-title">薪酬信息</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- 小时工资 -->
              <div>
                <label for="hourlyRate" class="form-label">小时工资(元) <span class="text-red-500">*</span></label>
                <div class="mt-1 relative rounded-md shadow-sm">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span class="text-gray-500 sm:text-sm">¥</span>
                  </div>
                  <input
                    type="number"
                    id="hourlyRate"
                    v-model="workHourForm.hourlyRate"
                    class="form-input pl-8"
                    step="0.01"
                    min="0"
                    required
                    @change="calculatePayableAmount"
                  >
                </div>
              </div>

              <!-- 日工资 -->
              <div>
                <label for="dailyWage" class="form-label">日工资(元)</label>
                <div class="mt-1 relative rounded-md shadow-sm">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span class="text-gray-500 sm:text-sm">¥</span>
                  </div>
                  <input
                    type="number"
                    id="dailyWage"
                    v-model="workHourForm.dailyWage"
                    class="form-input pl-8"
                    step="0.01"
                    min="0"
                    @change="calculateHourlyRate"
                  >
                </div>
              </div>

              <!-- 应付金额 -->
              <div>
                <label for="payableAmount" class="form-label">应付金额(元) <span class="text-red-500">*</span></label>
                <div class="mt-1 relative rounded-md shadow-sm">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span class="text-gray-500 sm:text-sm">¥</span>
                  </div>
                  <input
                    type="number"
                    id="payableAmount"
                    v-model="workHourForm.payableAmount"
                    class="form-input pl-8"
                    step="0.01"
                    min="0"
                    required
                  >
                </div>
              </div>
            </div>
          </div>

          <!-- 审批信息区块 -->
          <div class="form-section">
            <h2 class="form-section-title">审批信息</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <!-- 员工签字 -->
              <div class="flex items-center">
                <input
                  type="checkbox"
                  id="employeeSign"
                  v-model="workHourForm.employeeSign"
                  class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                >
                <label for="employeeSign" class="ml-2 block text-sm text-gray-700">员工已签字</label>
              </div>

              <!-- 主管签字 -->
              <div class="flex items-center">
                <input
                  type="checkbox"
                  id="supervisorSign"
                  v-model="workHourForm.supervisorSign"
                  class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                >
                <label for="supervisorSign" class="ml-2 block text-sm text-gray-700">主管已签字</label>
              </div>

              <!-- 绩效等级 -->
              <div>
                <label for="performanceLevel" class="block text-sm font-medium text-gray-700">绩效等级</label>
                <select
                  id="performanceLevel"
                  v-model="workHourForm.performanceLevel"
                  class="form-input"
                >
                  <option value="">未评定</option>
                  <option value="A">A - 优秀</option>
                  <option value="B">B - 良好</option>
                  <option value="C">C - 一般</option>
                  <option value="D">D - 较差</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 提交按钮 -->
          <div class="flex justify-end space-x-4 border-t pt-6">
            <button
              type="button"
              @click="resetForm"
              class="btn btn-secondary"
            >
              重置
            </button>
            <button
              type="submit"
              class="btn btn-primary"
            >
              提交记录
            </button>
          </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from 'vue-toastification';

// 服务导入
import workhoursService from '@/services/workhours.service';
import projectService from '@/services/project.service';
import { useDepartmentStore } from '@/stores/department';
import apiService from '@/services/apiService';

const router = useRouter();
const toast = useToast();

// 加载和错误状态
const loading = ref(false);
const error = ref('');

// 项目列表和员工列表
const projects = ref([]);
const employees = ref([]);

// 员工筛选相关
const employeeFilter = ref('');
const showEmployeeDropdown = ref(false);
const filteredEmployees = ref([]);

// 点击页面其他地方关闭下拉框
const closeDropdownOnOutsideClick = (event) => {
  const dropdown = document.getElementById('employeeNameFilter');
  if (dropdown && !dropdown.contains(event.target)) {
    showEmployeeDropdown.value = false;
  }
};

// 添加和移除全局点击事件监听器
onMounted(() => {
  document.addEventListener('click', closeDropdownOnOutsideClick);
});

// 组件卸载时移除事件监听器
const onBeforeUnmount = () => {
  document.removeEventListener('click', closeDropdownOnOutsideClick);
};

// 部门存储
const departmentStore = useDepartmentStore();
const departmentOptions = computed(() => departmentStore.getDepartmentOptions);

// 工时表单数据
const workHourForm = ref({
  // 员工信息
  employeeName: '',
  employeeId: '',
  department: '',
  // 基本信息
  date: new Date().toISOString().split('T')[0],
  startTime: '09:00',
  endTime: '18:00',
  workHours: 8,
  employmentType: '合同工',
  workLocation: '办公室',
  // 项目信息
  projectName: '',
  remarks: '',
  // 薪酬信息
  hourlyRate: 100,
  payableAmount: 800,
  dailyWage: 800,
  overtime: false,
  absence: false,
  // 审批信息
  employeeSign: false,
  supervisorSign: false,
  performanceLevel: '',
});

// 计算工作时长
const calculateWorkHours = () => {
  if (workHourForm.value.startTime && workHourForm.value.endTime) {
    const start = new Date(`2000-01-01T${workHourForm.value.startTime}`);
    const end = new Date(`2000-01-01T${workHourForm.value.endTime}`);

    // 如果结束时间小于开始时间，认为是跨天工作
    if (end < start) {
      end.setDate(end.getDate() + 1);
    }

    const diffMs = end - start;
    const diffHours = diffMs / (1000 * 60 * 60);

    // 减去午休时间（如果工作时间跨越中午12点到13点）
    let lunchBreak = 0;
    if (start.getHours() < 13 && end.getHours() >= 13) {
      lunchBreak = 1; // 1小时午休
    }

    workHourForm.value.workHours = Math.max(0, diffHours - lunchBreak).toFixed(1);
    calculatePayableAmount();
  }
};

// 根据工时和小时工资计算应付金额
const calculatePayableAmount = () => {
  const hours = parseFloat(workHourForm.value.workHours);
  const rate = parseFloat(workHourForm.value.hourlyRate);

  if (!isNaN(hours) && !isNaN(rate)) {
    // 如果是加班，可以应用加班费率（例如1.5倍）
    const overtimeMultiplier = workHourForm.value.overtime ? 1.5 : 1;
    workHourForm.value.payableAmount = (hours * rate * overtimeMultiplier).toFixed(2);
    workHourForm.value.dailyWage = (hours * rate).toFixed(2);
  }
};

// 根据日工资和工时计算小时工资
const calculateHourlyRate = () => {
  const hours = parseFloat(workHourForm.value.workHours);
  const dailyWage = parseFloat(workHourForm.value.dailyWage);

  if (!isNaN(hours) && !isNaN(dailyWage) && hours > 0) {
    workHourForm.value.hourlyRate = (dailyWage / hours).toFixed(2);
    calculatePayableAmount();
  }
};

// 根据用工类型更新工资率
const updateRateByEmploymentType = () => {
  // 更新小时工资率
  if (workHourForm.value.employmentType === '合同工') {
    workHourForm.value.hourlyRate = 120;
  } else if (workHourForm.value.employmentType === '临时工') {
    workHourForm.value.hourlyRate = 80;
  }
  
  // 更新日工资和应付金额
  const hours = parseFloat(workHourForm.value.workHours);
  const rate = parseFloat(workHourForm.value.hourlyRate);
  
  if (!isNaN(hours) && !isNaN(rate)) {
    // 设置日工资
    workHourForm.value.dailyWage = (hours * rate).toFixed(2);
    
    // 如果是加班，应用加班费率（1.5倍）
    const overtimeMultiplier = workHourForm.value.overtime ? 1.5 : 1;
    workHourForm.value.payableAmount = (hours * rate * overtimeMultiplier).toFixed(2);
  }
};

// 获取项目列表
const fetchProjects = async () => {
  loading.value = true;
  error.value = '';
  try {
    // 获取进行中的项目
    const response = await projectService.getProjects({status: 'in_progress'});
    projects.value = response.data.results || [];
  } catch (err) {
    console.error('获取项目列表失败:', err);
    error.value = '获取项目列表失败: ' + (err.response?.data?.message || err.message);
    toast.error(error.value);
  } finally {
    loading.value = false;
  }
};

// 验证表单并提交工时记录
const validateAndSubmit = () => {
  // 验证必填字段
  const requiredFields = [
    { field: 'employeeName', label: '员工姓名' },
    { field: 'employeeId', label: '员工编号' },
    { field: 'date', label: '日期' },
    { field: 'startTime', label: '开始时间' },
    { field: 'endTime', label: '结束时间' },
    { field: 'workHours', label: '工作时长' },
    { field: 'projectName', label: '项目名称' },
    { field: 'employmentType', label: '用工类型' },
    { field: 'department', label: '部门' }
  ];

  for (const { field, label } of requiredFields) {
    if (!workHourForm.value[field]) {
      toast.error(`${label}是必填项，请先选择员工`);
      return;
    }
  }

  // 所有验证通过，提交表单
  submitWorkHours();
};

// 提交工时记录
const submitWorkHours = async () => {
  loading.value = true;
  error.value = '';
  try {
    console.log('提交的数据:', workHourForm.value);
    await workhoursService.createWorkHour(workHourForm.value);
    toast.success('工时记录提交成功');
    router.push('/workhours');
  } catch (err) {
    console.error('提交工时记录失败:', err);
    error.value = '提交工时记录失败: ' + (err.response?.data?.message || err.message);
    toast.error(error.value);
    loading.value = false; // 只在错误时在这里设置为false，成功时会跳转页面
  }
};

// 重置表单
const resetForm = () => {
  workHourForm.value = {
    employeeName: '',
    employeeId: '',
    department: '',
    date: new Date().toISOString().split('T')[0],
    startTime: '09:00',
    endTime: '18:00',
    workHours: 8,
    employmentType: '合同工',
    workLocation: '办公室',
    projectName: '',
    remarks: '',
    hourlyRate: 100,
    payableAmount: 800,
    dailyWage: 800,
    overtime: false,
    absence: false,
    employeeSign: false,
    supervisorSign: false,
    performanceLevel: '',
  };
};

// 返回工时列表页
const goToWorkHoursList = () => {
  router.push('/workhours');
};

// 筛选员工
const filterEmployees = () => {
  if (!employeeFilter.value) {
    filteredEmployees.value = employees.value;
    return;
  }

  const searchTerm = employeeFilter.value.toLowerCase();
  filteredEmployees.value = employees.value.filter(emp =>
    emp.name.toLowerCase().includes(searchTerm) ||
    (emp.department && emp.department.toLowerCase().includes(searchTerm))
  );
};

// 处理员工选择
const selectEmployee = (employee) => {
  workHourForm.value.employeeId = employee.id;
  workHourForm.value.employeeName = employee.name;
  workHourForm.value.department = employee.department;
  
  // 根据员工类型设置用工类型
  if (employee.employeeType === 'contract') {
    workHourForm.value.employmentType = '合同工';
  } else if (employee.employeeType === 'temporary') {
    workHourForm.value.employmentType = '临时工';
  }
  
  // 根据用工类型更新工资率
  updateRateByEmploymentType();
  
  employeeFilter.value = '';
  showEmployeeDropdown.value = false;
};

// 处理员工选择 (保留原有函数以兼容其他地方的调用)
const handleEmployeeChange = () => {
  const selectedEmployee = employees.value.find(emp => emp.id === workHourForm.value.employeeId);
  if (selectedEmployee) {
    workHourForm.value.employeeName = selectedEmployee.name;
    workHourForm.value.department = selectedEmployee.department;
    
    // 根据员工类型设置用工类型
    if (selectedEmployee.employeeType === 'contract') {
      workHourForm.value.employmentType = '合同工';
    } else if (selectedEmployee.employeeType === 'temporary') {
      workHourForm.value.employmentType = '临时工';
    }
    
    // 根据用工类型更新工资率
    updateRateByEmploymentType();
  }
};

// 获取员工列表
const fetchEmployees = async () => {
  loading.value = true;
  error.value = '';
  try {
    const response = await apiService.getAllEmployees({ limit: 100 });
    employees.value = response.results || [];
    filteredEmployees.value = employees.value; // 初始化筛选列表
    
    // 格式化员工数据，确保必要字段存在
    employees.value = employees.value.map(emp => ({
      ...emp,
      // 确保部门字段存在
      department: emp.department || '未分配',
      // 确保员工类型字段存在
      employeeType: emp.employeeType || 'contract'
    }));
  } catch (err) {
    console.error('获取员工列表失败:', err);
    error.value = '获取员工列表失败: ' + (err.response?.data?.message || err.message);
    toast.error(error.value);
  } finally {
    loading.value = false;
  }
};

// 页面加载时获取项目列表、员工列表和部门数据
onMounted(() => {
  // 添加点击事件监听器，用于关闭下拉框
  document.addEventListener('click', closeDropdownOnOutsideClick);
  
  // 获取初始数据
  fetchProjects();
  fetchEmployees();
  departmentStore.fetchDepartments();
  
  // 计算初始的工作时长和应付金额
  calculateWorkHours();
});
</script>

<style scoped>
.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.page-header {
  @apply flex justify-between items-center mb-6;
  animation: fadeSlideIn 0.5s ease-out;
}

.card {
  @apply bg-white rounded-xl p-6 mb-6;
  animation: fadeSlideIn 0.6s ease-out;
}

.form-section {
  @apply border-b border-gray-200 pb-6 mb-6 last:border-0 last:pb-0 last:mb-0;
}

.form-section-title {
  @apply text-lg font-semibold text-gray-700 mb-4;
  position: relative;
}

.form-section-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: -8px;
  height: 2px;
  width: 40px;
  background-color: #3b82f6;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50;
}

.btn {
  @apply py-2 px-4 rounded-lg focus:outline-none focus:ring transition-all duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-200;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-gray-200;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 必填项标记样式 */
.text-red-500 {
  color: #ef4444;
  font-weight: bold;
  margin-left: 2px;
}

/* 表单字段悬停效果 */
.form-input:hover {
  border-color: #93c5fd;
}

/* 表单字段焦点效果 */
.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
  outline: none;
}
</style>
