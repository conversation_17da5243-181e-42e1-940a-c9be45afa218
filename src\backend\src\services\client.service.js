const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { Client, User } = require('../models');

/**
 * Create a client
 * @param {Object} clientBody
 * @returns {Promise<Client>}
 */
const createClient = async (clientBody) => {
  return Client.create(clientBody);
};

/**
 * Get client by id
 * @param {string} id
 * @returns {Promise<Client>}
 */
const getClientById = async (id) => {
  const client = await Client.findByPk(id, {
    include: [
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!client) {
    throw new ApiError(404, 'Client not found');
  }
  return client;
};

/**
 * Update client by id
 * @param {string} clientId
 * @param {Object} updateBody
 * @returns {Promise<Client>}
 */
const updateClientById = async (clientId, updateBody) => {
  const client = await getClientById(clientId);
  Object.assign(client, updateBody);
  await client.save();
  return client;
};

/**
 * Delete client by id
 * @param {string} clientId
 * @returns {Promise<void>}
 */
const deleteClientById = async (clientId) => {
  const client = await getClientById(clientId);
  await client.destroy();
};

/**
 * Query for clients
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Clients and pagination info
 */
const queryClients = async (filter, options) => {
  const { page, limit, sortBy, sortOrder } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};

  if (filter.type) {
    whereClause.type = filter.type;
  }

  if (filter.status) {
    whereClause.status = filter.status;
  }

  if (filter.search) {
    whereClause[Op.or] = [
      { name: { [Op.like]: `%${filter.search}%` } },
      { contactName: { [Op.like]: `%${filter.search}%` } },
      { phone: { [Op.like]: `%${filter.search}%` } },
      { email: { [Op.like]: `%${filter.search}%` } },
      { notes: { [Op.like]: `%${filter.search}%` } }
    ];
  }

  // Query with pagination
  const { count, rows } = await Client.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ],
    order: [[sortBy, sortOrder]],
    limit,
    offset
  });

  return {
    results: rows,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

module.exports = {
  createClient,
  getClientById,
  updateClientById,
  deleteClientById,
  queryClients
};
