const express = require('express');
const helmet = require('helmet');
const compression = require('compression');
const cors = require('cors');
const passport = require('passport');
const httpStatus = require('http-status');
const config = require('./config/config');
const morgan = require('./config/morgan');
const { jwtStrategy } = require('./config/passport');
const routes = require('./routes');
const { errorConverter, errorHandler } = require('./middlewares/error.middleware');
const ApiError = require('./utils/ApiError');
const { serve, setup } = require('./config/swagger');

const app = express();

// Set security HTTP headers with XSS protection
app.use(helmet({
  xssFilter: true,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// Enable logging in development environment
if (config.env !== 'test') {
  app.use(morgan.successHandler);
  app.use(morgan.errorHandler);
}

// Parse json request body
app.use(express.json());

// Parse urlencoded request body
app.use(express.urlencoded({ extended: true }));

// JWT authentication
app.use(passport.initialize());
passport.use('jwt', jwtStrategy);

// Enable gzip compression
app.use(compression({
  // Filter out responses that should not be compressed
  filter: (req, res) => {
    if (req.path === '/api/health') {
      // Skip compression for health check endpoint to avoid potential issues
      return false;
    }
    // Default compression behavior for other routes
    return compression.filter(req, res);
  }
}));

// Enable CORS
app.use(cors({
  origin: function(origin, callback) {
    // 允许所有来源访问，解决开发环境中的CORS问题
    callback(null, true);
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
    'Cache-Control',
    'Pragma',
    'Expires'
  ],
  exposedHeaders: ['Content-Range', 'X-Content-Range']
}));

app.options('*', cors());

// Swagger API documentation
app.use('/api-docs', serve, setup);

// API health check endpoint (no authentication required)
app.get('/api/health', (req, res) => {
  try {
    const healthData = {
      status: 'ok',
      server: 'Information Management System API',
      version: '1.0.0',
      environment: config && config.env ? config.env : 'unknown',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(healthData);
  } catch (error) {
    console.error('Health check error:', error);

    // Ensure we always have a valid status code
    const statusCode = 500;

    res.status(statusCode).json({
      status: 'error',
      success: false,
      message: error.message || 'Health check failed',
      error: {
        name: error.name,
        detail: error.message,
        code: error.code
      }
    });
  }
});

// Alias for health check endpoint to support frontend calls
app.get('/api/health-check', (req, res) => {
  try {
    const healthData = {
      status: 'ok',
      server: 'Information Management System API',
      version: '1.0.0',
      environment: config && config.env ? config.env : 'unknown',
      timestamp: new Date().toISOString(),
    };

    res.status(200).json(healthData);
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: 'error',
      success: false,
      message: error.message || 'Health check failed',
      error: {
        name: error.name,
        detail: error.message,
        code: error.code
      }
    });
  }
});

// Public test endpoint for projects (no authentication required)
app.get('/api/public/projects', async (req, res, next) => {
  try {
    console.log('Public projects endpoint called with query:', req.query);

    const { projectService } = require('./services');
    const filter = {};

    // Safely extract query parameters
    const page = req.query.page ? parseInt(req.query.page, 10) : 1;
    const limit = req.query.limit ? parseInt(req.query.limit, 10) : 10;
    const sortBy = req.query.sortBy || 'createdAt';
    const sortOrder = req.query.sortOrder || 'desc';

    // Only add valid search parameters if they exist
    if (req.query.search) filter.search = req.query.search;
    if (req.query.status) filter.status = req.query.status;
    if (req.query.manager) filter.manager = req.query.manager;

    const options = {
      page,
      limit,
      sortBy,
      sortOrder,
      include: []
    };

    console.log('Querying projects with filter:', filter, 'and options:', options);

    const result = await projectService.queryProjects(filter, options);
    console.log('Public projects query successful, returning results');
    res.status(200).json(result);
  } catch (error) {
    console.error('Public projects endpoint error:', error);

    // Check for common database schema errors
    if (error.message && error.message.includes('does not exist')) {
      const columnMatch = error.message.match(/column\s+["']?([^"'\s.]+)["']?\s+does not exist/i);
      const tableName = error.message.match(/relation\s+["']?([^"'\s.]+)["']?\s+does not exist/i);

      if (columnMatch) {
        const columnName = columnMatch[1];
        console.error(`Database schema error: Column '${columnName}' does not exist`);
        return res.status(500).json({
          success: false,
          message: "Database schema error",
          error: {
            name: error.name,
            detail: `Column '${columnName}' referenced in query does not exist in the database.`,
            code: error.parent?.code
          }
        });
      } else if (tableName) {
        const table = tableName[1];
        console.error(`Database schema error: Table '${table}' does not exist`);
        return res.status(500).json({
          success: false,
          message: "Database schema error",
          error: {
            name: error.name,
            detail: `Table '${table}' referenced in query does not exist in the database.`,
            code: error.parent?.code
          }
        });
      }
    }

    // Send a clearer error message about database schema issues
    if (error.name === 'SequelizeDatabaseError') {
      return res.status(500).json({
        success: false,
        message: "Database error",
        error: {
          name: error.name,
          detail: error.message,
          code: error.parent?.code
        }
      });
    }

    // 处理Sequelize唯一约束错误
    if (error.name === 'SequelizeUniqueConstraintError' && error.fields) {
      const field = Object.keys(error.fields)[0];
      const value = error.fields[field];
      return res.status(400).json({
        success: false,
        message: `${field} '${value}' 已存在`,
        error: {
          name: error.name,
          fields: error.fields,
          code: error.parent?.code || 'UNIQUE_CONSTRAINT_ERROR'
        }
      });
    }

    // Handle other errors
    return res.status(500).json({
      success: false,
      message: "服务器内部错误",
      error: {
        name: error.name,
        detail: error.message,
        code: error.parent?.code
      }
    });
  }
});

// Set up API routes
app.use('/api', routes);

// Handle 404 errors for non-existent API endpoints
app.use((req, res, next) => {
  next(new ApiError(404, 'Not found'));
});

// Convert errors to ApiError, if needed
app.use(errorConverter);

// Handle error responses
app.use(errorHandler);

module.exports = app;