<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">{{ isEdit ? '编辑合同' : '新建合同' }}</h1>
        <p class="page-subtitle">{{ isEdit ? '修改现有合同记录' : '创建新的合同记录' }}</p>
      </div>
      <router-link to="/contracts" class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        返回列表
      </router-link>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误提示：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
      <div v-if="error.includes('保存失败')" class="mt-2 text-sm">
        <p>可能的原因：</p>
        <ul class="list-disc ml-5 mt-1">
          <li>该合同编号可能已被使用</li>
          <li>网络连接暂时中断</li>
          <li>服务器暂时不可用，请稍后再试</li>
        </ul>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">操作成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="card shadow-md p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">正在{{ isEdit ? '获取合同信息' : '准备表单' }}，请稍候...</p>
      <p class="text-gray-500 text-sm mt-2">系统正在{{ isEdit ? '从数据库读取合同详细信息' : '初始化新建合同表单' }}，这可能需要几秒钟时间</p>
    </div>

    <!-- 表单区域 -->
    <form v-else id="contract-form" @submit.prevent="submitForm" class="space-y-8 bg-white p-8 rounded-lg shadow">
      <!-- 基本信息 -->
      <div class="form-section">
        <h2 class="form-section-title">基本信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="contract-number" class="form-label">合同编号 <span class="form-required">*</span></label>
            <input
              type="text"
              id="contract-number"
              v-model="contractForm.number"
              class="form-input"
              placeholder="系统将自动生成编号，您也可以自行修改"
              required
              oninvalid="this.setCustomValidity('请输入合同编号')"
              oninput="this.setCustomValidity('')"
            />
            <span class="text-xs text-gray-500 mt-1 block">系统自动生成唯一编号，也可手动输入</span>
          </div>
          <div>
            <label for="contract-name" class="form-label">合同名称 <span class="form-required">*</span></label>
            <input
              type="text"
              id="contract-name"
              v-model="contractForm.name"
              class="form-input"
              placeholder="请输入合同名称"
              required
              oninvalid="this.setCustomValidity('请输入合同名称')"
              oninput="this.setCustomValidity('')"
            />
            <span class="text-xs text-gray-500 mt-1 block">请输入能清晰描述合同内容和目的的名称</span>
          </div>
          <div>
            <label for="contract-type" class="form-label">合同类型 <span class="form-required">*</span></label>
            <select id="contract-type" v-model="contractForm.contractType" class="form-input" required oninvalid="this.setCustomValidity('请选择合同类型')" oninput="this.setCustomValidity('')">
              <option value="" disabled>-- 选择类型 --</option>
              <option value="sales">销售合同</option>
              <option value="purchase">采购合同</option>
              <option value="service">服务合同</option>
              <option value="labor">劳务合同</option>
              <option value="lease">租赁合同</option>
            </select>
            <span class="text-xs text-gray-500 mt-1 block">选择最符合合同性质的类型</span>
          </div>
          <div>
            <label for="status" class="form-label">状态</label>
            <select id="status" v-model="contractForm.status" class="form-input">
              <option value="draft">草稿</option>
              <option value="reviewing">审核中</option>
              <option value="active">已生效</option>
              <option value="expired">已到期</option>
              <option value="terminated">已终止</option>
            </select>
            <span class="text-xs text-gray-500 mt-1 block">新建合同默认为草稿状态</span>
          </div>
        </div>
      </div>

      <!-- 合同对象 -->
      <div class="form-section">
        <h2 class="form-section-title">合同对象</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="our-party" class="form-label">我方主体 <span class="form-required">*</span></label>
            <input
              type="text"
              id="our-party"
              v-model="contractForm.ourParty"
              class="form-input"
              placeholder="请输入我方主体"
              required
              oninvalid="this.setCustomValidity('请输入我方主体')"
              oninput="this.setCustomValidity('')"
            />
            <span class="text-xs text-gray-500 mt-1 block">我方签约的公司或组织名称</span>
          </div>
          <div>
            <label for="counterparty" class="form-label">对方主体 <span class="form-required">*</span></label>
            <input
              type="text"
              id="counterparty"
              v-model="contractForm.counterparty"
              class="form-input"
              placeholder="请输入对方主体"
              required
              oninvalid="this.setCustomValidity('请输入对方主体')"
              oninput="this.setCustomValidity('')"
            />
            <span class="text-xs text-gray-500 mt-1 block">对方签约的公司或组织名称</span>
          </div>
          <div>
            <label for="our-signatory" class="form-label">我方签约人</label>
            <input
              type="text"
              id="our-signatory"
              v-model="contractForm.ourSignatory"
              class="form-input"
              placeholder="请输入我方签约人"
            />
            <span class="text-xs text-gray-500 mt-1 block">我方授权签署合同的代表</span>
          </div>
          <div>
            <label for="counter-signatory" class="form-label">对方签约人</label>
            <input
              type="text"
              id="counter-signatory"
              v-model="contractForm.counterSignatory"
              class="form-input"
              placeholder="请输入对方签约人"
            />
            <span class="text-xs text-gray-500 mt-1 block">对方授权签署合同的代表</span>
          </div>
        </div>
      </div>

      <!-- 时间金额 -->
      <div class="form-section">
        <h2 class="form-section-title">时间金额</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="signing-date" class="form-label">签订日期 <span class="form-required">*</span></label>
            <input
              type="date"
              id="signing-date"
              v-model="contractForm.signingDate"
              class="form-input"
              required
              max="3000-12-31"
              oninvalid="this.setCustomValidity('请选择签订日期')"
              oninput="this.setCustomValidity('')"
            />
            <span class="text-xs text-gray-500 mt-1 block">合同双方签字的日期</span>
          </div>
          <div>
            <label for="effective-date" class="form-label">生效日期</label>
            <input
              type="date"
              id="effective-date"
              v-model="contractForm.effectiveDate"
              class="form-input"
              max="3000-12-31"
            />
            <span class="text-xs text-gray-500 mt-1 block">合同开始生效的日期，默认为签订日期</span>
          </div>
          <div>
            <label for="expiry-date" class="form-label">到期日期</label>
            <input
              type="date"
              id="expiry-date"
              v-model="contractForm.expiryDate"
              class="form-input"
              max="3000-12-31"
            />
            <span class="text-xs text-gray-500 mt-1 block">合同效力终止的日期</span>
          </div>
          <div>
            <label for="amount" class="form-label">合同金额 <span class="form-required">*</span></label>
            <div class="mt-1 relative rounded-md shadow-sm">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <span class="text-gray-500 sm:text-sm">¥</span>
              </div>
              <input
                type="number"
                id="amount"
                v-model="contractForm.amount"
                class="form-input pl-8"
                min="0"
                step="0.01"
                placeholder="0.00"
                required
                oninvalid="this.setCustomValidity('请输入合同金额')"
                oninput="this.setCustomValidity('')"
              />
            </div>
            <span class="text-xs text-gray-500 mt-1 block">合同涉及的总金额，不含税</span>
          </div>
        </div>
      </div>

      <!-- 其他信息 -->
      <div class="form-section">
        <h2 class="form-section-title">其他信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="project" class="form-label">关联项目 <span class="form-required">*</span></label>
            <select id="project" v-model="contractForm.projectId" class="form-input" required oninvalid="this.setCustomValidity('请选择关联项目')" oninput="this.setCustomValidity('')">
              <option value="">-- 选择项目 --</option>
              <option v-for="project in projects" :key="project.id" :value="project.id">
                {{ project.name }}
              </option>
            </select>
            <span class="text-xs text-gray-500 mt-1 block">与合同相关的项目</span>
          </div>
          <div>
            <label for="department" class="form-label">所属部门</label>
            <select id="department" v-model="contractForm.department" class="form-input">
              <option value="">-- 选择部门 --</option>
              <option>销售部</option>
              <option>采购部</option>
              <option>财务部</option>
              <option>行政部</option>
              <option>人力资源部</option>
            </select>
            <span class="text-xs text-gray-500 mt-1 block">负责管理此合同的部门</span>
          </div>
        </div>
      </div>

      <!-- 备注信息 -->
      <div class="form-section">
        <h2 class="form-section-title">备注信息</h2>
        <div>
          <label for="description" class="form-label">详细说明</label>
          <textarea
            id="description"
            v-model="contractForm.description"
            class="form-input"
            rows="3"
            placeholder="添加合同说明"
          ></textarea>
          <span class="text-xs text-gray-500 mt-1 block">可记录合同的补充信息、特殊条款或其他需要注意的事项</span>
        </div>
      </div>

      <!-- 表单底部按钮区域 -->
      <div class="flex justify-end space-x-4 border-t pt-6">
        <button type="button" @click="resetForm" class="btn btn-secondary flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          重置表单
        </button>
        <button type="submit" class="btn btn-primary flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          {{ isEdit ? '保存更改' : '提交合同' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';

const router = useRouter();
const route = useRoute();

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id);
const contractId = computed(() => route.params.id);

// 项目列表数据
const projects = ref([]);
const loadingProjects = ref(false);

// 从API获取项目列表
const fetchProjects = async () => {
  loadingProjects.value = true;
  try {
    const response = await axios.get('/api/projects', {
      params: {  } // status: 'active'
    });
    projects.value = response.data.results || [];
  } catch (error) {
    console.error('获取项目列表失败:', error);
  } finally {
    loadingProjects.value = false;
  }
};

// 表单数据 - 与后端模型字段保持一致
const contractForm = ref({
  // 后端模型字段
  contractNumber: '', // 对应前端的 number
  name: '',
  contractType: '', // 对应前端的 type
  status: 'draft',
  amount: null,
  startDate: new Date().toISOString().substr(0, 10), // 对应前端的 effectiveDate
  endDate: '', // 对应前端的 expiryDate
  description: '',
  projectId: '',

  // 前端特有字段（不在后端模型中）
  number: '', // 临时存储，提交时会转换为 contractNumber
  type: '', // 临时存储，提交时会转换为 contractType
  ourParty: '',
  counterparty: '',
  ourSignatory: '',
  counterSignatory: '',
  signingDate: new Date().toISOString().substr(0, 10),
  effectiveDate: '', // 临时存储，提交时会转换为 startDate
  expiryDate: '', // 临时存储，提交时会转换为 endDate
  department: '',
});

// 加载状态
const loading = ref(false);
const error = ref('');
const successMessage = ref('');

// 生成合同编号
function generateContractNumber() {
  const date = new Date();
  const year = date.getFullYear().toString().slice(2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `CT-${year}${month}${day}-${random}`;
}

// 获取合同记录详情（用于编辑模式）
const fetchContractDetails = async (id) => {
  loading.value = true;
  error.value = '';

  try {
    const response = await axios.get(`/api/contracts/${id}`);
    const data = response.data;

    // 填充表单数据
    Object.keys(contractForm.value).forEach(key => {
      if (key in data) {
        contractForm.value[key] = data[key];
      }
    });
    
    // 确保编号从 contractNumber 同步到 number
    if (data.contractNumber) {
      contractForm.value.number = data.contractNumber;
    }
  } catch (err) {
    console.error('获取合同记录详情失败:', err);
    error.value = err.response?.data?.message || '获取合同记录详情失败，请稍后重试。';
  } finally {
    loading.value = false;
  }
};

// 重置表单
function resetForm() {
  if (isEdit.value) {
    // 如果是编辑模式，重新获取原始数据
    fetchContractDetails(contractId.value);
  } else {
    // 如果是创建模式，清空表单
    contractForm.value = {
      // 后端模型字段
      contractNumber: '',
      name: '',
      contractType: '',
      status: 'draft',
      amount: null,
      startDate: new Date().toISOString().substr(0, 10),
      endDate: '',
      description: '',
      projectId: '',

      // 前端特有字段
      number: generateContractNumber(), // 自动生成合同编号
      type: '',
      ourParty: '',
      counterparty: '',
      ourSignatory: '',
      counterSignatory: '',
      signingDate: new Date().toISOString().substr(0, 10),
      effectiveDate: '',
      expiryDate: '',
      department: '',
    };
  }
}

// 处理API错误
function handleApiError(err, action) {
  console.error(`${action}合同失败:`, err);

  if (!err.response || !err.response.data) {
    return err.message || `${action}失败，请重试`;
  }

  const { data } = err.response;

  if (data.message) {
    return data.message;
  }

  if (data.errors) {
    const errorMessages = Object.values(data.errors).flat();
    return errorMessages.join('\n');
  }

  return `${action}失败，请重试`;
}

// 提交表单
async function submitForm() {
  const amount = Number(contractForm.value.amount);
  if (!contractForm.value.amount || isNaN(amount) || amount < 0) {
    error.value = '合同金额不能为空且必须为大于等于0的数字';
    return;
  }
  
  // 验证项目ID是否已选择
  if (!contractForm.value.projectId) {
    error.value = '请选择关联项目';
    return;
  }
  
  contractForm.value.amount = amount;
  loading.value = true;
  error.value = '';

  const action = isEdit.value ? '更新' : '创建';

  try {
    // 准备提交数据，使用后端模型字段
    // 先同步前端特有字段到后端字段
    // 使用用户输入的编号，如果没有则使用自动生成的编号
    contractForm.value.contractNumber = contractForm.value.number || generateContractNumber();
    contractForm.value.startDate = contractForm.value.effectiveDate || contractForm.value.signingDate;
    contractForm.value.endDate = contractForm.value.expiryDate ||
                                new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().substr(0, 10);

    // 构建符合后端验证规则的数据
    const contractData = {
      // 必要字段
      name: contractForm.value.name,
      email: '<EMAIL>', // 添加必要的邮箱字段
      phone: '13800000000', // 添加必要的电话字段
      contractNumber: contractForm.value.contractNumber,
      startDate: contractForm.value.startDate,
      endDate: contractForm.value.endDate,
      position: contractForm.value.ourSignatory || '经理', // 使用我方签约人作为职位
      department: contractForm.value.department || '销售部', // 使用部门或默认值
      projectId: contractForm.value.projectId, // 关联项目ID (必填)
      contractType: contractForm.value.contractType,
      amount: contractForm.value.amount, // 新增金额字段
      hourlyRate: contractForm.value.amount ? contractForm.value.amount / 2000 : 100, // 根据合同金额计算小时费率
      status: mapContractStatus(contractForm.value.status),
      ourParty: contractForm.value.ourParty,         // 我方主体
      counterparty: contractForm.value.counterparty, // 对方主体
    };

    // 将合同类型从前端映射到后端
    function mapContractType(frontendType) {
      return frontendType
    }

    // 将状态从前端映射到后端
    function mapContractStatus(frontendStatus) {
      switch (frontendStatus) {
        case 'active':
          return 'active';
        case 'expired':
          return 'completed';
        case 'terminated':
          return 'inactive';
        default:
          return 'active';
      }
    }

    if (isEdit.value) {
      await axios.patch(`/api/contracts/${contractId.value}`, contractData);
    } else {
      await axios.post('/api/contracts', contractData);
    }

    successMessage.value = `合同${action}成功！`;
    setTimeout(() => {
      router.push('/contracts');
    }, 1500);
  } catch (err) {
    error.value = handleApiError(err, action);
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  // 获取项目列表
  await fetchProjects();

  // 如果是编辑模式，获取合同记录详情
  if (isEdit.value) {
    await fetchContractDetails(contractId.value);
  } else {
    // 如果是创建模式，自动生成合同编号
    contractForm.value.number = generateContractNumber();
  }
});
</script>

<style scoped>
.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.page-header {
  @apply flex items-center justify-between mb-6;
  animation: fadeSlideIn 0.5s ease-out;
}

.card {
  @apply bg-white rounded-xl p-6 mb-6;
  animation: fadeSlideIn 0.6s ease-out;
}

.form-section {
  @apply border-b border-gray-200 pb-6 mb-6 last:border-0 last:pb-0 last:mb-0;
}

.form-section-title {
  @apply text-lg font-semibold text-gray-700 mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-required {
  @apply text-red-500;
}

.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50;
}

.btn {
  @apply py-2 px-4 rounded-lg focus:outline-none focus:ring transition-all duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-200;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-gray-200;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>