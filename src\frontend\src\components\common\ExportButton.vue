<template>
  <div class="export-button-container">
    <button
      @click="toggleDropdown"
      class="export-button flex items-center px-3 py-2 text-sm font-medium rounded-md"
      :class="buttonClass"
      :disabled="disabled || loading"
    >
      <svg v-if="!loading" class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <svg v-else class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
      </svg>
      {{ loading ? '导出中...' : buttonText }}
    </button>

    <div v-if="isOpen" class="export-dropdown absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg z-10 ring-1 ring-black ring-opacity-5">
      <div class="py-1" role="menu" aria-orientation="vertical">
        <button
          v-for="format in availableFormats"
          :key="format.value"
          @click="exportData(format.value)"
          class="w-full text-left block px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
          :class="{ 'text-gray-700 dark:text-gray-200': true }"
          role="menuitem"
        >
          <div class="flex items-center">
            <component :is="format.icon" class="w-4 h-4 mr-2" />
            {{ format.label }}
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import exportService from '@/services/exportService';
import notificationService from '@/services/notification.service';

// Props
const props = defineProps({
  // Data to export
  data: {
    type: Array,
    required: true
  },
  // Headers for the export (array of objects with title and key properties)
  headers: {
    type: Array,
    required: true
  },
  // Entity name for the export filename
  entityName: {
    type: String,
    default: 'data'
  },
  // Custom filename (optional)
  filename: {
    type: String,
    default: ''
  },
  // Button text
  buttonText: {
    type: String,
    default: '导出数据'
  },
  // Button variant (primary, secondary, outline)
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'outline'].includes(value)
  },
  // Disabled state
  disabled: {
    type: Boolean,
    default: false
  },
  // Available export formats
  formats: {
    type: Array,
    default: () => ['csv', 'excel', 'json']
  },
  // Additional options for specific export formats
  additionalOptions: {
    type: Object,
    default: () => ({})
  }
});

// State
const isOpen = ref(false);
const loading = ref(false);

// Format icons
const formatIcons = {
  csv: {
    template: `
      <svg fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zM6 4h7v5h5v11H6V4zm8 3.5L16.5 10H14V7.5zM10 14c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3zm0 4c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/>
      </svg>
    `
  },
  excel: {
    template: `
      <svg fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zM6 4h7v5h5v11H6V4zm8 3.5L16.5 10H14V7.5zM7 15h2v2H7v-2zm4 0h2v2h-2v-2zm-4-4h2v2H7v-2zm4 0h2v2h-2v-2z"/>
      </svg>
    `
  },
  json: {
    template: `
      <svg fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zM6 4h7v5h5v11H6V4zm8 3.5L16.5 10H14V7.5zM8 17.5c0 .827-.673 1.5-1.5 1.5S5 18.327 5 17.5 5.673 16 6.5 16s1.5.673 1.5 1.5zm7 0c0 .827-.673 1.5-1.5 1.5s-1.5-.673-1.5-1.5.673-1.5 1.5-1.5 1.5.673 1.5 1.5z"/>
      </svg>
    `
  },
  pdf: {
    template: `
      <svg fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zM6 4h7v5h5v11H6V4zm8 3.5L16.5 10H14V7.5zM9 16h6v2H9v-2zm0-4h6v2H9v-2z"/>
      </svg>
    `
  }
};

// Computed properties
const buttonClass = computed(() => {
  const classes = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800 dark:bg-gray-700 dark:hover:bg-gray-600 dark:text-gray-200',
    outline: 'border border-gray-300 hover:bg-gray-100 text-gray-700 dark:border-gray-600 dark:hover:bg-gray-700 dark:text-gray-200'
  };
  return classes[props.variant] || classes.primary;
});

const availableFormats = computed(() => {
  const formatLabels = {
    csv: 'CSV 格式',
    excel: 'Excel 格式',
    json: 'JSON 格式',
    pdf: 'PDF 格式'
  };

  return props.formats.map(format => ({
    value: format,
    label: formatLabels[format] || format,
    icon: formatIcons[format] || formatIcons.csv
  }));
});

// Methods
const toggleDropdown = () => {
  if (props.disabled || loading.value) return;
  isOpen.value = !isOpen.value;
};

const closeDropdown = (e) => {
  // Close dropdown when clicking outside
  if (isOpen.value && !e.target.closest('.export-button-container')) {
    isOpen.value = false;
  }
};

const exportData = async (format) => {
  if (props.disabled || loading.value) return;

  // Close dropdown
  isOpen.value = false;

  // Check if data is available
  if (!props.data || props.data.length === 0) {
    notificationService.warning('导出失败', '没有可导出的数据');
    return;
  }

  // Set loading state
  loading.value = true;

  try {
    // Export data using the export service
    await exportService.exportData({
      data: props.data,
      headers: props.headers,
      format: format,
      filename: props.filename,
      entityName: props.entityName,
      additionalOptions: props.additionalOptions
    });
  } catch (error) {
    console.error('Export error:', error);
    notificationService.error('导出失败', error.message || '导出数据时发生错误');
  } finally {
    // Reset loading state
    loading.value = false;
  }
};

// Lifecycle hooks
onMounted(() => {
  document.addEventListener('click', closeDropdown);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown);
});
</script>

<style scoped>
.export-button-container {
  position: relative;
  display: inline-block;
}

.export-dropdown {
  min-width: 160px;
}
</style>
