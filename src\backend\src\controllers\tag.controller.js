const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { tagService } = require('../services');
const pick = require('../utils/pick');

/**
 * Create a tag
 * @route POST /api/tags
 */
const createTag = catchAsync(async (req, res) => {
  const tag = await tagService.createTag(req.body);
  res.status(201).send(tag);
});

/**
 * Get all tags
 * @route GET /api/tags
 */
const getTags = catchAsync(async (req, res) => {
  // If the simplified parameter is true, return all tags without pagination
  if (req.query.simplified === 'true') {
    const tags = await tagService.getAllTags();
    return res.send(tags);
  }
  
  const filter = pick(req.query, ['name']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const result = await tagService.queryTags(filter, options);
  res.send(result);
});

/**
 * Get tag by id
 * @route GET /api/tags/:id
 */
const getTag = catchAsync(async (req, res) => {
  const tag = await tagService.getTagById(req.params.tagId);
  if (!tag) {
    return res.status(404).send({ message: 'Tag not found' });
  }
  res.send(tag);
});

/**
 * Update tag
 * @route PATCH /api/tags/:id
 */
const updateTag = catchAsync(async (req, res) => {
  const tag = await tagService.updateTagById(req.params.tagId, req.body);
  res.send(tag);
});

/**
 * Delete tag
 * @route DELETE /api/tags/:id
 */
const deleteTag = catchAsync(async (req, res) => {
  await tagService.deleteTagById(req.params.tagId);
  res.status(204).send();
});

module.exports = {
  createTag,
  getTags,
  getTag,
  updateTag,
  deleteTag,
};