<template>
  <div class="max-w-6xl mx-auto">
    <!-- Error messages -->
    <div v-if="errorMessage" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ errorMessage }}</p>
    </div>

    <!-- Success messages -->
    <div v-if="successMessage" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
        </svg>
        <span class="font-medium">成功：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
    </div>

    <!-- Document Header Section -->
    <div v-if="loading" class="text-center py-12">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-2 text-gray-600">Loading document...</p>
    </div>

    <div v-else-if="!document" class="card text-center py-12">
      <svg class="w-16 h-16 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="mt-4 text-xl font-medium text-gray-900">Document not found</h3>
      <p class="mt-2 text-gray-600">The document you're looking for doesn't exist or has been deleted</p>
      <div class="mt-6">
        <router-link to="/documents" class="btn btn-primary">
          Back to Documents
        </router-link>
      </div>
    </div>

    <div v-else>
      <!-- Document Header -->
      <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div class="flex-grow">
          <div class="flex items-center mb-2">
            <span v-if="document.category" class="badge badge-blue mr-2">{{ document.category.name }}</span>
            <div v-for="tag in document.tags" :key="tag.id" class="badge badge-gray mr-2">{{ tag.name }}</div>
          </div>
          <h1 class="text-3xl font-bold text-gray-900">{{ document.title }}</h1>
          <div class="mt-2 text-sm text-gray-500 flex items-center gap-x-4">
            <span>Created {{ formatDate(document.createdAt) }}</span>
            <span>|</span>
            <span>Last updated {{ formatDate(document.updatedAt) }}</span>
            <span>|</span>
            <span class="flex items-center">
              <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-1">
                {{ getInitials(document.user?.name) }}
              </span>
              {{ document.user?.name || 'Unknown user' }}
            </span>
          </div>
        </div>
        <div class="mt-4 md:mt-0 flex items-center space-x-3">
          <router-link :to="`/documents/${document.id}`" class="btn btn-secondary">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Edit
          </router-link>
          <button @click="confirmDelete" class="btn btn-danger">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
            </svg>
            Delete
          </button>
        </div>
      </div>

      <!-- Document Content -->
      <div class="card mb-6">
        <div v-if="document.description" class="mb-4 pb-4 border-b border-gray-100">
          <h2 class="text-lg font-medium text-gray-700 mb-2">Description</h2>
          <p class="text-gray-700">{{ document.description }}</p>
        </div>
        <div class="prose max-w-none" v-html="document.content"></div>
      </div>

      <!-- Attachments Section -->
      <div v-if="document.attachments && document.attachments.length > 0" class="card mb-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Attachments</h2>
        <div class="grid gap-4 md:grid-cols-2">
          <div v-for="attachment in document.attachments" :key="attachment.id" class="flex items-center p-3 border rounded-lg hover:bg-gray-50">
            <div class="bg-blue-50 p-2 rounded-md mr-3">
              <svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
              </svg>
            </div>
            <div class="flex-grow truncate">
              <div class="text-sm font-medium text-gray-900 truncate">{{ attachment.fileName }}</div>
              <div class="text-xs text-gray-500">{{ formatSize(attachment.fileSize) }}</div>
            </div>
            <a
              :href="`${getApiBaseUrl()}/attachments/${attachment.id}/download`"
              class="ml-4 text-blue-600 hover:text-blue-800"
              target="_blank"
              download
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
              </svg>
            </a>
          </div>
        </div>
      </div>

      <!-- Comments Section -->
      <div class="card">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Comments</h2>

        <!-- New Comment Form -->
        <div class="mb-6">
          <form @submit.prevent="addComment">
            <textarea
              v-model="newComment"
              rows="3"
              class="form-input w-full"
              placeholder="Add a comment..."
              required
            ></textarea>
            <div class="mt-2 flex justify-end">
              <button type="submit" class="btn btn-primary" :disabled="isSubmittingComment">
                <span v-if="isSubmittingComment" class="flex items-center">
                  <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Adding...
                </span>
                <span v-else>Add Comment</span>
              </button>
            </div>
          </form>
        </div>

        <!-- Comments List -->
        <div v-if="document.comments && document.comments.length > 0">
          <div v-for="comment in sortedComments" :key="comment.id" class="mb-4 last:mb-0 pb-4 last:pb-0 border-b last:border-0 border-gray-100">
            <div class="flex items-start">
              <div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-medium mr-3">
                {{ getInitials(comment.user?.name) }}
              </div>
              <div class="flex-grow">
                <div class="flex items-center justify-between mb-1">
                  <div class="text-sm font-medium text-gray-900">{{ comment.user?.name || 'Unknown user' }}</div>
                  <div class="text-xs text-gray-500">{{ formatDate(comment.createdAt) }}</div>
                </div>
                <div class="text-gray-700">{{ comment.content }}</div>
                <div class="mt-2 flex items-center text-xs text-gray-500" v-if="comment.user?.id === authStore.user?.id">
                  <button @click="editComment(comment)" class="mr-3 hover:text-gray-700">Edit</button>
                  <button @click="deleteComment(comment.id)" class="hover:text-red-600">Delete</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-6 text-gray-500">
          No comments yet. Be the first to add one!
        </div>
      </div>
    </div>

    <!-- Edit Comment Modal -->
    <div v-if="showEditCommentModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Edit Comment</h3>
        <form @submit.prevent="updateComment">
          <textarea
            v-model="editCommentForm.content"
            rows="3"
            class="form-input w-full"
            placeholder="Edit your comment..."
            required
          ></textarea>
          <div class="mt-4 flex justify-end space-x-3">
            <button
              type="button"
              @click="showEditCommentModal = false"
              class="btn btn-secondary"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="isSubmittingComment"
            >
              <span v-if="isSubmittingComment" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </span>
              <span v-else>Save Changes</span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-1">Confirm Deletion</h3>
          <p class="text-gray-500">
            Are you sure you want to delete "{{ document?.title }}"? This action cannot be undone.
          </p>

          <div class="mt-6 flex justify-center space-x-3">
            <button
              type="button"
              @click="showDeleteModal = false"
              class="btn btn-secondary"
            >
              Cancel
            </button>
            <button
              type="button"
              @click="deleteDocument"
              class="btn btn-danger"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Deleting...
              </span>
              <span v-else>Delete Document</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import { useAuthStore } from '../../stores/auth';

const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();

// Document state
const document = ref(null);
const loading = ref(true);
const errorMessage = ref('');
const successMessage = ref('');

// Comment state
const newComment = ref('');
const isSubmittingComment = ref(false);
const showEditCommentModal = ref(false);
const editCommentForm = ref({
  id: null,
  content: ''
});

// Delete modal state
const showDeleteModal = ref(false);
const isDeleting = ref(false);

// Sort comments by creation date (newest first)
const sortedComments = computed(() => {
  if (!document.value?.comments) return [];
  return [...document.value.comments].sort((a, b) =>
    new Date(b.createdAt) - new Date(a.createdAt)
  );
});

// Fetch document on component mount
onMounted(async () => {
  const documentId = route.params.id;
  if (!documentId) {
    router.push('/documents');
    return;
  }

  await fetchDocument(documentId);
});

// Fetch document data
const fetchDocument = async (id) => {
  loading.value = true;
  try {
    const response = await axios.get(`/api/documents/${id}`);
    document.value = response.data;
  } catch (error) {
    console.error('Error fetching document:', error);
    document.value = null;
  } finally {
    loading.value = false;
  }
};

// Format date for display
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// Format file size
const formatSize = (bytes) => {
  if (!bytes) return '0 Bytes';

  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${parseFloat((bytes / Math.pow(1024, i)).toFixed(2))} ${sizes[i]}`;
};

// Get API base URL for downloads
const getApiBaseUrl = () => {
  return import.meta.env.VITE_API_URL;
};

// Get user initials for avatar
const getInitials = (name) => {
  if (!name) return '?';

  const names = name.split(' ');
  if (names.length === 1) return names[0].charAt(0).toUpperCase();
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
};

// Add a comment
const addComment = async () => {
  if (!newComment.value.trim()) return;

  isSubmittingComment.value = true;

  try {
    const response = await axios.post(`/api/documents/${document.value.id}/comments`, {
      content: newComment.value.trim()
    });

    // Add the new comment to the document
    if (!document.value.comments) document.value.comments = [];
    document.value.comments.push(response.data);

    // Clear the form
    newComment.value = '';
  } catch (error) {
    console.error('Error adding comment:', error);
    errorMessage.value = '添加评论失败，请稍后重试。';
    setTimeout(() => {
      errorMessage.value = '';
    }, 5000);
  } finally {
    isSubmittingComment.value = false;
  }
};

// Edit a comment
const editComment = (comment) => {
  editCommentForm.value = {
    id: comment.id,
    content: comment.content
  };
  showEditCommentModal.value = true;
};

// Update a comment
const updateComment = async () => {
  if (!editCommentForm.value.content.trim()) return;

  isSubmittingComment.value = true;

  try {
    const response = await axios.patch(`/api/comments/${editCommentForm.value.id}`, {
      content: editCommentForm.value.content.trim()
    });

    // Update the comment in the document
    const index = document.value.comments.findIndex(c => c.id === editCommentForm.value.id);
    if (index !== -1) {
      document.value.comments[index] = response.data;
    }

    // Close the modal
    showEditCommentModal.value = false;
  } catch (error) {
    console.error('Error updating comment:', error);
    errorMessage.value = '更新评论失败，请稍后重试。';
    setTimeout(() => {
      errorMessage.value = '';
    }, 5000);
  } finally {
    isSubmittingComment.value = false;
  }
};

// Delete a comment
const deleteComment = async (commentId) => {
  if (!confirm('您确定要删除此评论吗？')) return;

  try {
    await axios.delete(`/api/comments/${commentId}`);

    // Remove the comment from the document
    document.value.comments = document.value.comments.filter(c => c.id !== commentId);

    successMessage.value = '评论已成功删除。';
    setTimeout(() => {
      successMessage.value = '';
    }, 3000);
  } catch (error) {
    console.error('Error deleting comment:', error);
    errorMessage.value = '删除评论失败，请稍后重试。';
    setTimeout(() => {
      errorMessage.value = '';
    }, 5000);
  }
};

// Delete document confirmation
const confirmDelete = () => {
  showDeleteModal.value = true;
};

// Delete document
const deleteDocument = async () => {
  isDeleting.value = true;

  try {
    await axios.delete(`/api/documents/${document.value.id}`);
    showDeleteModal.value = false;
    router.push('/documents');
  } catch (error) {
    console.error('Error deleting document:', error);
    errorMessage.value = '删除文档失败，请稍后重试。';
    setTimeout(() => {
      errorMessage.value = '';
    }, 5000);
    isDeleting.value = false;
    showDeleteModal.value = false;
  }
};
</script>

<style>
/* Style for content renders */
.prose {
  @apply text-gray-900;
}
.prose h1 {
  @apply text-2xl font-bold mt-6 mb-4;
}
.prose h2 {
  @apply text-xl font-bold mt-5 mb-3;
}
.prose h3 {
  @apply text-lg font-bold mt-4 mb-2;
}
.prose p {
  @apply mb-4;
}
.prose ul, .prose ol {
  @apply pl-5 mb-4;
}
.prose ul {
  @apply list-disc;
}
.prose ol {
  @apply list-decimal;
}
.prose a {
  @apply text-blue-600 hover:underline;
}
.prose blockquote {
  @apply pl-4 border-l-4 border-gray-300 italic text-gray-700 my-4;
}
.prose pre {
  @apply bg-gray-100 p-4 rounded my-4 overflow-auto;
}
.prose code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm;
}
.prose table {
  @apply border-collapse w-full my-4;
}
.prose th, .prose td {
  @apply border border-gray-300 p-2;
}
.prose th {
  @apply bg-gray-100;
}
</style>