/**
 * Validation utility functions
 * 
 * This module provides functions for validating form inputs and API data.
 */

/**
 * Validates an email address
 * @param {string} email - The email address to validate
 * @returns {boolean} - True if the email is valid, false otherwise
 */
export const isValidEmail = (email) => {
  if (!email) return false;
  
  // Basic email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validates a phone number
 * @param {string} phone - The phone number to validate
 * @returns {boolean} - True if the phone number is valid, false otherwise
 */
export const isValidPhone = (phone) => {
  if (!phone) return false;
  
  // Allow digits, spaces, dashes, parentheses, and plus sign
  const phoneRegex = /^[+]?[(]?[0-9]{1,4}[)]?[-\s.]?[0-9]{1,4}[-\s.]?[0-9]{1,9}$/;
  return phoneRegex.test(phone);
};

/**
 * Validates a URL
 * @param {string} url - The URL to validate
 * @returns {boolean} - True if the URL is valid, false otherwise
 */
export const isValidUrl = (url) => {
  if (!url) return false;
  
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
};

/**
 * Validates a date string
 * @param {string} dateStr - The date string to validate
 * @returns {boolean} - True if the date is valid, false otherwise
 */
export const isValidDate = (dateStr) => {
  if (!dateStr) return false;
  
  const date = new Date(dateStr);
  return !isNaN(date.getTime());
};

/**
 * Validates a numeric value
 * @param {string|number} value - The value to validate
 * @param {number} min - The minimum allowed value (optional)
 * @param {number} max - The maximum allowed value (optional)
 * @returns {boolean} - True if the value is a valid number within the specified range
 */
export const isValidNumber = (value, min = null, max = null) => {
  if (value === null || value === undefined || value === '') return false;
  
  const num = Number(value);
  if (isNaN(num)) return false;
  
  if (min !== null && num < min) return false;
  if (max !== null && num > max) return false;
  
  return true;
};

/**
 * Validates a string length
 * @param {string} str - The string to validate
 * @param {number} minLength - The minimum allowed length (optional)
 * @param {number} maxLength - The maximum allowed length (optional)
 * @returns {boolean} - True if the string length is within the specified range
 */
export const isValidLength = (str, minLength = null, maxLength = null) => {
  if (str === null || str === undefined) return false;
  
  const length = String(str).length;
  
  if (minLength !== null && length < minLength) return false;
  if (maxLength !== null && length > maxLength) return false;
  
  return true;
};

/**
 * Validates a password
 * @param {string} password - The password to validate
 * @returns {boolean} - True if the password is valid, false otherwise
 */
export const isValidPassword = (password) => {
  if (!password) return false;
  
  // Password must be at least 8 characters and contain at least one uppercase letter,
  // one lowercase letter, one number, and one special character
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

/**
 * Validates a username
 * @param {string} username - The username to validate
 * @returns {boolean} - True if the username is valid, false otherwise
 */
export const isValidUsername = (username) => {
  if (!username) return false;
  
  // Username must be 3-20 characters and contain only letters, numbers, underscores, and hyphens
  const usernameRegex = /^[a-zA-Z0-9_-]{3,20}$/;
  return usernameRegex.test(username);
};

/**
 * Validates a product code
 * @param {string} code - The product code to validate
 * @returns {boolean} - True if the product code is valid, false otherwise
 */
export const isValidProductCode = (code) => {
  if (!code) return false;
  
  // Product code must be 3-20 characters and contain only letters, numbers, underscores, and hyphens
  const codeRegex = /^[a-zA-Z0-9_-]{3,20}$/;
  return codeRegex.test(code);
};

/**
 * Validates a price value
 * @param {string|number} price - The price to validate
 * @returns {boolean} - True if the price is valid, false otherwise
 */
export const isValidPrice = (price) => {
  if (price === null || price === undefined || price === '') return false;
  
  const num = Number(price);
  if (isNaN(num) || num < 0) return false;
  
  // Check that the price has at most 2 decimal places
  const decimalPlaces = (num.toString().split('.')[1] || '').length;
  if (decimalPlaces > 2) return false;
  
  return true;
};

/**
 * Validates a form object against a validation schema
 * @param {Object} formData - The form data to validate
 * @param {Object} validationSchema - The validation schema
 * @returns {Object} - An object with validation results
 */
export const validateForm = (formData, validationSchema) => {
  const errors = {};
  let isValid = true;
  
  for (const field in validationSchema) {
    const rules = validationSchema[field];
    const value = formData[field];
    
    // Skip validation if the field is not required and the value is empty
    if (!rules.required && (value === null || value === undefined || value === '')) {
      continue;
    }
    
    // Check required fields
    if (rules.required && (value === null || value === undefined || value === '')) {
      errors[field] = rules.requiredMessage || `请填写${field}`;
      isValid = false;
      continue;
    }
    
    // Check email format
    if (rules.email && !isValidEmail(value)) {
      errors[field] = rules.emailMessage || '请输入有效的电子邮箱地址';
      isValid = false;
      continue;
    }
    
    // Check phone format
    if (rules.phone && !isValidPhone(value)) {
      errors[field] = rules.phoneMessage || '请输入有效的电话号码';
      isValid = false;
      continue;
    }
    
    // Check URL format
    if (rules.url && !isValidUrl(value)) {
      errors[field] = rules.urlMessage || '请输入有效的URL';
      isValid = false;
      continue;
    }
    
    // Check date format
    if (rules.date && !isValidDate(value)) {
      errors[field] = rules.dateMessage || '请输入有效的日期';
      isValid = false;
      continue;
    }
    
    // Check numeric value
    if (rules.number) {
      if (!isValidNumber(value, rules.min, rules.max)) {
        if (rules.min !== null && rules.max !== null) {
          errors[field] = rules.numberMessage || `请输入 ${rules.min} 到 ${rules.max} 之间的数字`;
        } else if (rules.min !== null) {
          errors[field] = rules.numberMessage || `请输入不小于 ${rules.min} 的数字`;
        } else if (rules.max !== null) {
          errors[field] = rules.numberMessage || `请输入不大于 ${rules.max} 的数字`;
        } else {
          errors[field] = rules.numberMessage || '请输入有效的数字';
        }
        isValid = false;
        continue;
      }
    }
    
    // Check string length
    if (rules.length) {
      if (!isValidLength(value, rules.minLength, rules.maxLength)) {
        if (rules.minLength !== null && rules.maxLength !== null) {
          errors[field] = rules.lengthMessage || `长度应为 ${rules.minLength} 到 ${rules.maxLength} 个字符`;
        } else if (rules.minLength !== null) {
          errors[field] = rules.lengthMessage || `长度不能少于 ${rules.minLength} 个字符`;
        } else if (rules.maxLength !== null) {
          errors[field] = rules.lengthMessage || `长度不能超过 ${rules.maxLength} 个字符`;
        }
        isValid = false;
        continue;
      }
    }
    
    // Check password format
    if (rules.password && !isValidPassword(value)) {
      errors[field] = rules.passwordMessage || '密码必须至少包含8个字符，并包含大小写字母、数字和特殊字符';
      isValid = false;
      continue;
    }
    
    // Check username format
    if (rules.username && !isValidUsername(value)) {
      errors[field] = rules.usernameMessage || '用户名必须为3-20个字符，只能包含字母、数字、下划线和连字符';
      isValid = false;
      continue;
    }
    
    // Check product code format
    if (rules.productCode && !isValidProductCode(value)) {
      errors[field] = rules.productCodeMessage || '产品编码必须为3-20个字符，只能包含字母、数字、下划线和连字符';
      isValid = false;
      continue;
    }
    
    // Check price format
    if (rules.price && !isValidPrice(value)) {
      errors[field] = rules.priceMessage || '请输入有效的价格（非负数，最多两位小数）';
      isValid = false;
      continue;
    }
    
    // Check custom validation
    if (rules.validator && typeof rules.validator === 'function') {
      const validationResult = rules.validator(value, formData);
      if (validationResult !== true) {
        errors[field] = validationResult || rules.validatorMessage || '验证失败';
        isValid = false;
        continue;
      }
    }
  }
  
  return { isValid, errors };
};

/**
 * Creates a validation schema for a form
 * @param {Object} schema - The validation schema definition
 * @returns {Object} - The validation schema
 */
export const createValidationSchema = (schema) => {
  return schema;
};

export default {
  isValidEmail,
  isValidPhone,
  isValidUrl,
  isValidDate,
  isValidNumber,
  isValidLength,
  isValidPassword,
  isValidUsername,
  isValidProductCode,
  isValidPrice,
  validateForm,
  createValidationSchema
};
