const express = require('express');
const router = express.Router();
const workhourController = require('../controllers/workhour.controller');

// 获取所有工时记录
router.get('/', workhourController.getWorkHours);

// 获取工时统计数据
router.get('/statistics', workhourController.getWorkHoursStatistics);

// 获取工时日历数据
router.get('/calendar', workhourController.getWorkHoursCalendar);

// 获取单个工时记录
router.get('/:id', workhourController.getWorkHour);

// 创建新的工时记录
router.post('/', workhourController.createWorkHour);

// 更新工时记录
router.put('/:id', workhourController.updateWorkHour);

// 删除工时记录
router.delete('/:id', workhourController.deleteWorkHour);

module.exports = router;
