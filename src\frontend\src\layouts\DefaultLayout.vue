<template>
  <div class="min-h-screen flex flex-col">
    <div class="flex flex-1 h-[calc(100vh-4rem)] overflow-hidden">
      <Sidebar />
      <div class="flex-1 flex flex-col overflow-hidden">
        <AppHeader />
        <GlobalBreadcrumb v-if="false"/>
        <main class="flex-1 overflow-y-auto p-4 sm:p-6">
          <router-view />
        </main>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '../stores/auth';
import { useThemeStore } from '../stores/theme';
import AppHeader from '../components/AppHeader.vue';
import Sidebar from '../components/Sidebar.vue';
import GlobalBreadcrumb from '../components/GlobalBreadcrumb.vue';

const authStore = useAuthStore();
const themeStore = useThemeStore();
</script>