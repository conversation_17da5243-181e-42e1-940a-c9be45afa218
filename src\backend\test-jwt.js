const jwt = require('jsonwebtoken');
const config = require('./src/config/config');

// Log the JWT secret
console.log('JWT Secret:', config.jwt.secret);

// Create a test payload
const payload = {
  sub: 'test-user-id',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour from now
  type: 'test'
};

// Sign the token
const token = jwt.sign(payload, config.jwt.secret);
console.log('Generated Token:', token);

// Verify the token
try {
  const decoded = jwt.verify(token, config.jwt.secret);
  console.log('Token verified successfully!');
  console.log('Decoded payload:', decoded);
} catch (error) {
  console.error('Token verification failed:', error.message);
}
