/**
 * API服务重定向文件
 *
 * 此文件从 apiService.js 导入并重新导出所有服务，以保持向后兼容性
 * 新代码应直接使用 apiService.js
 */

import apiService, {
  authService,
  userService,
  categoryService,
  documentService,
  tagService,
  commentService,
  projectService,
  supplierService,
  purchaseService,
  inventoryService,
  clientService,
  attachmentService,
  reimbursementService,
  subcontractService,
  workhoursService,
  employeeService,
  orderService,
  productService,
  financeService,
  constructionService
} from './apiService';

// 重新导出所有命名导出
export {
  authService,
  userService,
  categoryService,
  documentService,
  tagService,
  commentService,
  projectService,
  supplierService,
  purchaseService,
  inventoryService,
  clientService,
  attachmentService,
  reimbursementService,
  subcontractService,
  workhoursService,
  employeeService,
  orderService,
  productService,
  financeService,
  constructionService
};

// 重新导出默认导出
export default apiService;