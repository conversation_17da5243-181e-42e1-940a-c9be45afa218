const Joi = require('joi');
const { objectId } = require('./custom.validation');

const createReimbursement = {
  body: Joi.object().keys({
    projectName: Joi.string().required().messages({
      'string.empty': '项目名称不能为空',
      'any.required': '项目名称是必填项'
    }),
    projectId: Joi.string().uuid().required().messages({
      'string.empty': '项目ID不能为空',
      'string.guid': '项目ID必须是有效的UUID格式',
      'any.required': '项目ID是必填项'
    }),
    reimbursementDate: Joi.date().required().messages({
      'date.base': '报销日期必须是有效的日期格式',
      'any.required': '报销日期是必填项'
    }),
    reimbursementType: Joi.string().required().messages({
      'string.empty': '报销类型不能为空',
      'any.required': '报销类型是必填项'
    }),
    supplier: Joi.string().allow(null, ''),
    supplierId: Joi.string().uuid().allow(null).messages({
      'string.guid': '供应商ID必须是有效的UUID格式'
    }),
    reason: Joi.string().allow(null, ''),
    totalAmount: Joi.number().required().messages({
      'number.base': '价税合计必须是数字',
      'any.required': '价税合计是必填项'
    }),
    taxRate: Joi.number().required().messages({
      'number.base': '税率必须是数字',
      'any.required': '税率是必填项'
    }),
    invoiceType: Joi.string().required().messages({
      'string.empty': '发票类型不能为空',
      'any.required': '发票类型是必填项'
    }),
    paymentInfo: Joi.object().keys({
      bankName: Joi.string().allow(null, ''),
      accountNumber: Joi.string().allow(null, ''),
      accountName: Joi.string().allow(null, '')
    }),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'completed').default('pending').messages({
      'any.only': '状态必须是待处理(pending)、已批准(approved)、已拒绝(rejected)或已完成(completed)'
    }),
    reimburserId: Joi.string().uuid().required().messages({
      'string.empty': '报销人ID不能为空',
      'string.guid': '报销人ID必须是有效的UUID格式',
      'any.required': '报销人ID是必填项'
    }),
    existingAttachments: Joi.alternatives().try(
      Joi.array().items(Joi.string()),
      Joi.string()
    ).allow(null)
  })
};

const getReimbursement = {
  params: Joi.object().keys({
    id: Joi.string().required().messages({
      'string.empty': '报销记录ID不能为空',
      'any.required': '报销记录ID是必填项'
    })
  })
};

const updateReimbursement = {
  params: Joi.object().keys({
    id: Joi.string().required().messages({
      'string.empty': '报销记录ID不能为空',
      'any.required': '报销记录ID是必填项'
    })
  }),
  body: Joi.object().keys({
    projectName: Joi.string().messages({
      'string.empty': '项目名称不能为空'
    }),
    projectId: Joi.string().uuid().messages({
      'string.empty': '项目ID不能为空',
      'string.guid': '项目ID必须是有效的UUID格式'
    }),
    reimbursementDate: Joi.date().messages({
      'date.base': '报销日期必须是有效的日期格式'
    }),
    reimbursementType: Joi.string().messages({
      'string.empty': '报销类型不能为空'
    }),
    supplier: Joi.string().allow(null, ''),
    supplierId: Joi.string().uuid().allow(null).messages({
      'string.guid': '供应商ID必须是有效的UUID格式'
    }),
    reason: Joi.string().allow(null, ''),
    totalAmount: Joi.number().messages({
      'number.base': '价税合计必须是数字'
    }),
    taxRate: Joi.number().required().messages({
      'number.base': '税率必须是数字',
      'any.required': '税率是必填项'
    }),
    invoiceType: Joi.string().messages({
      'string.empty': '发票类型不能为空'
    }),
    paymentInfo: Joi.object().keys({
      bankName: Joi.string().allow(null, ''),
      accountNumber: Joi.string().allow(null, ''),
      accountName: Joi.string().allow(null, '')
    }),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'completed').messages({
      'any.only': '状态必须是待处理(pending)、已批准(approved)、已拒绝(rejected)或已完成(completed)'
    }),
    reimburserId: Joi.string().uuid().messages({
      'string.empty': '报销人ID不能为空',
      'string.guid': '报销人ID必须是有效的UUID格式'
    }),
    existingAttachments: Joi.alternatives().try(
      Joi.array().items(Joi.string()),
      Joi.string()
    ).allow(null)
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const deleteReimbursement = {
  params: Joi.object().keys({
    id: Joi.string().required().messages({
      'string.empty': '报销记录ID不能为空',
      'any.required': '报销记录ID是必填项'
    })
  })
};

const getReimbursements = {
  query: Joi.object().keys({
    projectName: Joi.string().allow('', null),
    reimbursementType: Joi.string().allow('', null),
    supplier: Joi.string().allow('', null),
    startDate: Joi.date().allow('', null).messages({
      'date.base': '开始日期必须是有效的日期格式'
    }),
    endDate: Joi.date().allow('', null).messages({
      'date.base': '结束日期必须是有效的日期格式'
    }),
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于1'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于1',
      'number.max': '每页数量不能超过100000'
    }),
    sortBy: Joi.string().default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    })
  })
};

module.exports = {
  createReimbursement,
  getReimbursement,
  updateReimbursement,
  deleteReimbursement,
  getReimbursements
};
