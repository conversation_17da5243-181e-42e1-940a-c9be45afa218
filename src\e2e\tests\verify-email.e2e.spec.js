const { test, expect } = require('@playwright/test');

test.describe('Verify Email Page', () => {
  test('should load verify email page', async ({ page }) => {
    await page.goto('/verify-email');
    await expect(page).toHaveURL(/.*verify-email/);
    await expect(page.locator('.verify-email-container')).toBeVisible();
  });

  test('should show error for invalid token', async ({ page }) => {
    await page.goto('/verify-email?token=invalid');
    await expect(page.locator('.error-message')).toBeVisible();
  });

  test('should show success for valid token', async ({ page }) => {
    await page.goto('/verify-email?token=validtoken');
    await expect(page.locator('.success-message')).toBeVisible();
  });
}); 