<template>
  <div v-if="authStore.isInitialized" class="min-h-screen" :class="{ 'dark': themeStore.isDarkMode }">
    <template v-if="authStore.isAuthenticated">
      <DefaultLayout>
        <router-view v-slot="{ Component }">
          <Suspense>
            <component :is="Component" />
            <template #fallback>
              <div class="flex items-center justify-center h-full">
                <div class="animate-spin rounded-full h-12 w-12 border-4 border-t-transparent" :style="{ borderColor: 'var(--primary-color) transparent var(--primary-color) var(--primary-color)' }"></div>
              </div>
            </template>
          </Suspense>
        </router-view>
      </DefaultLayout>
    </template>
    <template v-else>
      <router-view v-slot="{ Component }">
        <Suspense>
          <component :is="Component" />
          <template #fallback>
            <div class="flex items-center justify-center h-full">
              <div class="animate-spin rounded-full h-12 w-12 border-4 border-t-transparent" :style="{ borderColor: 'var(--primary-color) transparent var(--primary-color) var(--primary-color)' }"></div>
            </div>
          </template>
        </Suspense>
      </router-view>
    </template>
  </div>
  <div v-else class="min-h-screen flex items-center justify-center">
    <div class="animate-spin rounded-full h-12 w-12 border-4 border-t-transparent" :style="{ borderColor: 'var(--primary-color) transparent var(--primary-color) var(--primary-color)' }"></div>
  </div>

  <!-- Global Message Container -->
  <MessageContainer />

  <!-- Global Notification Container -->
  <NotificationContainer />

  <!-- Global Confirm Dialog -->
  <ConfirmDialog />
</template>

<script setup>
import { onMounted } from 'vue';
import { useAuthStore } from './stores/auth';
import { useThemeStore } from './stores/theme';
import { useUserMessageStore } from './stores/userMessage';
import DefaultLayout from './layouts/DefaultLayout.vue';
import MessageContainer from './components/MessageContainer.vue';
import NotificationContainer from './components/common/NotificationContainer.vue';
import ConfirmDialog from './components/common/ConfirmDialog.vue';

const authStore = useAuthStore();
const themeStore = useThemeStore();
const userMessageStore = useUserMessageStore();

onMounted(async () => {
  // Initialize theme
  themeStore.initTheme();

  if (!authStore.isInitialized) {
    await authStore.init();
  }

  // 如果用户已登录，初始化消息存储
  if (authStore.isAuthenticated) {
    await userMessageStore.init();
  }
});
</script>