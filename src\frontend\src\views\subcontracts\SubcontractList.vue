<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和操作按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">分包管理</h1>
        <p class="page-subtitle">管理和跟踪项目分包信息</p>
      </div>
      <div class="flex space-x-2">
        <router-link
          to="/subcontracts/create"
          class="btn btn-primary flex items-center shadow-md hover:shadow-lg transition-all duration-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          新建分包
        </router-link>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-card">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="flex-1">
          <label for="project-name" class="form-label font-medium">项目名称</label>
          <div class="relative">
            <input
              id="project-name"
              type="text"
              v-model="filters.projectName"
              class="form-input pl-10 py-2 mt-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
              placeholder="输入项目名称"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <label for="contract-name" class="form-label font-medium">合同名称</label>
          <input
            id="contract-name"
            type="text"
            v-model="filters.contractName"
            class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
            placeholder="输入合同名称"
          >
        </div>
        <div class="flex-1">
          <label for="contract-code" class="form-label font-medium">合同编号</label>
          <input
            id="contract-code"
            type="text"
            v-model="filters.contractCode"
            class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
            placeholder="输入合同编号"
          >
        </div>
        <div class="flex-1">
          <label for="contract-type" class="form-label font-medium">合同类型</label>
          <select
            id="contract-type"
            v-model="filters.contractType"
            class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
          >
            <option value="">全部</option>
            <option value="construction">施工分包</option>
            <option value="design">设计分包</option>
            <option value="material">材料分包</option>
          </select>
        </div>
        <div class="flex-1">
          <label for="contract-status" class="form-label font-medium">合同状态</label>
          <select
            id="contract-status"
            v-model="filters.contractStatus"
            class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
          >
            <option value="">全部</option>
            <option value="draft">草稿</option>
            <option value="pending">待审核</option>
            <option value="approved">已审核</option>
            <option value="executing">执行中</option>
            <option value="completed">已完成</option>
            <option value="terminated">已终止</option>
          </select>
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button
          @click="resetFilters"
          class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200"
          :disabled="loading"
        >
          重置
        </button>
        <button
          @click="search"
          class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200"
          :disabled="loading"
        >
          <span v-if="loading" class="inline-block animate-spin mr-1">↻</span>
          {{ loading ? '加载中...' : '查询' }}
        </button>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-4 p-4 bg-red-100 text-red-700 rounded-lg shadow-sm flex items-center justify-between">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        {{ error }}
      </div>
      <button @click="fetchSubcontracts()" class="text-red-700 hover:text-red-900 font-medium">
        重试
      </button>
    </div>

    <!-- 分包列表 -->
    <div class="card overflow-hidden shadow-md">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合同名称</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">合同编号</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">税率</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">开票日期</th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200" v-if="!loading">
            <tr v-for="subcontract in subcontracts" :key="subcontract.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ subcontract.projectName }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ subcontract.contractName }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ subcontract.contractCode }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span :class="getStatusClass(subcontract.contractStatus)">{{ getStatusText(subcontract.contractStatus) }}</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(subcontract.totalAmount) }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ subcontract.taxRate !== null ? Number(subcontract.taxRate).toFixed(2) : '-' }}%</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ formatDate(subcontract.invoiceDate) }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex justify-end space-x-3">                  <button
                    @click="router.push(`/subcontracts/${subcontract.id}`)"
                    class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                    </svg>
                    <span class="font-medium">编辑</span>
                  </button>
                  <button
                    @click="showDeleteConfirm(subcontract)"
                    class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                    <span class="font-medium">删除</span>
                  </button>
                </div>
              </td>
            </tr>
            <tr v-if="subcontracts.length === 0">
              <td class="px-6 py-4 text-center text-sm text-gray-500" colspan="8">暂无分包数据</td>
            </tr>
          </tbody>
          <tbody v-else>
            <tr>
              <td class="px-6 py-4 text-center text-sm text-gray-500" colspan="8">
                <div class="flex justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  加载中...
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 分页 -->
    <div class="mt-4 flex justify-between items-center px-6 py-4 border-t border-gray-200 bg-white rounded-b-lg shadow-md">
      <div class="text-sm text-gray-500">
        显示第 <span class="font-medium">{{ (currentPage - 1) * pageLimit + 1 }}</span> 至
        <span class="font-medium">{{ Math.min(currentPage * pageLimit, totalItems) }}</span> 条，
        共 <span class="font-medium">{{ totalItems }}</span> 条记录
      </div>
      <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
        <button
          @click="currentPage > 1 && currentPage--"
          :disabled="currentPage === 1"
          class="relative inline-flex items-center px-4 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          上一页
        </button>
        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
          第 {{ currentPage }} 页 / 共 {{ totalPages }} 页
        </span>
        <button
          @click="currentPage < totalPages && currentPage++"
          :disabled="currentPage >= totalPages"
          class="relative inline-flex items-center px-4 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          下一页
        </button>
      </nav>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 class="text-lg font-medium text-gray-900 mb-4">确认删除</h3>
        <p class="text-gray-600 mb-6">确定要删除这个分包记录吗？此操作不可撤销。</p>
        <div class="flex justify-end space-x-3">
          <button
            @click="showDeleteModal = false"
            class="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            取消
          </button>
          <button
            @click="deleteSubcontract"
            class="px-4 py-2 text-white bg-red-600 rounded-md hover:bg-red-700 transition-colors"
          >
            删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { formatCurrency, formatDate } from '@/utils/formatters'
import { subcontractService } from '@/services/api.service'

const router = useRouter()
const loading = ref(false)
const error = ref(null)
const currentPage = ref(1)
const totalPages = ref(1)
const totalItems = ref(0)
const showDeleteModal = ref(false)
const selectedSubcontract = ref(null)
const pageLimit = ref(10)

const filters = ref({
  projectName: '',
  contractName: '',
  contractCode: '',
  contractType: '',
  contractStatus: ''
})

const subcontracts = ref([])

const getStatusClass = (status) => {
  const classes = {
    draft: 'bg-gray-100 text-gray-800',
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    executing: 'bg-blue-100 text-blue-800',
    completed: 'bg-purple-100 text-purple-800',
    terminated: 'bg-red-100 text-red-800'
  }
  return `px-2 py-1 rounded-full text-xs ${classes[status] || 'bg-gray-100 text-gray-800'}`
}

const getStatusText = (status) => {
  const texts = {
    draft: '草稿',
    pending: '待审核',
    approved: '已审核',
    executing: '执行中',
    completed: '已完成',
    terminated: '已终止'
  }
  return texts[status] || status
}

// 获取分包列表
const fetchSubcontracts = async () => {
  loading.value = true
  error.value = null
  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      limit: pageLimit.value,
      ...filters.value
    }

    const response = await subcontractService.getSubcontracts(params)
    // 直接使用 response，因为 processResponse 已经处理过响应数据
    subcontracts.value = response.results || []
    totalItems.value = response.totalResults || 0
    totalPages.value = response.totalPages || 1
    pageLimit.value = response.limit || params.limit || 10
  } catch (err) {
    console.error('获取分包列表失败:', err)
    error.value = '获取分包数据失败，请重试'
  } finally {
    loading.value = false
  }
}

const search = () => {
  currentPage.value = 1
  fetchSubcontracts()
}

const resetFilters = () => {
  filters.value = {
    projectName: '',
    contractName: '',
    contractCode: '',
    contractType: '',
    contractStatus: ''
  }
  currentPage.value = 1
  fetchSubcontracts()
}

const showDeleteConfirm = (subcontract) => {
  selectedSubcontract.value = subcontract
  showDeleteModal.value = true
}

const deleteSubcontract = async () => {
  if (!selectedSubcontract.value) return

  loading.value = true
  try {
    await subcontractService.deleteSubcontract(selectedSubcontract.value.id)
    showDeleteModal.value = false
    selectedSubcontract.value = null
    fetchSubcontracts()
  } catch (error) {
    console.error('删除失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听页码变化
watch(currentPage, () => {
  fetchSubcontracts()
})

onMounted(() => {
  fetchSubcontracts()
})
</script>

<style scoped>
/* 状态标签样式 */
.status-tag {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-tag.gray {
  background-color: #f3f4f6;
  color: #1f2937;
}

.status-tag.yellow {
  background-color: #fef3c7;
  color: #92400e;
}

.status-tag.green {
  background-color: #d1fae5;
  color: #065f46;
}

.status-tag.red {
  background-color: #fee2e2;
  color: #b91c1c;
}

.status-tag.blue {
  background-color: #dbeafe;
  color: #1e40af;
}

.status-tag.purple {
  background-color: #ede9fe;
  color: #6d28d9;
}

/* Page transitions */
.page-header, .filter-card, .card {
  animation: fadeSlideIn 0.6s ease-out;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  animation-delay: 0s;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
}

.page-subtitle {
  color: #6b7280;
  margin-top: 0.25rem;
}

.filter-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  animation-delay: 0.1s;
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  animation-delay: 0.2s;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  color: #374151;
}

.form-input {
  display: block;
  width: 100%;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition-property: all;
  transition-duration: 200ms;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #4b5563;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}
</style>
