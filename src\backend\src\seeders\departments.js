const { Department } = require('../models');

/**
 * Seed initial department data
 */
async function seedDepartments() {
  try {
    console.log('Seeding departments...');
    
    // Check if departments already exist
    const existingDepartments = await Department.count();
    if (existingDepartments > 0) {
      console.log('Departments already exist, skipping seed');
      return;
    }
    
    // Create departments
    const departments = [
      {
        name: '总裁办',
        code: 'CEO-OFFICE',
        description: '公司最高决策机构',
        status: 'active',
      },
      {
        name: '人力资源部',
        code: 'HR',
        description: '负责公司人员招聘、培训、绩效考核等工作',
        parentId: 1,
        status: 'active',
      },
      {
        name: '财务部',
        code: 'FIN',
        description: '负责公司财务管理、预算控制等工作',
        parentId: 1,
        status: 'active',
      },
      {
        name: '技术部',
        code: 'TECH',
        description: '负责公司研发和技术支持工作',
        parentId: 1,
        status: 'active',
      },
      {
        name: '销售部',
        code: 'SALES',
        description: '负责公司产品销售和客户关系维护',
        parentId: 1,
        status: 'active',
      },
      {
        name: '市场部',
        code: 'MARKETING',
        description: '负责公司品牌推广和市场活动策划',
        parentId: 1,
        status: 'active',
      },
      {
        name: '前端开发组',
        code: 'FRONTEND',
        description: '负责公司前端产品开发',
        parentId: 4,
        status: 'active',
      },
      {
        name: '后端开发组',
        code: 'BACKEND',
        description: '负责公司后端系统开发',
        parentId: 4,
        status: 'active',
      },
      {
        name: '测试组',
        code: 'QA',
        description: '负责公司产品质量控制',
        parentId: 4,
        status: 'active',
      }
    ];
    
    // Insert departments
    await Department.bulkCreate(departments);
    
    console.log('Departments seeded successfully');
  } catch (error) {
    console.error('Error seeding departments:', error);
  }
}

module.exports = seedDepartments; 