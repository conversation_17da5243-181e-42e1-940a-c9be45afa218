const fs = require('fs');
const path = require('path');
const db = require('../models');

function getModelName(filename) {
  // Convert model filename to seeder name
  // e.g., 'user.model.js' -> 'userSeeder'
  return filename
    .replace('.model.js', '')
    .replace(/^./, c => c.toLowerCase()) + 'Seeder';
}

function analyzeModelDependencies() {
  const dependencies = {};
  const modelsDir = path.join(__dirname, '../models');
  const modelFiles = fs.readdirSync(modelsDir).filter(file => 
    file.endsWith('.model.js') && 
    !file.includes('Tag.model.js') // Exclude junction table models
  );

  // Initialize empty dependencies for all seeders
  modelFiles.forEach(file => {
    dependencies[getModelName(file)] = [];
  });

  // Analyze each model's associations
  modelFiles.forEach(file => {
    const modelPath = path.join(modelsDir, file);
    const modelContent = fs.readFileSync(modelPath, 'utf-8');
    
    // Look for belongsTo, hasOne, hasMany, and belongsToMany associations
    const associationMatches = modelContent.match(/\.(belongsTo|hasOne|hasMany|belongsToMany)\(.*?\)/gs) || [];
    
    const seederName = getModelName(file);
    
    associationMatches.forEach(match => {
      // Extract the model name from the association
      const modelMatch = match.match(/\((.*?)(,|\))/);
      if (modelMatch && modelMatch[1]) {
        let dependentModel = modelMatch[1].trim();
        // Clean up the model name (remove db. or quotes)
        dependentModel = dependentModel.replace(/^(db\.|\"|\')/g, '').replace(/(\"|\')/g, '');
        
        // Convert model name to seeder name
        const dependentSeeder = dependentModel.toLowerCase() + 'Seeder';
        
        // Add dependency if it's a belongsTo relationship and not a self-reference
        if (match.includes('belongsTo') && 
            !dependencies[seederName]?.includes(dependentSeeder) && 
            seederName !== dependentSeeder && // Skip self-references
            !dependentSeeder.includes('tag')) { // Skip tag-related dependencies as they're handled in main seeders
          dependencies[seederName].push(dependentSeeder);
        }
      }
    });
  });

  // Add some common dependencies that might not be captured through associations
  dependencies['roleSeeder'] = ['userSeeder'];
  dependencies['permissionSeeder'] = ['roleSeeder'];
  dependencies['tokenSeeder'] = ['userSeeder'];
  dependencies['attachmentSeeder'] = ['userSeeder'];
  dependencies['projectProgressSeeder'] = ['projectSeeder', 'userSeeder'];
  dependencies['documentSeeder'] = ['categorySeeder', 'userSeeder', 'tagSeeder'];
  dependencies['projectTaskSeeder'] = ['projectSeeder', 'contractWorkerSeeder', 'temporaryWorkerSeeder'];
  dependencies['projectFollowUpSeeder'] = ['projectSeeder', 'employeeSeeder'];

  // Remove any self-references that might have been added
  Object.keys(dependencies).forEach(seeder => {
    dependencies[seeder] = dependencies[seeder].filter(dep => dep !== seeder);
  });

  // Clean up any references to non-existent seeders
  Object.keys(dependencies).forEach(seeder => {
    dependencies[seeder] = dependencies[seeder].filter(dep => 
      dependencies.hasOwnProperty(dep) || dep === 'tagSeeder' || dep === 'userSeeder'
    );
  });

  return dependencies;
}

// Generate the dependencies
const dependencies = analyzeModelDependencies();

// Update the seed-test-data.js file
const seedTestDataPath = path.join(__dirname, 'seed-test-data.js');
const seedTestDataContent = fs.readFileSync(seedTestDataPath, 'utf-8');

// Replace the SEEDER_DEPENDENCIES object
const updatedContent = seedTestDataContent.replace(
  /const SEEDER_DEPENDENCIES = {[\s\S]*?};/m,
  `const SEEDER_DEPENDENCIES = ${JSON.stringify(dependencies, null, 2)};`
);

fs.writeFileSync(seedTestDataPath, updatedContent);

console.log('Seeder dependencies have been updated successfully!'); 