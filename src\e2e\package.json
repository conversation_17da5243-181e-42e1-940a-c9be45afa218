{"name": "yihe", "version": "1.0.0", "main": "index.js", "directories": {"doc": "docs"}, "scripts": {"test": "npx playwright test", "test:mcp": "node run-mcp-tests.js", "test:enhanced": "node run-enhanced-tests.js", "test:ui": "npx playwright test --ui", "report": "npx playwright show-report", "install:browsers": "npx playwright install", "clean": "rimraf logs screenshots test-results test-report"}, "repository": {"type": "git", "url": "https://gitee.com/sunshine51/yihe"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@playwright/test": "^1.52.0", "@types/node": "^22.15.3"}}