const morgan = require('morgan');
const config = require('./config');
const logger = require('./logger');

// Define custom token formats for successful and error logs
morgan.token('message', (req, res) => res.locals.errorMessage || '');

const successResponseFormat = `[:date[iso]] ${config.env} :method :url :status - :response-time ms`;
const errorResponseFormat = `[:date[iso]] ${config.env} :method :url :status - :response-time ms - message: :message`;

// Success handler - for logging successful requests
const successHandler = morgan(successResponseFormat, {
  skip: (req, res) => res.statusCode >= 400,
  stream: { write: (message) => logger.info(message.trim()) },
});

// Error handler - for logging requests with errors
const errorHandler = morgan(errorResponseFormat, {
  skip: (req, res) => res.statusCode < 400,
  stream: { write: (message) => logger.error(message.trim()) },
});

module.exports = {
  success<PERSON>and<PERSON>,
  errorHandler,
};
