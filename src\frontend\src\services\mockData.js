// 生成随机日期
function generateRandomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).toISOString();
}

// 生成随机字符串
function generateRandomString(length) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  return Array.from({ length }, () => chars.charAt(Math.floor(Math.random() * chars.length))).join('');
}

// 生成随机数字
function generateRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// 生成项目列表数据
export function generateProjectList(page = 1, limit = 10) {
  const totalProjects = 100;
  const startIndex = (page - 1) * limit;
  const endIndex = Math.min(startIndex + limit, totalProjects);
  
  const projects = Array.from({ length: endIndex - startIndex }, (_, index) => {
    const projectId = startIndex + index + 1;
    const status = ['planning', 'in_progress', 'completed', 'on_hold', 'cancelled'][Math.floor(Math.random() * 5)];
    const progress = Math.floor(Math.random() * 100);
    const startDate = generateRandomDate(new Date(2023, 0, 1), new Date(2024, 11, 31));
    const endDate = generateRandomDate(new Date(startDate), new Date(2025, 11, 31));
    
    return {
      id: projectId.toString(),
      code: `P${String(projectId).padStart(6, '0')}`,
      name: `项目${projectId}`,
      members: `成员${generateRandomNumber(1, 5)}`,
      manager: `负责人${generateRandomNumber(1, 10)}`,
      constructionUnit: `建设单位${generateRandomNumber(1, 20)}`,
      designUnit: `设计单位${generateRandomNumber(1, 20)}`,
      contractorUnit: `施工单位${generateRandomNumber(1, 20)}`,
      supervisorUnit: `监理单位${generateRandomNumber(1, 20)}`,
      reviewUnit: `审图单位${generateRandomNumber(1, 20)}`,
      address: `地址${generateRandomNumber(1, 100)}`,
      startDate,
      endDate,
      archiveNumber: `A${String(projectId).padStart(6, '0')}`,
      engineeringNumber: `E${String(projectId).padStart(6, '0')}`,
      usageType: ['商业', '住宅', '工业', '公共建筑'][Math.floor(Math.random() * 4)],
      engineeringType: ['建筑工程', '消防工程', '年度检测', '竣工检测', '消防安全评估', '消防维保'][Math.floor(Math.random() * 6)],
      constructionPermitNumber: `C${String(projectId).padStart(6, '0')}`,
      area: generateRandomNumber(100, 10000),
      specialSystem: `特殊系统${generateRandomNumber(1, 5)}`,
      companyManager: `公司负责人${generateRandomNumber(1, 10)}`,
      status,
      progress,
      clientContact: `对接人${generateRandomNumber(1, 10)}`,
      tags: ['重要', '紧急', '常规'][Math.floor(Math.random() * 3)]
    };
  });

  return {
    results: projects,
    totalResults: totalProjects,
    page,
    limit
  };
}

// 生成单个项目数据
function generateProject(id) {
  const status = ['planning', 'in_progress', 'completed', 'on_hold', 'cancelled'][Math.floor(Math.random() * 5)];
  const progress = Math.floor(Math.random() * 100);
  const startDate = generateRandomDate(new Date(2023, 0, 1), new Date(2024, 11, 31));
  const endDate = generateRandomDate(new Date(startDate), new Date(2025, 11, 31));
  
  return {
    id: id.toString(),
    code: `P${String(id).padStart(6, '0')}`,
    name: `项目${id}`,
    members: `成员${generateRandomNumber(1, 5)}`,
    manager: `负责人${generateRandomNumber(1, 10)}`,
    constructionUnit: `建设单位${generateRandomNumber(1, 20)}`,
    designUnit: `设计单位${generateRandomNumber(1, 20)}`,
    contractorUnit: `施工单位${generateRandomNumber(1, 20)}`,
    supervisorUnit: `监理单位${generateRandomNumber(1, 20)}`,
    reviewUnit: `审图单位${generateRandomNumber(1, 20)}`,
    address: `地址${generateRandomNumber(1, 100)}`,
    startDate,
    endDate,
    archiveNumber: `A${String(id).padStart(6, '0')}`,
    engineeringNumber: `E${String(id).padStart(6, '0')}`,
    usageType: ['商业', '住宅', '工业', '公共建筑'][Math.floor(Math.random() * 4)],
    engineeringType: ['建筑工程', '消防工程', '年度检测', '竣工检测', '消防安全评估', '消防维保'][Math.floor(Math.random() * 6)],
    constructionPermitNumber: `C${String(id).padStart(6, '0')}`,
    area: generateRandomNumber(100, 10000),
    specialSystem: `特殊系统${generateRandomNumber(1, 5)}`,
    companyManager: `公司负责人${generateRandomNumber(1, 10)}`,
    status,
    progress,
    clientContact: `对接人${generateRandomNumber(1, 10)}`,
    tags: ['重要', '紧急', '常规'][Math.floor(Math.random() * 3)]
  };
}

// 模拟API延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// 生成财务统计数据
function generateFinanceStats() {
  const incomeTypes = {
    '项目收入': { amount: Math.floor(Math.random() * 1000000), count: Math.floor(Math.random() * 50) },
    '其他收入': { amount: Math.floor(Math.random() * 500000), count: Math.floor(Math.random() * 30) },
    '投资收益': { amount: Math.floor(Math.random() * 300000), count: Math.floor(Math.random() * 20) }
  }

  const expenseTypes = {
    '人工成本': { amount: Math.floor(Math.random() * 800000), count: Math.floor(Math.random() * 100) },
    '材料成本': { amount: Math.floor(Math.random() * 600000), count: Math.floor(Math.random() * 80) },
    '设备成本': { amount: Math.floor(Math.random() * 400000), count: Math.floor(Math.random() * 60) },
    '其他支出': { amount: Math.floor(Math.random() * 200000), count: Math.floor(Math.random() * 40) }
  }

  const totalIncome = Object.values(incomeTypes).reduce((sum, type) => sum + type.amount, 0)
  const totalExpense = Object.values(expenseTypes).reduce((sum, type) => sum + type.amount, 0)

  // 生成月度趋势数据
  const monthlyTrend = Array.from({ length: 12 }, (_, i) => {
    const month = `${i + 1}月`
    const income = Math.floor(Math.random() * 200000)
    const expense = Math.floor(Math.random() * 150000)
    return {
      month,
      income,
      expense,
      netProfit: income - expense
    }
  })

  return {
    totalIncome,
    totalExpense,
    netProfit: totalIncome - totalExpense,
    incomeTypes,
    expenseTypes,
    monthlyTrend
  }
}

// 获取项目列表
const getProjects = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const projects = [
        { id: 'P001', name: '上海浦东新区消防工程', code: 'SHPD001' },
        { id: 'P002', name: '北京朝阳区消防改造', code: 'BJCY002' },
        { id: 'P003', name: '广州天河区消防检测', code: 'GZTH003' },
        { id: 'P004', name: '深圳南山区消防维保', code: 'SZNS004' },
        { id: 'P005', name: '杭州西湖区消防评估', code: 'HZXH005' },
        { id: 'P006', name: '南京鼓楼区消防验收', code: 'NJGL006' },
        { id: 'P007', name: '武汉江汉区消防设计', code: 'WHJH007' },
        { id: 'P008', name: '成都锦江区消防检测', code: 'CDJJ008' },
        { id: 'P009', name: '重庆渝中区消防维保', code: 'CQYZ009' },
        { id: 'P010', name: '西安雁塔区消防评估', code: 'XAYT010' }
      ]
      resolve({
        results: projects,
        totalResults: projects.length,
        page: 1,
        limit: 10
      })
    }, 500)
  })
}

// 获取财务记录列表
export const getFinances = ({ page = 1, limit = 5 }) => {
  return new Promise(async (resolve) => {
    try {
      // 获取所有项目
      const projectsResponse = await getProjects()
      const projects = projectsResponse.results
      
      // 为每个项目生成一条财务记录
      const allRecords = projects.map(project => {
        // 生成随机日期
        const generateRandomDate = () => {
          const start = new Date(2024, 0, 1)
          const end = new Date(2024, 11, 31)
          return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime())).toISOString().split('T')[0]
        }

        // 生成随机税率
        const generateRandomTaxRate = () => {
          const rates = [3, 6, 9, 13]
          return rates[Math.floor(Math.random() * rates.length)]
        }

        // 生成随机金额
        const generateRandomAmount = () => {
          return Math.floor(Math.random() * 100000) + 10000
        }

        // 生成随机公司名称
        const generateRandomCompany = (prefix) => {
          const cities = ['北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '重庆', '西安']
          const types = ['科技', '信息', '工程', '建设', '咨询', '服务', '贸易', '实业']
          const city = cities[Math.floor(Math.random() * cities.length)]
          const type = types[Math.floor(Math.random() * types.length)]
          return `${city}${prefix}${type}有限公司`
        }

        const financeRecord = {
          projectId: project.id,
          projectName: project.name,
          projectCode: project.code,
          invoiceDate: generateRandomDate(),
          taxRate: generateRandomTaxRate(),
          buyerInfo: generateRandomCompany('某某'),
          invoiceType: ['增值税专用发票', '增值税普通发票', '其他'][Math.floor(Math.random() * 3)],
          paymentDate: generateRandomDate(),
          inputTaxRate: generateRandomTaxRate(),
          sellerInfo: generateRandomCompany('某某'),
          inputInvoiceType: ['增值税专用发票', '增值税普通发票', '其他'][Math.floor(Math.random() * 3)],
          subcontractPaymentDate: generateRandomDate(),
          purchasePaymentDate: generateRandomDate(),
          reimbursementPaymentDate: generateRandomDate(),
          netIncome: generateRandomAmount(),
          attachments: [],
          operationRecords: [
            {
              type: 'create',
              operator: ['张三', '李四', '王五', '赵六'][Math.floor(Math.random() * 4)],
              time: new Date().toLocaleString('zh-CN'),
              content: '创建了财务记录'
            }
          ]
        }
        return financeRecord
      })

      // 计算总数
      const total = allRecords.length
      
      // 计算分页
      const start = (page - 1) * limit
      const end = start + limit
      const results = allRecords.slice(start, end)

      console.log('分页数据:', {
        page,
        limit,
        total,
        start,
        end,
        results
      })

      resolve({
        results,
        total,
        page,
        limit
      })
    } catch (error) {
      console.error('获取财务记录失败:', error)
      resolve({
        results: [],
        total: 0,
        page,
        limit
      })
    }
  })
}

// 获取单个财务记录
const getFinance = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const projectList = [
        { id: 'P001', name: '上海浦东新区消防工程', code: 'SHPD001' },
        { id: 'P002', name: '北京朝阳区消防改造', code: 'BJCY002' },
        { id: 'P003', name: '广州天河区消防检测', code: 'GZTH003' },
        { id: 'P004', name: '深圳南山区消防维保', code: 'SZNS004' },
        { id: 'P005', name: '杭州西湖区消防评估', code: 'HZXH005' },
        { id: 'P006', name: '南京鼓楼区消防验收', code: 'NJGL006' },
        { id: 'P007', name: '武汉江汉区消防设计', code: 'WHJH007' },
        { id: 'P008', name: '成都锦江区消防检测', code: 'CDJJ008' },
        { id: 'P009', name: '重庆渝中区消防维保', code: 'CQYZ009' },
        { id: 'P010', name: '西安雁塔区消防评估', code: 'XAYT010' }
      ]
      const project = projectList.find(p => p.id === id)
      
      if (!project) {
        resolve(null)
        return
      }

      const financeData = {
        projectId: project.id,
        projectName: project.name,
        projectCode: project.code,
        invoiceDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
        paymentDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
        taxRate: (Math.random() * 10 + 1).toFixed(2),
        buyerInfo: `购方${project.id}`,
        invoiceType: ['增值税专用发票', '增值税普通发票', '其他'][Math.floor(Math.random() * 3)],
        netIncome: (Math.random() * 100000).toFixed(2),
        inputTaxRate: (Math.random() * 10 + 1).toFixed(2),
        sellerInfo: `售方${project.id}`,
        inputInvoiceType: ['增值税专用发票', '增值税普通发票', '其他'][Math.floor(Math.random() * 3)],
        subcontractPaymentDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
        purchasePaymentDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
        reimbursementPaymentDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1).toISOString().split('T')[0],
        attachments: [],
        operationRecords: [
          {
            operator: '张三',
            time: new Date().toLocaleString('zh-CN'),
            type: 'create',
            content: '创建财务记录'
          }
        ]
      }
      resolve(financeData)
    }, 500)
  })
}

// 创建财务记录
const createFinance = (data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const newRecord = {
        ...data,
        operationRecords: [
          {
            operator: '张三',
            time: new Date().toLocaleString('zh-CN'),
            type: 'create',
            content: '创建财务记录'
          }
        ]
      }
      resolve(newRecord)
    }, 500)
  })
}

// 更新财务记录
const updateFinance = (id, data) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const updatedRecord = {
        ...data,
        operationRecords: [
          {
            operator: '张三',
            time: new Date().toLocaleString('zh-CN'),
            type: 'update',
            content: '更新财务记录'
          }
        ]
      }
      resolve(updatedRecord)
    }, 500)
  })
}

// 删除财务记录
const deleteFinance = (id) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({ success: true })
    }, 500)
  })
}

// 模拟数据服务
export const mockDataService = {
  getProjects: async ({ page = 1, limit = 10 }) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const mockProjects = Array.from({ length: 50 }, (_, index) => {
      const id = index + 1;
      const statuses = ['planning', 'in_progress', 'completed', 'on_hold', 'cancelled'];
      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const progress = Math.floor(Math.random() * 101);
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - Math.floor(Math.random() * 365));
      const endDate = new Date(startDate);
      endDate.setDate(endDate.getDate() + Math.floor(Math.random() * 365));
      
      return {
        id,
        code: `PROJ-${String(id).padStart(4, '0')}`,
        name: `项目 ${id}`,
        manager: `负责人 ${Math.floor(Math.random() * 5) + 1}`,
        members: `成员 ${Math.floor(Math.random() * 10) + 1}, 成员 ${Math.floor(Math.random() * 10) + 1}`,
        status,
        progress,
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        // 关联单位
        constructionUnit: `建设单位 ${Math.floor(Math.random() * 5) + 1}`,
        designUnit: `设计单位 ${Math.floor(Math.random() * 5) + 1}`,
        contractorUnit: `施工单位 ${Math.floor(Math.random() * 5) + 1}`,
        supervisorUnit: `监理单位 ${Math.floor(Math.random() * 5) + 1}`,
        reviewUnit: `审图单位 ${Math.floor(Math.random() * 5) + 1}`,
        // 项目基本信息
        address: `项目地址 ${Math.floor(Math.random() * 5) + 1}`,
        archiveNumber: `档案号-${String(id).padStart(4, '0')}`,
        engineeringNumber: `工程编号-${String(id).padStart(4, '0')}`,
        // 项目性质
        usageType: ['住宅', '商业', '工业', '公共建筑', '基础设施'][Math.floor(Math.random() * 5)],
        engineeringType: ['新建', '改建', '扩建', '装修', '维修'][Math.floor(Math.random() * 5)],
        // 其他信息
        constructionPermitNumber: `施工许可证号-${String(id).padStart(4, '0')}`,
        area: `${Math.floor(Math.random() * 10000) + 1000} 平方米`,
        specialSystem: ['无', '消防系统', '安防系统', '空调系统', '智能系统'][Math.floor(Math.random() * 5)],
        companyManager: `公司业务负责人 ${Math.floor(Math.random() * 5) + 1}`,
        clientContact: `甲方对接人 ${Math.floor(Math.random() * 5) + 1}`
      };
    });

    const start = (page - 1) * limit;
    const end = start + limit;
    const paginatedProjects = mockProjects.slice(start, end);

    return {
      results: paginatedProjects,
      totalResults: mockProjects.length,
      page,
      limit
    };
  },

  async getProject(id) {
    await delay(500); // 模拟500ms的网络延迟
    return generateProject(id);
  },

  async updateProject(id, projectData) {
    await delay(500); // 模拟500ms的网络延迟
    // 在实际应用中，这里会调用后端API更新项目
    console.log('更新项目:', id, projectData);
    return { success: true };
  },

  // 获取财务统计数据
  async getFinanceStats() {
    await new Promise(resolve => setTimeout(resolve, 500))
    return generateFinanceStats()
  },

  getFinances,
  getFinance,
  createFinance,
  updateFinance,
  deleteFinance
}; 