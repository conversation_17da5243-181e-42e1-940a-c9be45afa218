<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 加载状态 -->
    <div v-if="isLoading" class="bg-white rounded-lg shadow p-8 text-center">
      <div class="flex justify-center">
        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>
      <p class="mt-4 text-gray-600">正在加载采购单详细信息，请稍候...</p>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <router-link to="/purchases" class="text-blue-600 hover:text-blue-800 flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg><h1 class="page-title">采购单详情</h1>
          </router-link>
        </div>
      </div>
      <div class="px-6 py-4 bg-red-50 border-l-4 border-red-500">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm text-red-700">{{ error }}</p>
          </div>
        </div>
      </div>
      <div class="px-6 py-4 flex justify-center">
        <button @click="fetchPurchaseDetail" class="btn btn-primary">
          <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
          </svg>
          重新加载
        </button>
      </div>
    </div>

    <!-- 采购单详情 -->
    <div v-if="!isLoading && !error && purchaseOrder" class="bg-white rounded-lg shadow overflow-hidden">
      <!-- 采购单头部信息 -->
      <div class="px-6 py-5 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <router-link to="/purchases" class="text-blue-600 hover:text-blue-800 flex items-center">
            <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg><h1 class="page-title">采购单详情</h1>
          </router-link>


          <div class="flex space-x-2">
            <button class="btn btn-secondary">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
              </svg>
              打印
            </button>
            <router-link :to="`/purchases/edit/${purchaseOrder.id}`" class="btn btn-primary">
              <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              编辑
            </router-link>
          </div>
        </div>
      </div>

      <!-- 采购单基本信息 -->
      <div class="px-6 py-4 bg-gray-50">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <p class="text-sm font-medium text-gray-500">采购单号 2</p>
            <p class="mt-1 text-lg font-semibold">{{ purchaseOrder.id }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">供应商</p>
            <p class="mt-1">{{ purchaseOrder.supplier }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">采购日期</p>
            <p class="mt-1">{{ purchaseOrder.date }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">预计到货日期</p>
            <p class="mt-1">{{ purchaseOrder.expectedDeliveryDate }}</p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">状态</p>
            <p class="mt-1">
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="{
                'bg-green-100 text-green-800': purchaseOrder.status === '已完成',
                'bg-yellow-100 text-yellow-800': purchaseOrder.status === '待审核',
                'bg-blue-100 text-blue-800': purchaseOrder.status === '已审核'
              }">
                {{ purchaseOrder.status }}
              </span>
            </p>
          </div>
          <div>
            <p class="text-sm font-medium text-gray-500">创建人</p>
            <p class="mt-1">{{ purchaseOrder.creator }}</p>
          </div>
        </div>
      </div>

      <!-- 采购单明细 -->
      <div class="px-6 py-4">
        <h2 class="text-lg font-medium text-gray-900 mb-4">采购明细</h2>
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">序号</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品编码</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">产品名称</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格型号</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单位</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单价</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金额</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(item, index) in purchaseOrder.items" :key="index">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ index + 1 }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.code }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.name }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.spec }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.unit }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ item.quantity }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥{{ item.price }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">¥{{ item.amount }}</td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td colspan="7" class="px-6 py-4 text-right text-sm font-medium">合计：</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-bold">¥{{ purchaseOrder.totalAmount }}</td>
            </tr>
          </tfoot>
        </table>
      </div>

      <!-- 备注信息 -->
      <div class="px-6 py-4 border-t border-gray-200">
        <h2 class="text-lg font-medium text-gray-900 mb-2">备注</h2>
        <p class="text-gray-600">{{ purchaseOrder.remarks || '无' }}</p>
      </div>

      <!-- 审批记录 -->
      <div class="px-6 py-4 border-t border-gray-200">
        <h2 class="text-lg font-medium text-gray-900 mb-4">审批记录</h2>
        <div v-if="purchaseOrder.approvalRecords.length === 0" class="text-gray-500">暂无审批记录。该采购单尚未经过任何审批流程。</div>
        <div v-else class="space-y-4">
          <div v-for="(record, index) in purchaseOrder.approvalRecords" :key="index" class="flex items-start">
            <div class="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <span class="text-blue-600 font-medium">{{ record.approver.charAt(0) }}</span>
            </div>
            <div class="ml-3">
              <div class="text-sm font-medium text-gray-900">{{ record.approver }}</div>
              <div class="text-sm text-gray-500">{{ record.date }} - {{ record.status }}</div>
              <div class="mt-1 text-sm text-gray-600">{{ record.comments }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';

const route = useRoute();
const purchaseId = route.params.id;

// 状态
const isLoading = ref(true);
const error = ref(null);
const purchaseOrder = ref(null);

// 获取采购单详情
async function fetchPurchaseDetail() {
  isLoading.value = true;
  error.value = null;

  try {
    const response = await axios.get(`/api/purchases/${purchaseId}`);
    purchaseOrder.value = response.data;

    // 如果需要获取审批记录，可以再发送一个请求
    const approvalResponse = await axios.get(`/api/purchases/${purchaseId}/approvals`);
    purchaseOrder.value.approvalRecords = approvalResponse.data || [];

  } catch (err) {
    console.error('获取采购单详情失败:', err);
    error.value = err.response?.data?.message || '很抱歉，无法获取采购单详细信息。可能是网络连接问题或该采购单不存在。请点击下方"重新加载"按钮再试一次，或返回列表页查看其他采购单。';
  } finally {
    isLoading.value = false;
  }
}

// 格式化金额
function formatCurrency(value) {
  if (!value && value !== 0) return '0.00';
  return parseFloat(value).toFixed(2);
}

onMounted(() => {
  console.log(`加载采购单详情，ID: ${purchaseId}`);
  fetchPurchaseDetail();
});
</script>