<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="page-header">
      <div>
        <h1 class="page-title">用户管理</h1>
        <p class="page-subtitle">管理用户账户和访问权限</p>
      </div>
      <button
        @click="router.push('/admin/users/create')"
        class="btn btn-primary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        添加新用户
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="filter-card">
      <div class="flex flex-wrap gap-6 items-end">
        <div class="flex-grow min-w-[250px]">
          <label for="search" class="form-label font-medium">搜索用户</label>
          <div class="relative mt-2 flex">
            <div class="relative flex-grow">
              <input
                id="search"
                v-model="searchQuery"
                type="text"
                placeholder="按姓名、邮箱或角色搜索..."
                class="form-input h-10 pl-10 py-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
                @keyup.enter="fetchUsers"
              />
              <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>
        </div>
        <div class="min-w-[150px]">
          <label for="role-filter" class="form-label font-medium">用户角色</label>
          <select id="role-filter" v-model="roleFilter" class="form-input h-10 mt-2 py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200">
            <option value="">所有角色</option>
            <option value="admin">管理员</option>
           
            <option value="user">普通用户</option>
          </select>
        </div>
        <div class="min-w-[150px]">
          <label for="status-filter" class="form-label font-medium">状态</label>
          <select id="status-filter" v-model="statusFilter" class="form-input h-10 mt-2 py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200">
            <option value="">所有状态</option>
            <option value="active">激活</option>
            <option value="inactive">禁用</option>
          </select>
        </div>

      </div>
      <div class="flex justify-end mt-6">
        <button @click="resetFilters" class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200">重置</button>
        <button @click="fetchUsers" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200">查询</button>
      </div>
    </div>

    <!-- Users Table -->
    <div v-if="loading" class="text-center py-12">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-4 text-gray-600 font-medium">加载用户中...</p>
    </div>

    <div v-else-if="!users?.length" class="card text-center py-16 shadow-md">
      <svg class="w-20 h-20 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
      </svg>
      <h3 class="mt-6 text-xl font-medium text-gray-900">未找到用户</h3>
      <p class="mt-3 text-gray-600 max-w-md mx-auto">
        {{ searchQuery || roleFilter || statusFilter ? '请尝试调整搜索或筛选条件' : '点击添加新用户按钮创建第一个用户' }}
      </p>
      <div class="mt-8" v-if="!searchQuery && !roleFilter && !statusFilter">
        <button
          @click="router.push('/admin/users/create')"
          class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200">
          创建第一个用户
        </button>
      </div>
    </div>

    <div v-else>
      <div class="card overflow-hidden shadow-md">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">角色</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">部门</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建日期</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="user in users" :key="user.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center">
                      {{ getUserInitials(user.name) }}
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                      <div class="text-xs text-gray-500">{{ user.email }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="['badge', getRoleBadgeClass(user.role)]">
                    {{ getRoleLabel(user.role) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ getDepartmentName(user.departmentId) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="['badge', user.status === 'active' ? 'badge-success' : 'badge-gray']">
                    {{ user.status === 'active' ? '激活' : '禁用' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(user.createdAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    @click="router.push(`/admin/users/${user.id}`)"
                    class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                    </svg>
                    <span class="font-medium">编辑</span>
                  </button>
                  <button
                    @click="confirmResetPassword(user)"
                    class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-yellow-400 text-white hover:bg-yellow-500 hover:shadow-lg transition-all duration-200 mr-3 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-yellow-100 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 4h.01M17.657 16.657A8 8 0 118 4.343a8 8 0 019.657 12.314zM12 9v4" />
                    </svg>
                    <span class="font-medium">重置密码</span>
                  </button>
                  <button
                    @click="confirmDelete(user)"
                    class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                    <span class="font-medium">删除</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Pagination -->
      <div class="mt-8 flex justify-between items-center px-2">
        <div class="text-sm text-gray-500">
          显示 {{ (currentPage - 1) * pageSize + 1 }}-{{ Math.min(currentPage * pageSize, totalUsers) }} 条，共 {{ totalUsers }} 条记录
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="changePage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm"
            :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <span class="text-sm font-medium text-gray-700">第 {{ currentPage }} 页</span>
          <button
            @click="changePage(currentPage + 1)"
            :disabled="currentPage * pageSize >= totalUsers"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm"
            :class="{'opacity-50 cursor-not-allowed': currentPage * pageSize >= totalUsers}"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-1">确认删除</h3>
          <p class="text-gray-500">
            您确定要删除用户 {{ userToDelete?.name }} 吗？此操作无法撤销。
          </p>

          <div class="mt-6 flex justify-center space-x-3">
            <button
              type="button"
              @click="closeDeleteModal"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="button"
              @click="deleteUser"
              class="btn btn-danger"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                删除中...
              </span>
              <span v-else>删除用户</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Reset Password Modal -->
    <div v-if="showResetPasswordModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-amber-100 mb-4">
            <svg class="h-6 w-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-1">重置密码</h3>
          <p class="text-gray-500 mb-4">
            您正在为用户 {{ userToResetPassword?.name }} 重置密码
          </p>

          <div class="mt-2 mb-4">
            <label for="new-password" class="block text-left text-sm font-medium text-gray-700 mb-1">新密码</label>
            <input
              type="password"
              id="new-password"
              v-model="newPassword"
              class="form-input w-full rounded-md border-gray-300 shadow-sm"
              placeholder="输入新密码"
              @keyup.enter="!isResetting && newPassword.length >= 8 && resetPassword()"
            />
            <p v-if="passwordError" class="mt-1 text-left text-sm text-red-600">{{ passwordError }}</p>
          </div>

          <div class="flex justify-center space-x-3">
            <button
              type="button"
              @click="closeResetPasswordModal"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="button"
              @click="resetPassword"
              class="btn btn-warning"
              :disabled="isResetting || !newPassword"
            >
              <span v-if="isResetting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                处理中...
              </span>
              <span v-else>确认重置</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
import axios from 'axios';
import { useAuthStore } from '../../stores/auth';
import { useRouter } from 'vue-router';
import { useDepartmentStore } from '@/stores/department';
import { useNotificationStore } from '@/stores/notification';
import notificationService from '@/services/notification.service';

const authStore = useAuthStore();
const departmentStore = useDepartmentStore();
const notificationStore = useNotificationStore();
const currentUserId = computed(() => authStore.user?.id);
const router = useRouter();

// User listing state
const users = ref([]);
const loading = ref(true);
const totalUsers = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchQuery = ref('');
const roleFilter = ref('');
const statusFilter = ref('');
const departmentFilter = ref('');

// 部门选项
const departmentOptions = computed(() => departmentStore.getDepartmentOptions);

// Calculate pagination values
const startIndex = computed(() => (currentPage.value - 1) * pageSize.value);
const endIndex = computed(() => startIndex.value + pageSize.value);
const totalPages = computed(() => Math.ceil(totalUsers.value / pageSize.value));

// Modal state
const showDeleteModal = ref(false);
const userToDelete = ref(null);
const isDeleting = ref(false);

// Reset password modal state
const showResetPasswordModal = ref(false);
const userToResetPassword = ref(null);
const isResetting = ref(false);
const newPassword = ref('');
const passwordError = ref('');

// Fetch users on component mount and check for refresh parameter
onMounted(() => {
  // 检查URL中是否包含refresh参数，如果有则表示需要刷新页面
  const urlParams = new URLSearchParams(window.location.search);
  const refreshParam = urlParams.get('refresh');

  if (refreshParam) {
    // 如果有refresh参数，则清除该参数并刷新页面
    const url = new URL(window.location.href);
    url.searchParams.delete('refresh');
    window.history.replaceState({}, document.title, url.toString());
  }

  // 获取部门列表
  departmentStore.fetchDepartments();

  // 获取用户列表
  fetchUsers();
});

// Fetch users with pagination, search, and filters
const fetchUsers = async () => {
  loading.value = true;
  try {
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value || undefined,
      role: roleFilter.value || undefined,
      status: statusFilter.value || undefined,
      department: departmentFilter.value || undefined
    };
    const response = await axios.get('/api/users', { params });
    users.value = response.data.results || [];
    totalUsers.value = response.data.totalResults || 0;

    // 只在页码超出时调整 currentPage，不再递归 fetchUsers
    const totalPagesCalc = Math.ceil(totalUsers.value / pageSize.value);
    if (currentPage.value > totalPagesCalc && totalPagesCalc > 0) {
      currentPage.value = totalPagesCalc;
      return;
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
    users.value = [];
    totalUsers.value = 0;
  } finally {
    loading.value = false;
  }
};

// Debounced search implementation
let searchTimeout;
const debouncedFetchUsers = () => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    currentPage.value = 1; // Reset to first page on new search
    fetchUsers();
  }, 300);
};

// Pagination handling
const changePage = (page) => {
  if (page < 1 || page > totalPages.value) return;
  currentPage.value = page;
  fetchUsers();
};

// Format date for display
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Get user initials for avatar
const getUserInitials = (name) => {
  if (!name) return '?';

  const names = name.split(' ');
  if (names.length === 1) return names[0].charAt(0).toUpperCase();
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
};

// 获取角色标签
const getRoleLabel = (role) => {
  const roleLabels = {
    'admin': '管理员',
    'editor': '编辑者',
    'user': '普通用户',
    'manager': '经理'
  };
  return roleLabels[role] || role;
};

// 获取角色徽章样式
const getRoleBadgeClass = (role) => {
  const roleBadges = {
    'admin': 'badge-blue',
    'editor': 'badge-purple',
    'user': 'badge-gray',
    'manager': 'badge-green'
  };
  return roleBadges[role] || 'badge-gray';
};

// Delete user handling
const confirmDelete = (user) => {
  userToDelete.value = user;
  showDeleteModal.value = true;
};

const closeDeleteModal = () => {
  showDeleteModal.value = false;
  userToDelete.value = null;
};

const deleteUser = async () => {
  if (!userToDelete.value) return;

  isDeleting.value = true;

  try {
    // 保存用户名，因为closeDeleteModal会将userToDelete设置为null
    const userName = userToDelete.value.name;

    await axios.delete(`/api/users/${userToDelete.value.id}`);
    closeDeleteModal();
    fetchUsers();
    // 添加删除成功的提示
    notificationService.success('删除成功', `用户 ${userName} 已成功删除`);
  } catch (error) {
    console.error('删除用户失败:', error);
    // 添加删除失败的提示
    notificationService.error('删除失败', `删除用户失败: ${error.response?.data?.message || '未知错误'}`);
  } finally {
    isDeleting.value = false;
  }
};

// 重置过滤器
const resetFilters = () => {
  searchQuery.value = '';
  roleFilter.value = '';
  statusFilter.value = '';
  departmentFilter.value = '';
  fetchUsers();
};

// Get department name
const getDepartmentName = (departmentId) => {
  if (!departmentId) return '未分配';
  const department = departmentOptions.value.find(d => d.value === departmentId);
  return department ? department.label : '未分配';
};

// Reset password handling
const confirmResetPassword = (user) => {
  userToResetPassword.value = user;
  newPassword.value = '';
  passwordError.value = '';
  showResetPasswordModal.value = true;
};

const closeResetPasswordModal = () => {
  showResetPasswordModal.value = false;
  userToResetPassword.value = null;
  newPassword.value = '';
  passwordError.value = '';
};

const resetPassword = async () => {
  if (!userToResetPassword.value) return;

  // Validate password
  if (!newPassword.value) {
    passwordError.value = '请输入新密码';
    return;
  }

  if (newPassword.value.length < 8) {
    passwordError.value = '密码长度至少为8个字符';
    return;
  }

  isResetting.value = true;
  passwordError.value = '';

  try {
    console.log('开始重置密码，用户ID:', userToResetPassword.value.id);
    console.log('请求数据:', { newPassword: newPassword.value });

    // 获取认证令牌
    const token = localStorage.getItem('token');
    console.log('认证令牌存在:', !!token);

    // 使用带有认证的请求
    const response = await axios({
      method: 'post',
      url: `/api/users/${userToResetPassword.value.id}/reset-password`,
      data: {
        newPassword: newPassword.value
      },
      headers: {
        'Content-Type': 'application/json',
        ...(token ? { 'Authorization': `Bearer ${token}` } : {})
      }
    });

    console.log('重置密码成功，响应:', response.data);

    // 保存用户名，因为closeResetPasswordModal会将userToResetPassword设置为null
    const userName = userToResetPassword.value.name;

    closeResetPasswordModal();
    // Show success message
    notificationService.success('重置成功', `用户 ${userName} 的密码已重置`);
  } catch (error) {
    console.error('重置密码失败:', error);

    // 详细记录错误信息
    if (error.response) {
      console.error('错误状态码:', error.response.status);
      console.error('错误数据:', error.response.data);
      passwordError.value = error.response.data?.message || '重置密码失败，请重试';
    } else if (error.request) {
      console.error('未收到响应:', error.request);
      passwordError.value = '服务器未响应，请检查网络连接';
    } else {
      console.error('请求配置错误:', error.message);
      passwordError.value = '请求错误: ' + error.message;
    }

    // 显示错误消息
    notificationService.error('重置失败', passwordError.value);
  } finally {
    isResetting.value = false;
  }
};
</script>

<style scoped>
/* Page transitions */
.page-header {
  animation: fadeSlideIn 0.6s ease-out;
  animation-delay: 0s;
}

/* .filter-card, .card 的动画已去除，避免每次查询闪烁 */

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Badge animations */
.badge-success {
  animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(74, 222, 128, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(74, 222, 128, 0);
  }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

/* Button styling */
.btn-warning {
  background-color: rgb(245, 158, 11); /* bg-amber-500 */
  color: white;
  padding: 0.5rem 1rem; /* py-2 px-4 */
  border-radius: 0.375rem; /* rounded-md */
  transition-property: color, background-color; /* transition-colors */
  transition-duration: 200ms; /* duration-200 */
}
.btn-warning:hover {
  background-color: rgb(217, 119, 6); /* hover:bg-amber-600 */
}
</style>