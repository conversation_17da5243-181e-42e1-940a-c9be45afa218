<template>
  <div class="page-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">编辑工时记录</h1>
        <p class="page-subtitle">编辑员工工时信息</p>
      </div>
      <div class="flex space-x-2">
        <button
          @click="goBack"
          class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
          </svg>
          返回列表
        </button>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="mb-6">
      <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 rounded-md shadow-sm">
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
          </svg>
          <span class="font-medium">出错了：</span>
        </div>
        <p class="mt-2 whitespace-pre-line">{{ error }}</p>
      </div>
    </div>

    <!-- 成功提示 -->
    <div v-if="successMessage" class="mb-6">
      <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded-md shadow-sm">
        <div class="flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span class="font-medium">成功：</span>
        </div>
        <p class="mt-2 whitespace-pre-line">{{ successMessage }}</p>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="loader"></div>
      <span class="ml-3 text-gray-600">正在加载数据...</span>
    </div>

    <!-- 工时记录表单 -->
    <form v-if="!loading" @submit.prevent="validateAndSubmit" class="bg-white rounded-lg shadow-lg p-6 mb-6">
      <!-- 基本信息区块 -->
      <div class="form-section">
        <h2 class="form-section-title">基本信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 日期 -->
          <div>
            <label for="date" class="block text-sm font-medium text-gray-700">日期 <span class="form-required">*</span></label>
            <input
              type="date"
              id="date"
              v-model="workHourForm.date"
              class="form-input"
              required
            >
          </div>

          <!-- 开始时间 -->
          <div>
            <label for="startTime" class="block text-sm font-medium text-gray-700">开始时间 <span class="form-required">*</span></label>
            <input
              type="time"
              id="startTime"
              v-model="workHourForm.startTime"
              class="form-input"
              required
              @change="calculateWorkHours"
            >
          </div>

          <!-- 结束时间 -->
          <div>
            <label for="endTime" class="block text-sm font-medium text-gray-700">结束时间 <span class="form-required">*</span></label>
            <input
              type="time"
              id="endTime"
              v-model="workHourForm.endTime"
              class="form-input"
              required
              @change="calculateWorkHours"
            >
          </div>

          <!-- 工作时长 -->
          <div>
            <label for="workHours" class="block text-sm font-medium text-gray-700">工作时长(小时) <span class="form-required">*</span></label>
            <input
              type="number"
              id="workHours"
              v-model="workHourForm.workHours"
              class="form-input"
              step="0.5"
              min="0.5"
              max="24"
              required
            >
          </div>

          <!-- 工作地点 -->
          <div>
            <label for="workLocation" class="block text-sm font-medium text-gray-700">工作地点</label>
            <select
              id="workLocation"
              v-model="workHourForm.workLocation"
              class="form-input"
            >
              <option value="办公室">办公室</option>
              <option value="现场">现场</option>
              <option value="远程">远程</option>
              <option value="其他">其他</option>
            </select>
          </div>

          <!-- 加班标记 -->
          <div class="flex items-center h-full pt-6">
            <input
              type="checkbox"
              id="overtime"
              v-model="workHourForm.overtime"
              class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            >
            <label for="overtime" class="ml-2 block text-sm text-gray-700">加班</label>
          </div>
        </div>
      </div>

      <!-- 员工信息区块 -->
      <div class="form-section">
        <h2 class="form-section-title">员工信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 员工姓名 -->
          <div>
            <label for="employeeName" class="block text-sm font-medium text-gray-700">员工姓名 <span class="form-required">*</span></label>
            <input
              type="text"
              id="employeeName"
              v-model="workHourForm.employeeName"
              class="form-input"
              required
              @blur="autoFillEmployeeInfo"
            >
          </div>

          <!-- 部门 -->
          <div>
            <label for="department" class="block text-sm font-medium text-gray-700">部门</label>
            <input
              type="text"
              id="department"
              v-model="workHourForm.department"
              class="form-input bg-gray-100"
              readonly
            >
          </div>

          <!-- 用工类型 -->
          <div>
            <label for="employmentType" class="block text-sm font-medium text-gray-700">用工类型 <span class="form-required">*</span></label>
            <input
              type="text"
              id="employmentType"
              v-model="workHourForm.employmentType"
              class="form-input bg-gray-100"
              readonly
            >
          </div>

          <!-- 缺勤标记 -->
          <div class="flex items-center h-full pt-6">
            <input
              type="checkbox"
              id="absence"
              v-model="workHourForm.absence"
              class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            >
            <label for="absence" class="ml-2 block text-sm text-gray-700">缺勤</label>
          </div>
        </div>
      </div>

      <!-- 项目信息区块 -->
      <div class="form-section">
        <h2 class="form-section-title">项目信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 项目名称 -->
          <div>
            <label for="projectName" class="block text-sm font-medium text-gray-700">项目名称 <span class="form-required">*</span></label>
            <select
              id="projectName"
              v-model="workHourForm.projectName"
              class="form-input"
              required
            >
              <option value="">选择项目</option>
              <option v-for="project in projects" :key="project.id" :value="project.name">{{ project.name }}</option>
            </select>
          </div>

          <!-- 备注 -->
          <div>
            <label for="remarks" class="block text-sm font-medium text-gray-700">备注</label>
            <textarea
              id="remarks"
              v-model="workHourForm.remarks"
              rows="3"
              class="form-input"
            ></textarea>
          </div>
        </div>
      </div>

      <!-- 薪酬信息区块 -->
      <div class="form-section">
        <h2 class="form-section-title">薪酬信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 小时工资 -->
          <div>
            <label for="hourlyRate" class="block text-sm font-medium text-gray-700">小时工资(元/小时)</label>
            <input
              type="number"
              id="hourlyRate"
              v-model="workHourForm.hourlyRate"
              class="form-input"
              min="0"
              step="0.01"
              @change="calculatePayableAmount"
            >
          </div>

          <!-- 日工资 -->
          <div>
            <label for="dailyWage" class="block text-sm font-medium text-gray-700">日工资(元/天)</label>
            <input
              type="number"
              id="dailyWage"
              v-model="workHourForm.dailyWage"
              class="form-input"
              min="0"
              step="0.01"
              @change="calculateHourlyRate"
            >
          </div>

          <!-- 应付金额 -->
          <div>
            <label for="payableAmount" class="block text-sm font-medium text-gray-700">应付金额(元)</label>
            <input
              type="number"
              id="payableAmount"
              v-model="workHourForm.payableAmount"
              class="form-input"
              min="0"
              step="0.01"
              readonly
            >
          </div>
        </div>
      </div>

      <!-- 审批信息区块 -->
      <div class="form-section">
        <h2 class="form-section-title">审批信息</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 员工签字 -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="employeeSign"
              v-model="workHourForm.employeeSign"
              class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            >
            <label for="employeeSign" class="ml-2 block text-sm text-gray-700">员工已签字</label>
          </div>

          <!-- 主管签字 -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="supervisorSign"
              v-model="workHourForm.supervisorSign"
              class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            >
            <label for="supervisorSign" class="ml-2 block text-sm text-gray-700">主管已签字</label>
          </div>

          <!-- 绩效等级 -->
          <div>
            <label for="performanceLevel" class="block text-sm font-medium text-gray-700">绩效等级</label>
            <select
              id="performanceLevel"
              v-model="workHourForm.performanceLevel"
              class="form-input"
            >
              <option value="">未评定</option>
              <option value="A">A - 优秀</option>
              <option value="B">B - 良好</option>
              <option value="C">C - 一般</option>
              <option value="D">D - 较差</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="flex justify-end space-x-3 mt-8">
        <button
          type="button"
          @click="goBack"
          class="btn btn-secondary"
        >
          返回
        </button>
        <button
          type="submit"
          class="btn btn-primary"
          :disabled="loading"
        >
          {{ loading ? '保存中...' : '保存' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import workhoursService from '@/services/workhours.service';
import { useToast } from 'vue-toastification';
import { useDepartmentStore } from '@/stores/department';
import { projectService } from '@/services/apiService';

const router = useRouter();
const route = useRoute();
const toast = useToast();
const departmentStore = useDepartmentStore();

// 状态变量
const workHourForm = ref({
  // 员工信息
  employeeName: '',
  employeeId: '',
  department: '',
  // 基本信息
  date: new Date().toISOString().split('T')[0],
  startTime: '09:00',
  endTime: '18:00',
  workHours: 8,
  employmentType: '合同工',
  workLocation: '办公室',
  // 项目信息
  projectName: '',
  remarks: '',
  // 薪酬信息
  hourlyRate: 100,
  payableAmount: 800,
  dailyWage: 800,
  overtime: false,
  absence: false,
  // 审批信息
  employeeSign: false,
  supervisorSign: false,
  performanceLevel: '',
});

const loading = ref(false);
const error = ref('');
const successMessage = ref('');
const projects = ref([]);
const workHourId = ref(null);

// 获取部门选项
const departmentOptions = computed(() => {
  return departmentStore.departments.map(dept => ({
    value: dept.name,
    label: dept.name
  }));
});

// 计算工作时长
const calculateWorkHours = () => {
  if (workHourForm.value.startTime && workHourForm.value.endTime) {
    const start = new Date(`2000-01-01T${workHourForm.value.startTime}`);
    const end = new Date(`2000-01-01T${workHourForm.value.endTime}`);

    // 如果结束时间小于开始时间，认为是跨天工作
    if (end < start) {
      end.setDate(end.getDate() + 1);
    }

    const diffMs = end - start;
    const diffHours = diffMs / (1000 * 60 * 60);

    // 减去午休时间（如果工作时间跨越中午12点到13点）
    let lunchBreak = 0;
    if (start.getHours() < 13 && end.getHours() >= 13) {
      lunchBreak = 1; // 1小时午休
    }

    workHourForm.value.workHours = Math.max(0, diffHours - lunchBreak).toFixed(1);
    calculatePayableAmount();
  }
};

// 根据工时和小时工资计算应付金额
const calculatePayableAmount = () => {
  const hours = parseFloat(workHourForm.value.workHours);
  const rate = parseFloat(workHourForm.value.hourlyRate);

  if (!isNaN(hours) && !isNaN(rate)) {
    // 如果是加班，可以应用加班费率（例如1.5倍）
    const overtimeMultiplier = workHourForm.value.overtime ? 1.5 : 1;
    workHourForm.value.payableAmount = (hours * rate * overtimeMultiplier).toFixed(2);
    workHourForm.value.dailyWage = (hours * rate).toFixed(2);
  }
};

// 根据日工资和工时计算小时工资
const calculateHourlyRate = () => {
  const hours = parseFloat(workHourForm.value.workHours);
  const dailyWage = parseFloat(workHourForm.value.dailyWage);

  if (!isNaN(hours) && !isNaN(dailyWage) && hours > 0) {
    workHourForm.value.hourlyRate = (dailyWage / hours).toFixed(2);
    calculatePayableAmount();
  }
};

// 根据用工类型更新工资率
const updateRateByEmploymentType = () => {
  if (workHourForm.value.employmentType === '合同工') {
    workHourForm.value.hourlyRate = 120;
  } else if (workHourForm.value.employmentType === '临时工') {
    workHourForm.value.hourlyRate = 80;
  }
  calculatePayableAmount();
};

// 获取工时记录
const fetchWorkHour = async (id) => {
  loading.value = true;
  error.value = '';
  try {
    const response = await workhoursService.getWorkHour(id);
    const data = response.data;
    
    // 将数据映射到表单
    workHourForm.value = {
      // 员工信息
      employeeName: data.employeeName || '',
      employeeId: data.employeeId || '',
      department: data.department || '',
      // 基本信息
      date: data.date || new Date().toISOString().split('T')[0],
      startTime: data.startTime || '09:00',
      endTime: data.endTime || '18:00',
      workHours: data.workHours || 8,
      employmentType: data.employmentType || '合同工',
      workLocation: data.workLocation || '办公室',
      // 项目信息
      projectName: data.projectName || '',
      remarks: data.remarks || '',
      // 薪酬信息
      hourlyRate: data.hourlyRate || 100,
      payableAmount: data.payableAmount || 800,
      dailyWage: data.dailyWage || 800,
      overtime: data.overtime || false,
      absence: data.absence || false,
      // 审批信息
      employeeSign: data.employeeSign || false,
      supervisorSign: data.supervisorSign || false,
      performanceLevel: data.performanceLevel || '',
    };
    
  } catch (err) {
    console.error('获取工时记录失败:', err);
    error.value = '获取工时记录失败: ' + (err.response?.data?.message || err.message);
    toast.error(error.value);
  } finally {
    loading.value = false;
  }
};

// 获取项目列表
const fetchProjects = async () => {
  loading.value = true;
  error.value = '';
  try {
    // 获取进行中的项目
    const response = await projectService.getProjects({status: 'in_progress'});
    projects.value = response.data.results || [];
  } catch (err) {
    console.error('获取项目列表失败:', err);
    error.value = '获取项目列表失败: ' + (err.response?.data?.message || err.message);
    toast.error(error.value);
  } finally {
    loading.value = false;
  }
};

// 验证表单并提交
const validateAndSubmit = () => {
  // 验证必填字段
  const requiredFields = [
    { field: 'employeeName', label: '员工姓名' },
    { field: 'date', label: '日期' },
    { field: 'startTime', label: '开始时间' },
    { field: 'endTime', label: '结束时间' },
    { field: 'workHours', label: '工作时长' },
    { field: 'projectName', label: '项目名称' },
    { field: 'employmentType', label: '用工类型' }
  ];

  for (const { field, label } of requiredFields) {
    if (!workHourForm.value[field]) {
      toast.error(`${label}是必填项`);
      return;
    }
  }

  // 所有验证通过，提交表单
  submitWorkHours();
};

// 自动根据员工姓名带出部门和用工类型（假设有员工信息表，示例用静态数据）
const employeeInfoMap = {
  '张三': { department: '技术部', employmentType: '合同工' },
  '李四': { department: '市场部', employmentType: '临时工' },
  // 可扩展更多员工
};

function autoFillEmployeeInfo() {
  const info = employeeInfoMap[workHourForm.value.employeeName];
  if (info) {
    workHourForm.value.department = info.department;
    workHourForm.value.employmentType = info.employmentType;
    updateRateByEmploymentType();
  }
}

// 提交工时记录时，去除自动带出的部门和用工类型字段（只读时不提交）
const submitWorkHours = async () => {
  loading.value = true;
  error.value = '';
  successMessage.value = '';
  try {
    // 构造要提交的数据，去除只读字段
    const submitData = { ...workHourForm.value };
    // 如果部门和用工类型是自动带出的，则不提交
    if (employeeInfoMap[submitData.employeeName]) {
      delete submitData.department;
      delete submitData.employmentType;
    }
    await workhoursService.updateWorkHour(workHourId.value, submitData);
    
    // 显示成功消息
    successMessage.value = `工时记录已成功更新。\n\n员工：${workHourForm.value.employeeName}\n项目：${workHourForm.value.projectName}\n日期：${workHourForm.value.date}`;
    toast.success('工时记录更新成功');
    
    // 短暂延迟后返回列表页
    setTimeout(() => {
      goBack();
    }, 2000);
    
  } catch (err) {
    console.error('更新工时记录失败:', err);
    error.value = '更新工时记录失败: ' + (err.response?.data?.message || err.message);
    toast.error(error.value);
  } finally {
    loading.value = false;
  }
};

// 返回列表页
const goBack = () => {
  router.push('/workhours');
};

// 当表单中的工时或小时工资变化时，重新计算应付金额
watch([
  () => workHourForm.value.workHours,
  () => workHourForm.value.hourlyRate,
  () => workHourForm.value.overtime
], () => {
  calculatePayableAmount();
});

// 初始化
onMounted(async () => {
  // 获取路由参数中的ID
  workHourId.value = route.params.id;
  
  if (!workHourId.value) {
    error.value = '未提供工时记录ID';
    toast.error(error.value);
    return;
  }
  
  // 获取部门列表
  departmentStore.fetchDepartments();
  
  // 获取项目列表
  await fetchProjects();
  
  // 获取工时记录数据
  await fetchWorkHour(workHourId.value);
});
</script>

<style scoped>
.page-header {
  @apply flex justify-between items-center mb-6;
}

.page-title {
  @apply text-2xl font-bold text-gray-800;
}

.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}

.form-section {
  @apply mb-8 border-b pb-6;
}

.form-section-title {
  @apply text-lg font-medium text-gray-700 mb-4;
}

.form-input {
  @apply mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm;
}

.btn {
  @apply px-4 py-2 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-gray-500;
}

.loader {
  @apply w-6 h-6 border-2 border-t-2 border-gray-200 rounded-full animate-spin;
  border-top-color: #3498db;
}
</style>