const httpStatus = require('http-status');
const jwt = require('jsonwebtoken');
const moment = require('moment');
const catchAsync = require('../utils/catchAsync');
const { User } = require('../models');
const ApiError = require('../utils/ApiError');
const logger = require('../config/logger');
const config = require('../config/config');

/**
 * Login with email and password
 * @route POST /api/test/auth/login
 */
const login = catchAsync(async (req, res) => {
  const { email, password } = req.body;

  // Find user by email
  const user = await User.findOne({ where: { email } });

  if (!user) {
    throw new ApiError(401, 'Incorrect email or password');
  }

  // Generate access token
  const accessTokenExpires = moment().add(30, 'minutes');
  const accessToken = generateToken(user.id, accessTokenExpires, 'access', config.jwt.secret);

  // Generate refresh token
  const refreshTokenExpires = moment().add(30, 'days');
  const refreshToken = generateToken(user.id, refreshTokenExpires, 'refresh', config.jwt.secret);

  // Return user and tokens
  res.send({
    user,
    tokens: {
      access: {
        token: accessToken,
        expires: accessTokenExpires.toDate(),
      },
      refresh: {
        token: refreshToken,
        expires: refreshTokenExpires.toDate(),
      },
    },
  });
});

/**
 * Generate JWT token
 * @param {string} userId
 * @param {Moment} expires
 * @param {string} type
 * @param {string} secret
 * @returns {string}
 */
const generateToken = (userId, expires, type, secret) => {
  const payload = {
    sub: userId,
    iat: moment().unix(),
    exp: expires.unix(),
    type,
  };
  return jwt.sign(payload, secret);
};

/**
 * Verify token and get user
 * @route GET /api/test/auth/verify
 */
const verifyToken = catchAsync(async (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new ApiError(401, 'Please authenticate');
  }

  const token = authHeader.split(' ')[1];

  try {
    const payload = jwt.verify(token, config.jwt.secret);
    const user = await User.findByPk(payload.sub, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      throw new ApiError(401, 'User not found');
    }

    res.send({
      user,
      token: {
        payload,
        valid: true
      }
    });
  } catch (error) {
    logger.error(`Token verification error: ${error.message}`);
    throw new ApiError(401, 'Invalid token');
  }
});

module.exports = {
  login,
  verifyToken
};
