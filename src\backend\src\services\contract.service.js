const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { Contract, Project, User } = require('../models');

/**
 * Create a contract
 * @param {Object} contractBody
 * @returns {Promise<Contract>}
 */
const createContract = async (contractBody) => {
  // Check if project exists
  const project = await Project.findByPk(contractBody.projectId);
  if (!project) {
    throw new ApiError(400, 'Project not found');
  }

  // Check if contract number already exists
  if (await Contract.findOne({ where: { contractNumber: contractBody.contractNumber } })) {
    throw new ApiError(400, 'Contract number already exists');
  }

  return Contract.create(contractBody);
};

/**
 * Get contract by id
 * @param {string} id
 * @returns {Promise<Contract>}
 */
const getContractById = async (id) => {
  const contract = await Contract.findByPk(id, {
    include: [
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!contract) {
    throw new ApiError(404, 'Contract not found');
  }
  return contract;
};

/**
 * Update contract by id
 * @param {string} contractId
 * @param {Object} updateBody
 * @returns {Promise<Contract>}
 */
const updateContractById = async (contractId, updateBody) => {
  const contract = await getContractById(contractId);

  // Check if contract number is being updated and already exists
  if (updateBody.contractNumber && updateBody.contractNumber !== contract.contractNumber) {
    const existingContract = await Contract.findOne({
      where: { contractNumber: updateBody.contractNumber }
    });
    if (existingContract) {
      throw new ApiError(400, 'Contract number already exists');
    }
  }

  Object.assign(contract, updateBody);
  await contract.save();
  return contract;
};

/**
 * Delete contract by id
 * @param {string} contractId
 * @returns {Promise<void>}
 */
const deleteContractById = async (contractId) => {
  const contract = await getContractById(contractId);
  await contract.destroy();
};

/**
 * Query for contracts
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Contracts and pagination info
 */
const queryContracts = async (filter, options) => {
  const { page, limit, sortBy, sortOrder } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};
  
  if (filter.projectId) {
    whereClause.projectId = filter.projectId;
  }
  
  if (filter.status) {
    whereClause.status = filter.status;
  }
  
  if (filter.contractType) {
    whereClause.contractType = filter.contractType;
  }
  
  if (filter.search) {
    whereClause[Op.or] = [
      { name: { [Op.like]: `%${filter.search}%` } },
      { contractNumber: { [Op.like]: `%${filter.search}%` } },
      { description: { [Op.like]: `%${filter.search}%` } }
    ];
  }
  
  if (filter.startDateFrom) {
    whereClause.startDate = {
      ...whereClause.startDate,
      [Op.gte]: new Date(filter.startDateFrom)
    };
  }
  
  if (filter.startDateTo) {
    whereClause.startDate = {
      ...whereClause.startDate,
      [Op.lte]: new Date(filter.startDateTo)
    };
  }
  
  if (filter.endDateFrom) {
    whereClause.endDate = {
      ...whereClause.endDate,
      [Op.gte]: new Date(filter.endDateFrom)
    };
  }
  
  if (filter.endDateTo) {
    whereClause.endDate = {
      ...whereClause.endDate,
      [Op.lte]: new Date(filter.endDateTo)
    };
  }

  // Query with pagination
  const { count, rows } = await Contract.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ],
    order: [[sortBy, sortOrder]],
    limit,
    offset,
    distinct: true
  });

  return {
    results: rows,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

module.exports = {
  createContract,
  getContractById,
  updateContractById,
  deleteContractById,
  queryContracts
}; 