/* 统一页面风格和颜色 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 主色调 */
:root {
  --primary-color: #3b82f6; /* blue-500 */
  --primary-hover: #2563eb; /* blue-600 */
  --primary-light: #dbeafe; /* blue-100 */
  --primary-dark: #1d4ed8; /* blue-700 */
  
  --secondary-color: #64748b; /* slate-500 */
  --secondary-hover: #475569; /* slate-600 */
  --secondary-light: #f1f5f9; /* slate-100 */
  
  --success-color: #10b981; /* emerald-500 */
  --success-light: #d1fae5; /* emerald-100 */
  
  --danger-color: #ef4444; /* red-500 */
  --danger-hover: #dc2626; /* red-600 */
  --danger-light: #fee2e2; /* red-100 */
  
  --warning-color: #f59e0b; /* amber-500 */
  --warning-light: #fef3c7; /* amber-100 */
  
  --text-primary: #1e293b; /* slate-800 */
  --text-secondary: #64748b; /* slate-500 */
  --text-light: #94a3b8; /* slate-400 */
  
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc; /* slate-50 */
  --bg-tertiary: #f1f5f9; /* slate-100 */
  
  --border-color: #e2e8f0; /* slate-200 */
  --border-color-focus: #3b82f6; /* blue-500 */
}

@layer components {
  /* 页面布局组件 */
  .page-container {
    @apply max-w-6xl mx-auto px-4 py-6;
  }

  .page-header {
    @apply flex justify-between items-center mb-8;
  }

  .page-title {
    @apply text-2xl font-bold text-slate-800;
  }

  .page-subtitle {
    @apply text-slate-500 mt-1;
  }

  .back-button {
    @apply text-blue-600 hover:text-blue-800 mr-3 transition-colors duration-200;
  }

  /* 卡片组件 */
  .card {
    @apply bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 p-6 mb-6;
  }

  .card-header {
    @apply text-lg font-semibold text-slate-800 mb-5 flex items-center;
  }

  .card-icon {
    @apply w-5 h-5 mr-2 text-blue-500;
  }

  .card-content {
    @apply space-y-5;
  }

  /* 表单元素 */
  .form-label {
    @apply block text-sm font-medium text-slate-700 mb-1;
  }

  .form-input {
    @apply w-full rounded-lg border-slate-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-colors duration-200 p-2.5;
  }

  .form-required {
    @apply text-red-500;
  }

  .form-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-5;
  }

  .form-group {
    @apply space-y-5;
  }

  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply bg-white text-slate-700 border-slate-300 hover:bg-slate-50 focus:ring-blue-500;
  }

  .btn-success {
    @apply bg-emerald-600 text-white hover:bg-emerald-700 focus:ring-emerald-500;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .btn-warning {
    @apply bg-amber-500 text-white hover:bg-amber-600 focus:ring-amber-400;
  }

  /* 徽章样式 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-blue {
    @apply bg-blue-100 text-blue-800;
  }

  .badge-green, .badge-success {
    @apply bg-emerald-100 text-emerald-800;
  }

  .badge-red, .badge-danger {
    @apply bg-red-100 text-red-800;
  }

  .badge-yellow, .badge-warning {
    @apply bg-amber-100 text-amber-800;
  }

  .badge-purple {
    @apply bg-purple-100 text-purple-800;
  }

  .badge-gray {
    @apply bg-slate-100 text-slate-800;
  }

  /* 错误提示 */
  .error-alert {
    @apply mb-6 p-4 bg-red-50 text-red-700 rounded-lg border border-red-100 shadow-sm;
  }

  /* 加载状态 */
  .loading-container {
    @apply text-center py-16;
  }

  .loading-spinner {
    @apply inline-block animate-spin rounded-full h-10 w-10 border-4 border-slate-300 border-t-blue-600;
  }

  .loading-text {
    @apply mt-4 text-slate-600 font-medium;
  }

  /* 动画效果 */
  .fade-slide-in {
    animation: fadeSlideIn 0.6s ease-out;
  }
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
