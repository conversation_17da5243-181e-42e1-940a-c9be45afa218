const { expect } = require('chai');
const sinon = require('sinon');
const { Supplier, User } = require('../../../src/models');
const supplierService = require('../../../src/services/supplier.service');
const ApiError = require('../../../src/utils/ApiError');

describe('Supplier Service', () => {
  let sandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
  });

  afterEach(() => {
    sandbox.restore();
  });

  describe('createSupplier', () => {
    it('should create a supplier successfully', async () => {
      // Arrange
      const supplierData = {
        name: 'Test Supplier',
        category: 'material',
        address: 'Test Address',
        phone: '13800138000',
        contactName: 'Test Contact',
        contactPhone: '13900139000',
        contactEmail: '<EMAIL>',
        rating: 4,
        createdBy: '00000000-0000-0000-0000-000000000000'
      };

      const createStub = sandbox.stub(Supplier, 'create').resolves(supplierData);

      // Act
      const result = await supplierService.createSupplier(supplierData);

      // Assert
      expect(createStub.calledOnce).to.be.true;
      expect(result).to.deep.equal(supplierData);
    });
  });

  describe('getSupplierById', () => {
    it('should return supplier if found', async () => {
      // Arrange
      const supplierId = '1';
      const supplierData = {
        id: supplierId,
        name: 'Test Supplier',
        category: 'material',
        rating: 4
      };

      sandbox.stub(Supplier, 'findByPk').resolves(supplierData);

      // Act
      const result = await supplierService.getSupplierById(supplierId);

      // Assert
      expect(result).to.deep.equal(supplierData);
    });

    it('should throw error if supplier not found', async () => {
      // Arrange
      const supplierId = '999';
      sandbox.stub(Supplier, 'findByPk').resolves(null);

      // Act & Assert
      try {
        await supplierService.getSupplierById(supplierId);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(404);
        expect(error.message).to.include('not found');
      }
    });
  });

  describe('updateSupplierById', () => {
    it('should update supplier successfully', async () => {
      // Arrange
      const supplierId = '1';
      const supplierData = {
        id: supplierId,
        name: 'Test Supplier',
        category: 'material',
        rating: 4,
        save: sandbox.stub().resolves()
      };

      const updateData = {
        name: 'Updated Supplier',
        rating: 5
      };

      sandbox.stub(supplierService, 'getSupplierById').resolves(supplierData);

      // Act
      const result = await supplierService.updateSupplierById(supplierId, updateData);

      // Assert
      expect(result.name).to.equal(updateData.name);
      expect(result.rating).to.equal(updateData.rating);
      expect(supplierData.save.calledOnce).to.be.true;
    });
  });

  describe('deleteSupplierById', () => {
    it('should delete supplier successfully', async () => {
      // Arrange
      const supplierId = '1';
      const supplierData = {
        id: supplierId,
        name: 'Test Supplier',
        destroy: sandbox.stub().resolves()
      };

      sandbox.stub(supplierService, 'getSupplierById').resolves(supplierData);

      // Act
      await supplierService.deleteSupplierById(supplierId);

      // Assert
      expect(supplierData.destroy.calledOnce).to.be.true;
    });
  });

  describe('querySuppliers', () => {
    it('should return suppliers and pagination info', async () => {
      // Arrange
      const filter = { category: 'material' };
      const options = { page: 1, limit: 10 };
      const user = { id: '1', role: 'admin' };
      
      const suppliers = [
        { id: '1', name: 'Supplier 1' },
        { id: '2', name: 'Supplier 2' }
      ];
      
      const findAndCountAllStub = sandbox.stub(Supplier, 'findAndCountAll').resolves({
        count: 2,
        rows: suppliers
      });

      // Act
      const result = await supplierService.querySuppliers(filter, options, user);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      expect(result.results).to.deep.equal(suppliers);
      expect(result.pagination.totalResults).to.equal(2);
    });

    it('should filter suppliers by user for non-admin users', async () => {
      // Arrange
      const filter = { category: 'material' };
      const options = { page: 1, limit: 10 };
      const user = { id: '1', role: 'user' };
      
      const findAndCountAllStub = sandbox.stub(Supplier, 'findAndCountAll').resolves({
        count: 1,
        rows: [{ id: '1', name: 'Supplier 1' }]
      });

      // Act
      await supplierService.querySuppliers(filter, options, user);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause.createdBy).to.equal(user.id);
    });

    it('should handle search filter correctly', async () => {
      // Arrange
      const filter = { search: 'test' };
      const options = { page: 1, limit: 10 };
      
      const findAndCountAllStub = sandbox.stub(Supplier, 'findAndCountAll').resolves({
        count: 0,
        rows: []
      });

      // Act
      await supplierService.querySuppliers(filter, options);

      // Assert
      expect(findAndCountAllStub.calledOnce).to.be.true;
      // Verify that the where clause includes the search condition
      // This is a simplified check, the actual implementation might be more complex
      const whereClause = findAndCountAllStub.firstCall.args[0].where;
      expect(whereClause[Symbol.for('sequelize.Op.or')]).to.exist;
    });

    it('should handle error during query', async () => {
      // Arrange
      const filter = {};
      const options = { page: 1, limit: 10 };
      
      sandbox.stub(Supplier, 'findAndCountAll').throws(new Error('Database error'));

      // Act & Assert
      try {
        await supplierService.querySuppliers(filter, options);
        expect.fail('Expected error was not thrown');
      } catch (error) {
        expect(error).to.be.instanceOf(ApiError);
        expect(error.statusCode).to.equal(500);
        expect(error.message).to.include('Error querying suppliers');
      }
    });
  });
});
