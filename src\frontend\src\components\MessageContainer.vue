<template>
  <div>
    <!-- 对每个位置渲染一个消息组 -->
    <template v-for="position in positions" :key="position">
      <div :class="getPositionClass(position)" class="space-y-2 w-80">
        <Message
          v-for="msg in getMessagesByPosition(position)"
          :key="msg.id"
          :type="msg.type"
          :title="msg.title"
          :message="msg.message"
          :duration="msg.duration"
          :position="msg.position"
          :closable="msg.closable"
          :autodismiss="msg.autodismiss"
          @closed="handleClose(msg.id)"
        />
      </div>
    </template>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useMessageStore } from '../stores/message';
import Message from './Message.vue';

const messageStore = useMessageStore();

// 所有可能的位置
const positions = [
  'top-right', 'top-left', 'top-center',
  'bottom-right', 'bottom-left', 'bottom-center'
];

// 获取特定位置的消息
const getMessagesByPosition = (position) => {
  return messageStore.messages.filter(msg => msg.position === position);
};

// 根据位置获取定位类
const getPositionClass = (position) => {
  switch (position) {
    case 'top-left':
      return 'fixed top-0 left-0 m-4 z-50';
    case 'top-center':
      return 'fixed top-0 left-1/2 transform -translate-x-1/2 m-4 z-50';
    case 'top-right':
      return 'fixed top-0 right-0 m-4 z-50';
    case 'bottom-left':
      return 'fixed bottom-0 left-0 m-4 z-50';
    case 'bottom-center':
      return 'fixed bottom-0 left-1/2 transform -translate-x-1/2 m-4 z-50';
    case 'bottom-right':
      return 'fixed bottom-0 right-0 m-4 z-50';
    default:
      return 'fixed top-0 right-0 m-4 z-50';
  }
};

// 处理消息关闭
const handleClose = (id) => {
  messageStore.removeMessage(id);
};
</script> 