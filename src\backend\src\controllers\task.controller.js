const httpStatus = require('http-status');
const { Op } = require('sequelize');
const db = require('../models');
const ApiError = require('../utils/ApiError');
const { getPagination, getPagingData } = require('../utils/pagination');

/**
 * Create a task
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Task>}
 */
const createTask = async (req, res) => {
  const taskData = {
    ...req.body,
    createdBy: req.user.id
  };
  const task = await db.Task.create(taskData);
  res.status(201).send(task);
};

/**
 * Get all tasks
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Task[]>}
 */
const getTasks = async (req, res) => {
  const { page, size } = req.query;
  const { limit, offset } = getPagination(page, size);

  const condition = {};
  if (req.query.title) {
    condition.title = { [Op.like]: `%${req.query.title}%` };
  }
  if (req.query.status) {
    condition.status = req.query.status;
  }
  if (req.query.priority) {
    condition.priority = req.query.priority;
  }
  if (req.query.assignedTo) {
    condition.assignedTo = req.query.assignedTo;
  }
  if (req.query.projectId) {
    condition.projectId = req.query.projectId;
  }

  const data = await db.Task.findAndCountAll({
    where: condition,
    limit,
    offset,
    include: [
      {
        model: db.User,
        as: 'Assignee',
        attributes: ['id', 'username', 'email']
      },
      {
        model: db.User,
        as: 'Creator',
        attributes: ['id', 'username', 'email']
      },
      {
        model: db.Project,
        as: 'Project',
        attributes: ['id', 'name', 'code']
      }
    ]
  });

  const response = getPagingData(data, page, limit);
  res.send(response);
};

/**
 * Get task by id
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Task>}
 */
const getTask = async (req, res) => {
  const task = await db.Task.findByPk(req.params.taskId, {
    include: [
      {
        model: db.User,
        as: 'Assignee',
        attributes: ['id', 'username', 'email']
      },
      {
        model: db.User,
        as: 'Creator',
        attributes: ['id', 'username', 'email']
      },
      {
        model: db.Project,
        as: 'Project',
        attributes: ['id', 'name', 'code']
      }
    ]
  });
  if (!task) {
    throw new ApiError(404, 'Task not found');
  }
  res.send(task);
};

/**
 * Update task by id
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Task>}
 */
const updateTask = async (req, res) => {
  const task = await db.Task.findByPk(req.params.taskId);
  if (!task) {
    throw new ApiError(404, 'Task not found');
  }
  Object.assign(task, req.body);
  await task.save();
  res.send(task);
};

/**
 * Delete task by id
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<Task>}
 */
const deleteTask = async (req, res) => {
  const task = await db.Task.findByPk(req.params.taskId);
  if (!task) {
    throw new ApiError(404, 'Task not found');
  }
  await task.destroy();
  res.status(204).send();
};

module.exports = {
  createTask,
  getTasks,
  getTask,
  updateTask,
  deleteTask
}; 