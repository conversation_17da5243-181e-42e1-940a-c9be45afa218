/**
 * Custom validation rules for schemas
 */

/**
 * Custom validation for password
 * @param {string} value - The password value to validate
 * @param {Object} helpers - Joi validation helpers
 * @returns {string|Object} - The password value or error
 */
const password = (value, helpers) => {
  if (value.length < 8) {
    return helpers.message('密码长度必须至少为8个字符');
  }
  if (!value.match(/\d/) || !value.match(/[a-zA-Z]/)) {
    return helpers.message('密码必须包含至少1个字母和1个数字');
  }
  return value;
};

/**
 * Custom validation for UUID
 * @param {string} value - The UUID value to validate
 * @param {Object} helpers - Joi validation helpers
 * @returns {string|Object} - The UUID value or error
 */
const uuid = (value, helpers) => {
  const uuidPattern = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
  if (!uuidPattern.test(value)) {
    return helpers.message('"{{#label}}" 必须是有效的UUID格式');
  }
  return value;
};

/**
 * Custom validation for ID
 * @param {string} value - The ID value to validate (can be MongoDB ObjectId or UUID)
 * @param {Object} helpers - Joi validation helpers
 * @returns {string|Object} - The ID value or error
 */
const objectId = (value, helpers) => {
  // MongoDB ObjectId format (24 hex characters)
  const objectIdPattern = /^[0-9a-fA-F]{24}$/;
  // UUID format (8-4-4-4-12 hex characters)
  const uuidPattern = /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;

  if (!objectIdPattern.test(value) && !uuidPattern.test(value)) {
    return helpers.message('"{{#label}}" 必须是有效的MongoDB ObjectId或UUID格式');
  }
  return value;
};

/**
 * Custom validation for date format (YYYY-MM-DD)
 * Ensures dates are in year-month-day format
 * @param {string|Date} value - The date value to validate
 * @param {Object} helpers - Joi validation helpers
 * @returns {Date|Object} - The date value or error
 */
const dateFormat = (value, helpers) => {
  if (!value) return value;
  
  // If already a Date object, format it to YYYY-MM-DD
  if (value instanceof Date) {
    return value;
  }
  
  // Check if the string is in YYYY-MM-DD format
  const datePattern = /^\d{4}-\d{2}-\d{2}$/;
  if (typeof value === 'string' && !datePattern.test(value)) {
    return helpers.message('日期必须使用年-月-日格式 (YYYY-MM-DD)');
  }
  
  // Validate that it's a valid date
  const date = new Date(value);
  if (isNaN(date.getTime())) {
    return helpers.message('提供的日期无效');
  }
  
  return date;
};

module.exports = {
  password,
  uuid,
  objectId,
  dateFormat,
};