const { v4: uuidv4 } = require('uuid');
const { User } = require('../models');
const { Product } = require('../models/product.model');

async function seedProducts() {
  try {
    // Check if products already exist
    const productCount = await Product.count();
    if (productCount > 0) {
      console.log('Products already exist, skipping product seeding');
      return;
    }

    // Get or create a default admin user for createdBy field
    let adminUser = await User.findOne({ where: { role: 'admin' } });
    if (!adminUser) {
      adminUser = await User.create({
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Admin',
        lastName: 'User',
        role: 'admin'
      });
    }

    // Mock product data based on frontend mock
    const products = [
      {
        id: uuidv4(),
        name: '笔记本电脑',
        code: 'P20230001',
        category: '电子产品',
        description: '高性能商务笔记本电脑，适合办公使用',
        price: 5999,
        unit: '台',
        stock: 100,
        status: 'active',
        specifications: 'Intel i5处理器, 16GB内存, 512GB SSD',
        manufacturer: '联想',
        model: 'ThinkPad X1',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '智能手机',
        code: 'P20230002',
        category: '电子产品',
        description: '高端智能手机，拍照性能出色',
        price: 2999,
        unit: '台',
        stock: 200,
        status: 'active',
        specifications: '6.1英寸屏幕, 8GB内存, 128GB存储',
        manufacturer: '华为',
        model: 'P40',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '办公桌',
        code: 'P20230003',
        category: '家具',
        description: '现代简约风格办公桌，稳固耐用',
        price: 899,
        unit: '张',
        stock: 50,
        status: 'active',
        specifications: '120x60x75cm, 实木材质',
        manufacturer: '欧派',
        model: 'OP-D001',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '办公椅',
        code: 'P20230004',
        category: '家具',
        description: '人体工学设计办公椅，舒适透气',
        price: 499,
        unit: '把',
        stock: 80,
        status: 'active',
        specifications: '可调节高度, 网布靠背, 360度旋转',
        manufacturer: '西昊',
        model: 'M18',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '消防栓',
        code: 'P20230005',
        category: '消防设备',
        description: '室内消火栓，符合国家标准',
        price: 1299,
        unit: '套',
        stock: 30,
        status: 'active',
        specifications: '铸铁材质, 红色, 带玻璃',
        manufacturer: '金盾',
        model: 'JD-XHS-01',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '灭火器',
        code: 'P20230006',
        category: '消防设备',
        description: '手提式干粉灭火器，适用于多种火灾',
        price: 199,
        unit: '个',
        stock: 150,
        status: 'active',
        specifications: '4kg, 干粉灭火剂',
        manufacturer: '强安',
        model: 'MFQ4',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '烟感探测器',
        code: 'P20230007',
        category: '消防设备',
        description: '光电式烟雾报警器，灵敏度高',
        price: 89,
        unit: '个',
        stock: 200,
        status: 'active',
        specifications: 'DC9V供电, 声光报警',
        manufacturer: '海湾',
        model: 'JTY-GD-802',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '安全帽',
        code: 'P20230008',
        category: '安全用品',
        description: '工地用安全帽，抗冲击性强',
        price: 39,
        unit: '顶',
        stock: 300,
        status: 'active',
        specifications: 'ABS材质, 可调节头围',
        manufacturer: '梅思安',
        model: 'V-Gard',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '安全带',
        code: '*********',
        category: '安全用品',
        description: '高空作业安全带，承重能力强',
        price: 199,
        unit: '条',
        stock: 100,
        status: 'active',
        specifications: '全身式, 5点式, 最大承重150kg',
        manufacturer: '霍尼韦尔',
        model: 'DL-31',
        createdBy: adminUser.id
      },
      {
        id: uuidv4(),
        name: '防护服',
        code: '*********',
        category: '安全用品',
        description: '化学防护服，防酸碱',
        price: 299,
        unit: '套',
        stock: 50,
        status: 'active',
        specifications: 'L码, 连体式, 防酸碱',
        manufacturer: '3M',
        model: '4565',
        createdBy: adminUser.id
      }
    ];

    // Bulk create products
    await Product.bulkCreate(products);
    console.log('Successfully seeded products');
  } catch (error) {
    console.error('Error seeding products:', error);
    throw error;
  }
}

module.exports = seedProducts;
