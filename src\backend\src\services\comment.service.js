const httpStatus = require('http-status');
const { Comment, User } = require('../models');
const ApiError = require('../utils/ApiError');

/**
 * Create a comment
 * @param {Object} commentBody
 * @returns {Promise<Comment>}
 */
const createComment = async (commentBody) => {
  return Comment.create(commentBody);
};

/**
 * Get comments by document id
 * @param {string} documentId
 * @param {Object} options - Query options
 * @param {number} [options.limit] - Maximum number of results
 * @param {number} [options.page] - Current page
 * @returns {Promise<Object>}
 */
const getCommentsByDocumentId = async (documentId, options) => {
  const { limit, offset } = options;
  
  const comments = await Comment.findAndCountAll({
    where: { documentId },
    limit,
    offset,
    order: [['createdAt', 'DESC']],
    include: [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'name', 'email'],
      },
    ],
  });
  
  return {
    results: comments.rows,
    page: options.page,
    limit: options.limit,
    totalPages: Math.ceil(comments.count / options.limit),
    totalResults: comments.count,
  };
};

/**
 * Get comment by id
 * @param {string} id
 * @returns {Promise<Comment>}
 */
const getCommentById = async (id) => {
  return Comment.findByPk(id, {
    include: [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'name', 'email'],
      },
    ],
  });
};

/**
 * Update comment by id
 * @param {string} commentId
 * @param {Object} updateBody
 * @returns {Promise<Comment>}
 */
const updateCommentById = async (commentId, updateBody) => {
  const comment = await getCommentById(commentId);
  
  if (!comment) {
    throw new ApiError(404, 'Comment not found');
  }
  
  Object.assign(comment, updateBody);
  await comment.save();
  return comment;
};

/**
 * Delete comment by id
 * @param {string} commentId
 * @returns {Promise<void>}
 */
const deleteCommentById = async (commentId) => {
  const comment = await getCommentById(commentId);
  
  if (!comment) {
    throw new ApiError(404, 'Comment not found');
  }
  
  await comment.destroy();
};

module.exports = {
  createComment,
  getCommentsByDocumentId,
  getCommentById,
  updateCommentById,
  deleteCommentById,
};