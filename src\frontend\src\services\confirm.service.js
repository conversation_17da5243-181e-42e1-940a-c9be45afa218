/**
 * 确认服务 - 用于在应用程序中显示确认对话框
 * 提供比原生confirm更美观和功能更丰富的确认对话框
 */

import { reactive } from 'vue';

// 状态对象
const state = reactive({
  visible: false,
  title: '',
  message: '',
  confirmText: '',
  cancelText: '',
  loadingText: '',
  loading: false,
  resolve: null,
  reject: null,
  type: 'warning' // warning, danger, info
});

/**
 * 显示确认对话框
 * @param {Object} options 配置选项
 * @returns {Promise} 返回一个Promise，用户确认时resolve为true，取消时resolve为false
 */
const confirm = (options = {}) => {
  return new Promise((resolve, reject) => {
    state.visible = true;
    state.title = options.title || '确认操作';
    state.message = options.message || '您确定要执行此操作吗？';
    state.confirmText = options.confirmText || '确认';
    state.cancelText = options.cancelText || '取消';
    state.loadingText = options.loadingText || '处理中...';
    state.type = options.type || 'warning';
    state.resolve = resolve;
    state.reject = reject;
  });
};

/**
 * 确认删除对话框
 * @param {string} itemName 要删除的项目名称
 * @param {Object} options 其他配置选项
 * @returns {Promise} 返回一个Promise，用户确认时resolve为true，取消时resolve为false
 */
const confirmDelete = (itemName = '此项目', options = {}) => {
  return confirm({
    title: options.title || '确认删除',
    message: options.message || `尊敬的用户，您确定要删除"${itemName}"吗？\n\n请您注意以下重要影响：\n· 此操作执行后将无法恢复\n· 所有相关数据将被永久删除\n· 删除后将无法通过任何方式找回这些信息\n\n如果您确定不再需要此项目，请点击"删除"按钮继续。`,
    confirmText: options.confirmText || '删除',
    cancelText: options.cancelText || '取消',
    loadingText: options.loadingText || '删除中...',
    type: 'danger',
    ...options
  });
};

/**
 * 用户确认了对话框
 */
const handleConfirm = async (callback) => {
  try {
    state.loading = true;

    if (typeof callback === 'function') {
      await callback();
    }

    state.visible = false;
    state.loading = false;

    if (state.resolve) {
      state.resolve(true);
    }
  } catch (error) {
    console.error('确认操作失败:', error);
    state.loading = false;

    if (state.reject) {
      state.reject(error);
    }
  }
};

/**
 * 用户取消了对话框
 */
const handleCancel = () => {
  state.visible = false;

  if (state.resolve) {
    state.resolve(false);
  }
};

export default {
  // 状态
  state,

  // 方法
  confirm,
  confirmDelete,
  handleConfirm,
  handleCancel
};