/**
 * 脚本用于更新所有验证文件中的日期格式，使用年-月-日格式
 * Script to update date formats in all validation files to use year-month-day format
 */
const fs = require('fs');
const path = require('path');

// 验证文件目录
const validationsDir = path.join(__dirname, '..', 'validations');

// 读取目录中的所有文件
const files = fs.readdirSync(validationsDir);

// 处理每个验证文件
files.forEach(file => {
  try {
  if (file.endsWith('.validation.js')) {
    const filePath = path.join(validationsDir, file);
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 1. 导入 dateFormat 函数
    if (content.includes("const { objectId } = require('./custom.validation');")) {
      content = content.replace(
        "const { objectId } = require('./custom.validation');",
        "const { objectId, dateFormat } = require('./custom.validation');"
      );
    } else if (content.includes("require('./custom.validation')")) {
      // 处理其他可能的导入格式
      const importRegex = /(const\s+\{\s*[^}]*\s*\}\s*=\s*require\(['"]\.\/custom\.validation['"]\);)/;
      const match = content.match(importRegex);
      
      if (match) {
        const importStatement = match[1];
        const newImportStatement = importStatement.replace(/\{\s*([^}]*)\s*\}/, (_, imports) => {
          const importsList = imports.split(',').map(i => i.trim());
          if (!importsList.includes('dateFormat')) {
            importsList.push('dateFormat');
          }
          return `{ ${importsList.join(', ')} }`;
        });
        content = content.replace(importStatement, newImportStatement);
      } else if (content.includes("require('./custom.validation')")) {
        // 如果找不到特定格式，但有导入，添加一个新的导入
        content = content.replace(
          "const Joi = require('joi');",
          "const Joi = require('joi');\nconst { dateFormat } = require('./custom.validation');"
        );
      }
    } else {
      // 如果没有导入 custom.validation，添加导入
      content = content.replace(
        "const Joi = require('joi');",
        "const Joi = require('joi');\nconst { dateFormat } = require('./custom.validation');"
      );
    }
    
    // 2. 更新所有日期验证
    // 查找所有 Joi.date() 并添加 .custom(dateFormat)
    content = content.replace(/Joi\.date\(\)(?!\.custom\(dateFormat\))/g, 'Joi.date().custom(dateFormat)');
    
    // 3. 为所有日期验证添加错误消息（如果没有）
    const dateValidationRegex = /Joi\.date\(\)\.custom\(dateFormat\)([^.]*)\.messages\(\{([^}]*)\}\)/g;
    content = content.replace(dateValidationRegex, (match, modifiers, messages) => {
      if (!messages.includes('any.custom')) {
        const customMessage = "'any.custom': '日期必须使用年-月-日格式 (YYYY-MM-DD)'";
        // 检查消息是否以逗号结尾
        if (messages.trim().endsWith(',')) {
          return `Joi.date().custom(dateFormat)${modifiers}.messages({${messages}${customMessage}})`;
        } else {
          return `Joi.date().custom(dateFormat)${modifiers}.messages({${messages},${customMessage}})`;
        }
      }
      return match;
    });
    
    // 4. 处理没有错误消息的日期验证
    const dateWithoutMessagesRegex = /Joi\.date\(\)\.custom\(dateFormat\)([^.]*)(?!\.messages)/g;
    content = content.replace(dateWithoutMessagesRegex, (match, modifiers) => {
      return `Joi.date().custom(dateFormat)${modifiers}.messages({'date.base': '日期格式不正确', 'any.custom': '日期必须使用年-月-日格式 (YYYY-MM-DD)'})`;
    });
    
    // 5. 保存更新后的文件
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`Updated date formats in ${file}`);
  }
  } catch (error) {
    console.error(`Error processing file ${file}:`, error);
  }
});

console.log('All validation files have been updated to use year-month-day date format.');
