/**
 * Get pagination parameters
 * @param {number} page - Current page number
 * @param {number} size - Number of items per page
 * @returns {Object} Pagination parameters
 */
const getPagination = (page, size) => {
  const limit = size ? +size : 10;
  const offset = page ? (page - 1) * limit : 0;

  return { limit, offset };
};

/**
 * Format pagination data
 * @param {Object} data - Data with count and rows
 * @param {number} page - Current page number
 * @param {number} limit - Number of items per page
 * @returns {Object} Formatted pagination data
 */
const getPagingData = (data, page, limit) => {
  const { count: totalItems, rows: items } = data;
  const currentPage = page ? +page : 1;
  const totalPages = Math.ceil(totalItems / limit);

  return {
    items,
    meta: {
      totalItems,
      itemsPerPage: limit,
      totalPages,
      currentPage
    }
  };
};

module.exports = {
  getPagination,
  getPagingData
}; 