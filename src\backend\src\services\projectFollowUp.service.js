const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { ProjectFollowUp, Project, User } = require('../models');

/**
 * Create a project follow-up
 * @param {Object} followUpBody
 * @returns {Promise<ProjectFollowUp>}
 */
const createProjectFollowUp = async (followUpBody) => {
  // Check if project exists
  const project = await Project.findByPk(followUpBody.projectId);
  if (!project) {
    throw new ApiError(400, 'Project not found');
  }

  return ProjectFollowUp.create(followUpBody);
};

/**
 * Get project follow-up by id
 * @param {string} id
 * @returns {Promise<ProjectFollowUp>}
 */
const getProjectFollowUpById = async (id) => {
  const followUp = await ProjectFollowUp.findByPk(id, {
    include: [
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!followUp) {
    throw new ApiError(404, 'Project follow-up not found');
  }
  return followUp;
};

/**
 * Update project follow-up by id
 * @param {string} followUpId
 * @param {Object} updateBody
 * @returns {Promise<ProjectFollowUp>}
 */
const updateProjectFollowUpById = async (followUpId, updateBody) => {
  const followUp = await getProjectFollowUpById(followUpId);
  
  Object.assign(followUp, updateBody);
  await followUp.save();
  return followUp;
};

/**
 * Delete project follow-up by id
 * @param {string} followUpId
 * @returns {Promise<void>}
 */
const deleteProjectFollowUpById = async (followUpId) => {
  const followUp = await getProjectFollowUpById(followUpId);
  await followUp.destroy();
};

/**
 * Query for project follow-ups
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Follow-ups and pagination info
 */
const queryProjectFollowUps = async (filter, options) => {
  const { page, limit, sortBy, sortOrder } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};
  
  if (filter.projectId) {
    whereClause.projectId = filter.projectId;
  }
  
  if (filter.startDate && filter.endDate) {
    whereClause.followUpDate = {
      [Op.between]: [new Date(filter.startDate), new Date(filter.endDate)]
    };
  } else if (filter.startDate) {
    whereClause.followUpDate = {
      [Op.gte]: new Date(filter.startDate)
    };
  } else if (filter.endDate) {
    whereClause.followUpDate = {
      [Op.lte]: new Date(filter.endDate)
    };
  }

  // Query with pagination
  const { count, rows } = await ProjectFollowUp.findAndCountAll({
    where: whereClause,
    include: [
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ],
    order: [[sortBy, sortOrder]],
    limit,
    offset
  });

  return {
    results: rows,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

module.exports = {
  createProjectFollowUp,
  getProjectFollowUpById,
  updateProjectFollowUpById,
  deleteProjectFollowUpById,
  queryProjectFollowUps
}; 