<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold" :style="{ color: 'var(--text-primary)' }">消息中心</h1>

      <div class="flex space-x-4">
        <button
          v-if="userMessageStore.unreadCount > 0"
          @click="markAllAsRead"
          class="px-4 py-2 text-sm rounded-md"
          :style="{
            backgroundColor: 'var(--primary-color)',
            color: 'white'
          }"
        >
          全部标为已读
        </button>

        <router-link
          to="/test-messages"
          class="px-4 py-2 text-sm rounded-md flex items-center"
          :style="{
            backgroundColor: 'var(--secondary-color)',
            color: 'white'
          }"
        >
          测试消息生成器
        </router-link>
      </div>
    </div>

    <!-- 消息过滤器 -->
    <div class="bg-white rounded-lg shadow-sm mb-6 p-4 flex flex-wrap gap-4">
      <div class="flex items-center">
        <span class="mr-2 text-sm" :style="{ color: 'var(--text-secondary)' }">状态:</span>
        <select
          v-model="filter.isRead"
          class="rounded-md border-gray-300 text-sm focus:ring-indigo-500 focus:border-indigo-500"
        >
          <option value="">全部</option>
          <option value="false">未读</option>
          <option value="true">已读</option>
        </select>
      </div>

      <div class="flex items-center">
        <span class="mr-2 text-sm" :style="{ color: 'var(--text-secondary)' }">类型:</span>
        <select
          v-model="filter.type"
          class="rounded-md border-gray-300 text-sm focus:ring-indigo-500 focus:border-indigo-500"
        >
          <option value="">全部</option>
          <option value="system">系统消息</option>
          <option value="task">任务消息</option>
          <option value="notification">通知</option>
        </select>
      </div>
    </div>

    <!-- 消息列表 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div v-if="userMessageStore.loading" class="py-10 text-center">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-4 border-t-transparent" :style="{ borderColor: 'var(--primary-color) transparent var(--primary-color) var(--primary-color)' }"></div>
        <p class="mt-2 text-sm" :style="{ color: 'var(--text-secondary)' }">加载中...</p>
      </div>

      <div v-else-if="userMessageStore.error" class="py-10 text-center">
        <p class="text-sm" :style="{ color: 'var(--error-color)' }">{{ userMessageStore.error }}</p>
        <button
          @click="loadMessages"
          class="mt-2 px-4 py-2 text-sm rounded-md"
          :style="{
            backgroundColor: 'var(--primary-color)',
            color: 'white'
          }"
        >
          重试
        </button>
      </div>

      <div v-else-if="userMessageStore.messages.length === 0" class="py-10 text-center">
        <p class="text-sm" :style="{ color: 'var(--text-secondary)' }">暂无消息</p>
      </div>

      <div v-else>
        <div class="overflow-hidden">
          <table class="min-w-full divide-y" :style="{ borderColor: 'var(--secondary-border)' }">
            <thead :style="{ backgroundColor: 'var(--bg-secondary)' }">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" :style="{ color: 'var(--text-secondary)' }">
                  状态
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" :style="{ color: 'var(--text-secondary)' }">
                  标题
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" :style="{ color: 'var(--text-secondary)' }">
                  类型
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider" :style="{ color: 'var(--text-secondary)' }">
                  时间
                </th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider" :style="{ color: 'var(--text-secondary)' }">
                  操作
                </th>
              </tr>
            </thead>
            <tbody class="divide-y" :style="{ borderColor: 'var(--secondary-border)' }">
              <tr
                v-for="message in userMessageStore.sortedMessages"
                :key="message.id"
                :class="{ 'bg-blue-50': !message.isRead }"
                class="hover:bg-gray-50 cursor-pointer"
                @click="viewMessage(message)"
              >
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    v-if="!message.isRead"
                    class="inline-block w-2 h-2 rounded-full"
                    :style="{ backgroundColor: 'var(--primary-color)' }"
                  ></span>
                  <span v-else class="text-xs" :style="{ color: 'var(--text-secondary)' }">已读</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium" :style="{ color: 'var(--text-primary)' }">
                    {{ message.title }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-blue-100 text-blue-800': message.type === 'system',
                      'bg-green-100 text-green-800': message.type === 'task',
                      'bg-yellow-100 text-yellow-800': message.type === 'notification'
                    }"
                  >
                    {{ getMessageTypeText(message.type) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm" :style="{ color: 'var(--text-secondary)' }">
                  {{ formatTime(message.createdAt) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    v-if="!message.isRead"
                    @click.stop="markAsRead(message.id)"
                    class="inline-flex items-center px-3 py-1 rounded-md text-indigo-600 bg-indigo-50 hover:bg-indigo-100 hover:text-indigo-800 transition-colors duration-150 shadow-sm mr-3"
                  >
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7"/></svg>
                    标为已读
                  </button>
                  <button
                    @click.stop="confirmDelete(message.id)"
                    class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                    <span class="font-medium">删除</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="px-6 py-4 flex justify-between items-center border-t" :style="{ borderColor: 'var(--secondary-border)' }">
          <div class="text-sm" :style="{ color: 'var(--text-secondary)' }">
            共 {{ pagination.totalResults }} 条消息
          </div>
          <div class="flex space-x-2">
            <button
              @click="changePage(pagination.currentPage - 1)"
              :disabled="pagination.currentPage <= 1"
              class="px-3 py-1 rounded text-sm"
              :class="{ 'opacity-50 cursor-not-allowed': pagination.currentPage <= 1 }"
              :style="{
                backgroundColor: 'var(--primary-color)',
                color: 'white'
              }"
            >
              上一页
            </button>
            <button
              @click="changePage(pagination.currentPage + 1)"
              :disabled="pagination.currentPage >= pagination.totalPages"
              class="px-3 py-1 rounded text-sm"
              :class="{ 'opacity-50 cursor-not-allowed': pagination.currentPage >= pagination.totalPages }"
              :style="{
                backgroundColor: 'var(--primary-color)',
                color: 'white'
              }"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息详情对话框 -->
    <div v-if="selectedMessage" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4">
        <div class="px-6 py-4 border-b flex justify-between items-center" :style="{ borderColor: 'var(--secondary-border)' }">
          <h3 class="text-lg font-medium" :style="{ color: 'var(--text-primary)' }">
            {{ selectedMessage.title }}
          </h3>
          <button @click="selectedMessage = null" class="text-gray-400 hover:text-gray-500">
            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="px-6 py-4">
          <div class="mb-4 flex justify-between items-center">
            <span
              class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
              :class="{
                'bg-blue-100 text-blue-800': selectedMessage.type === 'system',
                'bg-green-100 text-green-800': selectedMessage.type === 'task',
                'bg-yellow-100 text-yellow-800': selectedMessage.type === 'notification'
              }"
            >
              {{ getMessageTypeText(selectedMessage.type) }}
            </span>
            <span class="text-sm" :style="{ color: 'var(--text-secondary)' }">
              {{ formatDateTime(selectedMessage.createdAt) }}
            </span>
          </div>
          <div class="text-sm whitespace-pre-line" :style="{ color: 'var(--text-primary)' }">
            {{ selectedMessage.content }}
          </div>
        </div>
        <div class="px-6 py-3 border-t flex justify-end" :style="{ borderColor: 'var(--secondary-border)' }">
          <button
            @click="selectedMessage = null"
            class="px-4 py-2 text-sm rounded-md"
            :style="{
              backgroundColor: 'var(--primary-color)',
              color: 'white'
            }"
          >
            关闭
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <div v-if="showDeleteConfirm" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="px-6 py-4">
          <h3 class="text-lg font-medium mb-2" :style="{ color: 'var(--text-primary)' }">确认删除</h3>
          <p class="text-sm" :style="{ color: 'var(--text-secondary)' }">确定要删除这条消息吗？此操作无法撤销。</p>
        </div>
        <div class="px-6 py-3 border-t flex justify-end space-x-3" :style="{ borderColor: 'var(--secondary-border)' }">
          <button
            @click="showDeleteConfirm = false"
            class="px-4 py-2 text-sm rounded-md border"
            :style="{
              borderColor: 'var(--secondary-border)',
              color: 'var(--text-secondary)'
            }"
          >
            取消
          </button>
          <button
            @click="deleteMessage"
            class="px-4 py-2 text-sm rounded-md"
            :style="{
              backgroundColor: 'var(--error-color)',
              color: 'white'
            }"
          >
            删除
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue';
import { useUserMessageStore } from '../stores/userMessage';
import { formatDistanceToNow, format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const userMessageStore = useUserMessageStore();
const selectedMessage = ref(null);
const showDeleteConfirm = ref(false);
const messageToDelete = ref(null);
const isDev = import.meta.env.DEV;

// 分页信息
const pagination = reactive({
  currentPage: 1,
  totalPages: 1,
  totalResults: 0
});

// 过滤条件
const filter = reactive({
  isRead: '',
  type: ''
});

// 格式化时间（相对时间）
const formatTime = (dateString) => {
  try {
    const date = new Date(dateString);
    return formatDistanceToNow(date, { addSuffix: true, locale: zhCN });
  } catch (error) {
    return dateString;
  }
};

// 格式化日期时间（绝对时间）
const formatDateTime = (dateString) => {
  try {
    const date = new Date(dateString);
    return format(date, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN });
  } catch (error) {
    return dateString;
  }
};

// 获取消息类型文本
const getMessageTypeText = (type) => {
  switch (type) {
    case 'system':
      return '系统消息';
    case 'task':
      return '任务消息';
    case 'notification':
      return '通知';
    default:
      return '其他';
  }
};

// 加载消息列表
const loadMessages = async () => {
  try {
    const params = {
      page: pagination.currentPage,
      limit: 10,
      ...filter
    };

    const result = await userMessageStore.fetchMessages(params);

    // 更新分页信息
    pagination.totalPages = result.totalPages;
    pagination.totalResults = result.totalResults;
    pagination.currentPage = result.currentPage;
  } catch (error) {
    console.error('加载消息失败:', error);
  }
};

// 切换页码
const changePage = (page) => {
  if (page < 1 || page > pagination.totalPages) {
    return;
  }

  pagination.currentPage = page;
  loadMessages();
};

// 查看消息详情
const viewMessage = async (message) => {
  selectedMessage.value = message;

  // 如果消息未读，标记为已读
  if (!message.isRead) {
    try {
      await userMessageStore.markAsRead(message.id);
    } catch (error) {
      console.error('标记消息为已读失败:', error);
    }
  }
};

// 标记消息为已读
const markAsRead = async (id) => {
  try {
    await userMessageStore.markAsRead(id);
  } catch (error) {
    console.error('标记消息为已读失败:', error);
  }
};

// 标记所有消息为已读
const markAllAsRead = async () => {
  try {
    await userMessageStore.markAllAsRead();
  } catch (error) {
    console.error('标记所有消息为已读失败:', error);
  }
};

// 确认删除消息
const confirmDelete = (id) => {
  messageToDelete.value = id;
  showDeleteConfirm.value = true;
};

// 删除消息
const deleteMessage = async () => {
  if (!messageToDelete.value) {
    return;
  }

  try {
    await userMessageStore.deleteMessage(messageToDelete.value);

    // 如果当前选中的消息被删除，关闭详情对话框
    if (selectedMessage.value && selectedMessage.value.id === messageToDelete.value) {
      selectedMessage.value = null;
    }

    // 关闭确认对话框
    showDeleteConfirm.value = false;
    messageToDelete.value = null;

    // 如果删除后当前页没有消息了，且不是第一页，则跳转到上一页
    if (userMessageStore.messages.length === 0 && pagination.currentPage > 1) {
      changePage(pagination.currentPage - 1);
    }
  } catch (error) {
    console.error('删除消息失败:', error);
  }
};

// 创建测试消息
const createTestMessage = async () => {
  try {
    const types = ['system', 'task', 'notification'];
    const randomType = types[Math.floor(Math.random() * types.length)];

    const messageData = {
      title: `测试消息 ${new Date().toLocaleTimeString()}`,
      content: `这是一条测试消息内容，创建于 ${new Date().toLocaleString()}`,
      type: randomType
    };

    await userMessageStore.createTestMessage(messageData);

    // 重新加载消息列表
    await loadMessages();
  } catch (error) {
    console.error('创建测试消息失败:', error);
  }
};

// 监听过滤条件变化
watch(filter, () => {
  pagination.currentPage = 1;
  loadMessages();
});

// 初始化
onMounted(async () => {
  try {
    // 如果消息存储已初始化且有消息，直接使用现有消息
    if (userMessageStore.isInitialized && userMessageStore.messages.length > 0) {
      // 更新分页信息
      pagination.totalResults = userMessageStore.messages.length;
      pagination.totalPages = Math.ceil(pagination.totalResults / 10);
      pagination.currentPage = 1;
    } else {
      // 否则重新加载消息
      await loadMessages();

      // 如果加载后仍然没有消息，尝试再次加载（可能是网络问题）
      if (userMessageStore.messages.length === 0 && !userMessageStore.error) {
        console.log('首次加载未获取到消息，尝试再次加载...');
        setTimeout(async () => {
          await loadMessages();
        }, 1000);
      }
    }
  } catch (error) {
    console.error('初始化消息列表失败:', error);
  }
});
</script>
