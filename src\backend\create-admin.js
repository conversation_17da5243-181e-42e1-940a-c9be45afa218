const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');
const { User } = require('./src/models');

async function createAdmin() {
  try {
    // 检查是否已存在管理员用户
    const existingAdmin = await User.findOne({ where: { email: '<EMAIL>' } });

    if (existingAdmin) {
      console.log('Admin user already exists, updating password...');

      // 更新密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('password123', salt);

      // 直接更新数据库中的密码字段，跳过模型的钩子函数
      await User.update(
        { password: hashedPassword },
        { where: { id: existingAdmin.id }, individualHooks: false }
      );

      // 重新获取用户
      const admin = await User.findByPk(existingAdmin.id);

      console.log('Admin password updated successfully');

      // 测试密码
      const isMatch = await bcrypt.compare('password123', admin.password);
      console.log(`Password "password123" matches: ${isMatch}`);

      return;
    }

    // 如果不存在，创建新的管理员用户
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('password123', salt);

    const admin = await User.create({
      id: uuidv4(),
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      lastName: '系统管理员',
      role: 'admin',
      gender: 'male',
      department: 'IT',
      position: '系统管理员',
      isActive: true,
      isAdmin: true
    });

    console.log('Admin user created successfully:', {
      id: admin.id,
      email: admin.email,
      username: admin.username,
      role: admin.role
    });

    // 测试密码
    const isMatch = await bcrypt.compare('password123', admin.password);
    console.log(`Password "password123" matches: ${isMatch}`);
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}

createAdmin();
