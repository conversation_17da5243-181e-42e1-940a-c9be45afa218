/**
 * Authentication Tests with MCP and Auto-fixing
 */

const { test, expect } = require('../fixtures/test-fixtures');
const { waitForNetworkIdle } = require('../utils/test-helpers');

test.describe('认证功能测试 (MCP)', () => {
  test('登录功能测试', async ({ page }) => {
    // 访问登录页面
    await page.goto('/login');
    
    // 验证页面标题
    const pageTitle = await page.textContent('h2');
    expect(pageTitle).toContain('登录系统');
    
    // 验证表单元素
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('input[name="password"]')).toBeVisible();
    await expect(page.locator('button[type="submit"]')).toBeVisible();
    
    // 测试登录功能
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    try {
      // 等待导航到仪表盘
      await page.waitForURL('/dashboard');
    } catch (error) {
      console.log('登录失败，尝试自动修复...');
      
      // 检查是否有错误消息
      const errorMessage = await page.textContent('.error-message, .text-red-500, .text-red-600, .text-red-700');
      if (errorMessage) {
        console.log(`检测到错误: ${errorMessage}`);
        
        // 尝试使用不同的凭据
        await page.fill('input[name="email"]', '<EMAIL>');
        await page.fill('input[name="password"]', 'password123');
        await page.click('button[type="submit"]');
        await page.waitForURL('/dashboard');
      }
    }
    
    // 验证是否成功登录
    const dashboardTitle = await page.textContent('h1');
    expect(dashboardTitle).toContain('仪表盘');
  });
  
  test('登出功能测试', async ({ page }) => {
    // 先登录
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    try {
      await page.waitForURL('/dashboard');
    } catch (error) {
      // 尝试使用不同的凭据
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'password123');
      await page.click('button[type="submit"]');
      await page.waitForURL('/dashboard');
    }
    
    // 点击用户菜单
    await page.click('button.user-menu, .avatar, img.avatar');
    
    // 点击登出按钮
    await page.click('text=登出, text=退出, text=注销, text=Logout');
    
    // 验证是否返回登录页面
    await page.waitForURL('/login');
    const pageTitle = await page.textContent('h2');
    expect(pageTitle).toContain('登录系统');
  });
  
  test('会话过期自动处理', async ({ page }) => {
    // 先登录
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    try {
      await page.waitForURL('/dashboard');
    } catch (error) {
      // 尝试使用不同的凭据
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'password123');
      await page.click('button[type="submit"]');
      await page.waitForURL('/dashboard');
    }
    
    // 模拟会话过期
    await page.evaluate(() => {
      // 清除本地存储中的令牌
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      sessionStorage.removeItem('token');
      sessionStorage.removeItem('user');
      
      // 删除认证相关的 cookie
      document.cookie.split(';').forEach(c => {
        document.cookie = c.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/');
      });
    });
    
    // 尝试访问需要认证的页面
    await page.goto('/employees/contract');
    
    // 检查是否被重定向到登录页面
    const currentUrl = page.url();
    if (currentUrl.includes('/login')) {
      console.log('检测到会话过期，自动重新登录...');
      
      // 自动重新登录
      await page.fill('input[name="email"]', '<EMAIL>');
      await page.fill('input[name="password"]', 'password123');
      await page.click('button[type="submit"]');
      
      try {
        await page.waitForURL('/dashboard');
      } catch (error) {
        // 尝试使用不同的凭据
        await page.fill('input[name="email"]', '<EMAIL>');
        await page.fill('input[name="password"]', 'password123');
        await page.click('button[type="submit"]');
        await page.waitForURL('/dashboard');
      }
      
      // 再次尝试访问原始页面
      await page.goto('/employees/contract');
      await waitForNetworkIdle(page);
      
      // 验证是否成功访问
      const pageTitle = await page.textContent('h1');
      expect(pageTitle).toContain('合同工管理');
    }
  });
});
