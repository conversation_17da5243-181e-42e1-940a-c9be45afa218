const express = require('express');
const { authenticate } = require('../middlewares/auth.middleware');
const { userMessageController } = require('../controllers');

const router = express.Router();

// 所有路由都需要认证
router.use(authenticate);

/**
 * @route GET /api/messages
 * @desc 获取当前用户的消息列表
 * @access Private
 */
router.get('/', userMessageController.getUserMessages);

/**
 * @route GET /api/messages/unread-count
 * @desc 获取当前用户的未读消息数量
 * @access Private
 */
router.get('/unread-count', userMessageController.getUnreadMessageCount);

/**
 * @route PATCH /api/messages/read-all
 * @desc 标记所有消息为已读
 * @access Private
 */
router.patch('/read-all', userMessageController.markAllMessagesAsRead);

/**
 * @route POST /api/messages/test
 * @desc 创建测试消息（仅用于开发和测试）
 * @access Private
 */
router.post('/test', userMessageController.createTestMessage);

/**
 * @route GET /api/messages/:id
 * @desc 获取消息详情
 * @access Private
 */
router.get('/:id', userMessageController.getUserMessageById);

/**
 * @route PATCH /api/messages/:id/read
 * @desc 标记消息为已读
 * @access Private
 */
router.patch('/:id/read', userMessageController.markMessageAsRead);

/**
 * @route DELETE /api/messages/:id
 * @desc 删除消息
 * @access Private
 */
router.delete('/:id', userMessageController.deleteUserMessage);

module.exports = router;
