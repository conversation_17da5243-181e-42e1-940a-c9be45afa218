const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const productTransactionValidation = require('../validations/productTransaction.validation');
const productTransactionController = require('../controllers/productTransaction.controller');

const router = express.Router();

router
  .route('/')
  .post(
    auth('manageProducts'),
    validate(productTransactionValidation.createProductTransaction),
    productTransactionController.createProductTransaction
  )
  .get(
    auth('getProducts'),
    validate(productTransactionValidation.getProductTransactions),
    productTransactionController.getProductTransactions
  );

// 入库操作路由
router
  .route('/in')
  .post(
    auth('manageProducts'),
    validate(productTransactionValidation.createStockInTransaction),
    productTransactionController.createStockInTransaction
  )
  .get(
    auth('getProducts'),
    validate(productTransactionValidation.getProductTransactions),
    productTransactionController.getStockInTransactions
  );

// 出库操作路由
router
  .route('/out')
  .post(
    auth('manageProducts'),
    validate(productTransactionValidation.createStockOutTransaction),
    productTransactionController.createStockOutTransaction
  )
  .get(
    auth('getProducts'),
    validate(productTransactionValidation.getProductTransactions),
    productTransactionController.getStockOutTransactions
  );

router
  .route('/:transactionId')
  .get(
    auth('getProducts'),
    validate(productTransactionValidation.getProductTransaction),
    productTransactionController.getProductTransaction
  );

module.exports = router; 