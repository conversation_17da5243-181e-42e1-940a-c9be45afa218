const httpStatus = require('http-status');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { ProductTransaction, Product, Project, User } = require('../models');
const { getProductById } = require('./product.service');
const { sequelize } = require('../config/database');

/**
 * Create a product transaction and update product stock
 * @param {Object} transactionBody
 * @returns {Promise<ProductTransaction>}
 */
const createProductTransaction = async (transactionBody) => {
  // Check if product exists
  const product = await getProductById(transactionBody.productId);
  
  // Check if project exists if projectId is provided
  if (transactionBody.projectId) {
    const project = await Project.findByPk(transactionBody.projectId);
    if (!project) {
      throw new ApiError(400, 'Project not found');
    }
  }
  
  // Calculate total price
  transactionBody.totalPrice = transactionBody.quantity * transactionBody.unitPrice;
  
  // Create transaction in a database transaction to ensure atomicity
  const result = await sequelize.transaction(async (t) => {
    // Create the transaction record
    const transaction = await ProductTransaction.create(transactionBody, { transaction: t });
    
    // Update product stock based on transaction type
    let stockChange = 0;
    if (transactionBody.type === 'in') {
      stockChange = transactionBody.quantity;
    } else if (transactionBody.type === 'out') {
      stockChange = -transactionBody.quantity;
      
      // Check if there's enough stock
      if (product.stock < transactionBody.quantity) {
        throw new ApiError(400, 'Insufficient stock');
      }
    } else if (transactionBody.type === 'adjustment') {
      stockChange = transactionBody.quantity; // Can be positive or negative for adjustments
    }
    
    // Update product stock
    product.stock += stockChange;
    await product.save({ transaction: t });
    
    return transaction;
  });
  
  return result;
};

/**
 * Get product transaction by id
 * @param {string} id
 * @returns {Promise<ProductTransaction>}
 */
const getProductTransactionById = async (id) => {
  const transaction = await ProductTransaction.findByPk(id, {
    include: [
      {
        model: Product,
        attributes: ['id', 'name', 'code', 'category', 'unit']
      },
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ]
  });

  if (!transaction) {
    throw new ApiError(404, 'Product transaction not found');
  }
  return transaction;
};

/**
 * Query for product transactions
 * @param {Object} filter - Filter options
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Product transactions and pagination info
 */
const queryProductTransactions = async (filter, options) => {
  const { page, limit, sortBy, sortOrder } = options;
  const offset = (page - 1) * limit;

  // Build the where clause based on filters
  const whereClause = {};
  
  if (filter.productId) {
    whereClause.productId = filter.productId;
  }
  
  if (filter.projectId) {
    whereClause.projectId = filter.projectId;
  }
  
  if (filter.type) {
    whereClause.type = filter.type;
  }
  
  if (filter.reference) {
    whereClause.reference = { [Op.like]: `%${filter.reference}%` };
  }
  
  if (filter.transactionDateFrom) {
    whereClause.transactionDate = {
      ...whereClause.transactionDate,
      [Op.gte]: new Date(filter.transactionDateFrom)
    };
  }
  
  if (filter.transactionDateTo) {
    whereClause.transactionDate = {
      ...whereClause.transactionDate,
      [Op.lte]: new Date(filter.transactionDateTo)
    };
  }

  // 构建Product模型的条件查询
  const productInclude = {
    model: Product,
    attributes: ['id', 'name', 'code', 'category', 'unit']
  };

  // 如果有产品名称或产品编码筛选，添加到Product模型查询条件中
  if (filter.productName || filter.productCode) {
    productInclude.where = {};

    if (filter.productName) {
      productInclude.where.name = { [Op.like]: `%${filter.productName}%` };
    }

    if (filter.productCode) {
      productInclude.where.code = { [Op.like]: `%${filter.productCode}%` };
    }
  }

  // Query with pagination
  const { count, rows } = await ProductTransaction.findAndCountAll({
    where: whereClause,
    include: [
      productInclude,
      {
        model: Project,
        attributes: ['id', 'name', 'projectNumber', 'status']
      },
      {
        model: User,
        as: 'Creator',
        attributes: ['id', 'username', 'lastName', 'email']
      }
    ],
    order: [[sortBy, sortOrder]],
    limit,
    offset,
    distinct: true
  });

  return {
    results: rows,
    pagination: {
      page,
      limit,
      totalPages: Math.ceil(count / limit),
      totalResults: count
    }
  };
};

module.exports = {
  createProductTransaction,
  getProductTransactionById,
  queryProductTransactions
}; 