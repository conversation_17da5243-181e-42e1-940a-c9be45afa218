/**
 * Database Migration Utilities
 * 
 * This module provides utility functions for database migration operations,
 * including schema querying, table management, and migration tracking.
 */

const { QueryTypes } = require('sequelize');
const logger = require('../config/logger');

/**
 * Initialize the migration tracking table
 * @param {Object} sequelize - Sequelize instance
 * @returns {Promise<void>}
 */
async function initMigrationTable(sequelize) {
  try {
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS "sequelize_meta" (
        "name" VARCHAR(255) NOT NULL PRIMARY KEY,
        "applied_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
      )
    `);
    logger.info('Migration tracking table initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize migration table:', error);
    throw error;
  }
}

/**
 * Get all applied migrations from the database
 * @param {Object} sequelize - Sequelize instance
 * @returns {Promise<Array<string>>} Array of applied migration names
 */
async function getAppliedMigrations(sequelize) {
  try {
    const result = await sequelize.query(
      'SELECT name FROM "sequelize_meta" ORDER BY name',
      { type: QueryTypes.SELECT }
    );
    return result.map(row => row.name);
  } catch (error) {
    logger.error('Error getting applied migrations:', error);
    throw error;
  }
}

/**
 * Record a migration as applied in the database
 * @param {Object} sequelize - Sequelize instance
 * @param {string} migrationName - Name of the migration file
 * @param {Object} transaction - Sequelize transaction object
 * @returns {Promise<void>}
 */
async function recordMigration(sequelize, migrationName, transaction) {
  try {
    await sequelize.query(
      'INSERT INTO "sequelize_meta" (name) VALUES (:name)',
      {
        replacements: { name: migrationName },
        transaction
      }
    );
    logger.info(`Recorded migration: ${migrationName}`);
  } catch (error) {
    logger.error(`Error recording migration ${migrationName}:`, error);
    throw error;
  }
}

/**
 * Remove a migration record from the database
 * @param {Object} sequelize - Sequelize instance
 * @param {string} migrationName - Name of the migration file
 * @param {Object} transaction - Sequelize transaction object
 * @returns {Promise<void>}
 */
async function removeMigration(sequelize, migrationName, transaction) {
  try {
    await sequelize.query(
      'DELETE FROM "sequelize_meta" WHERE name = :name',
      {
        replacements: { name: migrationName },
        transaction
      }
    );
    logger.info(`Removed migration record: ${migrationName}`);
  } catch (error) {
    logger.error(`Error removing migration ${migrationName}:`, error);
    throw error;
  }
}

/**
 * Get schema information for a specific table
 * @param {Object} sequelize - Sequelize instance
 * @param {string} schema - Database schema name
 * @param {string} tableName - Table name
 * @returns {Promise<Object>} Table schema information
 */
async function getTableSchema(sequelize, schema, tableName) {
  try {
    const [columns] = await sequelize.query(`
      SELECT
        column_name,
        data_type,
        character_maximum_length,
        is_nullable,
        column_default,
        udt_name
      FROM information_schema.columns
      WHERE table_schema = :schema
      AND table_name = :tableName
    `, {
      replacements: { schema, tableName },
      type: QueryTypes.SELECT
    });

    return columns;
  } catch (error) {
    logger.error(`Error getting schema for table ${tableName}:`, error);
    throw error;
  }
}

/**
 * Get all tables in a schema
 * @param {Object} sequelize - Sequelize instance
 * @param {string} schema - Database schema name
 * @returns {Promise<Array<string>>} Array of table names
 */
async function getAllTables(sequelize, schema) {
  try {
    const [tables] = await sequelize.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = :schema
      AND table_type = 'BASE TABLE'
    `, {
      replacements: { schema },
      type: QueryTypes.SELECT
    });

    return tables.map(table => table.table_name);
  } catch (error) {
    logger.error('Error getting all tables:', error);
    throw error;
  }
}

module.exports = {
  initMigrationTable,
  getAppliedMigrations,
  recordMigration,
  removeMigration,
  getTableSchema,
  getAllTables
};