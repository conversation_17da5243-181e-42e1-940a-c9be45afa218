<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6">
      <router-link to="/departments" class="text-blue-600 hover:text-blue-800 mr-2">
        <i class="fas fa-arrow-left"></i>
      </router-link>
      <h1 class="text-2xl font-bold text-gray-800">部门详情</h1>
    </div>

    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center items-center py-10">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
    </div>

    <!-- Error message -->
    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{{ error }}</p>
      <button @click="retryFetch" class="underline mt-2">重试</button>
    </div>

    <!-- Department details -->
    <div v-else-if="department" class="bg-white shadow-md rounded-lg overflow-hidden">
      <!-- Header with actions -->
      <div class="p-4 border-b bg-gray-50 flex justify-between items-center">
        <div>
          <h2 class="text-lg font-semibold">{{ department.name }}</h2>
          <p class="text-sm text-gray-500">部门代码: {{ department.code }}</p>
        </div>
        <div class="flex space-x-2">
          <router-link
            :to="`/departments/${department.id}/edit`"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            编辑
          </router-link>
          <button
            @click="confirmDelete"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
          >
            删除
          </button>
        </div>
      </div>

      <!-- Department information -->
      <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg font-medium mb-3">基本信息</h3>
          
          <div class="mb-4">
            <p class="text-sm text-gray-500 mb-1">状态</p>
            <p>
              <span
                :class="{
                  'px-2 py-1 text-xs rounded-full': true,
                  'bg-green-100 text-green-800': department.status === 'active',
                  'bg-red-100 text-red-800': department.status === 'inactive'
                }"
              >
                {{ department.status === 'active' ? '活跃' : '禁用' }}
              </span>
            </p>
          </div>
          
          <div class="mb-4">
            <p class="text-sm text-gray-500 mb-1">上级部门</p>
            <p v-if="department.parentDepartment">
              {{ department.parentDepartment.name }} ({{ department.parentDepartment.code }})
            </p>
            <p v-else class="text-gray-400">无</p>
          </div>
          
          <div class="mb-4">
            <p class="text-sm text-gray-500 mb-1">创建时间</p>
            <p>{{ formatDate(department.createdAt) }}</p>
          </div>
          
          <div class="mb-4">
            <p class="text-sm text-gray-500 mb-1">最后更新</p>
            <p>{{ formatDate(department.updatedAt) }}</p>
          </div>
        </div>
        
        <div>
          <h3 class="text-lg font-medium mb-3">详细信息</h3>
          
          <div class="mb-4">
            <p class="text-sm text-gray-500 mb-1">部门描述</p>
            <p v-if="department.description" class="whitespace-pre-line">{{ department.description }}</p>
            <p v-else class="text-gray-400">无描述</p>
          </div>
          
          <div class="mb-4">
            <h4 class="text-sm text-gray-500 mb-1">子部门</h4>
            <div v-if="department.childDepartments && department.childDepartments.length > 0">
              <ul class="list-disc list-inside">
                <li v-for="child in department.childDepartments" :key="child.id" class="mb-1">
                  <router-link :to="`/departments/${child.id}`" class="text-blue-600 hover:text-blue-800">
                    {{ child.name }} ({{ child.code }})
                  </router-link>
                </li>
              </ul>
            </div>
            <p v-else class="text-gray-400">无子部门</p>
          </div>
          
          <div class="mb-4">
            <h4 class="text-sm text-gray-500 mb-1">部门员工</h4>
            <div v-if="department.employees && department.employees.length > 0">
              <ul class="list-disc list-inside">
                <li v-for="employee in department.employees" :key="employee.id" class="mb-1">
                  {{ employee.username }} {{ employee.lastName || '' }}
                  <span v-if="employee.position" class="text-gray-500">({{ employee.position }})</span>
                </li>
              </ul>
            </div>
            <p v-else class="text-gray-400">无员工</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete confirmation modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-8 max-w-md w-full">
        <h3 class="text-xl font-medium mb-4">确认删除</h3>
        <p class="mb-6">
          您确定要删除部门 <span class="font-bold">{{ department?.name }}</span> 吗？此操作无法撤销。
        </p>
        <div class="flex justify-end space-x-4">
          <button
            @click="showDeleteModal = false"
            class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-100"
          >
            取消
          </button>
          <button
            @click="deleteDepartment"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            :disabled="deleteLoading"
          >
            <span v-if="deleteLoading">
              <i class="fas fa-spinner fa-spin mr-2"></i>删除中...
            </span>
            <span v-else>删除</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useDepartmentStore } from '@/stores/department';
import { useToast } from 'vue-toastification';

const route = useRoute();
const router = useRouter();
const departmentStore = useDepartmentStore();
const toast = useToast();

// State
const showDeleteModal = ref(false);
const deleteLoading = ref(false);

// Computed properties
const department = computed(() => departmentStore.getCurrentDepartment);
const loading = computed(() => departmentStore.isLoading);
const error = computed(() => departmentStore.getError);

// Methods
function formatDate(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

async function fetchDepartment() {
  try {
    await departmentStore.fetchDepartment(route.params.id);
  } catch (error) {
    console.error('Error fetching department:', error);
  }
}

function retryFetch() {
  departmentStore.resetError();
  fetchDepartment();
}

function confirmDelete() {
  showDeleteModal.value = true;
}

async function deleteDepartment() {
  deleteLoading.value = true;
  try {
    const departmentName = department.value.name;
    await departmentStore.deleteDepartment(department.value.id);
    showDeleteModal.value = false;
    
    toast.success(`部门 ${departmentName} 删除成功`);
    
    router.push('/departments');
  } catch (error) {
    console.error('Error deleting department:', error);
    toast.error(error.response?.data?.message || `删除部门失败`);
  } finally {
    deleteLoading.value = false;
  }
}

// Lifecycle hooks
onMounted(() => {
  fetchDepartment();
});
</script> 