const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const procurementValidation = require('../validations/procurement.validation');
const procurementController = require('../controllers/procurement.controller');

const router = express.Router();

router
  .route('/')
  .post(auth('manageProcurements'), validate(procurementValidation.createProcurement), procurementController.createProcurement)
  .get(auth('getProcurements'), validate(procurementValidation.getProcurements), procurementController.getProcurements);

router
  .route('/:procurementId')
  .get(auth('getProcurements'), validate(procurementValidation.getProcurement), procurementController.getProcurement)
  .patch(auth('manageProcurements'), validate(procurementValidation.updateProcurement), procurementController.updateProcurement)
  .delete(auth('manageProcurements'), validate(procurementValidation.deleteProcurement), procurementController.deleteProcurement);

module.exports = router; 