/**
 * 生成测试消息数据脚本
 * 
 * 此脚本用于生成测试消息数据，以便测试个人信息中心功能
 * 运行方式：node src/scripts/generateTestMessages.js
 */

const { sequelize } = require('../config/database');
const { User, UserMessage } = require('../models');
const { faker } = require('@faker-js/faker/locale/zh_CN');

// 设置随机种子以获得一致的结果
faker.seed(123);

/**
 * 生成测试消息数据
 * @param {number} count 要生成的消息数量
 */
async function generateTestMessages(count = 20) {
  try {
    console.log('开始生成测试消息数据...');
    
    // 获取所有用户
    const users = await User.findAll();
    
    if (users.length === 0) {
      console.error('没有找到用户，请先创建用户');
      return;
    }
    
    console.log(`找到 ${users.length} 个用户`);
    
    // 消息类型
    const messageTypes = ['system', 'task', 'notification'];
    
    // 系统消息标题模板
    const systemTitles = [
      '系统维护通知',
      '账户安全提醒',
      '系统更新公告',
      '功能升级通知',
      '操作指南更新'
    ];
    
    // 任务消息标题模板
    const taskTitles = [
      '新任务分配',
      '任务截止提醒',
      '任务状态更新',
      '任务评审邀请',
      '任务完成确认'
    ];
    
    // 通知消息标题模板
    const notificationTitles = [
      '会议提醒',
      '文档更新通知',
      '项目进度更新',
      '团队公告',
      '重要事项提醒'
    ];
    
    // 系统消息内容模板
    const systemContents = [
      '尊敬的用户，系统将于{date}进行例行维护，维护期间部分功能可能暂时无法使用，预计维护时间为{duration}小时，请提前做好工作安排。',
      '我们检测到您的账户于{date}有异常登录尝试，请及时检查账户安全并考虑修改密码。',
      '系统已更新至v{version}版本，新版本修复了已知问题并优化了用户体验，详情请查看更新日志。',
      '我们很高兴地通知您，系统新增了{feature}功能，现在您可以更高效地完成工作。',
      '操作指南已更新，新增了{feature}的使用说明，请前往帮助中心查看详情。'
    ];
    
    // 任务消息内容模板
    const taskContents = [
      '您有一个新的任务"{taskName}"已分配给您，请在{deadline}前完成。',
      '您的任务"{taskName}"即将到期，截止日期为{deadline}，请及时处理。',
      '您的任务"{taskName}"状态已更新为"{status}"，请查看详情。',
      '您被邀请参与任务"{taskName}"的评审，请在{deadline}前提交您的评审意见。',
      '您的任务"{taskName}"已被标记为完成，感谢您的辛勤工作！'
    ];
    
    // 通知消息内容模板
    const notificationContents = [
      '您有一个会议"{meetingName}"将于{date}在{location}举行，请准时参加。',
      '文档"{documentName}"已由{user}更新，请查看最新版本。',
      '项目"{projectName}"的进度已更新，当前完成度为{progress}%，请查看详情。',
      '团队公告：{announcement}',
      '重要事项提醒：{reminder}，请及时处理。'
    ];
    
    // 生成测试消息
    const messages = [];
    
    for (let i = 0; i < count; i++) {
      // 随机选择用户
      const user = users[Math.floor(Math.random() * users.length)];
      
      // 随机选择消息类型
      const type = messageTypes[Math.floor(Math.random() * messageTypes.length)];
      
      // 根据类型选择标题和内容模板
      let titles, contents;
      switch (type) {
        case 'system':
          titles = systemTitles;
          contents = systemContents;
          break;
        case 'task':
          titles = taskTitles;
          contents = taskContents;
          break;
        case 'notification':
          titles = notificationTitles;
          contents = notificationContents;
          break;
      }
      
      // 随机选择标题和内容
      const titleTemplate = titles[Math.floor(Math.random() * titles.length)];
      const contentTemplate = contents[Math.floor(Math.random() * contents.length)];
      
      // 替换模板中的占位符
      let title = titleTemplate;
      let content = contentTemplate
        .replace('{date}', faker.date.future().toLocaleDateString())
        .replace('{duration}', Math.floor(Math.random() * 5) + 1)
        .replace('{version}', `${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`)
        .replace('{feature}', faker.commerce.productName())
        .replace('{taskName}', faker.commerce.productName())
        .replace('{deadline}', faker.date.future().toLocaleDateString())
        .replace('{status}', ['进行中', '已完成', '已延期', '待审核'][Math.floor(Math.random() * 4)])
        .replace('{meetingName}', faker.company.buzzPhrase())
        .replace('{location}', faker.location.city() + '会议室')
        .replace('{documentName}', faker.system.fileName())
        .replace('{user}', faker.person.fullName())
        .replace('{projectName}', faker.commerce.productName())
        .replace('{progress}', Math.floor(Math.random() * 100))
        .replace('{announcement}', faker.lorem.sentence())
        .replace('{reminder}', faker.lorem.sentence());
      
      // 创建消息对象
      const message = {
        userId: user.id,
        title,
        content,
        type,
        isRead: Math.random() > 0.7, // 30%的消息为已读
        createdAt: faker.date.recent({ days: 30 }),
        updatedAt: new Date()
      };
      
      messages.push(message);
    }
    
    // 批量创建消息
    await UserMessage.bulkCreate(messages);
    
    console.log(`成功生成 ${messages.length} 条测试消息`);
  } catch (error) {
    console.error('生成测试消息数据失败:', error);
  } finally {
    // 关闭数据库连接
    await sequelize.close();
  }
}

// 执行生成测试数据的函数
generateTestMessages(30);
