const autocannon = require('autocannon');
const { expect } = require('chai');
const app = require('../../src/app');
const { sequelize } = require('../../src/models');

// 创建一个简单的性能测试框架
const performanceTest = async (title, options) => {
  console.log(`\n🚀 Running performance test: ${title}`);

  // 设置默认选项
  const defaultOptions = {
    url: 'http://localhost:3009',
    connections: 10,
    duration: 5,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  };

  // 合并选项
  const testOptions = { ...defaultOptions, ...options };

  // 运行性能测试
  const results = await autocannon(testOptions);

  // 输出结果
  console.log(`\n📊 Results for: ${title}`);
  console.log(`Requests per second: ${results.requests.average}`);
  console.log(`Latency: ${results.latency.average} ms`);
  console.log(`Throughput: ${results.throughput.average} bytes/sec`);

  return results;
};

// 在测试前启动服务器
let server;
before(async () => {
  // 确保数据库连接
  await sequelize.authenticate();

  // 启动服务器
  return new Promise((resolve) => {
    server = app.listen(3000, () => {
      console.log('Test server started on port 3000');
      resolve();
    });
  });
});

// 在测试后关闭服务器
after(async () => {
  // 关闭服务器
  await new Promise((resolve) => {
    server.close(() => {
      console.log('Test server closed');
      resolve();
    });
  });

  // 关闭数据库连接
  await sequelize.close();
});

describe('API Performance Tests', function() {
  // 设置较长的超时时间
  this.timeout(30000);

  // 登录并获取令牌
  let authToken;
  before(async () => {
    try {
      // 启动服务器后等待一些时间，确保服务器已经完全启动
      await new Promise(resolve => setTimeout(resolve, 1000));

      const loginResponse = await fetch('http://localhost:3009/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'Test@123'
        })
      });

      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        authToken = loginData.tokens.access.token;
        console.log('Successfully obtained auth token');
      } else {
        console.log('Failed to login:', await loginResponse.text());
      }
    } catch (error) {
      console.error('Error during login:', error.message);
    }
  });

  // 测试辅助函数 - 检查是否有认证令牌
  const skipIfNoAuth = (test) => {
    return async function() {
      if (!authToken) {
        console.log('Skipping test: No auth token available');
        this.skip();
        return;
      }
      return test.apply(this, arguments);
    };
  };

  it('GET /api/products should handle high load', skipIfNoAuth(async function() {
    const results = await performanceTest('GET /api/products', {
      url: 'http://localhost:3009/api/products',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });

    // 验证性能指标
    expect(results.errors).to.equal(0);
    expect(results.timeouts).to.equal(0);
    expect(results.requests.average).to.be.above(50); // 每秒至少处理50个请求
    expect(results.latency.average).to.be.below(150); // 平均延迟小于150ms
  }));

  it('GET /api/suppliers should handle high load', skipIfNoAuth(async function() {
    const results = await performanceTest('GET /api/suppliers', {
      url: 'http://localhost:3009/api/suppliers',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });

    // 验证性能指标
    expect(results.errors).to.equal(0);
    expect(results.timeouts).to.equal(0);
    expect(results.requests.average).to.be.above(30); // 每秒至少处理30个请求
    expect(results.latency.average).to.be.below(300); // 平均延迟小于300ms
  }));

  it('POST /api/products should handle moderate load', skipIfNoAuth(async function() {
    // 创建一个产品的请求体
    const productData = {
      name: 'Performance Test Product',
      code: `PT-${Date.now()}`,
      category: 'electronics',
      price: 100,
      unit: '个',
      stock: 10,
      createdBy: '90b49185-4413-43c8-865b-fdda12882ddb' // 添加必需的 createdBy 字段
    };

    const results = await performanceTest('POST /api/products', {
      url: 'http://localhost:3009/api/products',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify(productData),
      connections: 5, // 减少连接数，因为这是写操作
      duration: 3 // 减少持续时间，避免创建太多重复数据
    });

    // 验证性能指标
    expect(results.errors).to.equal(0);
    expect(results.timeouts).to.equal(0);
    expect(results.requests.average).to.be.above(10); // 每秒至少处理10个请求
    expect(results.latency.average).to.be.below(200); // 平均延迟小于200ms
  }));

  it('Search functionality should be performant', skipIfNoAuth(async function() {
    const results = await performanceTest('GET /api/products?search=test', {
      url: 'http://localhost:3009/api/products?search=test',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });

    // 验证性能指标
    expect(results.errors).to.equal(0);
    expect(results.timeouts).to.equal(0);
    expect(results.requests.average).to.be.above(30); // 每秒至少处理30个请求
    expect(results.latency.average).to.be.below(350); // 平均延迟小于350ms
  }));

  it('Filtering functionality should be performant', skipIfNoAuth(async function() {
    const results = await performanceTest('GET /api/products?category=electronics', {
      url: 'http://localhost:3009/api/products?category=electronics',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });

    // 验证性能指标
    expect(results.errors).to.equal(0);
    expect(results.timeouts).to.equal(0);
    expect(results.requests.average).to.be.above(30); // 每秒至少处理30个请求
    expect(results.latency.average).to.be.below(250); // 平均延迟小于250ms
  }));
});
