/**
 * Dashboard Tests with MCP and Auto-fixing
 */

const { test, expect } = require('../fixtures/test-fixtures');
const { waitForNetworkIdle } = require('../utils/test-helpers');

test.describe('仪表盘功能测试 (MCP)', () => {
  test.beforeEach(async ({ page }) => {
    // 使用自动修复的登录
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    
    // 等待导航完成，如果失败会自动修复
    try {
      await page.waitForURL('/dashboard');
    } catch (error) {
      console.log('登录失败，尝试自动修复...');
      // 检查是否有错误消息
      const errorMessage = await page.textContent('.error-message, .text-red-500, .text-red-600, .text-red-700');
      if (errorMessage) {
        console.log(`检测到错误: ${errorMessage}`);
        // 尝试使用不同的凭据
        await page.fill('input[name="email"]', '<EMAIL>');
        await page.fill('input[name="password"]', 'password123');
        await page.click('button[type="submit"]');
        await page.waitForURL('/dashboard');
      }
    }
  });

  test('仪表盘页面加载测试', async ({ page }) => {
    // 验证页面标题
    const pageTitle = await page.textContent('h1');
    expect(pageTitle).toContain('仪表盘');
    
    // 验证欢迎消息
    const welcomeMessage = await page.textContent('p.text-gray-500');
    expect(welcomeMessage).toContain('欢迎回来');
    
    // 验证统计卡片存在
    const cards = await page.$$('.bg-white.overflow-hidden.shadow.rounded-lg');
    expect(cards.length).toBeGreaterThan(0);
    
    // 检查是否有错误消息
    const errorElements = await page.$$('.error-message, .text-red-500, .text-red-600, .text-red-700');
    for (const element of errorElements) {
      const text = await element.textContent();
      if (text && text.trim()) {
        console.log(`仪表盘页面检测到错误: ${text}`);
      }
    }
  });
  
  test('仪表盘数据加载测试', async ({ page }) => {
    // 等待网络请求完成
    await waitForNetworkIdle(page);
    
    // 检查是否有图表元素
    const charts = await page.$$('.echarts-for-vue, canvas');
    
    // 如果没有图表，检查是否有其他数据展示元素
    if (charts.length === 0) {
      const dataElements = await page.$$('.grid-cols-1, .grid-cols-2, .grid-cols-3, .grid-cols-4');
      expect(dataElements.length).toBeGreaterThan(0);
    }
    
    // 检查控制台是否有错误
    const consoleMessages = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleMessages.push(msg.text());
      }
    });
    
    // 如果有控制台错误，尝试刷新页面
    if (consoleMessages.length > 0) {
      console.log('检测到控制台错误，尝试刷新页面...');
      await page.reload();
      await waitForNetworkIdle(page);
    }
  });
  
  test('仪表盘导航测试', async ({ page }) => {
    // 测试从仪表盘导航到其他页面
    
    // 查找导航菜单
    const navLinks = await page.$$('nav a, .sidebar a, .menu a');
    
    // 如果找到导航链接，测试点击第一个员工相关的链接
    let employeeLink = null;
    for (const link of navLinks) {
      const text = await link.textContent();
      if (text.includes('员工') || text.includes('合同工') || text.includes('临时工')) {
        employeeLink = link;
        break;
      }
    }
    
    if (employeeLink) {
      // 点击员工相关链接
      await employeeLink.click();
      await waitForNetworkIdle(page);
      
      // 验证是否导航到员工相关页面
      const currentUrl = page.url();
      expect(currentUrl).toContain('employee');
      
      // 返回仪表盘
      await page.goto('/dashboard');
      await waitForNetworkIdle(page);
      
      // 验证是否返回仪表盘
      const pageTitle = await page.textContent('h1');
      expect(pageTitle).toContain('仪表盘');
    } else {
      console.log('未找到员工相关导航链接，跳过导航测试');
    }
  });
  
  test('仪表盘响应式布局测试', async ({ page }) => {
    // 测试不同屏幕尺寸下的响应式布局
    
    // 桌面尺寸
    await page.setViewportSize({ width: 1920, height: 1080 });
    await waitForNetworkIdle(page);
    
    // 验证桌面布局
    const desktopCards = await page.$$('.bg-white.overflow-hidden.shadow.rounded-lg');
    const desktopCardsCount = desktopCards.length;
    
    // 平板尺寸
    await page.setViewportSize({ width: 768, height: 1024 });
    await waitForNetworkIdle(page);
    
    // 验证平板布局
    const tabletCards = await page.$$('.bg-white.overflow-hidden.shadow.rounded-lg');
    const tabletCardsCount = tabletCards.length;
    
    // 手机尺寸
    await page.setViewportSize({ width: 375, height: 667 });
    await waitForNetworkIdle(page);
    
    // 验证手机布局
    const mobileCards = await page.$$('.bg-white.overflow-hidden.shadow.rounded-lg');
    const mobileCardsCount = mobileCards.length;
    
    // 恢复默认尺寸
    await page.setViewportSize({ width: 1920, height: 900 });
    
    // 验证响应式布局是否正常工作
    expect(desktopCardsCount).toBeGreaterThan(0);
    expect(tabletCardsCount).toBeGreaterThan(0);
    expect(mobileCardsCount).toBeGreaterThan(0);
  });
});
