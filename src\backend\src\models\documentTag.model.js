const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { Document } = require('./document.model');
const { Tag } = require('./tag.model');

// Define the junction table for Document-Tag many-to-many relationship
const DocumentTag = sequelize.define('documenttag', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  documentId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'document',
      key: 'id'
    },
  },
  tagId: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'tag',
      key: 'id'
    },
  }
}, {
  timestamps: true,
});

// Set up the many-to-many association
Document.belongsToMany(Tag, {
  through: DocumentTag,
  foreignKey: 'documentId',
  otherKey: 'tagId'
});

Tag.belongsToMany(Document, {
  through: DocumentTag,
  foreignKey: 'tagId',
  otherKey: 'documentId'
});

module.exports = { DocumentTag };
