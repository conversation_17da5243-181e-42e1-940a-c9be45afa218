const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Category = sequelize.define('category', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 100]
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  parentId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'category',
      key: 'id'
    },
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE'
  },
  path: {
    type: DataTypes.STRING,
    allowNull: true
  },
  level: {
    type: DataTypes.INTEGER,
    defaultValue: 1,
    allowNull: false,
    field: 'level'
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    field: 'isActive'
  },
  iconUrl: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'iconUrl'
  },
  icon222Url: {
    type: DataTypes.STRING,
    allowNull: true,
    field: 'iconUrl'
  },
  sortOrder: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    field: 'sortOrder'
  }
}, {
  timestamps: true,
  hooks: {
    // Generate path for category hierarchy
    beforeSave: async (category, options) => {
      if (category.id && category.parentId) {
        const parent = await Category.findByPk(category.parentId);
        if (parent) {
          category.path = parent.path ? `${parent.path}.${category.id}` : category.id;
          category.level = parent.level + 1;
        }
      } else if (category.id) {
        category.path = category.id;
        category.level = 1;
      }
    },
    afterCreate: async (category, options) => {
      if (!category.path) {
        if (category.parentId) {
          const parent = await Category.findByPk(category.parentId);
          if (parent) {
            category.path = parent.path ? `${parent.path}.${category.id}` : category.id;
            category.level = parent.level + 1;
          } else {
            category.path = category.id;
            category.level = 1;
          }
        } else {
          category.path = category.id;
          category.level = 1;
        }
        await category.save({ hooks: false });
      }
    }
  }
});

// Self-referencing relation (parent-child)
Category.hasMany(Category, {
  as: 'children',
  foreignKey: 'parentId',
  onDelete: 'SET NULL',
  onUpdate: 'CASCADE'
});

Category.belongsTo(Category, {
  as: 'parent',
  foreignKey: 'parentId'
});

module.exports = { Category };