<template>
  <div class="page-container">
    <!-- 页面标题和按钮 -->
    <div class="page-header">
      <div class="flex items-center">
        <h1 class="page-title">编辑客户信息</h1>
      </div>
      <div class="flex space-x-3">
        <router-link to="/clients" class="btn btn-secondary shadow-sm hover:shadow-md transition-all duration-200">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>         
          返回列表
        </router-link>
      </div>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="error-alert">
      <div class="flex">
        <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        {{ error }}
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p class="loading-text">加载中...</p>
    </div>

    <!-- 表单内容 -->
    <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <!-- 左侧：基本信息 -->
      <div class="md:col-span-2 space-y-8">
        <!-- 基本信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            基本信息
          </h2>
          <div class="form-group">
            <div class="form-grid">
              <div>
                <label for="name" class="form-label">客户名称 <span class="text-red-500">*</span></label>
                <input id="name" v-model="clientForm.name" type="text" class="form-input p-2.5 transition-colors duration-200" required
                  placeholder="请输入客户名称" autocomplete="off" />
              </div>
              <div>
                <label for="code" class="form-label">客户编号</label>
                <input id="code" v-model="clientForm.code" type="text" class="form-input"
                  placeholder="自动生成或手动输入" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="type" class="form-label">客户类型</label>
                <select id="type" v-model="clientForm.type" class="form-input">
                  <option value="company">企业客户</option>
                  <option value="individual">个人客户</option>
                  <option value="government">政府单位</option>
                </select>
              </div>
              <div>
                <label for="status" class="form-label">客户状态</label>
                <select id="status" v-model="clientForm.status" class="form-input">
                  <option value="active">活跃</option>
                  <option value="inactive">非活跃</option>
                  <option value="potential">潜在</option>
                </select>
              </div>
            </div>

            <div>
              <label for="description" class="form-label">客户描述</label>
              <textarea id="description" v-model="clientForm.description" rows="3" class="form-input"
                placeholder="输入客户的简要描述信息..."></textarea>
            </div>
          </div>
        </div>

        <!-- 联系信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
            </svg>
            联系信息
          </h2>
          <div class="form-group">
            <div class="form-grid">
              <div>
                <label for="contactName" class="form-label">联系人姓名</label>
                <input id="contactName" v-model="clientForm.contactName" type="text" class="form-input"
                  placeholder="请输入联系人姓名" />
              </div>
              <div>
                <label for="contactTitle" class="form-label">联系人职位</label>
                <input id="contactTitle" v-model="clientForm.contactTitle" type="text" class="form-input"
                  placeholder="请输入联系人职位" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="phone" class="form-label">联系电话</label>
                <input id="phone" v-model="clientForm.phone" type="tel" class="form-input"
                  placeholder="请输入联系电话" />
              </div>
              <div>
                <label for="email" class="form-label">电子邮箱</label>
                <input id="email" v-model="clientForm.email" type="email" class="form-input"
                  placeholder="请输入电子邮箱" />
              </div>
            </div>

            <div>
              <label for="address" class="form-label">地址</label>
              <textarea id="address" v-model="clientForm.address" rows="2" class="form-input"
                placeholder="请输入详细地址信息..."></textarea>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：附加信息 -->
      <div class="space-y-8">
        <!-- 财务信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            财务信息
          </h2>
          <div class="form-group">
            <div>
              <label for="taxId" class="form-label">税号</label>
              <input id="taxId" v-model="clientForm.taxId" type="text" class="form-input"
                placeholder="请输入税号" />
            </div>
            <div>
              <label for="bankName" class="form-label">开户银行</label>
              <input id="bankName" v-model="clientForm.bankName" type="text" class="form-input"
                placeholder="请输入开户银行" />
            </div>
            <div>
              <label for="bankCode" class="form-label">联行号</label>
              <input id="bankCode" v-model="clientForm.bankCode" type="text" class="form-input"
                placeholder="请输入联行号" />
            </div>
            <div>
              <label for="bankAccount" class="form-label">银行账户</label>
              <input id="bankAccount" v-model="clientForm.bankAccount" type="text" class="form-input"
                placeholder="请输入银行账户" />
            </div>
          </div>
        </div>

        <!-- 其他信息卡片 -->
        <div class="card">
          <h2 class="card-header">
            <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            其他信息
          </h2>
          <div class="form-group">
            <div>
              <label for="source" class="form-label">客户来源</label>
              <select id="source" v-model="clientForm.source" class="form-input">
                <option value="referral">推荐</option>
                <option value="website">网站</option>
                <option value="advertisement">广告</option>
                <option value="exhibition">展会</option>
                <option value="other">其他</option>
              </select>
            </div>
            <div>
              <label for="industry" class="form-label">所属行业</label>
              <select id="industry" v-model="clientForm.industry" class="form-input">
                <option value="technology">科技</option>
                <option value="manufacturing">制造业</option>
                <option value="finance">金融</option>
                <option value="healthcare">医疗健康</option>
                <option value="education">教育</option>
                <option value="retail">零售</option>
                <option value="other">其他</option>
              </select>
            </div>
            <div>
              <label for="notes" class="form-label">备注</label>
              <textarea id="notes" v-model="clientForm.notes" rows="3" class="form-input"
                placeholder="输入需要特别注意的事项..."></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 保存按钮 -->
    <div class="mt-8 flex justify-end">
      <button @click="saveClient" class="btn btn-primary shadow-sm hover:shadow-md transition-all duration-200" :disabled="isSaving">
        <svg v-if="isSaving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        保存
      </button>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div class="bg-white rounded-lg shadow-2xl max-w-md w-full p-6 transform transition-all">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">确认删除</h3>
          <p class="text-gray-500 mb-6">
            确定要删除客户 {{ clientForm.name }} 吗？此操作不可撤销。
          </p>

          <div class="flex justify-center space-x-4">
            <button
              type="button"
              @click="showDeleteModal = false"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="button"
              @click="deleteClient"
              class="btn btn-danger shadow-sm hover:shadow-md transition-all duration-200"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                删除中...
              </span>
              <span v-else>确认删除</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import apiService from '@/services/apiService';
import clientStore from '@/stores/clientStore';

const route = useRoute();
const router = useRouter();
const clientId = computed(() => route.params.id);
const isEdit = computed(() => route.name === 'ClientEdit');
const isNewClient = computed(() => clientId.value === 'create');

// 状态变量
const loading = ref(false);
const error = ref('');
const isSaving = ref(false);
const isDeleting = ref(false);
const showDeleteModal = ref(false);

// 表单数据
const clientForm = reactive({
  name: '',
  code: '',
  type: '',
  status: '',
  description: '',
  contactName: '',
  contactTitle: '',
  phone: '',
  email: '',
  address: '',
  taxId: '',
  bankAccount: '',
  bankName: '',
  bankCode: '',
  source: '',
  industry: '',
  notes: ''
});

// 获取客户详情
onMounted(async () => {
  await fetchClientDetails();
});

// 组件卸载时清除客户名称
onUnmounted(() => {
  clientStore.clearClientName();
});

// 获取客户详情数据
async function fetchClientDetails() {
  // 如果是新客户，不需要获取数据
  if (isNewClient.value) {
    // 设置默认值
    clientForm.type = 'company';
    clientForm.status = 'active';
    return;
  }

  loading.value = true;
  error.value = '';

  try {
    // 从 API 获取数据
    const response = await apiService.getClientById(clientId.value);

    // 处理响应数据
    const clientData = response;

    // 安全检查
    if (clientData && typeof clientData === 'object') {
      // 填充表单数据
      Object.keys(clientForm).forEach(key => {
        if (key in clientData) {
          clientForm[key] = clientData[key];
        }
      });

      // 更新客户名称到全局存储
      if (clientData.name) {
        // 使用客户存储更新客户名称
        clientStore.setClientName(clientData.name);

        // 更新文档标题
        document.title = `编辑客户信息 - ${clientData.name}`;
      }
    } else {
      console.warn('客户数据格式不符合预期:', clientData);
      throw new Error('获取到的客户数据格式不正确');
    }
  } catch (err) {
    console.error('获取客户详情失败:', err);
    error.value = err.response?.data?.message || '获取客户详情失败，请重试';
  } finally {
    loading.value = false;
  }
}

// 保存客户信息
async function saveClient() {
  isSaving.value = true;
  error.value = '';

  try {
    // 只验证客户名称
    if (!clientForm.name?.trim()) {
      throw new Error('请输入客户名称');
    }

    // 准备提交的数据
    const clientData = { ...clientForm };

    // 调用API
    if (isNewClient.value) {
      try {
        // 创建新客户
        await apiService.createClient(clientData);
        handleSuccess('创建成功');
      } catch (err) {
        console.error('创建客户时出现错误:', err);

        // 检查是否是状态码错误，且数据已经成功插入
        if (err.response?.status === 500 && err.response?.data?.message?.includes('Invalid status code')) {
          handleSuccess('创建成功');
          return;
        }

        // 其他错误继续抛出
        throw err;
      }
    } else {
      // 更新客户
      await apiService.updateClient(clientId.value, clientData);
      handleSuccess('保存成功');
    }
  } catch (err) {
    console.error('保存客户失败:', err);

    // 处理错误
    if (err.message === '请输入客户名称') {
      error.value = err.message;
    } else if (err.response?.data?.message) {
      error.value = err.response.data.message;
    } else {
      error.value = '保存失败，请重试';
    }

    // 显示错误消息
    if (window.$message) {
      window.$message.error(error.value);
    }
  } finally {
    isSaving.value = false;
  }
}

// 处理成功保存的情况
function handleSuccess(message) {
  // 显示成功消息
  if (window.$message) {
    window.$message.success(message);
  }

  // 立即返回列表页
  router.replace({
    path: '/clients',
    query: {
      refresh: Date.now(), // 强制刷新列表
      page: 1 // 返回第一页
    }
  });
}

// 确认删除客户
function confirmDelete() {
  showDeleteModal.value = true;
}

// 删除客户
async function deleteClient() {
  isDeleting.value = true;

  try {
    // 调用API删除客户
    await apiService.deleteClient(clientId.value);

    // 显示成功消息
    if (window.$message) {
      window.$message.success('删除成功');
    }

    // 关闭删除弹窗
    showDeleteModal.value = false;

    // 立即返回列表页并强制刷新
    router.replace({
      path: '/clients',
      query: {
        refresh: Date.now(), // 添加时间戳强制刷新
        page: 1 // 返回第一页
      }
    });
  } catch (err) {
    console.error('删除客户失败:', err);

    // 处理错误
    error.value = err.response?.data?.message || '删除客户失败，请重试';
    if (window.$message) {
      window.$message.error(error.value);
    }
    // 关闭删除弹窗
    showDeleteModal.value = false;
  } finally {
    isDeleting.value = false;
  }
}

// 获取客户名称首字母
function getClientInitials(name) {
  if (!name) return '?';

  const names = name.split(' ');
  if (names.length === 1) {
    return name.charAt(0).toUpperCase();
  }
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
}
</script>