/**
 * 增强版测试夹具 - 支持自动错误检测和修复
 * 用于 Playwright MCP 测试
 */

const base = require('@playwright/test');
const {
  login,
  checkForErrors,
  autoFix,
  takeScreenshot,
  detectAndFixPageErrors,
  smartClick,
  smartWaitForSelector,
  waitForNetworkIdle,
  generateTestData
} = require('../utils/test-helpers');

/**
 * 增强版测试夹具 - 支持自动错误检测和修复
 */
exports.test = base.test.extend({
  // 测试信息
  testInfo: [async ({}, use) => {
    // 创建测试信息对象，用于在测试中共享数据
    const testInfo = {
      startTime: new Date(),
      errors: [],
      warnings: [],
      autoFixes: [],
      screenshots: [],
      testData: {}
    };

    await use(testInfo);
  }, { scope: 'test' }],

  // 自动修复页面夹具
  autoFixPage: async ({ page, testInfo }, use) => {
    // 获取测试名称
    const testName = testInfo.title || '未命名测试';

    // 记录测试开始
    console.log(`\n🚀 开始测试: ${testName}`);

    // 重写页面方法以添加自动修复功能
    const originalGoto = page.goto.bind(page);
    page.goto = async (url, options = {}) => {
      console.log(`📄 导航到: ${url}`);

      try {
        const response = await originalGoto(url, options);

        // 等待网络空闲
        await waitForNetworkIdle(page);

        // 检查导航后的错误
        await detectAndFixPageErrors(page, testName);

        return response;
      } catch (error) {
        console.error(`❌ 导航到 ${url} 失败:`, error.message);

        // 确定错误类型
        let errorType = 'navigation-timeout';
        if (error.message.includes('timeout')) {
          errorType = 'navigation-timeout';
        } else if (error.message.includes('net::ERR')) {
          errorType = 'network-error';
        }

        // 尝试自动修复
        const fixed = await autoFix(page, errorType, testName);
        if (fixed) {
          console.log(`✅ 已修复导航错误，重试...`);
          return await originalGoto(url, options);
        } else {
          // 如果无法修复，截图并抛出错误
          await takeScreenshot(page, testName, `navigation-error-${url.replace(/[^a-zA-Z0-9]/g, '-')}`);
          throw error;
        }
      }
    };

    // 重写点击方法以添加自动修复功能
    const originalClick = page.click.bind(page);
    page.click = async (selector, options = {}) => {
      console.log(`🖱️ 点击元素: ${selector}`);

      try {
        // 使用智能点击替代普通点击
        await smartClick(page, selector, {
          ...options,
          alternativeSelectors: [
            // 常见的替代选择器
            selector.replace('button', 'a'),
            selector.replace('a', 'button'),
            `${selector}, ${selector} *`,
            `[aria-label="${selector.replace(/["']/g, '')}"]`,
            `[title="${selector.replace(/["']/g, '')}"]`,
            `[data-testid="${selector.replace(/["']/g, '')}"]`
          ]
        });

        // 如果点击的是提交按钮，检查表单验证错误
        const isSubmitButton =
          selector.includes('submit') ||
          selector.includes('保存') ||
          selector.includes('提交') ||
          await page.$(selector).then(el =>
            el?.evaluate(node =>
              node.type === 'submit' ||
              node.getAttribute('type') === 'submit' ||
              node.textContent.includes('提交') ||
              node.textContent.includes('保存')
            )
          );

        if (isSubmitButton) {
          // 等待可能的表单提交
          await page.waitForTimeout(1000);

          // 检查表单验证错误
          const { hasErrors, errors } = await checkForErrors(page);
          if (hasErrors) {
            console.log(`⚠️ 检测到表单验证错误:`, errors);

            // 尝试自动修复表单错误
            const fixed = await autoFix(page, 'form-validation', testName);
            if (fixed) {
              console.log(`✅ 已修复表单错误，重新提交...`);
              // 重新点击提交按钮
              return await smartClick(page, selector, options);
            }
          }
        }

        // 点击后检查页面错误
        await detectAndFixPageErrors(page, testName);
      } catch (error) {
        console.error(`❌ 点击元素 ${selector} 失败:`, error.message);

        // 尝试自动修复
        const fixed = await autoFix(page, 'element-not-found', testName);
        if (fixed) {
          console.log(`✅ 已修复元素错误，重试点击...`);
          return await originalClick(selector, options);
        } else {
          // 如果无法修复，截图并抛出错误
          await takeScreenshot(page, testName, `click-error-${selector.replace(/[^a-zA-Z0-9]/g, '-')}`);
          throw error;
        }
      }
    };

    // 重写填充方法以添加智能填充功能
    const originalFill = page.fill.bind(page);
    page.fill = async (selector, value, options = {}) => {
      console.log(`✏️ 填充字段 ${selector}: ${value}`);

      try {
        // 等待元素可见
        await smartWaitForSelector(page, selector, options);

        // 如果值为空且选择器提示了字段类型，生成合适的测试数据
        if (!value) {
          const fieldType =
            selector.includes('email') ? 'email' :
            selector.includes('name') || selector.includes('姓名') ? 'name' :
            selector.includes('phone') || selector.includes('电话') ? 'phone' :
            selector.includes('idNumber') || selector.includes('身份证') ? 'idNumber' :
            selector.includes('date') || selector.includes('日期') ? 'date' :
            'text';

          value = generateTestData(fieldType);
          console.log(`🤖 自动生成 ${fieldType} 数据: ${value}`);
        }

        await originalFill(selector, value, options);
      } catch (error) {
        console.error(`❌ 填充字段 ${selector} 失败:`, error.message);

        // 尝试自动修复
        const fixed = await autoFix(page, 'element-not-found', testName);
        if (fixed) {
          console.log(`✅ 已修复元素错误，重试填充...`);
          return await originalFill(selector, value, options);
        } else {
          // 如果无法修复，截图并抛出错误
          await takeScreenshot(page, testName, `fill-error-${selector.replace(/[^a-zA-Z0-9]/g, '-')}`);
          throw error;
        }
      }
    };

    // 添加对话框处理
    page.on('dialog', dialog => {
      console.log(`🔔 检测到对话框: ${dialog.type()}, 消息: ${dialog.message()}`);
      dialog.accept();
    });

    // 添加控制台错误处理
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`🔴 控制台错误: ${msg.text()}`);
        testInfo.errors.push({
          type: 'console',
          message: msg.text(),
          timestamp: new Date()
        });
      }
    });

    // 添加请求失败处理
    page.on('requestfailed', request => {
      console.log(`🔴 请求失败: ${request.url()}`);
      testInfo.errors.push({
        type: 'network',
        url: request.url(),
        message: request.failure()?.errorText || '未知错误',
        timestamp: new Date()
      });
    });

    await use(page);

    // 测试结束后记录信息
    console.log(`\n✅ 测试完成: ${testName}`);
    console.log(`⏱️ 耗时: ${(new Date() - testInfo.startTime) / 1000}秒`);

    if (testInfo.errors.length > 0) {
      console.log(`⚠️ 测试期间发现 ${testInfo.errors.length} 个错误`);
    }

    if (testInfo.autoFixes.length > 0) {
      console.log(`🔧 应用了 ${testInfo.autoFixes.length} 个自动修复`);
    }
  },

  // 已认证页面夹具
  authenticatedPage: async ({ page, testInfo }, use) => {
    console.log(`🔑 使用认证页面...`);

    try {
      await login(page);
      console.log(`✅ 登录成功`);
    } catch (error) {
      console.error(`❌ 登录失败:`, error.message);

      // 尝试使用备用凭据
      try {
        console.log(`🔄 尝试使用备用凭据...`);
        await login(page, '<EMAIL>', 'password123');
        console.log(`✅ 使用备用凭据登录成功`);
      } catch (retryError) {
        console.error(`❌ 备用凭据也失败:`, retryError.message);
        await takeScreenshot(page, 'login-failure', 'auth-error');
        throw new Error(`无法登录: ${error.message}`);
      }
    }

    await use(page);
  },

  // 智能页面夹具 - 组合了自动修复和认证功能
  smartPage: async ({ autoFixPage, testInfo }, use) => {
    // 这个夹具组合了自动修复和其他增强功能

    // 添加智能方法
    autoFixPage.smartNavigateTo = async (url, expectedTitleText) => {
      await autoFixPage.goto(url);

      // 检查页面标题
      if (expectedTitleText) {
        const titleElements = await autoFixPage.$$('h1, h2, h3, .page-title');
        let foundTitle = false;

        for (const element of titleElements) {
          const text = await element.textContent();
          if (text.includes(expectedTitleText)) {
            foundTitle = true;
            break;
          }
        }

        if (!foundTitle) {
          console.log(`⚠️ 警告: 页面标题不包含预期文本 "${expectedTitleText}"`);
          testInfo.warnings.push({
            type: 'title-mismatch',
            expected: expectedTitleText,
            url: url,
            timestamp: new Date()
          });
        }
      }
    };

    // 添加智能表单填充方法
    autoFixPage.smartFillForm = async (formData) => {
      for (const [fieldName, value] of Object.entries(formData)) {
        // 尝试多种选择器
        const selectors = [
          `input[name="${fieldName}"]`,
          `textarea[name="${fieldName}"]`,
          `select[name="${fieldName}"]`,
          `input[id="${fieldName}"]`,
          `textarea[id="${fieldName}"]`,
          `select[id="${fieldName}"]`,
          `[placeholder="${fieldName}"]`,
          `label:has-text("${fieldName}") + input`,
          `label:has-text("${fieldName}") + textarea`,
          `label:has-text("${fieldName}") + select`
        ];

        // 尝试每个选择器
        let filled = false;
        for (const selector of selectors) {
          try {
            const element = await autoFixPage.$(selector);
            if (element) {
              // 根据元素类型处理
              const tagName = await element.evaluate(el => el.tagName.toLowerCase());
              const type = await element.evaluate(el => el.type);

              if (tagName === 'select') {
                await autoFixPage.selectOption(selector, value);
              } else if (type === 'checkbox') {
                if (value) {
                  await autoFixPage.check(selector);
                } else {
                  await autoFixPage.uncheck(selector);
                }
              } else {
                await autoFixPage.fill(selector, String(value));
              }

              filled = true;
              break;
            }
          } catch (error) {
            // 继续尝试下一个选择器
          }
        }

        if (!filled) {
          console.log(`⚠️ 警告: 无法找到字段 "${fieldName}" 的输入元素`);
        }
      }
    };

    await use(autoFixPage);
  }
});

exports.expect = base.expect;
