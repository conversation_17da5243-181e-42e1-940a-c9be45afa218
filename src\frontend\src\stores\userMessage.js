import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import axios from 'axios';

/**
 * 用户消息存储
 * 用于管理用户的消息通知
 */
export const useUserMessageStore = defineStore('userMessage', () => {
  // 消息列表
  const messages = ref([]);
  // 未读消息数量
  const unreadCount = ref(0);
  // 加载状态
  const loading = ref(false);
  // 错误信息
  const error = ref(null);
  // 是否已初始化
  const isInitialized = ref(false);

  // 按时间排序的消息列表（最新的在前面）
  const sortedMessages = computed(() => {
    return [...messages.value].sort((a, b) => {
      return new Date(b.createdAt) - new Date(a.createdAt);
    });
  });

  // 未读消息列表
  const unreadMessages = computed(() => {
    return sortedMessages.value.filter(message => !message.isRead);
  });

  /**
   * 获取用户消息列表
   * @param {Object} params - 查询参数
   * @param {number} retryCount - 重试次数
   * @returns {Promise<Array>} - 消息列表
   */
  const fetchMessages = async (params = {}, retryCount = 2) => {
    try {
      loading.value = true;
      error.value = null;

      // 使用完整的URL，避免相对路径问题
      const apiUrl = import.meta.env.VITE_API_URL ;
      const response = await axios.get(`${apiUrl}/api/messages`, {
        params,
        // 添加时间戳防止缓存
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        // 确保携带凭证
        withCredentials: true,
        // 增加超时时间
        timeout: 10000
      });

      // 确保返回的数据是数组
      if (response.data && Array.isArray(response.data.results)) {
        messages.value = response.data.results;
      } else if (response.data && typeof response.data === 'object') {
        // 如果返回的不是预期的格式，但有数据，尝试适配
        messages.value = response.data.results || [];
        console.warn('消息数据格式不符合预期:', response.data);
      } else {
        console.error('获取消息返回的数据格式不正确:', response.data);
        messages.value = [];
      }

      return response.data;
    } catch (err) {
      console.error('获取消息列表失败:', err);

      // 提供更详细的错误信息
      if (err.code === 'ERR_NETWORK') {
        error.value = '网络连接错误，无法连接到消息服务器';
      } else if (err.code === 'ECONNABORTED') {
        error.value = '请求超时，服务器响应时间过长';
      } else {
        error.value = err.response?.data?.message || '获取消息列表失败';
      }

      // 如果还有重试次数，则重试
      if (retryCount > 0) {
        console.log(`获取消息失败，将在${1000 * (3 - retryCount)}毫秒后重试，剩余重试次数: ${retryCount}`);

        // 使用指数退避策略，重试间隔随着重试次数增加而增加
        const retryDelay = 1000 * (3 - retryCount);

        // 延迟后重试
        return new Promise(resolve => {
          setTimeout(async () => {
            try {
              const result = await fetchMessages(params, retryCount - 1);
              resolve(result);
            } catch (retryErr) {
              // 最后一次重试失败，但不影响应用运行，返回空数组
              console.error('获取消息列表最终失败:', retryErr);
              messages.value = [];
              resolve({ results: [] });
            }
          }, retryDelay);
        });
      }

      // 即使最后失败也不抛出异常，而是返回空数组，避免阻塞应用
      messages.value = [];
      return { results: [] };
    } finally {
      loading.value = false;
      isInitialized.value = true;
    }
  };

  /**
   * 获取未读消息数量
   * @param {number} retryCount - 重试次数
   * @returns {Promise<number>} - 未读消息数量
   */
  const fetchUnreadCount = async (retryCount = 2) => {
    try {
      // 使用完整的URL，避免相对路径问题
      const apiUrl = import.meta.env.VITE_API_URL ;
      const response = await axios.get(`${apiUrl}/api/messages/unread-count`, {
        // 添加时间戳防止缓存
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        // 确保携带凭证
        withCredentials: true,
        // 增加超时时间
        timeout: 10000
      });

      unreadCount.value = response.data.count || 0;
      return unreadCount.value;
    } catch (err) {
      console.error('获取未读消息数量失败:', err);

      // 提供更详细的错误信息
      if (err.code === 'ERR_NETWORK') {
        error.value = '网络连接错误，无法连接到消息服务器';
      } else if (err.code === 'ECONNABORTED') {
        error.value = '请求超时，服务器响应时间过长';
      } else {
        error.value = err.response?.data?.message || '获取未读消息数量失败';
      }

      // 如果还有重试次数，则重试
      if (retryCount > 0) {
        console.log(`获取未读消息数量失败，将在${1000 * (3 - retryCount)}毫秒后重试，剩余重试次数: ${retryCount}`);

        // 使用指数退避策略，重试间隔随着重试次数增加而增加
        const retryDelay = 1000 * (3 - retryCount);

        // 延迟后重试
        return new Promise(resolve => {
          setTimeout(async () => {
            try {
              const result = await fetchUnreadCount(retryCount - 1);
              resolve(result);
            } catch (retryErr) {
              // 最后一次重试失败，但不影响应用运行，返回0
              console.error('获取未读消息数量最终失败:', retryErr);
              unreadCount.value = 0;
              resolve(0);
            }
          }, retryDelay);
        });
      }

      // 出错时默认为0，不影响应用运行
      unreadCount.value = 0;
      return 0;
    }
  };

  /**
   * 标记消息为已读
   * @param {string} id - 消息ID
   * @returns {Promise<Object>} - 更新后的消息
   */
  const markAsRead = async (id) => {
    try {
      // 使用完整的URL，避免相对路径问题
      const apiUrl = import.meta.env.VITE_API_URL ;
      const response = await axios.patch(`${apiUrl}/api/messages/${id}/read`, {}, {
        // 确保携带凭证
        withCredentials: true,
        // 增加超时时间
        timeout: 10000
      });

      // 更新本地消息状态
      const index = messages.value.findIndex(msg => msg.id === id);
      if (index !== -1) {
        messages.value[index].isRead = true;
      }

      // 更新未读消息数量
      await fetchUnreadCount();

      return response.data;
    } catch (err) {
      console.error('标记消息为已读失败:', err);

      // 提供更详细的错误信息
      if (err.code === 'ERR_NETWORK') {
        error.value = '网络连接错误，无法连接到消息服务器';
      } else if (err.code === 'ECONNABORTED') {
        error.value = '请求超时，服务器响应时间过长';
      } else {
        error.value = err.response?.data?.message || '标记消息为已读失败';
      }

      // 即使失败也不抛出异常，避免阻塞应用
      return null;
    }
  };

  /**
   * 标记所有消息为已读
   * @returns {Promise<Object>} - 更新结果
   */
  const markAllAsRead = async () => {
    try {
      // 使用完整的URL，避免相对路径问题
      const apiUrl = import.meta.env.VITE_API_URL ;
      const response = await axios.patch(`${apiUrl}/api/messages/read-all`, {}, {
        // 确保携带凭证
        withCredentials: true,
        // 增加超时时间
        timeout: 10000
      });

      // 更新本地消息状态
      messages.value.forEach(msg => {
        msg.isRead = true;
      });

      // 更新未读消息数量
      unreadCount.value = 0;

      return response.data;
    } catch (err) {
      console.error('标记所有消息为已读失败:', err);

      // 提供更详细的错误信息
      if (err.code === 'ERR_NETWORK') {
        error.value = '网络连接错误，无法连接到消息服务器';
      } else if (err.code === 'ECONNABORTED') {
        error.value = '请求超时，服务器响应时间过长';
      } else {
        error.value = err.response?.data?.message || '标记所有消息为已读失败';
      }

      // 即使失败也不抛出异常，避免阻塞应用
      return null;
    }
  };

  /**
   * 删除消息
   * @param {string} id - 消息ID
   * @returns {Promise<boolean>} - 是否删除成功
   */
  const deleteMessage = async (id) => {
    try {
      // 使用完整的URL，避免相对路径问题
      const apiUrl = import.meta.env.VITE_API_URL ;
      await axios.delete(`${apiUrl}/api/messages/${id}`, {
        // 确保携带凭证
        withCredentials: true,
        // 增加超时时间
        timeout: 10000
      });

      // 从本地消息列表中移除
      const index = messages.value.findIndex(msg => msg.id === id);
      if (index !== -1) {
        const isUnread = !messages.value[index].isRead;
        messages.value.splice(index, 1);

        // 如果删除的是未读消息，更新未读消息数量
        if (isUnread) {
          unreadCount.value = Math.max(0, unreadCount.value - 1);
        }
      }

      return true;
    } catch (err) {
      console.error('删除消息失败:', err);

      // 提供更详细的错误信息
      if (err.code === 'ERR_NETWORK') {
        error.value = '网络连接错误，无法连接到消息服务器';
      } else if (err.code === 'ECONNABORTED') {
        error.value = '请求超时，服务器响应时间过长';
      } else {
        error.value = err.response?.data?.message || '删除消息失败';
      }

      // 即使失败也不抛出异常，避免阻塞应用
      return false;
    }
  };

  /**
   * 创建测试消息（仅用于开发和测试）
   * @param {Object} messageData - 消息数据
   * @returns {Promise<Object>} - 创建的消息
   */
  const createTestMessage = async (messageData = {}) => {
    try {
      console.log('Store: 创建测试消息，数据:', messageData);

      // 使用完整的URL，避免相对路径问题
      const apiUrl = import.meta.env.VITE_API_URL ;
      const response = await axios.post(`${apiUrl}/api/messages/test`, messageData, {
        // 确保携带凭证
        withCredentials: true,
        // 增加超时时间
        timeout: 10000
      });

      console.log('Store: 创建测试消息成功，响应:', response.data);

      // 添加到本地消息列表
      messages.value.unshift(response.data);

      // 更新未读消息数量
      unreadCount.value += 1;

      return response.data;
    } catch (err) {
      console.error('Store: 创建测试消息失败:', err);

      // 提供更详细的错误信息
      if (err.code === 'ERR_NETWORK') {
        error.value = '网络连接错误，无法连接到消息服务器';
        console.error('网络连接错误，无法连接到消息服务器:', err);
      } else if (err.code === 'ECONNABORTED') {
        error.value = '请求超时，服务器响应时间过长';
        console.error('请求超时，服务器响应时间过长:', err);
      } else if (err.response) {
        console.error('Store: API响应状态码:', err.response.status);
        console.error('Store: API响应数据:', err.response.data);

        if (err.response.data?.error?.message) {
          error.value = err.response.data.error.message;
        } else if (err.response.data?.message) {
          error.value = err.response.data.message;
        } else {
          error.value = '创建测试消息失败: ' + err.message;
        }
      } else {
        error.value = '创建测试消息失败: ' + err.message;
      }

      // 即使失败也不抛出异常，避免阻塞应用
      return null;
    }
  };

  /**
   * 初始化消息存储
   * 获取未读消息数量和最新消息
   */
  const init = async () => {
    if (!isInitialized.value) {
      try {
        // 获取未读消息数量
        await fetchUnreadCount();

        // 同时获取最新的几条消息
        await fetchMessages({ limit: 10 });

        isInitialized.value = true;
      } catch (err) {
        console.error('初始化消息存储失败:', err);

        // 提供更详细的错误信息
        if (err.code === 'ERR_NETWORK') {
          console.warn('网络连接错误，无法连接到消息服务器。应用将继续运行，但消息功能可能不可用。');
        } else if (err.code === 'ECONNABORTED') {
          console.warn('请求超时，服务器响应时间过长。应用将继续运行，但消息功能可能不可用。');
        }

        // 设置初始化标志，但不阻止应用继续运行
        isInitialized.value = true;
      }
    }
  };

  return {
    messages,
    sortedMessages,
    unreadMessages,
    unreadCount,
    loading,
    error,
    isInitialized,
    fetchMessages,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    deleteMessage,
    createTestMessage,
    init
  };
});
