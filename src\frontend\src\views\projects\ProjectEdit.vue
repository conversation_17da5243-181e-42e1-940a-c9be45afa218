<template>
  <div class="page-container">
    <!-- 页面标题和按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">编辑项目</h1>
        <p class="page-subtitle">修改现有项目信息</p>
      </div>
      <router-link to="/projects" class="btn btn-secondary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        返回列表
      </router-link>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-md shadow-sm">
      <div class="flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="font-medium">错误提示：</span>
      </div>
      <p class="mt-2 whitespace-pre-line">{{ error }}</p>
      
      <!-- 如果是项目不存在错误，显示额外的操作按钮 -->
      <div v-if="error.includes('项目不存在') || error.includes('资源不存在')" class="mt-4 flex items-center space-x-4">
        <button 
          @click="router.push('/projects')" 
          class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
          查看所有项目
        </button>
        <button 
          @click="router.push('/projects/create')" 
          class="inline-flex items-center px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          创建新项目
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="card shadow-md p-8 text-center">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600 mb-4"></div>
      <p class="text-gray-600 font-medium">正在获取项目信息，请稍候...</p>
      <p class="text-gray-500 text-sm mt-2">系统正在从数据库读取项目详细信息，这可能需要几秒钟时间</p>
    </div>

    <!-- 表单内容 -->
    <form v-if="!isLoading" @submit.prevent="saveProject" class="space-y-8 bg-white p-8 rounded-lg shadow">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- 左侧：基本信息和进度 -->
        <div class="md:col-span-2 space-y-8">
          <!-- 基本信息卡片 -->
          <div class="card">
            <h2 class="card-header">
              <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              基本信息
            </h2>
            <div class="form-group">
              <div class="form-grid">
                <div>
                  <label for="name" class="form-label">项目名称 <span class="form-required">*</span></label>
                  <input id="name" v-model="projectForm.name" type="text" class="form-input" required
                    placeholder="请输入项目名称" />
                </div>
                <div>
                <label for="code" class="form-label">项目编号</label>
                <input id="code" v-model="projectForm.code" type="text" class="form-input"
                  placeholder="自动生成或手动输入" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="status" class="form-label">项目状态</label>
                <select id="status" v-model="projectForm.status" class="form-input">
                  <option value="planning">规划中</option>
                  <option value="in_progress">进行中</option>
                  <option value="on_hold">已暂停</option>
                  <option value="completed">已完成</option>
                  <option value="cancelled">已取消</option>
                </select>
              </div>
              <div>
                <label for="manager" class="form-label">项目负责人 <span class="form-required">*</span></label>
                <select id="manager" v-model="projectForm.manager" class="form-input" required>
                  <option value="">请选择项目负责人</option>
                  <option v-for="user in userOptions" :key="user.id" :value="user.name">
                    {{ user.name }}
                  </option>
                </select>
              </div>
            </div>

            <div class="form-grid">
              <div class="relative">
                <label for="constructionUnit" class="form-label">建设单位</label>
                <input
                  id="constructionUnit"
                  v-model="constructionUnitInput"
                  @input="onConstructionUnitInput"
                  @focus="onConstructionUnitInput"
                  @blur="hideConstructionUnitSuggestions"
                  type="text"
                  class="form-input"
                  placeholder="请输入建设单位"
                  autocomplete="off"
                />
                <ul v-if="showConstructionUnitSuggestions && constructionUnitSuggestions.length" class="absolute z-10 bg-white border border-gray-200 rounded shadow w-full mt-1 max-h-40 overflow-auto">
                  <li
                    v-for="(suggestion, idx) in constructionUnitSuggestions"
                    :key="suggestion + idx"
                    @mousedown.prevent="selectConstructionUnitSuggestion(suggestion)"
                    class="px-3 py-2 cursor-pointer hover:bg-blue-100"
                  >
                    {{ suggestion }}
                  </li>
                </ul>
              </div>
              <div>
                <label for="designUnit" class="form-label">设计单位</label>
                <input id="designUnit" v-model="projectForm.designUnit" type="text" class="form-input"
                  placeholder="请输入设计单位" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="contractorUnit" class="form-label">施工单位</label>
                <input id="contractorUnit" v-model="projectForm.contractorUnit" type="text" class="form-input"
                  placeholder="请输入施工单位" />
              </div>
              <div>
                <label for="supervisorUnit" class="form-label">监理单位</label>
                <input id="supervisorUnit" v-model="projectForm.supervisorUnit" type="text" class="form-input"
                  placeholder="请输入监理单位" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="reviewUnit" class="form-label">审图单位</label>
                <input id="reviewUnit" v-model="projectForm.reviewUnit" type="text" class="form-input"
                  placeholder="请输入审图单位" />
              </div>
              <div>
                <label for="address" class="form-label">项目地址</label>
                <input id="address" v-model="projectForm.address" type="text" class="form-input"
                  placeholder="请输入项目地址" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="archiveNumber" class="form-label">项目档案号</label>
                <input id="archiveNumber" v-model="projectForm.archiveNumber" type="text" class="form-input"
                  placeholder="请输入项目档案号" />
              </div>
              <div>
                <label for="engineeringNumber" class="form-label">工程编号</label>
                <input id="engineeringNumber" v-model="projectForm.engineeringNumber" type="text" class="form-input"
                  placeholder="请输入工程编号" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="usageType" class="form-label">使用性质 <span class="form-required">*</span></label>
                <select id="usageType" v-model="projectForm.usageType" class="form-input" required>
                  <option value="">请选择</option>
                  <option value="commercial">商业</option>
                  <option value="residential">住宅</option>
                  <option value="industrial">工业</option>
                  <option value="public">公共建筑</option>
                </select>
              </div>
              <div>
                <label for="engineeringType" class="form-label">工程性质 <span class="form-required">*</span></label>
                <select id="engineeringType" v-model="projectForm.engineeringType" class="form-input" required>
                  <option value="">请选择</option>
                  <option value="building">建筑工程</option>
                  <option value="fire">消防工程</option>
                  <option value="annual">年度检测</option>
                  <option value="completion">竣工检测</option>
                  <option value="safety">消防安全评估</option>
                  <option value="maintenance">消防维保</option>
                </select>
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="constructionPermitNumber" class="form-label">施工许可证号</label>
                <input id="constructionPermitNumber" v-model="projectForm.constructionPermitNumber" type="text" class="form-input"
                  placeholder="请输入施工许可证号" />
              </div>
              <div>
                <label for="area" class="form-label">面积</label>
                <input id="area" v-model="projectForm.area" type="number" class="form-input"
                  placeholder="请输入面积" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="specialSystem" class="form-label">特殊系统</label>
                <input id="specialSystem" v-model="projectForm.specialSystem" type="text" class="form-input"
                  placeholder="请输入特殊系统" />
              </div>
              <div>
                <label for="companyManager" class="form-label">公司业务负责人</label>
                <input id="companyManager" v-model="projectForm.companyManager" type="text" class="form-input"
                  placeholder="请输入公司业务负责人" />
              </div>
            </div>

            <div class="form-grid">
              <div>
                <label for="clientContact" class="form-label">甲方项目对接人</label>
                <input id="clientContact" v-model="projectForm.clientContact" type="text" class="form-input"
                  placeholder="请输入甲方项目对接人" />
              </div>
            </div>

            <div>
              <label for="description" class="form-label">项目描述</label>
              <textarea id="description" v-model="projectForm.description" rows="3" class="form-input"
                placeholder="输入项目的简要描述信息..."></textarea>
            </div>

            <div>
              <label for="project-tags" class="form-label">项目标签</label>
              <div id="project-tags" class="flex flex-wrap gap-2 mt-2">
                <div v-for="(tag, index) in availableTags" :key="tag"
                  @click="toggleTag(tag)"
                  :class="[
                    'cursor-pointer px-3 py-1 rounded-full text-sm transition-colors duration-200',
                    projectForm.tags.includes(tag)
                      ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  ]">
                  {{ tag }}
                </div>
                <div class="relative">
                  <input
                    v-model="newTag"
                    @keydown.enter.prevent="addNewTag"
                    type="text"
                    placeholder="+ 添加新标签"
                    class="form-input p-1.5 text-sm min-w-[120px] max-w-[200px]"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：时间信息和详细信息 -->
        <div class="space-y-8">
          <!-- 时间信息卡片 -->
          <div class="card">
            <h2 class="card-header">
              <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              时间信息
            </h2>
            <div class="form-group">
              <div class="form-grid">
                <div>
                  <label for="startDate" class="form-label">开始日期 <span class="form-required">*</span></label>
                  <input id="startDate" v-model="projectForm.startDate" type="date" class="form-input" required max="3000-12-31" />
                </div>
                <div>
                  <label for="endDate" class="form-label">计划结束日期 <span class="form-required">*</span></label>
                  <input id="endDate" v-model="projectForm.endDate" type="date" class="form-input" required max="3000-12-31" />
                </div>
              </div>

              <div>
                <label for="progress" class="form-label">当前进度</label>
                <div class="flex items-center space-x-3">
                  <input
                    id="progress"
                    type="range"
                    min="0"
                    max="100"
                    step="5"
                    v-model="projectForm.progress"
                    class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <span class="font-medium" :class="getProgressTextColor(projectForm.progress)">{{ projectForm.progress }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5 mt-2">
                  <div class="h-2.5 rounded-full" :class="getProgressBarColor(projectForm.progress)" :style="{ width: `${projectForm.progress}%` }"></div>
                </div>
              </div>

              <div>
                <label for="reminders" class="form-label">重要事项提醒</label>
                <textarea id="reminders" v-model="projectForm.reminders" rows="2" class="form-input"
                  placeholder="记录需要提醒的重要事项..."></textarea>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：详细信息 -->
        <div class="space-y-8">
          <!-- 项目细节卡片 -->
          <div class="card">
            <h2 class="card-header">
              <svg class="card-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              项目细节
            </h2>
            <div class="form-group">
              <div>
                <label for="designDirection" class="form-label">设计方向</label>
                <input id="designDirection" v-model="projectForm.designDirection" type="text" class="form-input"
                  placeholder="请输入设计方向" />
              </div>
              <div>
                <label for="constructionDirection" class="form-label">施工方向</label>
                <input id="constructionDirection" v-model="projectForm.constructionDirection" type="text" class="form-input"
                  placeholder="请输入施工方向" />
              </div>
              <div>
                <label for="supervisor" class="form-label">监理单位</label>
                <input id="supervisor" v-model="projectForm.supervisor" type="text" class="form-input"
                  placeholder="请输入监理单位名称" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import axios from 'axios';
import apiService from '@/services/apiService';

const router = useRouter();
const route = useRoute();
const error = ref('');
const isLoading = ref(true);
const isSaving = ref(false);
const newTag = ref('');
const showAddClientModal = ref(false);
const clientSearchQuery = ref('');
const loadingClients = ref(false);
const availableClients = ref([]);
const clientRoleInput = ref('main_client');
const clientNotesInput = ref('');
const selectedClientId = ref('');
const projectClients = ref([]);
const userOptions = ref([]);

// 获取项目信息
async function fetchProject() {
  try {
    // 获取项目ID
    const projectId = route.params.id;
    if (!projectId) {
      error.value = '未指定项目ID，无法获取项目信息';
      isLoading.value = false;
      return;
    }
    
    // 获取项目详情
    const projectResponse = await apiService.getProject(projectId);
    
    // 填充表单数据
    projectForm.name = projectResponse.name || '';
    projectForm.code = projectResponse.code || '';
    projectForm.status = projectResponse.status || 'planning';
    projectForm.priority = projectResponse.priority || 'medium';
    projectForm.startDate = projectResponse.startDate ? new Date(projectResponse.startDate).toISOString().split('T')[0] : '';
    projectForm.endDate = projectResponse.endDate ? new Date(projectResponse.endDate).toISOString().split('T')[0] : '';
    projectForm.progress = projectResponse.progress || 0;
    projectForm.budget = projectResponse.budget || 0;
    projectForm.description = projectResponse.description || '';
    projectForm.constructionUnit = projectResponse.constructionUnit || '';
    projectForm.designUnit = projectResponse.designUnit || '';
    projectForm.contractorUnit = projectResponse.contractorUnit || '';
    projectForm.supervisorUnit = projectResponse.supervisorUnit || '';
    projectForm.reviewUnit = projectResponse.reviewUnit || '';
    projectForm.address = projectResponse.address || '';
    projectForm.location = projectResponse.location || '';
    projectForm.area = projectResponse.area || 0;
    projectForm.floors = projectResponse.floors || 0;
    projectForm.height = projectResponse.height || 0;
    projectForm.structureType = projectResponse.structureType || '';
    projectForm.buildingType = projectResponse.buildingType || '';
    projectForm.fireRating = projectResponse.fireRating || '';
    projectForm.seismicLevel = projectResponse.seismicLevel || '';
    projectForm.supervisor = projectResponse.supervisor || '';
    projectForm.leadArchitect = projectResponse.leadArchitect || '';
    projectForm.constructionDirection = projectResponse.constructionDirection || '';
    
    // 获取项目关联客户
    const clientsResponse = await apiService.projectClients.getProjectClients(projectId);
    projectClients.value = clientsResponse || [];
    
    // 设置标签
    if (projectResponse.tags && Array.isArray(projectResponse.tags)) {
      projectForm.tags = projectResponse.tags;
    }
    
    isLoading.value = false;
    
  } catch (err) {
    console.error('获取项目信息失败:', err);
    error.value = err.response?.data?.message || '获取项目信息失败，请重试';
    isLoading.value = false;
  }
}

// 组件挂载时先获取用户列表，然后获取项目信息
onMounted(async () => {
  try {
    // 获取用户列表供项目负责人选择
    const usersResponse = await apiService.getUsers();
    userOptions.value = usersResponse.results || [];
  } catch (err) {
    console.error('获取用户列表失败:', err);
  }
  
  // 获取项目信息
  await fetchProject();
  
  // 加载可选客户
  await loadAvailableClients();
});

// 客户分页相关
const clientPageNumber = ref(1);
const clientPageSize = ref(10);

// 建设单位自动补全相关
const constructionUnitSuggestions = ref([]);
const showConstructionUnitSuggestions = ref(false);
const constructionUnitInput = ref('');
let constructionUnitFetchTimeout = null;

// 获取用户列表
const fetchUsers = async () => {
  try {
    const users = await apiService.users.getUsers();
    userOptions.value = users;
    return users;
  } catch (err) {
    console.error('获取用户列表失败:', err);
    error.value = '获取用户列表失败，请刷新页面重试';
    throw err;
  }
};

// 可选标签
const availableTags = [
  '设计', '规划', '商业', '住宅', '工业', '改造',
  '景观', '教育', '医疗', '交通', '文化', '体育'
];

// 表单数据
const projectForm = reactive({
  name: '',
  code: '',
  status: 'planning',
  manager: '',
  description: '',
  startDate: '',
  endDate: '',
  progress: 0,
  designDirection: '',
  constructionDirection: '',
  supervisor: '',
  reminders: '',
  tags: [],
  teamMembers: [
    { id: '' }
  ],
  constructionUnit: '',
  designUnit: '',
  contractorUnit: '',
  supervisorUnit: '',
  reviewUnit: '',
  address: '',
  archiveNumber: '',
  engineeringNumber: '',
  usageType: '',
  engineeringType: '',
  constructionPermitNumber: '',
  area: '',
  specialSystem: '',
  companyManager: '',
  clientContact: ''
});

// 获取项目信息
async function fetchProject() {
  try {
    const projectId = route.params.id;
    if (!projectId) {
      error.value = '项目ID不能为空';
      isLoading.value = false;
      return null;
    }

    // 验证项目ID格式（UUID）
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(projectId)) {
      error.value = '无效的项目ID格式';
      setTimeout(() => {
        router.push('/projects');
      }, 3000);
      isLoading.value = false;
      return null;
    }

    const response = await axios.get(`/api/projects/${projectId}`);
    const project = response.data;

    // 填充表单数据
    if (project) {
      // 基本信息字段
      projectForm.name = project.name || '';
      projectForm.code = project.code || '';
      projectForm.status = project.status || 'planning';
      projectForm.manager = project.manager || '';

      projectForm.description = project.description || '';
      projectForm.startDate = project.startDate ? project.startDate.split('T')[0] : '';
      projectForm.endDate = project.endDate ? project.endDate.split('T')[0] : '';
      projectForm.progress = project.progress || 0;
      projectForm.designDirection = project.designDirection || '';
      projectForm.constructionDirection = project.constructionDirection || '';
      projectForm.supervisor = project.supervisor || '';
      projectForm.reminders = project.reminders || '';
      projectForm.tags = Array.isArray(project.tags) ? project.tags : (project.tags ? JSON.parse(project.tags) : []);
      
      // 团队成员
      projectForm.teamMembers = project.members && project.members.length > 0 ? project.members.map(member => ({ id: member })) : [{ id: '' }];
      
      // 其他字段
      projectForm.constructionUnit = project.constructionUnit || '';
      projectForm.designUnit = project.designUnit || '';
      projectForm.contractorUnit = project.contractorUnit || '';
      projectForm.supervisorUnit = project.supervisorUnit || '';
      projectForm.reviewUnit = project.reviewUnit || '';
      projectForm.address = project.address || '';
      projectForm.archiveNumber = project.archiveNumber || '';
      projectForm.engineeringNumber = project.engineeringNumber || '';
      projectForm.usageType = project.usageType || '';
      projectForm.engineeringType = project.engineeringType || '';
      projectForm.constructionPermitNumber = project.constructionPermitNumber || '';
      projectForm.area = project.area || '';
      projectForm.specialSystem = project.specialSystem || '';
      projectForm.companyManager = project.companyManager || '';
      projectForm.clientContact = project.clientContact || '';      // 获取项目关联的客户
      try {
        console.log(`正在获取项目ID: ${projectId} 的关联客户...`);
        
        // 测试直接使用axios调用后端端点
        console.log('直接使用axios调用API端点...');
        try {
          const directResponse = await axios.get(`/api/projects/${projectId}/clients`);
          console.log('直接调用返回结果:', directResponse.data);
        } catch (axiosErr) {
          console.error('直接调用失败:', axiosErr);
        }
        
        // 使用apiService调用
        console.log('使用apiService调用...');
        const clientsResponse = await apiService.projectClients.getProjectClients(projectId);
        console.log(`获取到项目客户数据:`, clientsResponse);
        projectClients.value = clientsResponse || [];
        console.log(`处理后的项目客户数据:`, projectClients.value);
      } catch (err) {
        console.error('获取项目关联客户失败:', err);
        // 如果是404错误（项目不存在），显示友好提示并重定向
        if (err.message && err.message.includes('资源不存在')) {
          error.value = '项目不存在或已被删除，请检查项目ID是否正确';
          // 3秒后自动跳转到项目列表
          setTimeout(() => {
            router.push('/projects');
          }, 3000);
          return null;
        }
        // 其他错误只记录但不阻止页面加载
        console.warn('无法获取项目关联客户，将显示空列表');
        projectClients.value = [];
      }
    }
    // 成功获取项目信息后，重置加载状态
    isLoading.value = false;
    return project;
  } catch (err) {
    console.error('获取项目信息失败:', err);
    
    // 处理404错误（项目不存在）
    if (err.response?.status === 404) {
      error.value = '项目不存在或已被删除，3秒后将跳转到项目列表';
      setTimeout(() => {
        router.push('/projects');
      }, 3000);
    } else if (err.response?.status === 403) {
      error.value = '您没有权限访问此项目';
    } else if (err.response?.status === 401) {
      error.value = '请先登录后再访问';
    } else {
      error.value = err.response?.data?.message || '获取项目信息失败，请重试';
    }
    
    isLoading.value = false;
    return null;
  }
}

// 组件挂载时先获取用户列表，然后获取项目信息
onMounted(async () => {
  try {
    console.log('当前路由参数:', route.params);
    console.log('项目ID:', route.params.id);
    await fetchUsers();
    await fetchProject();
    loadAvailableClients(); // 加载可选客户列表
  } catch (err) {
    console.error('初始化页面失败:', err);
    error.value = '加载数据失败，请刷新页面重试';
  }
});

// 允许的字段列表
const allowedUpdateFields = [
  'name', 'members', 'manager', 'constructionUnit', 'designUnit', 'contractorUnit', 'supervisorUnit', 'reviewUnit',
  'address', 'startDate', 'endDate', 'archiveNumber', 'engineeringNumber', 'usageType', 'engineeringType',
  'constructionPermitNumber', 'area', 'specialSystem', 'companyManager', 'status', 'progress', 'clientContact', 'tags'
];

function filterProjectUpdatePayload(form) {
  const payload = {};
  for (const key of allowedUpdateFields) {
    if (form[key] !== undefined) payload[key] = form[key];
  }
  // reviewUnit 如为数组或对象，转为字符串
  if (typeof payload.reviewUnit !== 'string' && payload.reviewUnit !== undefined) {
    payload.reviewUnit = String(payload.reviewUnit);
  }
  return payload;
}

// 客户状态显示相关函数
function getClientStatusText(status) {
  const statusMap = {
    active: '活跃',
    inactive: '非活跃',
    pending: '待审核',
    suspended: '已暂停',
    unknown: '未知'
  };
  return statusMap[status] || '未知';
}

function getClientStatusClass(status) {
  if (status === 'active') return 'text-green-600';
  if (status === 'inactive') return 'text-gray-600';
  if (status === 'pending') return 'text-orange-600';
  if (status === 'suspended') return 'text-red-600';
  return 'text-gray-600';
}

// 获取客户姓名首字母作为头像
function getClientInitials(name) {
  if (!name) return '?';
  return name.charAt(0).toUpperCase();
}

// 搜索客户
async function searchClients() {
  if (clientSearchQuery.value.trim() === '') {
    availableClients.value = [];
    return;
  }

  loadingClients.value = true;
  error.value = '';

  try {
    const response = await apiService.getClients({
      search: clientSearchQuery.value,
      limit: 20 // 限制搜索结果数量
    });
    availableClients.value = response.results || [];
  } catch (err) {
    console.error('搜索客户失败:', err);
    error.value = '搜索客户失败，请重试';
  } finally {
    loadingClients.value = false;
  }
}

// 加载可选客户列表
async function loadAvailableClients() {
  try {
    const response = await apiService.getClients({
      limit: 100 // 获取较多客户供选择
    });
    availableClients.value = response.results || [];
  } catch (err) {
    console.error('加载客户列表失败:', err);
  }
}

// 加载更多客户
async function loadMoreClients() {
  try {
    const lastId = availableClients.value.length > 0 ? 
      availableClients.value[availableClients.value.length - 1].id : null;
    
    const response = await apiService.getClients({
      limit: 50,
      after: lastId
    });
    
    if (response.results && response.results.length > 0) {
      availableClients.value = [...availableClients.value, ...response.results];
    } else {
      // 显示没有更多客户的提示
      alert('没有更多客户了');
    }
  } catch (err) {
    console.error('加载更多客户失败:', err);
    error.value = '加载更多客户失败，请重试';
  }
}

// 客户被选中时的处理
function onClientSelected() {
  if (!selectedClientId.value) return;
  
  // 可以在这里添加其他逻辑，比如自动填充一些字段
  const selectedClient = availableClients.value.find(c => c.id === selectedClientId.value);
  if (selectedClient) {
    // 例如，可以自动设置项目的一些属性
    console.log('已选择客户:', selectedClient.name);
  }
}

// 从下拉列表添加客户
async function addClientFromDropdown() {
  if (!selectedClientId.value) return;
  
  try {
    const projectId = route.params.id;
    const clientId = selectedClientId.value;
    
    // 获取客户详情
    const client = availableClients.value.find(c => c.id === clientId);
    if (!client) {
      console.error('未找到选择的客户:', clientId);
      return;
    }
    
    // 创建关联
    await apiService.projectClients.updateProjectClient(projectId, clientId, {
      role: clientRoleInput.value,
      notes: clientNotesInput.value
    });
    
    // 更新本地数据
    projectClients.value = [{
      ...client,
      role: clientRoleInput.value,
      notes: clientNotesInput.value,
      createdAt: new Date().toISOString()
    }];
    
    // 重置选择
    selectedClientId.value = '';
    
  } catch (err) {
    console.error('关联客户失败:', err);
    error.value = '关联客户失败，请重试';
  }
}

// 替换客户
async function replaceClient() {
  if (!selectedClientId.value || projectClients.value.length === 0) return;
  
  try {
    const projectId = route.params.id;
    const oldClientId = projectClients.value[0].id;
    const newClientId = selectedClientId.value;
    
    // 如果是同一个客户，只更新角色和备注
    if (oldClientId === newClientId) {
      await apiService.projectClients.updateProjectClient(projectId, newClientId, {
        role: clientRoleInput.value,
        notes: clientNotesInput.value
      });
      
      // 更新本地数据
      projectClients.value[0] = {
        ...projectClients.value[0],
        role: clientRoleInput.value,
        notes: clientNotesInput.value,
        updatedAt: new Date().toISOString()
      };
    } else {
      // 先移除旧客户
      await apiService.projectClients.removeProjectClient(projectId, oldClientId);
      
      // 再添加新客户
      const client = availableClients.value.find(c => c.id === newClientId);
      if (!client) {
        console.error('未找到选择的客户:', newClientId);
        return;
      }
      
      await apiService.projectClients.updateProjectClient(projectId, newClientId, {
        role: clientRoleInput.value,
        notes: clientNotesInput.value
      });
      
      // 更新本地数据
      projectClients.value = [{
        ...client,
        role: clientRoleInput.value,
        notes: clientNotesInput.value,
        createdAt: new Date().toISOString()
      }];
    }
    
    // 重置选择
    selectedClientId.value = '';
    
  } catch (err) {
    console.error('更换客户失败:', err);
    error.value = '更换客户失败，请重试';
  }
}

// 添加客户关联
async function addClient(clientId) {
  const client = availableClients.value.find(c => c.id === clientId);
  if (!client) {
    console.error('未找到客户:', clientId);
    return;
  }

  // 创建关联对象
  const clientWithAssociation = {
    ...client,
    role: clientRoleInput.value,
    notes: clientNotesInput.value,
    createdAt: new Date().toISOString()
  };

  // 只允许一个客户关联，替换现有客户
  projectClients.value = [clientWithAssociation];
  
  // 关闭弹窗并重置搜索
  showAddClientModal.value = false;
  clientSearchQuery.value = '';
  clientRoleInput.value = 'main_client';
  clientNotesInput.value = '';
  availableClients.value = [];
  
  // 更新项目-客户关联
  try {
    const projectId = route.params.id;
    await apiService.projectClients.updateProjectClient(projectId, clientId, {
      role: clientRoleInput.value,
      notes: clientNotesInput.value
    });
  } catch (err) {
    console.error('更新项目客户关联失败:', err);
    error.value = '更新项目客户关联失败，请重试';
  }
}

// 处理客户选择
async function handleClientSelection() {
  if (!selectedClientId.value) return;
  
  try {
    const projectId = route.params.id;
    const clientId = selectedClientId.value;
    
    // 获取客户详情
    const client = availableClients.value.find(c => c.id === clientId);
    if (!client) {
      console.error('未找到选择的客户:', clientId);
      return;
    }
    
    // 创建关联
    await apiService.projectClients.updateProjectClient(projectId, clientId, {
      role: 'main_client', // 默认为主要客户
      notes: ''
    });
    
    // 更新本地数据
    projectClients.value = [{
      ...client,
      role: 'main_client',
      notes: '',
      createdAt: new Date().toISOString()
    }];
    
    // 重置选择
    selectedClientId.value = '';
    
  } catch (err) {
    console.error('关联客户失败:', err);
    error.value = '关联客户失败，请重试';
  }
}

// 移除客户关联
async function removeClient(clientId) {
  try {
    const projectId = route.params.id;
    
    // 调用API删除关联
    await apiService.projectClients.removeProjectClient(projectId, clientId);
    
    // 更新本地数据
    projectClients.value = projectClients.value.filter(c => c.id !== clientId);
    
  } catch (err) {
    console.error('移除客户关联失败:', err);
    error.value = '移除客户关联失败，请重试';
  }
}

// 保存项目信息
async function saveProject() {
  // 表单验证
  if (!projectForm.name) {
    error.value = '请输入项目名称';
    return;
  }

  if (!projectForm.manager) {
    error.value = '请输入项目负责人';
    return;
  }

  if (!projectForm.startDate) {
    error.value = '请选择开始日期';
    return;
  }

  if (!projectForm.endDate) {
    error.value = '请选择计划结束日期';
    return;
  }

  // 验证日期大小关系
  if (new Date(projectForm.endDate) < new Date(projectForm.startDate)) {
    error.value = '计划结束日期不能早于开始日期';
    return;
  }

  isSaving.value = true;
  error.value = '';

  try {
    const projectId = route.params.id;
    if (!projectId) {
      error.value = '项目ID不能为空';
      return;
    }
    // 移除空的团队成员
    projectForm.teamMembers = projectForm.teamMembers.filter(member => member.id !== '');
    // 只提交允许的字段
    const payload = filterProjectUpdatePayload(projectForm);
    // 同步 teamMembers 到 members 字段
    const memberIds = Array.isArray(projectForm.teamMembers)
      ? projectForm.teamMembers.map(member => member.id).filter(Boolean)
      : [];
    // 直接使用用户ID数组，因为后端验证和模型都期望 members 是 UUID 数组
    payload.members = memberIds.length > 0 ? memberIds : [];
    // 保证 tags 字段为数组
    if (payload.tags && typeof payload.tags === 'string') {
      try {
        payload.tags = JSON.parse(payload.tags);
      } catch {
        payload.tags = payload.tags.split(',').map(t => t.trim()).filter(Boolean);
      }
    }
    if (!Array.isArray(payload.tags)) {
      payload.tags = [];
    }
    // 更新项目信息
    await apiService.updateProject(projectId, payload);
    // 返回项目列表页
    router.push('/projects');
  } catch (err) {
    console.error('更新项目失败:', err);
    error.value = err.response?.data?.message || '更新项目失败，请重试';
  } finally {
    isSaving.value = false;
  }
}

// 添加新标签
function addNewTag() {
  if (newTag.value && !projectForm.tags.includes(newTag.value)) {
    projectForm.tags.push(newTag.value);
    newTag.value = '';
  }
}

// 切换标签选中状态
function toggleTag(tag) {
  const index = projectForm.tags.indexOf(tag);
  if (index === -1) {
    projectForm.tags.push(tag);
  } else {
    projectForm.tags.splice(index, 1);
  }
}

// 添加团队成员
function addTeamMember() {
  projectForm.teamMembers.push({ id: '' });
}

// 移除团队成员
function removeTeamMember(index) {
  projectForm.teamMembers.splice(index, 1);
  // 确保至少有一个成员选择框
  if (projectForm.teamMembers.length === 0) {
    addTeamMember();
  }
}

// 获取进度条颜色
function getProgressBarColor(progress) {
  if (progress < 25) return 'bg-red-600';
  if (progress < 50) return 'bg-yellow-600';
  if (progress < 75) return 'bg-blue-600';
  return 'bg-green-600';
}

// 获取进度文本颜色
function getProgressTextColor(progress) {
  if (progress < 25) return 'text-red-600';
  if (progress < 50) return 'text-yellow-600';
  if (progress < 75) return 'text-blue-600';
  return 'text-green-600';
}

// 自动补全：输入时获取建议
async function onConstructionUnitInput(e) {
  const value = e.target.value;
  projectForm.constructionUnit = value;
  constructionUnitInput.value = value;
  if (constructionUnitFetchTimeout) clearTimeout(constructionUnitFetchTimeout);
  if (!value) {
    constructionUnitSuggestions.value = [];
    showConstructionUnitSuggestions.value = false;
    return;
  }
  constructionUnitFetchTimeout = setTimeout(async () => {
    try {
      const suggestions = await apiService.getConstructionUnits(value);
      constructionUnitSuggestions.value = suggestions;
      showConstructionUnitSuggestions.value = suggestions.length > 0;
    } catch (err) {
      constructionUnitSuggestions.value = [];
      showConstructionUnitSuggestions.value = false;
    }
  }, 200);
}

function selectConstructionUnitSuggestion(suggestion) {
  projectForm.constructionUnit = suggestion;
  constructionUnitInput.value = suggestion;
  showConstructionUnitSuggestions.value = false;
}

function hideConstructionUnitSuggestions() {
  setTimeout(() => { showConstructionUnitSuggestions.value = false; }, 150);
}

// 组件挂载时获取项目信息
onMounted(() => {
  fetchProject();
});

// Client related methods
// 客户相关方法
const getClientInitials = (name) => {
  if (!name) return '';
  return name.split(' ').map(n => n.charAt(0)).join('').toUpperCase().slice(0, 2);
};

const getClientStatusClass = (status) => {
  switch (status) {
    case 'active': return 'badge badge-success';
    case 'inactive': return 'badge badge-secondary';
    case 'potential': return 'badge badge-warning';
    default: return 'badge badge-secondary';
  }
};

const getClientStatusText = (status) => {
  switch (status) {
    case 'active': return '活跃';
    case 'inactive': return '非活跃';
    case 'potential': return '潜在客户';
    default: return '未知状态';
  }
};

// 搜索客户
const searchClients = async () => {
  if (!clientSearchQuery.value) return;
  
  loadingClients.value = true;
  try {
    const params = {
      query: clientSearchQuery.value,
      limit: 10
    };
    const response = await apiService.clientService.getClients(params);
    availableClients.value = response.data.clients || [];
  } catch (error) {
    console.error('Error searching clients:', error);
    ElMessage.error('搜索客户失败');
  } finally {
    loadingClients.value = false;
  }
};

// 加载更多客户到下拉列表
const loadMoreClients = async () => {
  loadingClients.value = true;
  try {
    clientPageNumber.value += 1;
    const params = {
      page: clientPageNumber.value,
      limit: clientPageSize.value
    };
    const response = await apiService.clientService.getClients(params);
    const newClients = response.data.clients || [];
    if (newClients.length > 0) {
      availableClients.value = [...availableClients.value, ...newClients];
    } else {
      ElMessage.info('没有更多客户可加载');
      clientPageNumber.value -= 1;
    }
  } catch (error) {
    console.error('Error loading more clients:', error);
    ElMessage.error('加载更多客户失败');
    clientPageNumber.value -= 1;
  } finally {
    loadingClients.value = false;
  }
};

// 初始加载客户列表用于下拉选择
const initClientDropdown = async () => {
  loadingClients.value = true;
  try {
    const params = {
      page: 1,
      limit: clientPageSize.value
    };
    const response = await apiService.clientService.getClients(params);
    availableClients.value = response.data.clients || [];
  } catch (error) {
    console.error('Error loading initial clients for dropdown:', error);
    ElMessage.error('加载客户列表失败');
  } finally {
    loadingClients.value = false;
  }
};

// 当客户被选中时
const onClientSelected = () => {
  // 可以在这里做一些附加操作
  // 如根据选中的客户ID获取客户详情等
};

// 添加客户关联
const addClient = async (client) => {
  try {
    const clientData = {
      clientId: client.id,
      role: clientRoleInput.value,
      notes: clientNotesInput.value || ''
    };
    
    await apiService.projectClientService.addProjectClient(projectId.value, clientData);
    
    // 刷新项目客户列表
    await fetchProjectClients();
    
    // 重置表单并关闭模态框
    clientRoleInput.value = 'main_client';
    clientNotesInput.value = '';
    showAddClientModal.value = false;
    
    ElMessage.success('客户关联添加成功');
  } catch (error) {
    console.error('Error adding client to project:', error);
    ElMessage.error('添加客户关联失败: ' + error.message);
  }
};

// 从下拉列表添加客户
const addClientFromDropdown = async () => {
  if (!selectedClientId.value) return;
  
  try {
    loading.value = true;
    
    // 查找客户详情
    const clientData = {
      clientId: selectedClientId.value,
      role: clientRoleInput.value,
      notes: clientNotesInput.value
    };
    
    // 添加客户关联
    await apiService.projectClientService.addProjectClient(projectId.value, clientData);
    toast.success('客户关联成功');
    
    // 重新加载客户列表
    await fetchProjectClients();
    
    // 清空输入
    selectedClientId.value = '';
    clientRoleInput.value = 'main_client';
    clientNotesInput.value = '';
    
  } catch (error) {
    console.error('添加客户关联失败:', error);
    toast.error(`添加客户关联失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

// 替换客户
const replaceClient = async () => {
  if (!selectedClientId.value || projectClients.value.length === 0) return;
  
  try {
    loading.value = true;
    
    // 先移除现有客户
    await apiService.projectClients.removeProjectClient(projectId.value, projectClients.value[0].id);
    
    // 添加新客户
    const clientData = {
      clientId: selectedClientId.value,
      role: clientRoleInput.value,
      notes: clientNotesInput.value
    };
    
    await apiService.projectClientService.addProjectClient(projectId.value, clientData);
    toast.success('客户更换成功');
    
    // 重新加载客户列表
    await fetchProjectClients();
    
    // 清空输入
    selectedClientId.value = '';
    clientRoleInput.value = 'main_client';
    clientNotesInput.value = '';
    
  } catch (error) {
    console.error('更换客户失败:', error);
    toast.error(`更换客户失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

// 移除客户关联
const removeClient = async (clientId) => {
  if (!confirm('确定要移除此客户关联吗？')) {
    return;
  }
  
  try {
    const projectId = route.params.id;
    await apiService.projectClients.removeProjectClient(projectId, clientId);
    
    // 更新本地数据
    projectClients.value = projectClients.value.filter(c => c.id !== clientId);
  } catch (err) {
    console.error('移除客户关联失败:', err);
    error.value = err.response?.data?.message || '移除客户关联失败，请重试';
  }
}

<style scoped>
.page-title {
  @apply text-2xl font-bold text-gray-800;
}
.page-subtitle {
  @apply text-sm text-gray-500 mt-1;
}
.page-header {
  @apply flex justify-between items-center mb-6;
  animation: fadeSlideIn 0.5s ease-out;
}
.form-section-title {
  @apply text-lg font-semibold text-gray-700 mb-4;
}
.form-section {
  @apply mb-8;
}
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}
.form-input {
  @apply mt-1 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm border-gray-300 rounded-md;
}
.btn {
  @apply inline-flex justify-center py-2 px-4 border shadow-sm text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2;
}
.btn-primary {
  @apply text-white bg-blue-600 hover:bg-blue-700 border-transparent focus:ring-blue-500;
}
.btn-secondary {
  @apply text-gray-700 bg-white hover:bg-gray-50 border-gray-300 focus:ring-blue-500;
}

/* 标签动画 */
.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* 进度条样式 */
input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: #2563eb;
  border-radius: 50%;
  cursor: pointer;
}

input[type=range]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #2563eb;
  border-radius: 50%;
  cursor: pointer;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 客户选择模态框样式 */
.fixed {
  position: fixed;
}

.inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.bg-gray-600 {
  background-color: rgba(31, 41, 55, var(--tw-bg-opacity));
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.overflow-y-auto {
  overflow-y: auto;
}

.h-full {
  height: 100%;
}

.w-full {
  width: 100%;
}

.z-50 {
  z-index: 50;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.relative {
  position: relative;
}

.p-5 {
  padding: 1.25rem;
}

.bg-white {
  background-color: rgb(255 255 255);
}

.max-w-md {
  max-width: 28rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.shadow-xl {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px 0 rgba(0, 0, 0, 0.05);
}

.mb-4 {
  margin-bottom: 1rem;
}

.flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.items-center {
  align-items: center;
}

.text-lg {
  font-size: 1.125rem;
}

.font-medium {
  font-weight: 500;
}

.text-gray-900 {
  color: rgb(17 24 39);
}

.text-gray-400 {
  color: rgb(156 163 175);
}

.hover\:text-gray-500:hover {
  color: rgb(107 114 128);
}

.h-6 {
  height: 1.5rem;
}

.w-6 {
  width: 1.5rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.p-2 {
  padding: 0.5rem;
}

.bg-blue-500 {
  background-color: rgb(59 130 246);
}

.text-white {
  color: rgb(255 255 255);
}

.rounded-md {
  border-radius: 0.375rem;
}

.hover\:bg-blue-600:hover {
  background-color: rgb(37 99 235);
}

.text-center {
  text-align: center;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.text-gray-500 {
  color: rgb(107 114 128);
}

.max-h-60 {
  max-height: 15rem;
}

.overflow-y-auto {
  overflow-y: auto;
}

.divide-y {
  border-top-width: 1px;
  border-bottom-width: 1px;
}

.divide-gray-200 {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235);
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.hover\:bg-gray-50:hover {
  background-color: rgb(249 250 251);
}

.text-blue-600 {
  color: rgb(37 99 235);
}

.hover\:text-blue-800:hover {
  color: rgb(29 78 216);
}

.font-medium {
  font-weight: 500;
}

.mt-4 {
  margin-top: 1rem;
}

.pt-4 {
  padding-top: 1rem;
}

.border-t {
  border-top-width: 1px;
}

.text-gray-500 {
  color: rgb(107 114 128);
}

.text-gray-900 {
  color: rgb(17 24 39);
}

.text-sm {
  font-size: 0.875rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.cursor-pointer {
  cursor: pointer;
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}
</style>