classDiagram
    %% 用户管理模块
    class User {
        +uuid id
        +string username
        +string password
        +string name
        +int gender
        +string idCard
        +string phone
        +uuid departmentId
        +uuid positionId
        +Date createdAt
        +Date updatedAt
        +login(username, password) AuthToken
        +logout() void
        +updateProfile(userInfo) User
        +changePassword(oldPassword, newPassword) boolean
    }

    class Role {
        +uuid id
        +string name
        +string description
        +Date createdAt
        +Date updatedAt
        +addPermission(permissionId) boolean
        +removePermission(permissionId) boolean
        +getPermissions() Array~Permission~
    }

    class Permission {
        +uuid id
        +string name
        +string code
        +string description
        +string module
        +Date createdAt
        +Date updatedAt
    }

    class Department {
        +uuid id
        +string name
        +uuid parentId
        +string description
        +Date createdAt
        +Date updatedAt
        +addUser(userId) boolean
        +removeUser(userId) boolean
        +getUsers() Array~User~
    }

    class Position {
        +uuid id
        +string name
        +string description
        +Date createdAt
        +Date updatedAt
    }

    %% 项目管理模块
    class Project {
        +uuid id
        +string name
        +string code
        +uuid managerId
        +Date startDate
        +Date endDate
        +string designDirection
        +string constructionDirection
        +string supervisor
        +string status
        +Array reminders
        +Date createdAt
        +Date updatedAt
        +createTask(taskInfo) Task
        +updateProgress(progressInfo) boolean
        +addContract(contractInfo) Contract
        +addPurchase(purchaseInfo) PurchaseRequest
        +getProgress() Progress
        +getTasks() Array~Task~
        +getContracts() Array~Contract~
        +getPurchases() Array~PurchaseRequest~
    }

    class Progress {
        +uuid id
        +uuid projectId
        +string stage
        +float completionRate
        +string currentStatus
        +Date deliveryDate
        +boolean isDelayed
        +string delayReason
        +Date updatedAt
        +updateStatus(status, completionRate) boolean
        +setDelay(isDelayed, reason) boolean
    }

    class Task {
        +uuid id
        +uuid projectId
        +string name
        +string type
        +string status
        +string priority
        +uuid assigneeId
        +Date planStartDate
        +Date planEndDate
        +Date actualStartDate
        +Date actualEndDate
        +float remainingWorkload
        +string description
        +Date createdAt
        +Date updatedAt
        +updateStatus(status) boolean
        +assignTo(userId) boolean
        +updateWorkload(remainingWorkload) boolean
    }

    class ProjectFollowUp {
        +uuid id
        +uuid projectId
        +Date followUpDate
        +string content
        +uuid creatorId
        +Date createdAt
        +Date updatedAt
        +addAttachment(attachmentId) boolean
        +getAttachments() Array~Attachment~
    }

    %% 合同管理
    class Contract {
        +uuid id
        +string code
        +string name
        +string type
        +string status
        +float amount
        +uuid projectId
        +Array invoiceInfo
        +Date createdAt
        +Date updatedAt
        +addAttachment(attachmentId) boolean
        +getAttachments() Array~Attachment~
        +updateStatus(status) boolean
        +addPaymentRecord(paymentInfo) PaymentRecord
    }

    class PaymentRecord {
        +uuid id
        +uuid contractId
        +float amount
        +Date paymentDate
        +string paymentMethod
        +string status
        +string remark
        +Date createdAt
        +Date updatedAt
        +addInvoice(invoiceInfo) Invoice
    }

    class Invoice {
        +uuid id
        +uuid paymentRecordId
        +string invoiceNumber
        +string invoiceType
        +float amount
        +float taxRate
        +Date issueDate
        +string status
        +Date createdAt
        +Date updatedAt
    }

    %% 客户管理
    class Customer {
        +uuid id
        +string name
        +string address
        +string phone
        +uuid categoryId
        +string level
        +Array tags
        +string remark
        +Date createdAt
        +Date updatedAt
        +addContact(contactInfo) Contact
        +getContacts() Array~Contact~
        +addFollowUp(followUpInfo) CustomerFollowUp
        +getFollowUps() Array~CustomerFollowUp~
    }

    class Contact {
        +uuid id
        +uuid customerId
        +string name
        +string phone
        +string email
        +string position
        +boolean isPrimary
        +Date createdAt
        +Date updatedAt
    }

    class CustomerFollowUp {
        +uuid id
        +uuid customerId
        +string stage
        +string content
        +uuid creatorId
        +Date followUpDate
        +Date createdAt
        +Date updatedAt
    }

    class CustomerCategory {
        +uuid id
        +string name
        +string description
        +Date createdAt
        +Date updatedAt
    }

    %% 供应商管理
    class Supplier {
        +uuid id
        +string name
        +string address
        +string phone
        +uuid categoryId
        +string level
        +string remark
        +Date createdAt
        +Date updatedAt
        +addContact(contactInfo) SupplierContact
        +getContacts() Array~SupplierContact~
        +addBankAccount(accountInfo) BankAccount
        +getBankAccounts() Array~BankAccount~
    }

    class SupplierContact {
        +uuid id
        +uuid supplierId
        +string name
        +string phone
        +string email
        +string position
        +boolean isPrimary
        +Date createdAt
        +Date updatedAt
    }

    class BankAccount {
        +uuid id
        +uuid supplierId
        +string bankName
        +string branchName
        +string accountNumber
        +string accountName
        +Date createdAt
        +Date updatedAt
    }

    class SupplierCategory {
        +uuid id
        +string name
        +string description
        +Date createdAt
        +Date updatedAt
    }

    %% 采购管理
    class PurchaseRequest {
        +uuid id
        +uuid projectId
        +string content
        +uuid supplierId
        +int quantity
        +float amount
        +string invoiceType
        +string status
        +Date purchaseDate
        +uuid creatorId
        +Date createdAt
        +Date updatedAt
        +approve() boolean
        +reject(reason) boolean
        +addProduct(productInfo) PurchaseProduct
        +getProducts() Array~PurchaseProduct~
    }

    class PurchaseProduct {
        +uuid id
        +uuid purchaseRequestId
        +string name
        +string model
        +string manufacturer
        +int quantity
        +float unitPrice
        +float taxRate
        +Date createdAt
        +Date updatedAt
    }

    class ReturnRecord {
        +uuid id
        +uuid purchaseRequestId
        +string batchNumber
        +string returnReason
        +string invoiceInfo
        +Date returnDate
        +Date createdAt
        +Date updatedAt
    }

    %% 库存管理
    class Inventory {
        +uuid id
        +string name
        +string model
        +string manufacturer
        +int quantity
        +Date entryDate
        +float unitPrice
        +Date createdAt
        +Date updatedAt
        +addStock(quantity, entryDate) boolean
        +reduceStock(quantity, outDate, reason) InventoryRecord
        +getStockRecords() Array~InventoryRecord~
    }

    class InventoryRecord {
        +uuid id
        +uuid inventoryId
        +string operationType
        +int quantity
        +Date operationDate
        +string reason
        +uuid operatorId
        +uuid projectId
        +Date createdAt
    }

    class Attachment {
        +uuid id
        +string name
        +string path
        +string type
        +int size
        +uuid uploaderId
        +string relatedType
        +uuid relatedId
        +Date createdAt
    }

    %% 考勤管理
    class Attendance {
        +uuid id
        +uuid projectId
        +uuid employeeId
        +string employeeType
        +Date workDate
        +float workHours
        +string workContent
        +float workload
        +uuid recorderId
        +Date createdAt
        +Date updatedAt
    }

    %% 财务管理
    class FinancialRecord {
        +uuid id
        +uuid projectId
        +Date recordDate
        +string voucherType
        +uuid supplierId
        +float taxRate
        +string invoiceType
        +float totalAmount
        +string paymentStatus
        +string remark
        +uuid creatorId
        +Date createdAt
        +Date updatedAt
        +addAttachment(attachmentId) boolean
        +getAttachments() Array~Attachment~
    }

    %% 系统设置
    class SystemSetting {
        +string key
        +string value
        +string description
        +string group
        +Date updatedAt
    }

    class AuditLog {
        +uuid id
        +uuid userId
        +string action
        +string module
        +string description
        +string ipAddress
        +Object oldData
        +Object newData
        +Date createdAt
    }

    %% 关系定义
    User "*" -- "*" Role : has
    Role "*" -- "*" Permission : contains
    User "*" -- "1" Department : belongs to
    User "*" -- "1" Position : has
    
    Project "1" -- "1" Progress : has
    Project "1" -- "*" Task : contains
    Project "1" -- "*" ProjectFollowUp : has
    Project "1" -- "*" Contract : has
    User "1" -- "*" Project : manages
    
    Contract "1" -- "*" PaymentRecord : has
    PaymentRecord "1" -- "*" Invoice : has
    
    Customer "1" -- "*" Contact : has
    Customer "1" -- "*" CustomerFollowUp : has
    Customer "*" -- "1" CustomerCategory : belongs to
    
    Supplier "1" -- "*" SupplierContact : has
    Supplier "1" -- "*" BankAccount : has
    Supplier "*" -- "1" SupplierCategory : belongs to
    
    Project "1" -- "*" PurchaseRequest : has
    Supplier "1" -- "*" PurchaseRequest : supplies
    PurchaseRequest "1" -- "*" PurchaseProduct : contains
    PurchaseRequest "1" -- "*" ReturnRecord : may have
    
    Inventory "1" -- "*" InventoryRecord : has
    
    Project "1" -- "*" Attendance : records
    Project "1" -- "*" FinancialRecord : has
    
    User "1" -- "*" AuditLog : generates