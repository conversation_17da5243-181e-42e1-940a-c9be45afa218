module.exports.authService = require('./auth.service');
module.exports.emailService = require('./email.service');
module.exports.tokenService = require('./token.service');
module.exports.userService = require('./user.service');
module.exports.categoryService = require('./category.service');
module.exports.supplierService = require('./supplier.service');
module.exports.projectService = require('./project.service');
module.exports.documentService = require('./document.service');
module.exports.tagService = require('./tag.service');
module.exports.attachmentService = require('./attachment.service');
module.exports.commentService = require('./comment.service');
module.exports.projectProgressService = require('./projectProgress.service');
module.exports.projectTaskService = require('./projectTask.service');
module.exports.projectFollowUpService = require('./projectFollowUp.service');
module.exports.contractService = require('./contract.service');
module.exports.procurementService = require('./procurement.service');
module.exports.customerService = require('./customer.service');
module.exports.clientService = require('./client.service');
module.exports.productService = require('./product.service');
module.exports.productTransactionService = require('./productTransaction.service');
module.exports.financeService = require('./finance.service');
module.exports.attendanceService = require('./attendance.service');
module.exports.reimbursementService = require('./reimbursement.service');
module.exports.userMessageService = require('./userMessage.service');
module.exports.dashboardService = require('./dashboard.service');