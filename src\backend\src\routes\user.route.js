const express = require('express');
const validate = require('../middlewares/validate.middleware');
const userValidation = require('../validations/user.validation');
const userController = require('../controllers/user.controller');
const auth = require('../middlewares/auth.middleware');
const { simpleAuthenticate } = require('../middlewares/simple-auth.middleware');

const router = express.Router();

// User routes
router
  .route('/')
  .post(auth('manageUsers'), validate(userValidation.createUser), userController.createUser)
  .get(simpleAuthenticate, validate(userValidation.getUsers), userController.getUsers);

router
  .route('/:userId')
  .get(auth('getUsers'), validate(userValidation.getUser), userController.getUser)
  .patch(auth('manageUsers'), validate(userValidation.updateUser), userController.updateUser)
  .delete(auth('manageUsers'), validate(userValidation.deleteUser), userController.deleteUser);

// Mass update route example
router
  .route('/update/batch')
  .post(
    auth('manageUsers'),
    validate(userValidation.updateBatchUsers),
    userController.updateBatchUsers
  );

module.exports = router;