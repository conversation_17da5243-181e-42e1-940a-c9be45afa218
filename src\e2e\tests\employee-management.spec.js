// @ts-check
import { test, expect } from '@playwright/test';

test.describe('员工管理页面测试', () => {
  // 测试登录页面是否正常加载
  test('登录页面测试', async ({ page }) => {
    // 访问登录页面
    await page.goto('/login');

    // 等待页面加载
    await page.waitForTimeout(2000);

    // 验证页面标题包含"登录"字样
    const pageContent = await page.textContent('body');
    expect(pageContent).toContain('登录');

    // 验证登录表单存在
    expect(await page.isVisible('form')).toBeTruthy();
    expect(await page.isVisible('input[type="email"]')).toBeTruthy();
    expect(await page.isVisible('input[type="password"]')).toBeTruthy();
    expect(await page.isVisible('button[type="submit"]')).toBeTruthy();
  });

  // 测试页面标题是否符合中文要求
  test('验证页面标题中文化', async ({ page }) => {
    // 访问登录页面
    await page.goto('/login');

    // 验证登录页面标题是中文
    const loginPageContent = await page.textContent('body');
    expect(loginPageContent).toContain('登录系统');

    // 验证忘记密码链接是中文
    expect(loginPageContent).toContain('忘记密码');
  });
});
