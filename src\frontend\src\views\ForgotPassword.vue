<template>
  <div class="flex items-center justify-center min-h-screen py-12 px-4 sm:px-6 lg:px-8 bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <div class="card w-full max-w-md bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden p-8 transition-colors duration-200">
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">找回密码</h1>
        <p class="mt-2 text-gray-600 dark:text-gray-300">
          请输入您的邮箱地址，我们将向您发送密码重置链接
        </p>
      </div>

      <div v-if="status" :class="[
        'mt-4 p-4 rounded-md flex items-start space-x-3 transition-all duration-200', 
        status.type === 'error' ? 'bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400' : 
                                 'bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400'
      ]">
        <div class="flex-shrink-0 mt-0.5">
          <svg v-if="status.type === 'error'" class="h-5 w-5 text-red-400 dark:text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <svg v-else class="h-5 w-5 text-green-400 dark:text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
        </div>
        <p>{{ status.message }}</p>
      </div>

      <form v-if="!emailSent" @submit.prevent="handleSubmit" class="mt-6 space-y-6">
        <div>
          <label for="email" class="form-label block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">邮箱地址 <span class="text-red-500">*</span></label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
              </svg>
            </div>
            <input
              id="email"
              v-model="email"
              name="email"
              type="email"
              autocomplete="email"
              required
              class="form-input pl-10 w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white transition-colors duration-200"
              :disabled="isLoading"
              placeholder="请输入您的注册邮箱"
              aria-describedby="email-error"
            />
          </div>
          <div v-if="errors.email" id="email-error" class="text-red-500 dark:text-red-400 text-sm mt-1 transition-opacity duration-200">{{ errors.email }}</div>
        </div>

        <div>
          <button
            type="submit"
            class="w-full btn btn-primary flex justify-center py-2.5 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 dark:ring-offset-gray-800"
            :disabled="isLoading"
            aria-live="polite"
          >
            <span v-if="isLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              发送重置链接中...
            </span>
            <span v-else>发送重置链接</span>
          </button>
        </div>

        <div class="text-center">
          <router-link to="/login" class="text-sm text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 flex items-center justify-center transition-colors duration-200 focus:outline-none focus:underline">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            返回登录
          </router-link>
        </div>
      </form>

      <div v-else class="mt-6 space-y-6 animate-fadeIn">
        <div class="text-center">
          <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 dark:bg-green-900/40 mb-4 transition-colors duration-200">
            <svg class="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
          <h2 class="text-xl font-medium text-gray-900 dark:text-white">请查收邮件</h2>
          <p class="mt-2 text-gray-600 dark:text-gray-300">
            我们已向 <strong class="font-medium text-gray-900 dark:text-white">{{ email }}</strong> 发送了密码重置链接
          </p>
          <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-md text-sm transition-colors duration-200">
            <p class="font-medium">提示：</p>
            <ul class="list-disc list-inside mt-1 space-y-1">
              <li>重置链接有效期为30分钟</li>
              <li>如未收到邮件，请检查垃圾邮件文件夹</li>
              <li>确保输入的是您注册时使用的邮箱</li>
            </ul>
          </div>
          <p class="mt-4 text-sm text-gray-500 dark:text-gray-400">
            没有收到邮件？
            <button 
              @click="resendEmail" 
              class="text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300 focus:outline-none focus:underline font-medium transition-colors duration-200"
              :disabled="isLoading"
              aria-live="polite"
            >
              <span v-if="isLoading">正在重新发送...</span>
              <span v-else>点击重新发送</span>
            </button>
          </p>
        </div>
        
        <div class="text-center">
          <router-link to="/login" class="block w-full py-2.5 px-4 border border-indigo-300 dark:border-indigo-500/30 text-sm font-medium rounded-md text-indigo-600 dark:text-indigo-400 bg-white dark:bg-gray-800 hover:bg-indigo-50 dark:hover:bg-indigo-900/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:ring-offset-gray-800 transition-colors duration-200">
            <div class="flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              返回登录页面
            </div>
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useAuthStore } from '../stores/auth';
import { useThemeStore } from '../stores/theme';

// Component state
const email = ref('');
const errors = reactive({});
const status = ref(null);
const isLoading = ref(false);
const emailSent = ref(false);

// Stores
const authStore = useAuthStore();
const themeStore = useThemeStore(); // 用于暗黑模式支持

// 首次加载页面时的焦点管理
onMounted(() => {
  document.getElementById('email')?.focus();
});

// Email validation with more robust regex
const isValidEmail = (email) => {
  // 更严格的邮箱验证规则
  const regex = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return regex.test(String(email).toLowerCase());
};

// Form validation
const validateForm = () => {
  errors.email = '';
  
  if (!email.value.trim()) {
    errors.email = '请输入邮箱地址';
    return false;
  }
  
  if (!isValidEmail(email.value)) {
    errors.email = '请输入有效的邮箱地址';
    return false;
  }
  
  return true;
};

// Clear status after a delay
const clearStatusAfterDelay = (delay = 5000) => {
  if (status.value?.type === 'success') {
    setTimeout(() => {
      status.value = null;
    }, delay);
  }
};

// Submit handler with debounce protection
let isSubmitting = false;
const handleSubmit = async () => {
  // 防止重复提交
  if (isSubmitting) return;
  isSubmitting = true;
  
  if (!validateForm()) {
    isSubmitting = false;
    return;
  }
  
  isLoading.value = true;
  status.value = null;
  
  try {
    await authStore.requestPasswordReset(email.value);
    emailSent.value = true;
  } catch (error) {
    let errorMessage = '发送重置链接失败，请稍后再试。';
    
    // 根据错误类型提供更具体的错误信息
    if (error.response) {
      switch (error.response.status) {
        case 404:
          errorMessage = '此邮箱地址未注册。';
          break;
        case 429:
          errorMessage = '请求过于频繁，请稍后再试。';
          break;
        default:
          errorMessage = error.response.data?.message || errorMessage;
      }
    }
    
    status.value = {
      type: 'error',
      message: errorMessage
    };
  } finally {
    isLoading.value = false;
    isSubmitting = false;
  }
};

// Resend email with debounce protection
let isResending = false;
const resendEmail = async () => {
  // 防止重复点击
  if (isResending) return;
  isResending = true;
  
  isLoading.value = true;
  status.value = null;
  
  try {
    await authStore.requestPasswordReset(email.value);
    status.value = {
      type: 'success',
      message: '重置链接已重新发送，请查收邮件。'
    };
    clearStatusAfterDelay();
  } catch (error) {
    let errorMessage = '重新发送重置链接失败，请稍后再试。';
    
    // 根据错误类型提供更具体的错误信息
    if (error.response) {
      switch (error.response.status) {
        case 429:
          errorMessage = '请求过于频繁，请稍后再试。';
          break;
        default:
          errorMessage = error.response.data?.message || errorMessage;
      }
    }
    
    status.value = {
      type: 'error',
      message: errorMessage
    };
  } finally {
    isLoading.value = false;
    isResending = false;
  }
};
</script>

<style scoped>
/* 添加一些过渡动画 */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 改进按钮悬停效果 */
.btn-primary {
  position: relative;
  overflow: hidden;
}

.btn-primary::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.btn-primary:hover::after {
  width: 250%;
  height: 500%;
}
</style>