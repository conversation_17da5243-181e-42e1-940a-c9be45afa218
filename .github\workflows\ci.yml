name: CI/CD Pipeline

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:
    inputs:
      deploy_environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

jobs:
  backend-tests:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: src/backend/package-lock.json

    - name: Install backend dependencies
      run: |
        cd src/backend
        npm ci

    - name: Set up environment variables
      run: |
        cd src/backend
        echo "DB_HOST=localhost" >> .env
        echo "DB_PORT=5432" >> .env
        echo "DB_USER=postgres" >> .env
        echo "DB_PASSWORD=postgres" >> .env
        echo "DB_NAME=test_db" >> .env
        echo "JWT_SECRET=test-jwt-secret" >> .env
        echo "NODE_ENV=test" >> .env

    - name: Run backend unit tests with coverage
      run: |
        cd src/backend
        npm run test:unit:coverage

    - name: Run backend integration tests
      run: |
        cd src/backend
        npm run test:integration

    - name: Upload backend test coverage
      uses: actions/upload-artifact@v3
      with:
        name: backend-coverage
        path: src/backend/coverage/
        retention-days: 7

  frontend-tests:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: src/frontend/package-lock.json

    - name: Install frontend dependencies
      run: |
        cd src/frontend
        npm ci

    - name: Run frontend unit tests with coverage
      run: |
        cd src/frontend
        npm test -- --coverage

    - name: Upload frontend test coverage
      uses: actions/upload-artifact@v3
      with:
        name: frontend-coverage
        path: src/frontend/coverage/
        retention-days: 7

    - name: Install Playwright browsers
      run: |
        cd src/frontend
        npx playwright install --with-deps chromium

    - name: Run frontend E2E tests
      run: |
        cd src/frontend
        npx playwright test

  lint:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install ESLint
      run: npm install -g eslint

    - name: Lint backend code
      run: |
        cd src/backend
        npm ci
        npm run lint || true

    - name: Lint frontend code
      run: |
        cd src/frontend
        npm ci
        npm run lint || true

  security-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        ports:
          - 5432:5432
        options: >
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: src/backend/package-lock.json

    - name: Install backend dependencies
      run: |
        cd src/backend
        npm ci

    - name: Set up environment variables
      run: |
        cd src/backend
        echo "DB_HOST=localhost" >> .env
        echo "DB_PORT=5432" >> .env
        echo "DB_USER=postgres" >> .env
        echo "DB_PASSWORD=postgres" >> .env
        echo "DB_NAME=test_db" >> .env
        echo "JWT_SECRET=test-jwt-secret" >> .env
        echo "NODE_ENV=test" >> .env

    - name: Run security tests
      run: |
        cd src/backend
        npm run test:security

    - name: Run OWASP ZAP scan
      uses: zaproxy/action-baseline@v0.7.0
      with:
        target: 'http://localhost:3009'

  performance-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]

    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        ports:
          - 5432:5432
        options: >
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: src/backend/package-lock.json

    - name: Install backend dependencies
      run: |
        cd src/backend
        npm ci

    - name: Set up environment variables
      run: |
        cd src/backend
        echo "DB_HOST=localhost" >> .env
        echo "DB_PORT=5432" >> .env
        echo "DB_USER=postgres" >> .env
        echo "DB_PASSWORD=postgres" >> .env
        echo "DB_NAME=test_db" >> .env
        echo "JWT_SECRET=test-jwt-secret" >> .env
        echo "NODE_ENV=test" >> .env

    - name: Run performance tests
      run: |
        cd src/backend
        npm run test:performance

  build:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests, lint, security-tests, performance-tests]
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Build backend
      run: |
        cd src/backend
        npm ci
        npm run build --if-present

    - name: Build frontend
      run: |
        cd src/frontend
        npm ci
        npm run build

    - name: Upload backend build
      uses: actions/upload-artifact@v3
      with:
        name: backend-build
        path: src/backend/dist/
        retention-days: 1

    - name: Upload frontend build
      uses: actions/upload-artifact@v3
      with:
        name: frontend-build
        path: src/frontend/dist/
        retention-days: 1

  deploy-staging:
    runs-on: ubuntu-latest
    needs: [build]
    if: (github.event_name == 'push' && github.ref == 'refs/heads/develop') || (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy_environment == 'staging')
    environment: staging

    steps:
    - uses: actions/checkout@v3

    - name: Download backend build
      uses: actions/download-artifact@v3
      with:
        name: backend-build
        path: backend-dist

    - name: Download frontend build
      uses: actions/download-artifact@v3
      with:
        name: frontend-build
        path: frontend-dist

    - name: Deploy to staging server
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.STAGING_HOST }}
        username: ${{ secrets.STAGING_USERNAME }}
        key: ${{ secrets.STAGING_SSH_KEY }}
        script: |
          # Create deployment directory
          mkdir -p ~/app/deploy-$(date +%Y%m%d%H%M%S)
          cd ~/app/deploy-$(date +%Y%m%d%H%M%S)

          # Copy build files
          mkdir -p backend frontend

          # Stop existing services
          pm2 stop all || true

          # Start new services
          cd backend
          npm install --production
          pm2 start index.js --name backend

          cd ../frontend
          # Serve frontend with nginx or similar

          # Update symlink to current deployment
          cd ~/app
          ln -sfn deploy-$(date +%Y%m%d%H%M%S) current

          # Cleanup old deployments (keep last 5)
          ls -dt deploy-* | tail -n +6 | xargs rm -rf

          echo "Deployment completed successfully"

  deploy-production:
    runs-on: ubuntu-latest
    needs: [build]
    if: (github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')) || (github.event_name == 'workflow_dispatch' && github.event.inputs.deploy_environment == 'production')
    environment: production

    steps:
    - uses: actions/checkout@v3

    - name: Download backend build
      uses: actions/download-artifact@v3
      with:
        name: backend-build
        path: backend-dist

    - name: Download frontend build
      uses: actions/download-artifact@v3
      with:
        name: frontend-build
        path: frontend-dist

    - name: Deploy to production server
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.PRODUCTION_HOST }}
        username: ${{ secrets.PRODUCTION_USERNAME }}
        key: ${{ secrets.PRODUCTION_SSH_KEY }}
        script: |
          # Create deployment directory
          mkdir -p ~/app/deploy-$(date +%Y%m%d%H%M%S)
          cd ~/app/deploy-$(date +%Y%m%d%H%M%S)

          # Copy build files
          mkdir -p backend frontend

          # Stop existing services
          pm2 stop all || true

          # Start new services
          cd backend
          npm install --production
          pm2 start index.js --name backend

          cd ../frontend
          # Serve frontend with nginx or similar

          # Update symlink to current deployment
          cd ~/app
          ln -sfn deploy-$(date +%Y%m%d%H%M%S) current

          # Cleanup old deployments (keep last 5)
          ls -dt deploy-* | tail -n +6 | xargs rm -rf

          echo "Deployment completed successfully"
