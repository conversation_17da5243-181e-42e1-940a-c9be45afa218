<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold mb-6">设置</h1>

    <div v-if="status" :class="['mb-4 p-3 rounded-md',
      status.type === 'error' ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700']">
      {{ status.message }}
    </div>

    <div class="bg-white rounded-lg shadow p-6">
      <form @submit.prevent="saveSettings" class="space-y-6">
        <!-- 常规设置 -->
        <div>
          <h2 class="text-lg font-semibold mb-4">常规设置</h2>
          <div class="space-y-4">
            <div>
              <label for="language" class="block text-sm font-medium text-gray-700">语言</label>
              <select
                id="language"
                v-model="settings.language"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                :disabled="isLoading"
              >
                <option value="en">英文</option>
                <option value="zh">中文</option>
              </select>
            </div>
            <div>
              <label for="theme" class="block text-sm font-medium text-gray-700">主题</label>
              <select
                id="theme"
                v-model="settings.theme"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                :disabled="isLoading"
              >
                <option value="light">浅色</option>
                <option value="dark">深色</option>
                <option value="system">跟随系统</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 通知设置 -->
        <div>
          <h2 class="text-lg font-semibold mb-4">通知设置</h2>
          <div class="space-y-4">
            <div class="flex items-center">
              <input
                id="emailNotifications"
                type="checkbox"
                v-model="settings.emailNotifications"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                :disabled="isLoading"
              >
              <label for="emailNotifications" class="ml-2 block text-sm text-gray-900">邮件通知</label>
            </div>
            <div class="flex items-center">
              <input
                id="pushNotifications"
                type="checkbox"
                v-model="settings.pushNotifications"
                class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                :disabled="isLoading"
              >
              <label for="pushNotifications" class="ml-2 block text-sm text-gray-900">推送通知</label>
            </div>
          </div>
        </div>

        <!-- 保存按钮 -->
        <div class="flex justify-end">
          <button
            type="submit"
            class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
            :disabled="isLoading"
          >
            <span v-if="isLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              保存中...
            </span>
            <span v-else>保存更改</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import { useAuthStore } from '../stores/auth';

const authStore = useAuthStore();
const isLoading = ref(false);
const status = ref(null);

// Settings form data
const settings = ref({
  language: 'zh',
  theme: 'light',
  emailNotifications: true,
  pushNotifications: false
});

// Load user settings
onMounted(async () => {
  try {
    isLoading.value = true;
    const response = await axios.get('/api/users/me/settings');
    settings.value = {
      ...settings.value,
      ...response.data
    };
  } catch (error) {
    console.error('Failed to load settings:', error);
    status.value = {
      type: 'error',
      message: '加载设置失败'
    };
  } finally {
    isLoading.value = false;
  }
});

// Save settings
const saveSettings = async () => {
  try {
    isLoading.value = true;
    status.value = null;

    await axios.put('/api/users/me/settings', settings.value);

    status.value = {
      type: 'success',
      message: '设置已保存'
    };
  } catch (error) {
    console.error('Failed to save settings:', error);
    status.value = {
      type: 'error',
      message: '保存设置失败'
    };
  } finally {
    isLoading.value = false;
  }
};
</script>