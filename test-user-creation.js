// 测试用户创建和登录
async function testUserCreationAndLogin() {
  const API_BASE_URL = process.env.API_URL || 'http://localhost:3001';
  
  // 测试用户数据
  const testUser = {
    username: 'testuser' + Date.now(),
    email: 'test' + Date.now() + '@example.com',
    password: 'password123',
    lastName: '测试用户',
    role: 'user'
  };

  try {
    console.log('🔄 开始测试用户创建和登录...');
    
    // 1. 创建用户
    console.log('📝 创建用户:', testUser.username);
    const createResponse = await fetch(`${API_BASE_URL}/api/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testUser)
    });
    
    if (!createResponse.ok) {
      const errorData = await createResponse.text();
      throw new Error(`创建用户失败: ${createResponse.status} - ${errorData}`);
    }
    
    const createData = await createResponse.json();
    console.log('✅ 用户创建成功:', createData);
    
    // 2. 尝试登录
    console.log('🔐 尝试登录...');
    const loginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testUser.email,
        password: testUser.password
      })
    });
    
    if (!loginResponse.ok) {
      const errorData = await loginResponse.text();
      throw new Error(`登录失败: ${loginResponse.status} - ${errorData}`);
    }
    
    const loginData = await loginResponse.json();
    console.log('✅ 登录成功!');
    console.log('🎯 Token:', loginData.tokens?.access?.token ? '已获取' : '未获取');
    console.log('👤 用户信息:', {
      id: loginData.user?.id,
      username: loginData.user?.username,
      email: loginData.user?.email
    });
    
    return true;
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 运行测试
testUserCreationAndLogin()
  .then(success => {
    if (success) {
      console.log('\n🎉 所有测试通过！用户创建和登录功能正常。');
    } else {
      console.log('\n💥 测试失败，需要检查问题。');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('💥 测试执行出错:', error);
    process.exit(1);
  }); 