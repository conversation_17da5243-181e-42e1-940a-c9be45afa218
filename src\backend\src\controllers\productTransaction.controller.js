const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const catchAsync = require('../utils/catchAsync');
const { productTransactionService } = require('../services');

/**
 * Create a new product transaction
 * @route POST /api/product-transactions
 */
const createProductTransaction = catchAsync(async (req, res) => {
  const transaction = await productTransactionService.createProductTransaction({
    ...req.body,
    createdBy: req.user.id
  });
  res.status(201).send(transaction);
});

/**
 * Create a stock in transaction (入库操作)
 * @route POST /api/product-transactions/in
 */
const createStockInTransaction = catchAsync(async (req, res) => {
  const transaction = await productTransactionService.createProductTransaction({
    ...req.body,
    type: 'in',
    createdBy: req.user.id
  });
  res.status(201).send(transaction);
});

/**
 * Create a stock out transaction (出库操作)
 * @route POST /api/product-transactions/out
 */
const createStockOutTransaction = catchAsync(async (req, res) => {
  const transaction = await productTransactionService.createProductTransaction({
    ...req.body,
    type: 'out',
    createdBy: req.user.id
  });
  res.status(201).send(transaction);
});

/**
 * Get a product transaction by ID
 * @route GET /api/product-transactions/:transactionId
 */
const getProductTransaction = catchAsync(async (req, res) => {
  const transaction = await productTransactionService.getProductTransactionById(req.params.transactionId);
  res.send(transaction);
});

/**
 * Get all product transactions with filtering
 * @route GET /api/product-transactions
 */
const getProductTransactions = catchAsync(async (req, res) => {
  const filter = req.query;
  const options = {
    sortBy: req.query.sortBy || 'transactionDate',
    limit: parseInt(req.query.limit, 10) || 10,
    page: parseInt(req.query.page, 10) || 1,
    sortOrder: req.query.sortOrder || 'desc',
  };
  const result = await productTransactionService.queryProductTransactions(filter, options);
  res.send(result);
});

/**
 * Get all transactions for a product
 * @route GET /api/products/:productId/transactions
 */
const getProductTransactionsForProduct = catchAsync(async (req, res) => {
  const filter = {
    ...req.query,
    productId: req.params.productId,
  };
  const options = {
    sortBy: req.query.sortBy || 'transactionDate',
    limit: parseInt(req.query.limit, 10) || 10,
    page: parseInt(req.query.page, 10) || 1,
    sortOrder: req.query.sortOrder || 'desc',
  };
  const result = await productTransactionService.queryProductTransactions(filter, options);
  res.send(result);
});

/**
 * Get all stock in transactions (入库记录)
 * @route GET /api/product-transactions/in
 */
const getStockInTransactions = catchAsync(async (req, res) => {
  const filter = {
    ...req.query,
    type: 'in',
  };
  const options = {
    sortBy: req.query.sortBy || 'transactionDate',
    limit: parseInt(req.query.limit, 10) || 10,
    page: parseInt(req.query.page, 10) || 1,
    sortOrder: req.query.sortOrder || 'desc',
  };
  const result = await productTransactionService.queryProductTransactions(filter, options);
  res.send(result);
});

/**
 * Get all stock out transactions (出库记录)
 * @route GET /api/product-transactions/out
 */
const getStockOutTransactions = catchAsync(async (req, res) => {
  const filter = {
    ...req.query,
    type: 'out',
  };
  const options = {
    sortBy: req.query.sortBy || 'transactionDate',
    limit: parseInt(req.query.limit, 10) || 10,
    page: parseInt(req.query.page, 10) || 1,
    sortOrder: req.query.sortOrder || 'desc',
  };
  const result = await productTransactionService.queryProductTransactions(filter, options);
  res.send(result);
});

// New inventory management methods
const createStockAdjustment = catchAsync(async (req, res) => {
  const adjustment = await productTransactionService.createProductTransaction({
    ...req.body,
    type: 'adjustment',
    createdBy: req.user.id,
  });
  res.status(201).send(adjustment);
});

const getStockAdjustments = catchAsync(async (req, res) => {
  const filter = {
    ...req.query,
    type: 'adjustment',
  };
  const options = {
    sortBy: req.query.sortBy || 'transactionDate',
    limit: parseInt(req.query.limit, 10) || 10,
    page: parseInt(req.query.page, 10) || 1,
    sortOrder: req.query.sortOrder || 'desc',
  };
  const result = await productTransactionService.queryProductTransactions(filter, options);
  res.send(result);
});

const getStockMovements = catchAsync(async (req, res) => {
  const filter = req.query;
  const options = {
    sortBy: req.query.sortBy || 'transactionDate',
    limit: parseInt(req.query.limit, 10) || 10,
    page: parseInt(req.query.page, 10) || 1,
    sortOrder: req.query.sortOrder || 'desc',
  };
  const result = await productTransactionService.queryProductTransactions(filter, options);
  res.send(result);
});

module.exports = {
  createProductTransaction,
  createStockInTransaction,
  createStockOutTransaction,
  getProductTransaction,
  getProductTransactions,
  getProductTransactionsForProduct,
  getStockInTransactions,
  getStockOutTransactions,
  createStockAdjustment,
  getStockAdjustments,
  getStockMovements,
}; 