const bcrypt = require('bcryptjs');
const { User } = require('./src/models');

async function testLogin() {
  try {
    // 查找用户
    const user = await User.findOne({ where: { email: '<EMAIL>' } });
    if (!user) {
      console.error('User not found');
      return;
    }

    console.log('User found:', {
      id: user.id,
      email: user.email,
      username: user.username,
      role: user.role,
      passwordHash: user.password
    });

    // 测试密码
    const testPasswords = ['password123', 'password123', 'admin', 'admin123'];
    
    for (const testPassword of testPasswords) {
      const isMatch = await bcrypt.compare(testPassword, user.password);
      console.log(`Password "${testPassword}" matches: ${isMatch}`);
    }

    // 使用模型的验证方法
    for (const testPassword of testPasswords) {
      const isValid = await user.validatePassword(testPassword);
      console.log(`Using model method - Password "${testPassword}" is valid: ${isValid}`);
    }
  } catch (error) {
    console.error('Error testing login:', error);
  }
}

testLogin();
