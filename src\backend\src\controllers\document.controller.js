const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { documentService } = require('../services');
const pick = require('../utils/pick');

/**
 * Create a document
 * @route POST /api/documents
 */
const createDocument = catchAsync(async (req, res) => {
  // Add the current user as the author
  const documentData = {
    ...req.body,
    authorId: req.user.id,
  };

  const document = await documentService.createDocument(documentData);
  res.status(201).send(document);
});

/**
 * Get all documents
 * @route GET /api/documents
 */
const getDocuments = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['title', 'categoryId', 'authorId']);

  // Remove empty strings from filter
  Object.keys(filter).forEach(key => {
    if (filter[key] === '') {
      delete filter[key];
    }
  });

  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const result = await documentService.queryDocuments(filter, options);
  res.send(result);
});

/**
 * Get document by id
 * @route GET /api/documents/:id
 */
const getDocument = catchAsync(async (req, res) => {
  const document = await documentService.getDocumentById(req.params.documentId);
  if (!document) {
    return res.status(404).send({ message: 'Document not found' });
  }
  res.send(document);
});

/**
 * Update document
 * @route PATCH /api/documents/:id
 */
const updateDocument = catchAsync(async (req, res) => {
  const document = await documentService.updateDocumentById(req.params.documentId, req.body);
  res.send(document);
});

/**
 * Delete document
 * @route DELETE /api/documents/:id
 */
const deleteDocument = catchAsync(async (req, res) => {
  await documentService.deleteDocumentById(req.params.documentId);
  // 使用明确的数字状态码，避免可能的undefined问题
  res.status(204).send();
});

/**
 * Add tags to document
 * @route POST /api/documents/:id/tags
 */
const addTags = catchAsync(async (req, res) => {
  const document = await documentService.addTagsToDocument(
    req.params.documentId,
    req.body.tagIds
  );
  res.send(document);
});

/**
 * Remove tags from document
 * @route DELETE /api/documents/:id/tags
 */
const removeTags = catchAsync(async (req, res) => {
  const document = await documentService.removeTagsFromDocument(
    req.params.documentId,
    req.body.tagIds
  );
  res.send(document);
});

module.exports = {
  createDocument,
  getDocuments,
  getDocument,
  updateDocument,
  deleteDocument,
  addTags,
  removeTags,
};