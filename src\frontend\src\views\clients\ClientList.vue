<template>
  <div class="max-w-6xl mx-auto px-4 py-6">
    <!-- 页面标题和新建按钮 -->
    <div class="page-header">
      <div>
        <h1 class="page-title">客户管理</h1>
        <p class="page-subtitle">管理和维护客户关系信息</p>
      </div>
      <router-link to="/clients/create" class="btn btn-primary flex items-center shadow-md hover:shadow-lg transition-all duration-200">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        新建客户
      </router-link>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-card">
      <div class="flex justify-between gap-4">
        <div class="flex-1">
          <label for="search" class="form-label font-medium">搜索客户</label>
          <div class="relative">
            <input
              id="search"
              v-model="searchQuery"
              type="text"
              placeholder="搜索客户名称、联系人或电话..."
              class="form-input pl-10 py-2 mt-2 w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200"
            />
            <svg class="w-5 h-5 text-gray-400 absolute left-3 top-[50%] transform -translate-y-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </div>
        <div class="flex-1">
          <label for="type-filter" class="form-label font-medium">客户类型</label>
          <select id="type-filter" v-model="typeFilter" class="form-input mt-2 w-full py-2 rounded-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200">
            <option value="">全部类型</option>
            <option value="company">企业客户</option>
            <option value="individual">个人客户</option>
            <option value="government">政府单位</option>
          </select>
        </div>
      </div>
      <div class="flex justify-end mt-6">
        <button @click="resetFilters" class="btn btn-secondary mr-3 shadow-sm hover:shadow transition-all duration-200">重置</button>
        <button @click="fetchClients" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200">查询</button>
      </div>
    </div>

    <!-- 客户列表 -->
    <div v-if="loading" class="text-center py-12">
      <div class="inline-block animate-spin rounded-full h-10 w-10 border-4 border-gray-300 border-t-blue-600"></div>
      <p class="mt-4 text-gray-600 font-medium">加载客户中...</p>
    </div>

    <div v-else-if="clients.length === 0" class="card text-center py-16 shadow-md">
      <svg class="w-20 h-20 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
      </svg>
      <h3 class="mt-6 text-xl font-medium text-gray-900">暂无客户</h3>
      <p class="mt-3 text-gray-600 max-w-md mx-auto">
        {{ searchQuery || typeFilter || statusFilter ? '尝试调整搜索条件或筛选条件' : '点击新建客户按钮创建您的第一个客户' }}
      </p>
      <div class="mt-8" v-if="!searchQuery && !typeFilter && !statusFilter">
        <router-link to="/clients/create" class="btn btn-primary shadow-md hover:shadow-lg transition-all duration-200">
          创建第一个客户
        </router-link>
      </div>
    </div>

    <div v-else>
      <!-- 客户表格 -->
      <div class="card overflow-hidden shadow-md">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户名称</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系人</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">联系方式</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">类型</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="client in clients" :key="client.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center">
                      {{ getClientInitials(client.name) }}
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900 hover:text-blue-600">
                        <router-link :to="`/clients/${client.id}`">{{ client.name }}</router-link>
                      </div>
                      <div class="text-xs text-gray-500">{{ client.code }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ client.contactName }}</div>
                  <div class="text-xs text-gray-500">{{ client.contactTitle }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ client.phone }}</div>
                  <div class="text-xs text-gray-500">{{ client.email }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="['badge', getTypeClass(client.type)]">
                    {{ getTypeText(client.type) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="['badge', getStatusClass(client.status)]">
                    {{ getStatusText(client.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">                  <router-link
                    :to="`/clients/${client.id}`"
                    class="inline-flex items-center px-3 py-1 rounded-lg shadow transition-all duration-200 bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-800 mr-3 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-blue-600 group-hover:text-blue-800 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536M9 13l6.071-6.071a2 2 0 112.828 2.828L11.828 15.828a4 4 0 01-1.414.943l-3.39 1.13a.5.5 0 01-.632-.632l1.13-3.39a4 4 0 01.943-1.414z" />
                    </svg>
                    <span class="font-medium">编辑</span>
                  </router-link>
                  <button
                    @click="confirmDelete(client)"
                    class="inline-flex items-center px-3 py-1.5 rounded-lg shadow bg-red-500 text-white hover:bg-red-600 hover:shadow-lg transition-all duration-200 group"
                  >
                    <svg class="w-4 h-4 mr-1.5 text-white group-hover:text-red-200 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6M1 7h22M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2" />
                    </svg>
                    <span class="font-medium">删除</span>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 分页 -->
      <div class="mt-8 flex justify-between items-center px-2">
        <div class="text-sm text-gray-500">
          显示第 {{ (currentPage - 1) * pageSize + 1 }} - {{ Math.min(currentPage * pageSize, totalClients) }} 个客户，共 {{ totalClients }} 个
        </div>
        <div class="flex items-center space-x-3">
          <button
            @click="prevPage"
            :disabled="currentPage === 1"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm"
            :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          <span class="text-sm font-medium text-gray-700">第 {{ currentPage }} 页</span>
          <button
            @click="nextPage"
            :disabled="currentPage * pageSize >= totalClients"
            class="btn btn-secondary py-2 px-3 rounded-lg flex items-center shadow-sm"
            :class="{'opacity-50 cursor-not-allowed': currentPage * pageSize >= totalClients}"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <div v-if="showDeleteModal" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50">
      <div class="bg-white rounded-lg shadow-2xl max-w-md w-full p-6 transform transition-all">
        <div class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">确认删除</h3>
          <p class="text-gray-500 mb-6">
            确定要删除客户 {{ selectedClient?.name }} 吗？此操作不可撤销。
          </p>

          <div class="flex justify-center space-x-4">
            <button
              type="button"
              @click="showDeleteModal = false"
              class="btn btn-secondary"
            >
              取消
            </button>
            <button
              type="button"
              @click="deleteClient"
              class="btn btn-danger shadow-sm hover:shadow-md transition-all duration-200"
              :disabled="isDeleting"
            >
              <span v-if="isDeleting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                删除中...
              </span>
              <span v-else>确认删除</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import apiService from '@/services/apiService';

// 路由
const router = useRouter();
const route = useRoute();

// 状态变量
const loading = ref(false);
const clients = ref([]);
const totalClients = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchQuery = ref('');
const typeFilter = ref('');
const statusFilter = ref('');
const showDeleteModal = ref(false);
const isDeleting = ref(false);
const selectedClient = ref(null);

// 获取客户列表
const fetchClients = async () => {
  loading.value = true;

  try {
    // 从 API 获取数据
    const params = {
      page: currentPage.value,
      limit: pageSize.value,
      search: searchQuery.value || undefined,
      type: typeFilter.value || undefined,
      status: statusFilter.value || undefined
    };

    const response = await apiService.getClients(params);

    // 检查响应数据
    if (response && Array.isArray(response.results)) {
      clients.value = response.results;
      totalClients.value = response.totalResults || 0;
    } else {
      console.warn('获取到的客户数据格式不正确:', response);
      clients.value = [];
      totalClients.value = 0;
    }
  } catch (err) {
    console.error('获取客户列表失败:', err);
    // 显示错误提示
    if (window.$message) {
      window.$message.error('获取客户列表失败: ' + (err.response?.data?.message || err.message || '请重试'));
    }
    clients.value = [];
    totalClients.value = 0;
  } finally {
    loading.value = false;
  }
};

// 重置过滤器
const resetFilters = () => {
  searchQuery.value = '';
  typeFilter.value = '';
  fetchClients();
};

// 监听路由和查询参数变化
watch(
  () => [route.path, route.query],
  ([newPath, newQuery]) => {
    // 如果是从客户创建页面返回，并且带有 refresh 参数
    if (newPath === '/clients' && newQuery.refresh) {
      currentPage.value = 1; // 返回第一页
      fetchClients();
    } else {
      fetchClients();
    }
  },
  { immediate: true }
);

// 分页处理
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchClients();
  }
};

const nextPage = () => {
  if (currentPage.value * pageSize.value < totalClients.value) {
    currentPage.value++;
    fetchClients();
  }
};

// 获取客户类型样式和文本
const getTypeClass = (type) => {
  switch (type) {
    case 'company': return 'badge-blue';
    case 'individual': return 'badge-green';
    case 'government': return 'badge-purple';
    default: return 'badge-gray';
  }
};

const getTypeText = (type) => {
  switch (type) {
    case 'company': return '企业客户';
    case 'individual': return '个人客户';
    case 'government': return '政府单位';
    default: return '未知';
  }
};

// 获取客户状态样式和文本
const getStatusClass = (status) => {
  switch (status) {
    case 'active': return 'badge-green';
    case 'inactive': return 'badge-gray';
    case 'potential': return 'badge-yellow';
    default: return 'badge-gray';
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 'active': return '活跃';
    case 'inactive': return '非活跃';
    case 'potential': return '潜在';
    default: return '未知';
  }
};

// 获取客户名称首字母
const getClientInitials = (name) => {
  if (!name) return '?';

  const names = name.split(' ');
  if (names.length === 1) {
    return name.charAt(0).toUpperCase();
  }
  return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
};

// 确认删除客户
function confirmDelete(client) {
  selectedClient.value = client;
  showDeleteModal.value = true;
}

// 删除客户
async function deleteClient() {
  if (!selectedClient.value) return;

  isDeleting.value = true;
  try {
    await apiService.deleteClient(selectedClient.value.id);

    // 显示成功消息
    if (window.$message) {
      window.$message.success('删除成功');
    }

    // 关闭删除弹窗
    showDeleteModal.value = false;
    selectedClient.value = null;

    // 强制刷新页面
    setTimeout(() => {
      window.location.reload();
    }, 500); // 给提示消息一点显示时间
  } catch (err) {
    console.error('删除客户失败:', err);
    if (window.$message) {
      window.$message.error(err.response?.data?.message || '删除客户失败，请重试');
    }
  } finally {
    isDeleting.value = false;
  }
}

// 初始加载
onMounted(() => {
  fetchClients();
});
</script>

<style scoped>
/* Page transitions */
.page-header, .filter-card, .card {
  animation: fadeSlideIn 0.6s ease-out;
}

.page-header {
  animation-delay: 0s;
}

.filter-card {
  animation-delay: 0.1s;
}

.card {
  animation-delay: 0.2s;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Table row hover animation */
tbody tr {
  transition: all 0.2s ease;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-gray-500;
}

.btn {
  @apply px-4 py-2 rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
}
</style>