const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const pick = require('../utils/pick');
const employeeService = require('../services/employee.service');
const ApiError = require('../utils/ApiError');

const createContractWorker = catchAsync(async (req, res) => {
  const worker = await employeeService.createContractWorker(req.body);
  res.status(201).send(worker);
});

const getContractWorkers = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['name', 'status', 'department', 'position', 'month', 'search']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const result = await employeeService.queryContractWorkers(filter, options);
  res.send(result);
});

const getContractWorker = catchAsync(async (req, res) => {
  const worker = await employeeService.getContractWorkerById(req.params.workerId);
  if (!worker) {
    throw new ApiError(404, 'Contract worker not found');
  }
  res.send(worker);
});

const updateContractWorker = catchAsync(async (req, res) => {
  const worker = await employeeService.updateContractWorkerById(req.params.workerId, req.body);
  res.send(worker);
});

const deleteContractWorker = catchAsync(async (req, res) => {
  await employeeService.deleteContractWorkerById(req.params.workerId);
  res.status(204).send();
});

const createTemporaryWorker = catchAsync(async (req, res) => {
  const worker = await employeeService.createTemporaryWorker(req.body);
  res.status(201).send(worker);
});

const getTemporaryWorkers = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['name', 'status', 'department', 'position', 'projectId', 'search']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const result = await employeeService.queryTemporaryWorkers(filter, options);
  res.send(result);
});

const getTemporaryWorker = catchAsync(async (req, res) => {
  const worker = await employeeService.getTemporaryWorkerById(req.params.workerId);
  if (!worker) {
    throw new ApiError(404, 'Temporary worker not found');
  }
  res.send(worker);
});

const updateTemporaryWorker = catchAsync(async (req, res) => {
  const worker = await employeeService.updateTemporaryWorkerById(req.params.workerId, req.body);
  res.send(worker);
});

const deleteTemporaryWorker = catchAsync(async (req, res) => {
  await employeeService.deleteTemporaryWorkerById(req.params.workerId);
  res.status(204).send();
});

/**
 * Get all employees (both contract and temporary workers)
 * @route GET /api/employees
 */
const getAllEmployees = catchAsync(async (req, res) => {
  const filter = pick(req.query, ['search', 'employeeType', 'department', 'status', 'position']);
  const options = pick(req.query, ['sortBy', 'limit', 'page']);
  const result = await employeeService.queryAllEmployees(filter, options);
  res.send(result);
});

module.exports = {
  createContractWorker,
  getContractWorkers,
  getContractWorker,
  updateContractWorker,
  deleteContractWorker,
  createTemporaryWorker,
  getTemporaryWorkers,
  getTemporaryWorker,
  updateTemporaryWorker,
  deleteTemporaryWorker,
  getAllEmployees,
};