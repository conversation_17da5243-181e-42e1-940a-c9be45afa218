const httpStatus = require('http-status');
const { Document, Tag, Attachment, User, Category } = require('../models');
const ApiError = require('../utils/ApiError');

/**
 * Create a document
 * @param {Object} documentBody
 * @returns {Promise<Document>}
 */
const createDocument = async (documentBody) => {
  const document = await Document.create(documentBody);

  // Associate tags if they are provided
  if (documentBody.tags && documentBody.tags.length > 0) {
    await document.setTags(documentBody.tags);
  }

  return document;
};

/**
 * Query for documents
 * @param {Object} filter - Sequelize filter
 * @param {Object} options - Query options
 * @param {string} [options.sortBy] - Sort option in the format: sortField:(asc|desc)
 * @param {number} [options.limit] - Maximum number of results per page
 * @param {number} [options.page] - Current page
 * @returns {Promise<Object>}
 */
const queryDocuments = async (filter, options) => {
  const { limit, offset } = options;

  const documents = await Document.findAndCountAll({
    where: filter,
    limit,
    offset,
    order: options.sortBy ? [[options.sortBy.split(':')[0], options.sortBy.split(':')[1]]] : [['updatedAt', 'DESC']],
    include: [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'username', 'email'],
      },
      {
        model: Category,
        as: 'category',
      },
      {
        model: Tag,
        through: { attributes: [] }, // Don't include junction table
      },
      {
        model: Attachment,
      },
    ],
  });

  return {
    results: documents.rows,
    page: options.page,
    limit: options.limit,
    totalPages: Math.ceil(documents.count / options.limit),
    totalResults: documents.count,
  };
};

/**
 * Get document by id
 * @param {string} id
 * @returns {Promise<Document>}
 */
const getDocumentById = async (id) => {
  return Document.findByPk(id, {
    include: [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'username', 'email'],
      },
      {
        model: Category,
        as: 'category',
      },
      {
        model: Tag,
        through: { attributes: [] }, // Don't include junction table
      },
      {
        model: Attachment,
      },
    ],
  });
};

/**
 * Update document by id
 * @param {string} documentId
 * @param {Object} updateBody
 * @returns {Promise<Document>}
 */
const updateDocumentById = async (documentId, updateBody) => {
  const document = await getDocumentById(documentId);
  if (!document) {
    throw new ApiError(404, 'Document not found');
  }

  // Update basic fields
  Object.keys(updateBody).forEach((key) => {
    if (key !== 'tag') {
      document[key] = updateBody[key];
    }
  });

  // Update tags if provided
  if (updateBody.tags && Array.isArray(updateBody.tags)) {
    await document.setTags(updateBody.tags);
  }

  await document.save();
  return getDocumentById(documentId); // Return fresh data with associations
};

/**
 * Delete document by id
 * @param {string} documentId
 * @returns {Promise<void>}
 */
const deleteDocumentById = async (documentId) => {
  const document = await getDocumentById(documentId);
  if (!document) {
    throw new ApiError(404, 'Document not found');
  }
  await document.destroy();
};

/**
 * Add tags to a document
 * @param {string} documentId
 * @param {Array} tagIds
 * @returns {Promise<Document>}
 */
const addTagsToDocument = async (documentId, tagIds) => {
  const document = await getDocumentById(documentId);
  if (!document) {
    throw new ApiError(404, 'Document not found');
  }

  await document.addTags(tagIds);
  return getDocumentById(documentId);
};

/**
 * Remove tags from a document
 * @param {string} documentId
 * @param {Array} tagIds
 * @returns {Promise<Document>}
 */
const removeTagsFromDocument = async (documentId, tagIds) => {
  const document = await getDocumentById(documentId);
  if (!document) {
    throw new ApiError(404, 'Document not found');
  }

  await document.removeTags(tagIds);
  return getDocumentById(documentId);
};

module.exports = {
  createDocument,
  queryDocuments,
  getDocumentById,
  updateDocumentById,
  deleteDocumentById,
  addTagsToDocument,
  removeTagsFromDocument,
};