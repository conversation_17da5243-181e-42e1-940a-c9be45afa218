<template>
  <div class="department-node">
    <div
      class="flex items-center p-2 rounded-md hover:bg-gray-100 cursor-pointer mb-1"
      @click="$emit('view', department.id)"
    >
      <div class="flex-1">
        <div class="font-medium">{{ department.name }} ({{ department.code }})</div>
      </div>
      <div>
        <i class="fas fa-chevron-right text-gray-400"></i>
      </div>
    </div>
    
    <!-- Render children recursively -->
    <div v-if="department.children && department.children.length > 0" class="pl-6 border-l-2 border-gray-200 ml-3">
      <div v-for="child in department.children" :key="child.id" class="mb-2">
        <DepartmentTreeNode :department="child" @view="id => $emit('view', id)" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

defineProps({
  department: {
    type: Object,
    required: true
  }
});

defineEmits(['view']);
</script>

<style scoped>
.department-node {
  transition: all 0.2s ease;
}
</style> 