const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Finance = sequelize.define(
  'finance',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    projectId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'project',
        key: 'id',
      },
    },
    supplierId: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'supplier',
        key: 'id',
      },
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isIn: [['income', 'expense']],
      },
    },
    category: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'USD',
    },
    date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    reference: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    paymentMethod: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'pending',
      validate: {
        isIn: [['pending', 'completed', 'cancelled']],
      },
    },
    attachments: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    createdBy: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'user',
        key: 'id',
      },
    },
    buyerInfo: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    invoiceDate: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    invoiceTaxRate: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
    },
    invoiceType: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    incomePayments: {
      type: DataTypes.JSON,
      allowNull: true,
      defaultValue: [],
    },
    sellerInfo: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    inputInvoiceDate: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    inputInvoiceTaxRate: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
    },
    inputInvoiceType: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    subcontractPayment: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
    },
    purchasePayment: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
    },
    reimbursementPayment: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
    },
    netIncome: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
    },
  },
  {
    timestamps: true,
  }
);

// Define associations
const setupAssociations = (db) => {
  Finance.belongsTo(db.Project, {
    foreignKey: 'projectId',
  });

  Finance.belongsTo(db.Supplier, {
    foreignKey: 'supplierId',
  });

  Finance.belongsTo(db.User, {
    foreignKey: 'createdBy',
    as: 'Creator',
  });
};

module.exports = {
  Finance,
  setupAssociations,
};