const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Attachment = sequelize.define('attachment', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  documentId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'document',
      key: 'id'
    }
  },
  reimbursementId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'reimbursement',
      key: 'id'
    }
  },
  subcontractId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'subcontract',
      key: 'id'
    }
  },
  fileName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  originalName: {
    type: DataTypes.STRING,
    allowNull: false
  },
  fileSize: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  mimeType: {
    type: DataTypes.STRING,
    allowNull: false
  },
  filePath: {
    type: DataTypes.STRING,
    allowNull: false
  },
  uploadedBy: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'user',
      key: 'id'
    }
  },
  isPublic: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  timestamps: true,
  freezeTableName: true
});

// Define associations
const setupAssociations = (models) => {
  if (models.User) {
    Attachment.belongsTo(models.User, {
      foreignKey: 'uploadedBy',
      as: 'uploader'
    });
  }

  if (models.Document) {
    Attachment.belongsTo(models.Document, {
      foreignKey: 'documentId',
      as: 'document'
    });
  }

  if (models.Reimbursement) {
    Attachment.belongsTo(models.Reimbursement, {
      foreignKey: 'reimbursementId',
      as: 'reimbursement'
    });
  }

  if (models.Subcontract) {
    Attachment.belongsTo(models.Subcontract, {
      foreignKey: 'subcontractId',
      as: 'subcontract'
    });
  }
};

module.exports = { Attachment, setupAssociations };