# Playwright MCP 自动化测试框架 (增强版)

本框架使用 Playwright 的 MCP (Multi-Channel Protocol) 功能实现自动化测试，并具有自动错误检测和修复功能。增强版提供了更强大的智能测试功能和更全面的错误处理能力。

## 功能特点

- 自动检测和修复常见测试问题
- 支持多浏览器测试 (Chromium, Firefox, WebKit)
- 详细的测试报告和错误日志
- 测试失败自动重试和智能修复
- 会话过期自动处理
- 表单验证错误自动修复
- 智能元素选择和交互
- 自动生成测试数据
- 自动截图和错误记录
- 网络请求监控和错误处理

## 安装

确保已安装 Node.js (v14+)，然后运行：

```bash
cd src/e2e
npm install
npm run install:browsers
```

## 运行测试

### 运行标准测试

```bash
npm test
```

### 运行带自动修复的 MCP 测试

```bash
npm run test:mcp
```

### 运行增强版测试 (推荐)

增强版测试提供了更强大的自动修复和智能测试功能。

```bash
npm run test:enhanced
# 或使用批处理文件
run-enhanced-tests.bat
```

### 使用 UI 模式运行测试

```bash
npm run test:ui
```

### 查看测试报告

```bash
npm run report
```

增强版测试会自动生成并打开HTML测试报告。

## 测试文件结构

- `tests/` - 测试用例文件
  - `*-mcp.spec.js` - 支持自动修复的 MCP 测试
  - `*-enhanced.spec.js` - 增强版智能测试
  - `*.spec.js` - 标准测试
- `fixtures/` - 测试夹具和共享配置
- `utils/` - 测试辅助函数
  - `test-helpers.js` - 测试辅助函数库
- `playwright.mcp.config.js` - MCP 测试配置
- `run-mcp-tests.js` - MCP 测试运行器
- `run-enhanced-tests.js` - 增强版测试运行器
- `logs/` - 错误日志目录
- `screenshots/` - 测试截图目录
- `test-results/` - 测试结果目录
- `test-report/` - 测试报告目录

## 自动修复功能

本框架能够自动检测和修复以下类型的问题：

1. **认证问题**
   - 会话过期
   - 登录凭据无效
   - 权限错误

2. **元素选择器问题**
   - 找不到元素
   - 元素选择器变更
   - 元素加载超时

3. **表单验证问题**
   - 必填字段验证
   - 格式验证错误
   - 数据验证失败

4. **导航和超时问题**
   - 页面加载超时
   - 网络请求超时
   - 导航错误

## 编写测试

### 编写标准 MCP 测试

创建新的 MCP 测试文件时，请遵循以下模式：

```javascript
// 导入 MCP 测试框架
const { test, expect } = require('../fixtures/test-fixtures');
const { navigateTo, fillForm } = require('../utils/test-helpers');

test.describe('功能测试 (MCP)', () => {
  test.beforeEach(async ({ page }) => {
    // 使用自动修复的登录
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');

    // 等待导航完成，如果失败会自动修复
    try {
      await page.waitForURL('/dashboard');
    } catch (error) {
      console.log('登录失败，尝试自动修复...');
      // 自动修复逻辑会在这里处理
    }
  });

  test('测试用例', async ({ page }) => {
    // 测试代码
  });
});
```

### 编写增强版智能测试 (推荐)

增强版测试提供了更强大的智能功能和自动修复能力：

```javascript
// 导入增强版测试框架
const { test, expect } = require('../fixtures/test-fixtures');
const { generateTestData } = require('../utils/test-helpers');

test.describe('功能测试 (增强版)', () => {
  // 使用智能页面夹具
  test('智能测试用例', async ({ smartPage, testInfo }) => {
    // 智能导航 - 自动处理错误和验证页面标题
    await smartPage.smartNavigateTo('/employees/contract', '合同工管理');

    // 智能表单填充 - 自动处理字段类型和验证
    const formData = {
      name: generateTestData('name'),
      idNumber: generateTestData('idNumber'),
      phone: generateTestData('phone')
    };
    await smartPage.smartFillForm(formData);

    // 智能点击 - 自动处理元素查找和错误
    await smartPage.click('button[type="submit"]');

    // 测试信息记录
    testInfo.testData.formData = formData;
  });
});
```

## 常见问题

### 测试失败但没有自动修复

某些复杂问题可能需要手动修复。查看测试报告中的错误详情，并检查相关的测试文件和应用代码。

### 如何添加新的自动修复规则

编辑 `run-mcp-tests.js` 文件中的 `analyzeError` 和 `applyFixes` 函数，添加新的错误模式和修复逻辑。

### 如何禁用自动修复

在 `run-mcp-tests.js` 文件中，将 `autoFix` 配置项设置为 `false`。

## 贡献

欢迎提交问题报告和改进建议！
