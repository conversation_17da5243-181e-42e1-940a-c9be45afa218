const Joi = require('joi');

// Note: File validation will primarily happen in the upload middleware
// This schema is for additional metadata validation
const createAttachmentSchema = {
  params: Joi.object().keys({
    documentId: Joi.string().uuid().required().messages({
      'string.empty': '文档ID不能为空',
      'string.guid': '文档ID必须是有效的UUID格式',
      'any.required': '文档ID是必填项'
    })
  }),
  body: Joi.object().keys({
    description: Joi.string().allow('', null),
    isPublic: Joi.boolean().default(false).messages({
      'boolean.base': '公开状态必须是布尔值'
    })
  })
};

const updateAttachmentSchema = {
  params: Joi.object().keys({
    id: Joi.string().uuid().required().messages({
      'string.empty': '附件ID不能为空',
      'string.guid': '附件ID必须是有效的UUID格式',
      'any.required': '附件ID是必填项'
    })
  }),
  body: Joi.object().keys({
    description: Joi.string().allow('', null),
    isPublic: Joi.boolean().messages({
      'boolean.base': '公开状态必须是布尔值'
    })
  }).min(1).messages({
    'object.min': '至少需要提供一个要更新的字段'
  })
};

const getAttachmentSchema = {
  params: Joi.object().keys({
    id: Joi.string().uuid().required().messages({
      'string.empty': '附件ID不能为空',
      'string.guid': '附件ID必须是有效的UUID格式',
      'any.required': '附件ID是必填项'
    })
  })
};

const deleteAttachmentSchema = {
  params: Joi.object().keys({
    id: Joi.string().uuid().required().messages({
      'string.empty': '附件ID不能为空',
      'string.guid': '附件ID必须是有效的UUID格式',
      'any.required': '附件ID是必填项'
    })
  })
};

const getAttachmentsSchema = {
  params: Joi.object().keys({
    documentId: Joi.string().uuid().required().messages({
      'string.empty': '文档ID不能为空',
      'string.guid': '文档ID必须是有效的UUID格式',
      'any.required': '文档ID是必填项'
    })
  }),
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).default(1).messages({
      'number.base': '页码必须是数字',
      'number.integer': '页码必须是整数',
      'number.min': '页码不能小于{#limit}'
    }),
    limit: Joi.number().integer().min(1).max(100000).default(10).messages({
      'number.base': '每页数量必须是数字',
      'number.integer': '每页数量必须是整数',
      'number.min': '每页数量不能小于{#limit}',
      'number.max': '每页数量不能超过{#limit}'
    }),
    sortBy: Joi.string().valid('fileName', 'fileSize', 'createdAt').default('createdAt').messages({
      'any.only': '排序字段必须是文件名(fileName)、文件大小(fileSize)或创建时间(createdAt)'
    }),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc').messages({
      'any.only': '排序方式必须是升序(asc)或降序(desc)'
    }),
    isPublic: Joi.boolean().allow(null).messages({
      'boolean.base': '公开状态必须是布尔值'
    })
  })
};

module.exports = {
  createAttachmentSchema,
  updateAttachmentSchema,
  getAttachmentSchema,
  deleteAttachmentSchema,
  getAttachmentsSchema
};